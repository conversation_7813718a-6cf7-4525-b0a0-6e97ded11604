{"version": 1.1, "pre-load-external-sets": [], "sets": {"messages": {"keys": ["testing.hello", "messages.testText", "messages.messagesTitle", "messages.feedbackModalHeader", "messages.feedbackModalContent", "messages.feedbackModalTextareaLabel", "messages.sendFeedback", "messages.feedbackSent", "messages.feedbackCharacterCount", "messages.errorSendingFeedback", "messages.nothingYet", "messages.messageDeleted", "messages.attachment", "messages.emptyMessage", "messages.messageFromYou", "messages.emptyStateStartAConversation", "messages.emptyStateChannelTitle", "messages.send", "messages.mute", "messages.unmute", "messages.noSearchResults", "messages.searchResultInChannelLabel", "messages.emptyStateChannelTitle", "messages.mute", "messages.unmute", "messages.organizations", "messages.searchFor", "messages.createMessage", "messages.groups", "messages.individuals", "messages.searchResultsFor", "messages.retry", "messages.couldNotCreateChannel", "messages.chatGuidelinesDisclosure", "messages.copied", "messages.reported", "messages.oldMessagesHeader", "messages.oldMessagesText", "messages.oldMessagesTextEndOfLifeV2", "messages.oldMessagesCta", "messages.members", "messages.cancel", "messages.enterConversationName", "messages.muteNotifications", "messages.unmuteNotifications", "messages.readBy<PERSON>ooter<PERSON><PERSON><PERSON>", "messages.readByOnePersonFooter<PERSON>abel", "messages.notViewed", "messages.viewed", "messages.messageViews", "messages.notViewedDescription", "messages.viewedDescription", "messages.safeSportEnforcementInfoForOrg", "messages.safeSportEnforcementError", "messages.safeSportEnforcementInfoUpcoming", "messages.safeSportEnforcementInfoUpcomingHighschool", "messages.learnMore", "messages.safeSportEnforcementCTA", "messages.changeGroupMembers", "messages.safeSportsCTA", "messages.searchBarInputPlaceholder", "messages.teamAdmin", "messages.orgAdmin", "messages.coach", "messages.athlete", "messages.guardian", "messages.cannotCreateChannelInOrg", "messages.readOnlyChannel", "messages.allTeamRolesGroupPickerNoFamilyMembers", "messages.allTeamRolesGroupPickerWithFamilyMembers", "messages.allTeamRolesHeaderNoFamilyMembers", "messages.allTeamRolesHeaderWithFamilyMembers", "messages.familyMember", "messages.cannotCreateChannelInOrgListAdmins", "messages.readOnlyChannelListAdmins", "messages.safeSportEnforcementInfoUpcomingAdministrator", "messages.safeSportEnforcementInfoUpcomingHighschoolAdministrator", "messages.hide", "messages.hideConfirmationAlert", "messages.hideConfirmationAlertConfirmButton", "messages.hideSuccessToast", "messages.hideFailureToast", "messages.channelTeamSelectorPlaceholder", "messages.channelTeamSelectorLabel", "messages.channelTeamSelectorHelpText", "messages.none", "messages.productUpdates.teamLabels.title", "messages.productUpdates.teamLabels.description", "messages.giveFeedback", "messages.newMessage", "messages.pressEnterToSend", "messages.rateLimitExceeded", "messages.productUpdates.organizeImprovements.title", "messages.productUpdates.organizeImprovements.description", "messages.messagingDisabledForOrg", "messages.cannotCreateMessagesForOrg", "messages.messagingTurnedOffByOrg", "messages.messagingDisabledNotice", "messages.messagingTurnedOffByOrgAdminCta"]}}, "base-language": {"testing.hello": "hello in English", "messages.testText": "How are ya now?", "messages.messagesTitle": "Messages", "messages.mute": "Mute", "messages.unmute": "Unmute", "messages.feedbackModalHeader": "Provide <PERSON>", "messages.feedbackModalContent": "How do you feel about the new Hudl messaging experience? How could it be improved? (Please do not include any personal information in your response.) *", "messages.feedbackModalTextareaLabel": "Comments", "messages.sendFeedback": "Send Feedback", "messages.feedbackSent": "Feed<PERSON> sent!", "messages.feedbackCharacterCount": "{characterCount}/2000", "messages.errorSendingFeedback": "Couldn't submit your feedback. Please try again.", "messages.nothingYet": "Nothing yet...", "messages.messageDeleted": "Message deleted", "messages.attachment": "Attachment...", "messages.emptyMessage": "Empty message...", "messages.messageFromYou": "You: ", "messages.emptyStateStartAConversation": "Start a conversation", "messages.emptyStateChannelTitle": "Great teamwork starts with communication.", "messages.organizations": "Organizations", "messages.searchFor": "Search for...", "messages.createMessage": "Create Message", "messages.groups": "Groups", "messages.individuals": "Individuals", "messages.searchResultsFor": "Search Results For", "messages.retry": "Retry", "messages.couldNotCreateChannel": "Unable to create message. Please try again", "messages.chatGuidelinesDisclosure": "To align with industry guidelines for electronic messaging for youth sports, Hudl messaging requires that chats with athletes include 2 coaches or a parent/guardian.", "messages.send": "Send", "messages.noSearchResults": "We're sorry. We weren't able to find a match.", "messages.searchResultInChannelLabel": "In: {channelName}", "messages.copied": "Copied!", "messages.reported": "Report Submitted.", "messages.oldMessagesHeader": "Messages has been updated.", "messages.oldMessagesText": "You can still read your old messages.", "messages.oldMessagesTextEndOfLifeV2": "You will be able to view your old messages on the web until November 4.", "messages.oldMessagesCta": "View old messages", "messages.members": "Members", "messages.cancel": "Cancel", "messages.enterConversationName": "Enter a conversation name", "messages.muteNotifications": "Mute Notifications", "messages.unmuteNotifications": "Unmute Notifications", "messages.readByFooterLabel": "Viewed by {readByCount} people", "messages.readByOnePersonFooterLabel": "Viewed by 1 person", "messages.notViewed": "Not Viewed ({notViewedCount} of {totalParticipantCount})", "messages.viewed": "Viewed ({viewedCount} of {totalParticipantCount})", "messages.messageViews": "Message Views", "messages.notViewedDescription": "Members who have not viewed your message. They may have turned off notifications or muted this conversation.", "messages.viewedDescription": "Members who have viewed your message.", "messages.safeSportEnforcementInfoForOrg": "Based on team settings within your organization, when a conversation includes an athlete and a coach/admin, you may need to include at least two coaches/admins.", "messages.safeSportEnforcementError": "Athlete safety settings require you to", "messages.safeSportEnforcementCTA": "add another coach or admin to start this conversation.", "messages.safeSportEnforcementInfoUpcoming": "On June 10th, athlete safety measures will be enforced. When a conversation includes an athlete and a coach/admin, you’ll need to include at least two coaches/admins. Org admins can adjust this in team settings.", "messages.safeSportEnforcementInfoUpcomingHighschool": "In January 2025, athlete safety measures will be enforced. When a conversation includes an athlete and a coach/admin, you’ll need to include at least two coaches/admins. Org admins can adjust this in team settings.", "messages.learnMore": "Learn More", "messages.changeGroupMembers": "Groups in this conversation already exist in Hudl. To add or remove group members, go to “Manage Team.” Only coaches and team admins can add or remove members.", "messages.safeSportsCTA": "Please add another coach or admin to start this conversation.", "messages.searchBarInputPlaceholder": "Search for conversations or replies", "messages.teamAdmin": "Team Admin", "messages.orgAdmin": "Organization Admin", "messages.coach": "Coach", "messages.athlete": "Athlete", "messages.guardian": "Guardian", "messages.cannotCreateChannelInOrg": "You can't create messages for this org. This can only be adjusted by an org admin in their org settings.", "messages.readOnlyChannel": "Only certain people can post in this channel. This can only be adjusted by an org admin.", "messages.allTeamRolesGroupPickerNoFamilyMembers": "Includes all team admins, coaches and athletes on this team.", "messages.allTeamRolesGroupPickerWithFamilyMembers": "Includes all team admins, coaches, athletes and family members on this team.", "messages.allTeamRolesHeaderNoFamilyMembers": "This conversation includes all team admins, coaches and athletes on this team.", "messages.allTeamRolesHeaderWithFamilyMembers": "This conversation includes all team admins, coaches, athletes and family members on this team.", "messages.familyMember": "Family Member", "messages.cannotCreateChannelInOrgListAdmins": "You can't create messages for this organization. This can only be adjusted by your organization administrator(s) in their organization settings: {orgAdminsList}", "messages.readOnlyChannelListAdmins": "Only certain people can post in this channel. This can only be adjusted by your organization administrator(s): {orgAdminsList}", "messages.safeSportEnforcementInfoUpcomingAdministrator": "On June 10th, athlete safety measures will be enforced. When a conversation includes an athlete and a coach/admin, you'll need to include at least two coaches/admins. Organization administrators can adjust this in team settings.", "messages.safeSportEnforcementInfoUpcomingHighschoolAdministrator": "In January 2025, athlete safety measures will be enforced. When a conversation includes an athlete and a coach/admin, you'll need to include at least two coaches/admins. Organization administrators can adjust this in team settings.", "messages.hide": "<PERSON>de", "messages.hideConfirmationAlert": "Are you sure you want to hide this conversation? You’ll still be able to find this conversation when new messages are sent by other members or by recreating it.", "messages.hideConfirmationAlertConfirmButton": "Yes, <PERSON>de", "messages.hideSuccessToast": "You hid one conversation", "messages.hideFailureToast": "We weren't able to hide this conversation. Please try again.", "messages.channelTeamSelectorPlaceholder": "Select a team", "messages.channelTeamSelectorLabel": "Team Label", "messages.channelTeamSelectorHelpText": "Use a team label to organize your conversations", "messages.none": "None", "messages.productUpdates.teamLabels.title": "Label Group Messages by Team", "messages.productUpdates.teamLabels.description": "Team labels make it easier to find and organize conversations in your inbox. You can edit team labels at any time.", "messages.giveFeedback": "<PERSON>", "messages.newMessage": "New Message", "messages.pressEnterToSend": "Press Enter to Send", "messages.rateLimitExceeded": "Rate limit exceeded. Try again in {secondsUntilRetry} seconds.", "messages.productUpdates.organizeImprovements.title": "What's New in Messaging", "messages.productUpdates.organizeImprovements.description": "It's now easier to start conversations, find the right people, and keep your inbox organized and clutter-free.", "messages.messagingDisabledForOrg": "Messaging is disabled for this organization.", "messages.cannotCreateMessagesForOrg": "You can't create messages for this org. This can only be adjusted by an org admin in their org settings.", "messages.messagingDisabledNotice": "Messaging has been turned off by your organization. This setting can be updated by an org admin.", "messages.messagingTurnedOffByOrg": "Messaging is currently turned off by your organization.", "messages.messagingTurnedOffByOrgAdminCta": "You can turn it on in org Settings."}}