import { type Locator, type Page } from '@playwright/test';

import { ConversationPage } from './ConversationPage';

/**
 * Organization Messaging Settings base page
 *
 */

export class OrganizationMessagingSettingsBasePage extends ConversationPage {
  readonly readOnlyConvoBanner: Locator;

  constructor(page: Page) {
    super(page);
    this.readOnlyConvoBanner = page.getByTestId('messaging-disabled-notice');
  }
}
