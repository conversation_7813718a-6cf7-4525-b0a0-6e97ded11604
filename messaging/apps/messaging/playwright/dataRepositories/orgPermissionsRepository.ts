import {
  OrgAccessLevelType,
  PermissionsSetType,
  WebMessagingGetOrgMessagingSettingsR1Query,
} from '../../src/graphql/graphqlTypes';

export function getOrgPermissionsViewOnly(): WebMessagingGetOrgMessagingSettingsR1Query {
  return {
    me: {
      id: '123',
      orgMessagingSettings: [
        {
          orgId: '182932',
          permissionsSet: PermissionsSetType.READ_ONLY,
          orgAccessLevel: OrgAccessLevelType.TEAM,
          messagingEnabled: false,
          __typename: 'UserOrgMessagingSettings',
        },
      ],
      __typename: 'User',
    },
  };
}

export function getOrgPermissionsReadRespondOnly(): WebMessagingGetOrgMessagingSettingsR1Query {
  return {
    me: {
      id: '123',
      orgMessagingSettings: [
        {
          orgId: '182932',
          permissionsSet: PermissionsSetType.READ_RESPOND_ONLY,
          orgAccessLevel: OrgAccessLevelType.TEAM,
          messagingEnabled: true,
          __typename: 'UserOrgMessagingSettings',
        },
      ],
      __typename: 'User',
    },
  };
}

export function getOrgPermissionsReadRespondCreate(): WebMessagingGetOrgMessagingSettingsR1Query {
  return {
    me: {
      id: '123',
      orgMessagingSettings: [
        {
          orgId: '182932',
          permissionsSet: PermissionsSetType.READ_RESPOND_CREATE,
          orgAccessLevel: OrgAccessLevelType.TEAM,
          messagingEnabled: true,
          __typename: 'UserOrgMessagingSettings',
        },
      ],
      __typename: 'User',
    },
  };
}
