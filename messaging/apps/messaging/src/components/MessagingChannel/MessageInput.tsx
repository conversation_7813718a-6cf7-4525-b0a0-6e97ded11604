import { type ChangeEvent, MouseEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import classNames from 'classnames';
import type { Event } from 'stream-chat';
import {
  AttachmentPreviewList,
  ChatAutoComplete,
  DefaultStreamChatGenerics,
  QuotedMessagePreview,
  useChannelActionContext,
  useChannelStateContext,
  useChatContext,
  useMessageInputContext,
} from 'stream-chat-react';

import { Checkbox, Divider, IconAttach, IconBolt } from '@hudl/uniform-web';
import { Button } from '@hudl/uniform-web-button-legacy';
import { format } from 'frontends-i18n';

import { PermissionsSetType, UpdateUserPreferenceOperation, UserPreferenceType } from '../../graphql/graphqlTypes';
import { useGetUserPreference, useUpdateUserPreference } from '../../hooks/useUserPreference';
import { useOrgMessagingSettingsContext } from '../MessagingContext';
import { MessagingDisabledNotice } from './MessagingDisabledNotice';
import { ReadOnlyInput } from './ReadOnlyInput';

import styles from './MessageInput.module.scss';

function MessageInput(): React.JSX.Element {
  const {
    handleSubmit,
    uploadNewFiles,
    openCommandsList,
    closeCommandsList,
    showCommandsList,
    isUploadEnabled,
    numberOfUploads,
    text,
    fileUploads,
  } = useMessageInputContext();

  const { quotedMessage } = useChannelStateContext('MessageInput');
  const { setQuotedMessage } = useChannelActionContext('MessageInput');
  const { channel } = useChatContext('MessageInput');
  const { orgMessagingSettings, isLoading } = useOrgMessagingSettingsContext();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { userPreference, userPreferenceLoading } = useGetUserPreference({
    preferenceName: 'messaging-press-enter-to-send',
    type: UserPreferenceType.BOOL,
    defaultValue: { asBool: true },
  });
  const { updateUserPreference } = useUpdateUserPreference();
  const [pressEnterToSend, setPressEnterToSend] = useState(userPreference?.preferenceValue.asBool);

  const orgSettings = useMemo(() => {
    return orgMessagingSettings?.find((settings) => settings.orgId === channel?.data?.team);
  }, [orgMessagingSettings, channel?.data?.team]);

  const canSendMessagesInChannel = useMemo(() => {
    return (
      isLoading ||
      (orgSettings?.messagingEnabled !== false && orgSettings?.permissionsSet !== PermissionsSetType.READ_ONLY)
    );
  }, [orgSettings, isLoading]);

  const isMessagingDisabled = useMemo(() => {
    return !isLoading && orgSettings?.messagingEnabled === false;
  }, [orgSettings, isLoading]);

  const handleSubmitClick = useCallback(
    (e?: MouseEvent<HTMLButtonElement>) => {
      if (e) {
        handleSubmit(e);
      } else {
        throw new Error('Unable to send message without event');
      }
    },
    [handleSubmit]
  );

  const handleCommandsClick = useCallback(() => {
    openCommandsList();
  }, [openCommandsList]);

  const handleAttachmentClick = useCallback(() => {
    fileInputRef.current!.click();
  }, [fileInputRef]);

  const handleAttachmentFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      uploadNewFiles(files);
    }
  };
  const hasUploads = numberOfUploads > 0;
  const hasMessage = !!text || hasUploads;
  const hasFiles = hasUploads && Object.keys(fileUploads).length > 0;

  useEffect(() => {
    if (userPreferenceLoading) return;
    if (userPreference) {
      setPressEnterToSend(userPreference.preferenceValue.asBool);
    } else {
      setPressEnterToSend(false);
    }
  }, [userPreference, userPreferenceLoading]);

  useEffect(() => {
    if (!text && showCommandsList) {
      closeCommandsList();
    }
  }, [text, numberOfUploads, closeCommandsList, showCommandsList]);

  useEffect(() => {
    const handleQuotedMessageUpdate = (e: Event<DefaultStreamChatGenerics>) => {
      if (!(quotedMessage && e.message?.id === quotedMessage.id)) return;
      if (e.type === 'message.deleted') {
        setQuotedMessage(undefined);
        return;
      }
      setQuotedMessage(e.message);
    };
    channel?.on('message.deleted', handleQuotedMessageUpdate);
    channel?.on('message.updated', handleQuotedMessageUpdate);

    return () => {
      channel?.off('message.deleted', handleQuotedMessageUpdate);
      channel?.off('message.updated', handleQuotedMessageUpdate);
    };
  }, [channel, quotedMessage, setQuotedMessage]);

  const onPressEnterToSendChange = useCallback(
    (e: ChangeEvent): void => {
      const { checked } = e.target as HTMLInputElement;
      setPressEnterToSend(checked);
      updateUserPreference({
        preferenceName: 'messaging-press-enter-to-send',
        value: { asBool: checked },
        operation: UpdateUserPreferenceOperation.SET,
      });
    },
    [updateUserPreference]
  );

  if (isMessagingDisabled) {
    return <MessagingDisabledNotice />;
  }

  if (!canSendMessagesInChannel) {
    return <ReadOnlyInput orgId={channel?.data?.team as string} />;
  }

  const handleSubmitEvent = (e: React.BaseSyntheticEvent): void => {
    // If the user is pressing enter and the checkbox is checked, we want to send the message
    if (pressEnterToSend) {
      handleSubmit(e);
    }
    // If the user is pressing enter and the checkbox is not checked, we want to add a new line
    else {
      const textarea = e.target as HTMLTextAreaElement;
      const { selectionStart, selectionEnd } = textarea;
      const value = textarea.value;
      textarea.value = value.substring(0, selectionStart) + '\n' + value.substring(selectionEnd);
      textarea.setSelectionRange(selectionStart + 1, selectionStart + 1);
    }
  };

  return (
    <div className={styles.composerContainer}>
      <div>
        <Divider color="level2-accent" />
        <div className="str-chat__message-input rfu-file-upload-button">
          <input
            className={styles.fileInput}
            type="file"
            ref={fileInputRef}
            onChange={handleAttachmentFileChange}
            multiple
          />
          <div className={'str-chat__message-input-inner ' + styles.inputContainer}>
            <div onClick={handleAttachmentClick} className={styles.baselineButton} data-qa-id="attachments-button">
              <IconAttach className={styles.icon} size="large" color="information" />
            </div>
            {!hasMessage && (
              <div onClick={handleCommandsClick} className={styles.baselineButton} data-qa-id="commands-button">
                <IconBolt className={styles.icon} size="large" color="action" />
              </div>
            )}
            <div className="str-chat__message-textarea-container">
              {isUploadEnabled && hasUploads && <AttachmentPreviewList />}
              {quotedMessage && <QuotedMessagePreview quotedMessage={quotedMessage} />}
              {!hasFiles && (
                <div className="str-chat__message-textarea-with-emoji-picker">
                  <ChatAutoComplete handleSubmit={handleSubmitEvent} />
                </div>
              )}
            </div>
            {hasMessage && (
              <Button
                buttonStyle="minimal"
                onClick={handleSubmitClick}
                className={classNames({
                  [styles.baselineButton]: true,
                })}
                qaId="send-message-button"
              >
                {format('messages.send')}
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className={styles.checkboxContainer}>
        <Checkbox
          label={format('messages.pressEnterToSend')}
          onChange={onPressEnterToSendChange}
          isChecked={pressEnterToSend}
        />
      </div>
    </div>
  );
}

export default MessageInput;
