import { useCallback, useMemo } from 'react';

import { useNavigate } from 'react-router-dom';

import { Text } from '@hudl/uniform-web';
import { Button } from '@hudl/uniform-web-button-legacy';
import { format } from 'frontends-i18n';

import { OrgAccessLevelType, PermissionsSetType } from '../../graphql/graphqlTypes';
import useMessageComposerRoute from '../../hooks/useMessagingComposerRoute';
import { useOrgMessagingSettingsContext } from '../MessagingContext';

import styles from './EmptyChannelPlaceholder.module.scss';

function EmptyChannelPlaceholder(): JSX.Element {
  const navigate = useNavigate();
  const route = useMessageComposerRoute();
  const { orgMessagingSettings, isLoading } = useOrgMessagingSettingsContext();

  const hasAnyOrgWithMessagingEnabled = useMemo(() => {
    return !isLoading && orgMessagingSettings?.some((settings) => settings.messagingEnabled === true);
  }, [isLoading, orgMessagingSettings]);

  const hasAnyOrgWithCreatePermissions = useMemo(() => {
    return (
      !isLoading &&
      orgMessagingSettings?.some(
        (settings) =>
          settings.messagingEnabled === true && settings.permissionsSet === PermissionsSetType.READ_RESPOND_CREATE
      )
    );
  }, [isLoading, orgMessagingSettings]);

  const isOrgAdmin = useMemo(() => {
    return (
      !isLoading &&
      orgMessagingSettings?.some((settings) => settings.orgAccessLevel === OrgAccessLevelType.ORGANIZATION)
    );
  }, [isLoading, orgMessagingSettings]);

  const onComposeClick = useCallback(() => {
    navigate({
      pathname: route,
    });
  }, [navigate, route]);

  const showMessagingDisabledMessage = !hasAnyOrgWithMessagingEnabled;
  const showAdminCta = showMessagingDisabledMessage && isOrgAdmin;

  const displayText = useMemo(() => {
    if (showMessagingDisabledMessage) {
      const baseText = format('messages.messagingTurnedOffByOrg');
      return showAdminCta ? `${baseText} ${format('messages.messagingTurnedOffByOrgAdminCta')}` : baseText;
    }
    return format('messages.emptyStateChannelTitle');
  }, [showMessagingDisabledMessage, showAdminCta]);

  return (
    <div className={styles.emptyChannelPlaceholderContainer}>
      <div>
        <Text className={styles.primaryText}>{displayText}</Text>
      </div>
      {hasAnyOrgWithMessagingEnabled && (
        <div>
          <Button
            size="small"
            buttonType="primary"
            buttonStyle="minimal"
            onClick={onComposeClick}
            isDisabled={!hasAnyOrgWithCreatePermissions}
          >
            {format('messages.emptyStateStartAConversation')}
          </Button>
        </div>
      )}
    </div>
  );
}

export default EmptyChannelPlaceholder;
