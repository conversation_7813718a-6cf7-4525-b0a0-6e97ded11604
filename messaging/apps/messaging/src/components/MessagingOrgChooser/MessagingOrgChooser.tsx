import { useCallback, useEffect, useMemo, useState } from 'react';

import { useQuery } from '@apollo/client';
import { useNavigate } from 'react-router-dom';
import { useChatContext } from 'stream-chat-react';

import { Divider, IconInformation, IconUiExpandCollapseRight, Spinner, Subhead, Text } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import {
  WebMessagingGetSchoolsByOrgR1Document as GetSchoolsByOrg,
  PermissionsSetType,
  School,
  WebMessagingGetSchoolsByOrgR1Query,
} from '../../graphql/graphqlTypes';
import { useOrgMessagingSettingsContext, useUserSearchContext } from '../MessagingContext';

import styles from '../MessagingOrgChooser/MessagingOrgChooser.module.scss';

type SchoolWithPermissionsSet = School & {
  permissionsSet: string;
  messagingEnabled: boolean;
};

function MessagingOrgChooser(): React.JSX.Element {
  const [schoolsWithPermissionsSet, setSchoolsWithPermissionsSet] = useState<SchoolWithPermissionsSet[]>([]);
  const { client } = useChatContext();
  const { orgMessagingSettings, isLoading } = useOrgMessagingSettingsContext();

  const schoolIds = client.user?.teams || [];
  const { data, loading } = useQuery<WebMessagingGetSchoolsByOrgR1Query>(GetSchoolsByOrg, {
    variables: { orgIDList: [...schoolIds] },
  });

  const filteredSortedOrgs: School[] = useMemo(() => {
    if (data?.schools) {
      const filteredSortedList = data.schools
        .filter((school): school is School => school !== null)
        .sort((a, b) => a.fullName.localeCompare(b.fullName));
      return filteredSortedList;
    }
    return [];
  }, [data]);

  useEffect(() => {
    if (isLoading) {
      return;
    }
    const schoolsToReturn: SchoolWithPermissionsSet[] = filteredSortedOrgs.map((school) => {
      const orgSettings = orgMessagingSettings?.find((org) => org.orgId === school.internalId);
      return {
        ...school,
        permissionsSet: orgSettings?.permissionsSet ?? 'READ_ONLY',
        messagingEnabled: orgSettings?.messagingEnabled ?? false,
      };
    });
    setSchoolsWithPermissionsSet(schoolsToReturn);
  }, [filteredSortedOrgs, orgMessagingSettings, isLoading]);

  if (loading || isLoading) {
    return (
      <div className={styles.centeredSpinner}>
        <Spinner size="large" />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Text className={styles.header}>{format('messages.organizations')}</Text>
      <OrgList schools={schoolsWithPermissionsSet} />
    </div>
  );
}

function OrgList({ schools }: { schools: SchoolWithPermissionsSet[] }): React.JSX.Element {
  return (
    <>
      {schools.map((school) => (
        <OrgRow key={`school_${school.internalId}`} school={school} />
      ))}
    </>
  );
}

function OrgRow({ school }: { school: SchoolWithPermissionsSet }): React.JSX.Element {
  const navigate = useNavigate();
  const { reset } = useUserSearchContext();
  const onClick = useCallback(() => {
    reset(school.internalId);
    navigate({
      pathname: `/messaging/compose/${school.internalId}`,
    });
  }, [navigate, reset, school.internalId]);

  const isDisabled = !school.messagingEnabled || school.permissionsSet !== PermissionsSetType.READ_RESPOND_CREATE;
  const disabledText = format('messages.cannotCreateMessagesForOrg');

  if (isDisabled) {
    return (
      <div>
        <div className={styles.orgRow}>
          <div className={styles.readOnlyOrgRowItem}>
            <Subhead level="subtle" className={styles.orgNameDisabled}>
              {school.fullName}
            </Subhead>
            <div className={styles.readOnlyNotice}>
              <IconInformation size="small" color="subtle" className={styles.readOnlyIcon} />
              <Text color="subtle" level="micro">
                {disabledText}
              </Text>
            </div>
          </div>
          <div className={styles.orgRowItem}>
            <IconUiExpandCollapseRight size="small" color="nonessential" />
          </div>
        </div>
        <Divider />
      </div>
    );
  }

  return (
    <div onClick={onClick} className={styles.clickable}>
      <div className={styles.orgRow}>
        <div className={styles.orgRowItem}>
          <Subhead level="subtle">{school.fullName}</Subhead>
        </div>
        <div className={styles.orgRowItem}>
          <IconUiExpandCollapseRight size="small" color="subtle" />
        </div>
      </div>
      <Divider />
    </div>
  );
}

export default MessagingOrgChooser;
