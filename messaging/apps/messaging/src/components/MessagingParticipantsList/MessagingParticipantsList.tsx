import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';

import { useQuery } from '@apollo/client';
import classnames from 'classnames';
import { useNavigate, useParams } from 'react-router-dom';
import { UserResponse } from 'stream-chat';
import { useChatContext } from 'stream-chat-react';

import { Logger } from '@hudl/frontends-logging';
import { getSafesportEnforcement } from '@hudl/stream-io';
import {
  AvatarUser,
  Divider,
  IconRemove,
  IconUiExpandCollapseDown,
  IconUiExpandCollapseUp,
  SelectMark,
  Spinner,
  Subhead,
  Text,
} from '@hudl/uniform-web';
import { Button } from '@hudl/uniform-web-button-legacy';
import { SearchInput } from '@hudl/uniform-web-forms-legacy';
import { format } from 'frontends-i18n';

import {
  AccountStatus,
  WebMessagingGetGroupsByTeamR2Document as GetGroupsByTeam,
  WebMessagingGetTeamsByOrgR1Document as GetTeamsByOrg,
  PermissionsSetType,
  WebMessagingGetGroupsByTeamR2Query,
  WebMessagingGetTeamsByOrgR1Query,
} from '../../graphql/graphqlTypes';
import useCreateChannel, { CreateChannelResponse } from '../../hooks/useCreateChannel';
import { Group, GroupAccordion } from '../../hooks/useGroups';
import useMessagingParticipantsMetadataBySchool from '../../hooks/useMessagingParticipantsMetadataBySchool';
import { AllTeamGroupName } from '../../utils/constants';
import getChannelName from '../../utils/getChannelName';
import { getInitialsFromName } from '../../utils/getInitialsFromName';
import {
  getMessagingParticipantsMetadata,
  getRoleDisplayString,
  ParticipantsDisplayMetadata,
} from '../../utils/getMessagingParticipantsMetadata';
import { LoggingAttributes } from '../../utils/loggingAttributes';
import AccordionList, {
  AccordionSection,
  SectionContentComponentProps,
  SectionHeaderComponentProps,
} from '../Accordion/Accordion';
import ChannelAvatar from '../MessagingChannelPreview/ChannelAvatar';
import {
  useCurrentChannelContext,
  useGroupSearchContext,
  useOrgMessagingSettingsContext,
  useUserSearchContext,
} from '../MessagingContext';
import SafeSportBanner from './SafeSportBanner';

import styles from './MessagingParticipantsList.module.scss';

interface Section {
  data: UserResponse[];
  title: string;
}

interface Props {
  results: UserResponse[];
  searchText: string;
  selectedUserIDs: string[];
  selectedUsers: UserResponse[];
  toggleUser: (user: UserResponse) => void;
  selectedGroups: Group[];
  toggleGroup: (group: Group) => void;
  loading: boolean;
  participantDisplayMetadata?: Record<string, ParticipantsDisplayMetadata>;
}

function MessagingParticipantsList(): React.JSX.Element {
  const logger = new Logger('StreamMessaging');
  const { orgId } = useParams();
  const { client } = useChatContext();
  const navigate = useNavigate();
  const { setActiveChannel } = useChatContext();
  const { changeChannel } = useCurrentChannelContext();
  const [messagingParticipantsMetadata, setMessagingParticipantsMetadata] = useState<
    Record<string, ParticipantsDisplayMetadata>
  >({});
  const {
    onChangeSearchText,
    setOrgID,
    reset,
    results,
    searchText,
    selectedUserIDs,
    toggleUser,
    selectedUsers,
    orgID: oldOrgId,
    loading,
  } = useUserSearchContext();
  const {
    selectedGroups,
    toggleGroup,
    selectedGroupIDs,
    reset: groupReset,
    setTeamsWithGroups,
  } = useGroupSearchContext();
  const [hasSafeSportEnforcement, setHasSafeSportEnforcement] = useState(false);
  const {
    createChannel,
    isCreating,
    channelCreationFailedSafesportEnforcement,
    setChannelCreationFailedSafesportEnforcement,
  } = useCreateChannel();

  const { orgMessagingSettings, isLoading: isLoadingOrgSettings } = useOrgMessagingSettingsContext();

  const {
    messagingParticipantsMetadata: allMessagingParticipantsMetadata,
    loading: loadingMessagingParticipantsMetadata,
    error: errorMessagingParticipantsMetadata,
  } = useMessagingParticipantsMetadataBySchool(orgId);

  useEffect(() => {
    if (allMessagingParticipantsMetadata?.length) {
      const metadata = getMessagingParticipantsMetadata(allMessagingParticipantsMetadata);
      setMessagingParticipantsMetadata(metadata);
    }
  }, [allMessagingParticipantsMetadata]);

  const { data: groupsData, loading: loadingGroupsByTeam } = useQuery(GetGroupsByTeam);
  const { data: teamsDataByOrg, loading: loadingTeamsByOrg } = useQuery(GetTeamsByOrg, {
    variables: { orgID: orgId },
    fetchPolicy: 'cache-first',
    nextFetchPolicy: 'cache-only',
  });

  useEffect(() => {
    const org = (teamsDataByOrg as WebMessagingGetTeamsByOrgR1Query)?.school;
    const activeTeams = org?.teams?.filter((team) => team?.teamStatus === AccountStatus.ENABLED);

    if (org?.internalId === orgId) {
      if (org?.isAdmin) {
        setTeamsWithGroups(activeTeams);
      } else {
        setTeamsWithGroups(
          activeTeams?.filter((team) =>
            (groupsData as WebMessagingGetGroupsByTeamR2Query)?.myTeams?.some((myTeam) => myTeam?.id === team?.id)
          )
        );
      }
    }
  }, [groupsData, teamsDataByOrg, orgId, setTeamsWithGroups, loadingTeamsByOrg, loadingGroupsByTeam]);

  useEffect(() => {
    setOrgID(orgId!);
    if (orgId !== oldOrgId) {
      reset(orgId);
    }
  }, [oldOrgId, orgId, reset, setOrgID]);

  useEffect(() => {
    const fetchEnforcement = async (): Promise<void> => {
      const enforcement = await getSafesportEnforcement(orgId ?? '');
      setHasSafeSportEnforcement(enforcement?.enforced || false);
    };
    fetchEnforcement();
  }, [orgId]);

  useEffect(() => {
    if (selectedUsers.length < 1 && selectedGroups.length < 1) {
      setChannelCreationFailedSafesportEnforcement(false);
    }
  }, [setChannelCreationFailedSafesportEnforcement, selectedGroups, selectedUsers]);

  // Verify that the org is not disabled
  useEffect(() => {
    if (isLoadingOrgSettings || !orgId) {
      return;
    }

    const orgSettings = orgMessagingSettings?.find((settings) => settings.orgId === orgId);

    // Check if org is disabled (messaging disabled or not READ_RESPOND_CREATE permissions)
    if (!orgSettings?.messagingEnabled || orgSettings?.permissionsSet !== PermissionsSetType.READ_RESPOND_CREATE) {
      // Redirect to org chooser
      navigate({
        pathname: '/messaging/compose/',
      });
    }
  }, [orgId, orgMessagingSettings, isLoadingOrgSettings, navigate, logger, client]);

  const onCreate = useCallback(async () => {
    if (isCreating) {
      return;
    }

    const successCallback = async (createChannelResponse: CreateChannelResponse): Promise<void> => {
      if (createChannelResponse.channelID && createChannelResponse.success) {
        const channelType = createChannelResponse?.CID?.split(':')[0] ?? 'messaging';

        const channel = client.getChannelById(channelType, createChannelResponse.channelID, {});
        if (!channel.initialized) {
          await channel.watch();
        }
        changeChannel(channel);
        setActiveChannel(channel);
        reset();
        groupReset();
        navigate({
          pathname: `/messaging/`,
        });
      }
    };

    const customChannelName = getChannelName(selectedGroups, selectedUsers, client.user?.name ?? '');

    createChannel(orgId!, successCallback, undefined, customChannelName ? customChannelName : undefined);
  }, [
    changeChannel,
    client,
    createChannel,
    groupReset,
    isCreating,
    navigate,
    orgId,
    reset,
    selectedGroups,
    selectedUsers,
    setActiveChannel,
  ]);

  const props = {
    results,
    searchText,
    selectedUserIDs,
    selectedUsers,
    toggleUser,
    selectedGroups,
    toggleGroup,
    loading,
  };

  if (loadingTeamsByOrg || loadingGroupsByTeam || loadingMessagingParticipantsMetadata || isLoadingOrgSettings) {
    return (
      <div className={styles.centeredSpinner}>
        <Spinner size="large" />
      </div>
    );
  }

  if (errorMessagingParticipantsMetadata) {
    logger.log('Error when fetching messaging participants metadata from School.', {
      [LoggingAttributes.FUNC_ATTRIBUTE]: 'StreamMessages',
      [LoggingAttributes.OP_ATTRIBUTE]: 'ErrorFetchingMessagingParticipantsMetadataFromSchool',
      [LoggingAttributes.USER_AGENT_ATTRIBUTE]: navigator.userAgent || 'undefined',
      [LoggingAttributes.USER_ID]: client.userID || client.user?.id || null,
      Error: errorMessagingParticipantsMetadata.toString(),
    });
  }

  return (
    <div className={styles.container}>
      <div className={styles.createMessageButton}>
        <Button
          isDisabled={selectedUserIDs.length < 1 && selectedGroupIDs.length < 1}
          onClick={onCreate}
          status={isCreating ? 'spinning' : undefined}
        >
          {format('messages.createMessage')}
        </Button>
      </div>
      <div className={styles.searchBar}>
        <SearchInput
          placeholder={format('messages.searchFor')}
          value={searchText}
          onChange={(input) => {
            onChangeSearchText(input.target.value);
          }}
          onDismissClick={() => {
            onChangeSearchText('');
          }}
        />
      </div>

      <SelectedParticipants
        {...props}
        participantDisplayMetadata={messagingParticipantsMetadata}
        hasError={channelCreationFailedSafesportEnforcement}
      />
      {hasSafeSportEnforcement || channelCreationFailedSafesportEnforcement ? (
        <SafeSportBanner hasError={channelCreationFailedSafesportEnforcement} orgId={orgId ?? ''} />
      ) : null}

      <GroupsSearch {...props} />
      {loading ? (
        <div className={styles.centeredSpinner}>
          <Spinner size="large" />
        </div>
      ) : (
        <IndividualsSearch {...props} participantDisplayMetadata={messagingParticipantsMetadata} />
      )}
    </div>
  );
}

function GroupSearchSectionItem({
  content,
  onContentClick,
}: SectionContentComponentProps<GroupAccordion, AccordionSection<GroupAccordion>> & {
  onContentClick: (content: Group) => void;
}): React.JSX.Element {
  const { selectedGroupIDs } = useGroupSearchContext();
  const onClick = useCallback(() => {
    onContentClick(content);
  }, [content, onContentClick]);

  const isSelected = selectedGroupIDs.indexOf(content.id) > -1;
  const memberCount = content.memberIds?.items?.length ?? 0;
  // NOTICE: Removing the family member component until we have clarity on their inclusion in groups
  // const currentUser = getUser();
  // const logger = new Logger('StreamMessaging');
  // let isFamilyMembersEnabled = false;
  // try {
  //   isFamilyMembersEnabled = getFeaturePrivilege(content.teamId, FeaturePrivilege.FamilyMembersEnabled);
  // } catch (error: any) {
  //   // getFeaturePrivilege throws an error if it fails - here it'd fail if teamId ever came back as null
  //   logger.log('Error when fetching FamilyMembersEnabled team feature toggle.', {
  //     [LoggingAttributes.FUNC_ATTRIBUTE]: 'StreamMessages',
  //     [LoggingAttributes.OP_ATTRIBUTE]: 'ErrorFetchingTeamFeatureToggle',
  //     [LoggingAttributes.USER_AGENT_ATTRIBUTE]: navigator.userAgent || 'undefined',
  //     [LoggingAttributes.USER_ID]: currentUser.id || null,
  //     Error: error,
  //   });
  // }

  return (
    <div onClick={onClick} className={styles.clickable} data-qa-id={`group-search-section-item-${content.name}`}>
      <div className={styles.resultRow}>
        <div className={classnames(styles.resultRowItem, styles.indent)}>
          <Subhead level="subtle" className={styles.groupPickerGroupName}>
            {content.name} {memberCount > 0 ? `(${memberCount})` : null}
          </Subhead>
          {content.name === AllTeamGroupName && (
            <Text level="micro" color={'subtle'}>
              {format('messages.allTeamRolesGroupPickerNoFamilyMembers')}
            </Text>
          )}
        </div>
        <div className={styles.resultRowItem}>
          <SelectMark size="medium" selectedState={isSelected ? 'selected' : 'unselected'} onClick={() => {}} />
        </div>
      </div>
      <Divider color={'level1-accent'} />
    </div>
  );
}

function GroupsSearch(props: Props): React.JSX.Element {
  const { accordionData, toggleGroup } = useGroupSearchContext();
  const onContentClick = useCallback((group: Group) => toggleGroup(group), [toggleGroup]);
  const ContentComponent = useCallback(
    (p: SectionContentComponentProps<GroupAccordion, AccordionSection<GroupAccordion>>) => (
      <GroupSearchSectionItem {...p} {...{ onContentClick }} />
    ),
    [onContentClick]
  );

  const sortedGroupsAccordionData = useMemo(() => {
    return [...accordionData].sort((a, b) => a.title.localeCompare(b.title));
  }, [accordionData]);

  if (sortedGroupsAccordionData.length === 0) {
    return <></>;
  }

  return (
    <>
      <div className={styles.individualsHeader}>
        <Text color={'subtle'}>{format('messages.groups')}</Text>
      </div>
      <AccordionList
        {...props}
        data={sortedGroupsAccordionData}
        {...{ SectionHeaderComponent }}
        SectionContentComponent={ContentComponent}
      />
    </>
  );
}

function SectionHeaderComponent({
  section,
  toggleExpanded,
  isExpanded,
}: SectionHeaderComponentProps<Group, AccordionSection<GroupAccordion>>): React.JSX.Element {
  const onClick = useCallback(() => {
    toggleExpanded();
  }, [toggleExpanded]);

  return (
    <div onClick={onClick} className={styles.clickable} data-qa-id={`team-group-dropdown-${section.title}`}>
      <div className={styles.resultRow}>
        <div className={styles.resultRowItem}>
          <Subhead level="subtle">{section.title}</Subhead>
        </div>
        <div className={styles.resultRowItem}>
          <ExpandCollapseIcon isExpanded={isExpanded} />
        </div>
      </div>
    </div>
  );
}

function ExpandCollapseIcon({ isExpanded }: { isExpanded: boolean }): React.JSX.Element {
  const Icon = isExpanded ? IconUiExpandCollapseUp : IconUiExpandCollapseDown;
  return <Icon size="small" color="subtle" />;
}

function SelectedParticipants(props: Props & { hasError: boolean }): React.JSX.Element {
  const { selectedUsers, selectedGroups, hasError } = props;
  const hasSelection = selectedUsers.length > 0 || selectedGroups.length > 0;
  return (
    <div className={classnames(styles.selectedParticipants, hasError && hasSelection && styles.errorBorder)}>
      {selectedGroups.map((group) => {
        return <UserPill key={`group_${group.id}`} {...props} who={group} isGroup />;
      })}
      {selectedUsers.map((user) => {
        return <UserPill key={`user_${user.id}`} {...props} who={user} isGroup={false} />;
      })}
    </div>
  );
}

function UserPill(
  props: Props & { who: UserResponse | Group; isGroup: boolean; imageUrl?: string }
): React.JSX.Element {
  const { who, toggleUser, isGroup, toggleGroup, imageUrl } = props;
  const onClick = useCallback(() => {
    if (isGroup) {
      toggleGroup(who as Group);
    } else {
      toggleUser(who as UserResponse);
    }
  }, [isGroup, toggleGroup, toggleUser, who]);
  return (
    <div className={styles.pill} onClick={onClick}>
      <ChannelAvatar name={who.name ?? ''} online={false} size={'xsmall'} isGroup={isGroup} avatarImageURL={imageUrl} />
      {who.name}
      <IconRemove />
    </div>
  );
}

function IndividualsSearch(props: Props): React.JSX.Element {
  const { results, searchText, participantDisplayMetadata } = props;
  const [sections, setSections] = useState<Section[]>([]);

  const resultsLength = results?.length || 0;
  useEffect(() => {
    const newSections: {
      [key: string]: {
        data: UserResponse[];
        title: string;
      };
    } = {};

    results?.forEach((user) => {
      const initial = user.name?.slice(0, 1).toUpperCase();

      if (!initial || !participantDisplayMetadata || !participantDisplayMetadata[user.id]) {
        return;
      }

      if (!newSections[initial]) {
        newSections[initial] = {
          data: [user],
          title: initial,
        };
      } else {
        newSections[initial].data.push(user);
      }
    });
    setSections(Object.values(newSections));
  }, [results, resultsLength, participantDisplayMetadata]);

  return (
    <>
      <div className={styles.individualsHeader}>
        <Text color={'subtle'}>
          {searchText.length > 0
            ? `${format('messages.searchResultsFor')} "${searchText}"`
            : format('messages.individuals')}
        </Text>
      </div>
      <IndividualsSearchSectionList sections={sections} {...props} />
    </>
  );
}

function IndividualsSearchSectionList(props: Props & { sections: Section[] }): React.JSX.Element {
  const { sections } = props;
  // Sort sections by title
  sections.sort((a, b) => a.title.localeCompare(b.title));
  // Sort users in each section by name
  sections.forEach((section) => {
    section.data.sort((a, b) => (a.name ?? '').localeCompare(b.name ?? ''));
  });

  return (
    <>
      {sections.map((section) => {
        return (
          <>
            <div className={styles.sectionHeader}>
              <Subhead level={'subtle'}>{section.title}</Subhead>
            </div>
            {section.data.map((user: UserResponse) => (
              <IndividualsSearchSectionItem user={user} {...props} />
            ))}
          </>
        );
      })}
    </>
  );
}

function IndividualsSearchSectionItem({
  user,
  selectedUserIDs,
  toggleUser,
  participantDisplayMetadata,
}: Props & { user: UserResponse }): React.JSX.Element | null {
  const onClick = useCallback(() => {
    toggleUser(user);
  }, [user, toggleUser]);

  // No metadata - must be a disabled member from stream
  if (!participantDisplayMetadata || !participantDisplayMetadata[user.id]) {
    return null;
  }

  const metadata = participantDisplayMetadata[user.id];
  const isSelected = selectedUserIDs.indexOf(user.id) > -1;
  return (
    <div onClick={onClick} className={styles.clickable}>
      <div className={styles.resultRow}>
        <div className={styles.resultRowItem}>
          <AvatarUser initials={getInitialsFromName(user.name ?? '')} />
          <div className={styles.resultRowItemTextColumn}>
            <Text level="small">
              <b>{user.name}</b>
            </Text>
            <Text level="micro" hideOverflow>
              {getRoleDisplayString(metadata.maxRole, metadata.isOrgAdmin, metadata.athleteNames)}
            </Text>
          </div>
        </div>
        <div className={styles.resultRowItem}>
          <SelectMark size="medium" selectedState={isSelected ? 'selected' : 'unselected'} onClick={() => {}} />
        </div>
      </div>
      <Divider color={'level1-accent'} />
    </div>
  );
}

export default MessagingParticipantsList;
