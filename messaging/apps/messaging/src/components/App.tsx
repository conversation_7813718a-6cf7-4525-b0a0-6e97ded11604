import { useEffect, useMemo } from 'react';

import { IntlProvider } from 'react-intl';
import { Outlet } from 'react-router-dom';

import { Environment, ToastMessenger, TooltipProvider } from '@hudl/uniform-web';
import { WebNav } from '@local/webnav';
import { getIntl } from 'frontends-i18n';
import { initQualtrics } from 'frontends-shared';

import useOrgMessagingSettings from '../hooks/useOrgMessagingSettings';

import styles from './App.module.scss';

function App(): JSX.Element {
  const i18n = getIntl();
  const { orgMessagingSettings, isLoading } = useOrgMessagingSettings();

  const hasAnyOrgWithMessagingEnabled = useMemo(() => {
    return !isLoading && orgMessagingSettings?.some((settings) => settings.messagingEnabled === true);
  }, [isLoading, orgMessagingSettings]);

  useEffect(() => {
    // Only initialize Qualtrics if user has orgs with messaging enabled
    if (hasAnyOrgWithMessagingEnabled) {
      initQualtrics();
    }
  }, [hasAnyOrgWithMessagingEnabled]);

  return (
    <Environment environment="light" className={styles.environment}>
      <IntlProvider messages={i18n.messages} locale={i18n.locale} textComponent="span">
        <TooltipProvider>
          <Outlet />
        </TooltipProvider>
      </IntlProvider>
      <ToastMessenger />
      <WebNav isVisible />
    </Environment>
  );
}

export default App;
