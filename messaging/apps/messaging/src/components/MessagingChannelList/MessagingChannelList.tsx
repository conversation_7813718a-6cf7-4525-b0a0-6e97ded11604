import { type ChangeEvent, PropsWithChildren, useCallback, useEffect, useMemo, useState } from 'react';

import { debounce } from 'lodash';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ChannelFilters } from 'stream-chat';
import { ChannelListMessengerProps, DefaultStreamChatGenerics, useChatContext } from 'stream-chat-react';

import { Logger } from '@hudl/frontends-logging';
import { ActionList, IconHelp, Spinner, Text } from '@hudl/uniform-web';
import { Button } from '@hudl/uniform-web-button-legacy';
import { SearchInput } from '@hudl/uniform-web-forms-legacy';
import { format } from 'frontends-i18n';
import { getUserId } from 'frontends-shared';

import { SearchType } from '../../enum/SearchType';
import { PermissionsSetType } from '../../graphql/graphqlTypes';
import useMessageComposerRoute from '../../hooks/useMessagingComposerRoute';
import { LoggingAttributes } from '../../utils/loggingAttributes';
import FeedbackModal from '../FeedbackModal/FeedbackModal';
import { NewMessageIcon } from '../Icons/NewMessageIcon';
import MessageSearchResultList from '../MessageSearchResults/MessageSearchResultList';
import { SearchResult } from '../MessageSearchResults/SearchResults';
import { useCurrentChannelContext, useOrgMessagingSettingsContext } from '../MessagingContext';

import styles from './MessagingChannelList.module.scss';

function MessagingChannelListHeader(): React.JSX.Element {
  const navigate = useNavigate();
  const { orgMessagingSettings, isLoading } = useOrgMessagingSettingsContext();
  const route = useMessageComposerRoute();

  const hasAnyOrgWithMessagingEnabled = useMemo(() => {
    return (
      !isLoading &&
      orgMessagingSettings?.some(
        (settings) =>
          settings.messagingEnabled === true && settings.permissionsSet === PermissionsSetType.READ_RESPOND_CREATE
      )
    );
  }, [isLoading, orgMessagingSettings]);

  const hasAnyOrgWithMessagingEnabledForFeedback = useMemo(() => {
    return !isLoading && orgMessagingSettings?.some((settings) => settings.messagingEnabled === true);
  }, [isLoading, orgMessagingSettings]);

  const isNewMessageButtonDisabled = useMemo(() => {
    return isLoading || !hasAnyOrgWithMessagingEnabled;
  }, [isLoading, hasAnyOrgWithMessagingEnabled]);

  const onComposeClick = useCallback(() => {
    navigate({
      pathname: route,
    });
  }, [navigate, route]);

  const [feedbackModalOpen, setFeedbackModalOpen] = useState(false);

  return (
    <div>
      <div className={styles.messagingInboxHeader}>
        <FeedbackModal isOpen={feedbackModalOpen} onClose={() => setFeedbackModalOpen(false)} />
        <Text level="large" color="default" className={styles.messagingInboxHeaderTitle}>
          {format('messages.messagesTitle')}
        </Text>
        {hasAnyOrgWithMessagingEnabledForFeedback && (
          <ActionList
            buttonIcon={<IconHelp />}
            size="small"
            style="minimal"
            type="secondary"
            actions={[
              {
                text: format('messages.giveFeedback'),
                onPress: () => setFeedbackModalOpen(true),
                qaId: 'messaging-feedback-action',
              },
            ]}
            qaId="messaging-feedback-action-list"
          />
        )}
      </div>
      <div>
        <Button
          buttonType="primary"
          icon={<NewMessageIcon />}
          className={styles.messagingInboxHeaderNewMessageButton}
          qaId="create-new-message-button"
          onClick={onComposeClick}
          isDisabled={isNewMessageButtonDisabled}
        >
          {format('messages.newMessage')}
        </Button>
      </div>
    </div>
  );
}

function MessagingChannelList({ children, loading }: PropsWithChildren<ChannelListMessengerProps>): React.JSX.Element {
  const { client, channel, setActiveChannel } = useChatContext();
  const logger = new Logger('StreamMessaging');
  const user = getUserId();

  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchActive, setSearchActive] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [isSearchLoading, setIsSearchLoading] = useState(false);
  const { changeChannel } = useCurrentChannelContext();
  const [searchParams] = useSearchParams();
  const [hasPreloadedChannel, setHasPreloadedChannel] = useState(false);

  useEffect(() => {
    if (hasPreloadedChannel || loading) {
      return;
    }
    setHasPreloadedChannel(true);
    const CID_B64 = searchParams.get('conversation');
    if (!CID_B64) {
      return;
    }
    const CID = atob(CID_B64);
    const split = CID.split(':');
    if (split.length !== 2) {
      return;
    }
    const channelType = split[0];
    const channelId = split[1];
    const preloadedChannel = client.getChannelById(channelType, channelId, {});
    changeChannel(preloadedChannel);
    setActiveChannel?.(preloadedChannel);
  }, [changeChannel, client, hasPreloadedChannel, searchParams, setActiveChannel]);

  const messageFilter: ChannelFilters<DefaultStreamChatGenerics> = useMemo(
    () => ({
      members: { $in: [`${user}`] },
    }),
    [user]
  );

  const onSearchError = (error: Error | string) => {
    logger.log('Error when searching.', {
      [LoggingAttributes.FUNC_ATTRIBUTE]: 'StreamMessages',
      [LoggingAttributes.OP_ATTRIBUTE]: 'ErrorSearching',
      [LoggingAttributes.USER_AGENT_ATTRIBUTE]: navigator.userAgent || 'undefined',
      [LoggingAttributes.USER_ID]: client.userID || client.user?.id || null,
      Error: error.toString(),
    });
  };

  const search = async (newSearchText: string) => {
    setSearchActive(true);
    setIsSearchLoading(true);
    try {
      const results: SearchResult[] = [];
      setIsSearchLoading(false);
      const finalResults = results
        .concat(await channelSearch(newSearchText))
        .concat(await channelHiddenSearch(newSearchText))
        .concat(await usersSearch(newSearchText))
        .concat(await messageSearch(newSearchText));
      setSearchResults(finalResults);
    } catch (e) {
      onSearchError(e as Error);
      setIsSearchLoading(false);
      setSearchResults([]);
    }
  };

  const messageSearch = async (newSearchText: string) => {
    const results = await client.search(messageFilter, newSearchText, { sort: { created_at: -1 } });
    const messageResponseList = results.results.map((result) => result.message) as SearchResult[];

    // sets the type of each res to message
    messageResponseList.forEach((messageRes) => {
      messageRes.TypeOfSearch = SearchType.Message;
    });
    return messageResponseList;
  };

  const channelSearch = async (newSearchText: string) => {
    // filter for getting all the channels that include both the user and the name of the channel that matches
    const filters: ChannelFilters<DefaultStreamChatGenerics> = {
      name: { $autocomplete: newSearchText },
      members: { $in: [`${user}`] },
      last_message_at: { $exists: true },
    };
    const channelResponseList = (await client.queryChannels(filters)) as SearchResult[];

    // sets the type of each res to message
    channelResponseList.forEach((channelRes) => {
      channelRes.TypeOfSearch = SearchType.Channel;
    });
    return channelResponseList;
  };

  const channelHiddenSearch = async (newSearchText: string) => {
    // filter for getting all the channels that include both the user and the name of the channel that matches
    const filters: ChannelFilters<DefaultStreamChatGenerics> = {
      name: { $autocomplete: newSearchText },
      members: { $in: [`${user}`] },
      hidden: { $eq: true },
    };
    const channelResponseList = (await client.queryChannels(filters)) as SearchResult[];

    // sets the type of each res to message
    channelResponseList.forEach((channelRes) => {
      channelRes.TypeOfSearch = SearchType.Channel;
    });
    return channelResponseList;
  };

  const usersSearch = async (newSearchText: string) => {
    const channelFilters: ChannelFilters = {
      type: 'messaging',
      member_count: 2,
      'member.user.name': { $autocomplete: newSearchText },
      members: { $in: [client.userID || ''] },
    };

    const membersResponseList = (await client.queryChannels(channelFilters)) as SearchResult[];

    // sets the type of each res to member
    membersResponseList.forEach((memberRes) => (memberRes.TypeOfSearch = SearchType.User));

    return membersResponseList;
  };

  const debouncedSearch = useCallback(debounce(search, 500), []);

  const onSearchInputCleared = () => {
    debouncedSearch.cancel();
    setSearchResults([]);
    setSearchText('');
    setSearchActive(false);
  };

  const onSearchInputTextChanged = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.value === '') {
      onSearchInputCleared();
    } else {
      setSearchText(e.target.value);
      debouncedSearch(e.target.value);
    }
  };

  let bottomHalfChannelList;
  if (loading) {
    bottomHalfChannelList = (
      <div className={styles.spinnerContainer}>
        <Spinner size="large" />
      </div>
    );
  } else if (searchActive) {
    bottomHalfChannelList = <MessageSearchResultList isLoading={isSearchLoading} searchResults={searchResults} />;
  } else {
    bottomHalfChannelList = <div>{children}</div>;
  }

  return (
    <div className={styles.messagingInboxContainer}>
      <MessagingChannelListHeader />
      <SearchInput
        value={searchText}
        onChange={(e) => onSearchInputTextChanged(e)}
        formSize="small"
        placeholder={format('messages.searchBarInputPlaceholder')}
        style={{ marginTop: '8px', marginBottom: '8px' }}
        onDismissClick={onSearchInputCleared}
        isDisabled={loading || channel === undefined} // If the user is in any channel, one is guaranteed to be active. Disable searching if there is no active channel.
        qaId="messaging-message-search-bar"
      />
      {bottomHalfChannelList}
    </div>
  );
}

export default MessagingChannelList;
