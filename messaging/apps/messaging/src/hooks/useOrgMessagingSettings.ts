import { useEffect, useState } from 'react';

import { useQuery } from '@apollo/client';

import { WebMessagingGetOrgMessagingSettingsR1Document as GetOrgMessagingSettings } from '../graphql/graphqlTypes';

interface OrgMessagingSettings {
  orgId: string;
  permissionsSet: string;
  orgAccessLevel: string;
  messagingEnabled: boolean;
}

export interface OrgMessagingSettingsResult {
  isLoading: boolean;
  orgMessagingSettings: OrgMessagingSettings[];
}

export default function useOrgMessagingSettings(): OrgMessagingSettingsResult {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [orgMessagingSettings, setOrgMessagingSettings] = useState<OrgMessagingSettings[]>([]);

  const { data, loading } = useQuery(GetOrgMessagingSettings);

  useEffect(() => {
    setIsLoading(loading);
    setOrgMessagingSettings(data?.me?.orgMessagingSettings);
  }, [data, loading]);

  return {
    orgMessagingSettings,
    isLoading,
  };
}
