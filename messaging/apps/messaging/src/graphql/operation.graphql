# Queries

query Web_Messaging_GetSchoolsByOrg_r1($orgIDList: [String]!) {
  schools(schoolIds: $orgIDList) {
    fullName
    internalId
    abbreviation
    isEnabled
    orgClassificationId

    orgAdmins {
      id
      firstName
      lastName
    }
  }
}

query Web_Messaging_GetGroupsByTeam_r1 {
  myTeams {
    organization {
      internalId
      id
      fullName
    }
    groups {
      items {
        id
        name
        memberIds {
          items
        }
      }
    }
    name
    id
    internalId
    featureToggles {
      id
      isEnabled
    }
  }
}

query Web_Messaging_GetGroupsByTeam_r2 {
  myTeams {
    organization {
      internalId
      id
      fullName
    }
    groups(includeFamilyMembers: true) {
      items {
        id
        name
        memberIds {
          items
        }
      }
    }
    name
    id
    internalId
    featureToggles {
      id
      isEnabled
    }
  }
}

query Web_Messaging_GetGroupsByOrg_r1 {
  myOrganizations {
    id
    internalId
    isAdmin
    teams {
      organization {
        internalId
        id
        fullName
      }
      groups {
        items {
          id
          name
          memberIds {
            items
          }
        }
      }
      name
      id
      internalId
      teamStatus
      featureToggles {
        id
        isEnabled
      }
    }
  }
}

query Web_Messaging_GetGroupsByOrg_r2 {
  myOrganizations {
    id
    internalId
    isAdmin
    teams {
      organization {
        internalId
        id
        fullName
      }
      groups(includeFamilyMembers: true) {
        items {
          id
          name
          memberIds {
            items
          }
        }
      }
      name
      id
      internalId
      teamStatus
      featureToggles {
        id
        isEnabled
      }
    }
  }
}

mutation Web_Messaging_CreateChannel_r1($createChannelInput: CreateChannelInput!) {
  createChannel(createChannelInput: $createChannelInput) {
    ... on Channel {
      id
      internalId
      cid
    }
    ... on ChannelCreationError {
      code
      message
    }
  }
}

query Web_Messaging_GetTeamById_r1($teamID: String!) {
  team(id: $teamID) {
    internalId
    messagingSettings {
      safeSportEnforced
    }
  }
}

query Web_Messaging_GetMessagingParticipantsMetadataBySchool_r1(
  $internalSchoolId: String!
  $first: Int!
  $after: Cursor
) {
  school(internalSchoolId: $internalSchoolId) {
    id
    messagingParticipantsMetadata(first: $first, after: $after) {
      ...SchoolMessagingParticipantsMetadata
    }
  }
}

fragment SchoolMessagingParticipantsMetadata on MessagingParticipantsMetadataConnection {
  edges {
    cursor
    node {
      userId
      maxRole
      isOrgAdmin
      athletes {
        id
        firstName
        lastName
      }
    }
  }
  pageInfo {
    endCursor
    hasNextPage
    hasPreviousPage
    startCursor
  }
  totalCount
}

query Web_Messaging_GetOrgMessagingSettings_r1 {
  me {
    id
    orgMessagingSettings {
      orgId
      permissionsSet
      orgAccessLevel
      messagingEnabled
    }
  }
}

query GetChannelDetails($input: GetChannelDetailsInput!) {
  channelDetails(channelDetailsInput: $input) @include(if: true) {
    maxRole
    coreTeamOptions {
      teamId
      teamName
      teamAvatar
      teamSportString
      teamPrimaryColor
      teamSecondaryColor
    }
  }
}

query Web_Messaging_GetUserNoticeDetails_r1 {
  me {
    id
    userMessagingNoticeDetails {
      lastViewedAt
    }
  }
}

query Web_Messaging_GetTeamsByOrg_r1($orgID: String!) {
  school(internalSchoolId: $orgID) {
    id
    internalId
    isAdmin
    teams {
      organization {
        internalId
        id
        fullName
      }
      groups(includeFamilyMembers: true) {
        items {
          id
          name
          memberIds {
            items
          }
        }
      }
      name
      id
      internalId
      teamStatus
      featureToggles {
        id
        isEnabled
      }
    }
  }
}

mutation Web_Messaging_CreateOrUpdateUserNoticeDetails_r1(
  $createOrUpdateUserNoticeDetailsInput: CreateOrUpdateUserNoticeDetailsInput!
) {
  createOrUpdateUserNoticeDetails(createOrUpdateUserNoticeDetailsInput: $createOrUpdateUserNoticeDetailsInput) {
    lastViewedAt
  }
}

mutation Web_Messaging_UpdateUserPreference_r1($input: UpdateUserPreferenceInput!) {
  updateUserPreference(input: $input) {
    userPreference {
      preferenceName
      preferenceValue {
        asBool
        asInteger
        asString
      }
    }
  }
}

query Web_Messaging_GetUserPreference_r1($input: GetUserPreferenceInput!) {
  userPreference(input: $input) {
    preferenceName
    preferenceValue {
      asBool
      asInteger
      asString
    }
  }
}
