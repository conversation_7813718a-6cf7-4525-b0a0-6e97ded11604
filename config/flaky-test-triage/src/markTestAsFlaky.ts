import { Project, SourceFile, SyntaxKind } from 'ts-morph';

const FLAKY_IMPORT = '@hudl/vitest-config/itFlaky';

/**
 * Find any existing import for `itFlaky` from `FLAKY_IMPORT`
 */
export const findExistingIsFlakyImport = (sourceFile: SourceFile): boolean => {
  const existingImport = sourceFile
    .getImportDeclarations()
    .find(
      (importDeclaration) =>
        importDeclaration.getModuleSpecifierValue().includes(FLAKY_IMPORT) &&
        importDeclaration.getNamedImports().some((n) => n.getName() === 'itFlaky')
    );
  return Boolean(existingImport);
};

/**
 * Add the import for `itFlaky` at the top of the file, pre-commit hooks will ensure this is formatted correctly
 */
export const addIsFlakyImport = (sourceFile: SourceFile): void => {
  sourceFile.insertImportDeclaration(0, {
    namedImports: ['itFlaky'],
    moduleSpecifier: FLAKY_IMPORT,
  });
};

/**
 * Replace `it` call with `itFlaky` for the flaky test
 */
export const replaceItWithItFlaky = (sourceFile: SourceFile, testName: string): boolean => {
  const callExprs = sourceFile.getDescendantsOfKind(SyntaxKind.CallExpression);
  for (const call of callExprs) {
    const expr = call.getExpression();
    if (expr.getText() === 'it') {
      const args = call.getArguments();
      const firstArg = args[0];
      if (firstArg?.getKind() === SyntaxKind.StringLiteral && firstArg.getText().slice(1, -1) === testName) {
        expr.replaceWithText('itFlaky');
        return true;
      }
    }
  }
  return false;
};

/**
 * Marks a test as flaky by replacing the `it` call with `itFlaky`
 * Use process.stderr.write to print log when using vitest's `--silent` option
 */
export const markTestAsFlaky = async (filePath: string, testName: string): Promise<boolean> => {
  process.stderr.write(`\n[RETRY REPORTER] Marking test "${testName}" as flaky`);
  const project = new Project();
  const sourceFile = project.addSourceFileAtPath(filePath);

  let importAdded = false;

  const existingImport = findExistingIsFlakyImport(sourceFile);

  // If no existing import, add it at the top of the file
  if (!existingImport) {
    addIsFlakyImport(sourceFile);
    importAdded = true;
  }

  const hasMarkedTest = replaceItWithItFlaky(sourceFile, testName);

  // Save file changes
  await sourceFile.save();

  const markTestResultMessage = hasMarkedTest
    ? `\n[RETRY REPORTER] Updated ${filePath}${importAdded ? ' with import\n' : ''}`
    : `\n[RETRY REPORTER] Update skipped, test was already marked as flaky`;

  process.stderr.write(markTestResultMessage);

  return hasMarkedTest;
};
