import { shouldSkipTest, skipTest } from '../skipTest';

describe('shouldSkipTest', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
    delete process.env.CI_ON_MAIN;
    delete process.env.CI_ON_MERGE_QUEUE;
  });

  afterEach(() => {
    // Reset process.env before each test
    process.env = originalEnv;
  });

  it('should return false if no env vars are set', () => {
    expect(shouldSkipTest()).toBe(false);
  });

  it('should return true if CI_MERGE_QUEUE is set', () => {
    process.env.CI_ON_MERGE_QUEUE = 'true';
    expect(shouldSkipTest()).toBe(true);
  });

  it('should return true if CI_ON_MAIN is set', () => {
    process.env.CI_ON_MAIN = 'true';
    expect(shouldSkipTest()).toBe(true);
  });
});

describe('skipTest', () => {
  it('should call the test function with a skipped name', () => {
    const testFn = vi.fn();
    const name = 'testName';

    skipTest(name, testFn);

    expect(testFn).toHaveBeenCalledWith(`[SKIPPED] ${name}`, expect.any(Function));
  });
});
