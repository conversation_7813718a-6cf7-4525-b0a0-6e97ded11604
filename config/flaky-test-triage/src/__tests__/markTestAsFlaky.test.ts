import { Project, SourceFile } from 'ts-morph';

import { addIsFlakyImport, findExistingIsFlakyImport, replaceItWithItFlaky } from '../markTestAsFlaky';

const nonFlakyTestVitestFileSource = `
it('not currently marked as flaky', () => {
  expect(true).toBe(false);
});

`;

const flakyTestVitestFileSource = `
import { itFlaky } from '@hudl/vitest-config/itFlaky';

itFlaky('currently marked as flaky', () => {
  expect(true).toBe(false);
});
`;

const setupProject = (source: string): { project: Project; sourceFile: SourceFile } => {
  const project = new Project({ useInMemoryFileSystem: true });
  const sourceFile = project.createSourceFile('testFile.ts', source);
  return { project, sourceFile };
};

describe('findExistingIsFlakyImport', () => {
  it('should return true if the import for itFlaky already exists', () => {
    const { sourceFile } = setupProject(flakyTestVitestFileSource);

    expect(findExistingIsFlakyImport(sourceFile)).toBe(true);
  });

  it('should return false if the import for itFlaky does not exist', () => {
    const { sourceFile } = setupProject(nonFlakyTestVitestFileSource);

    expect(findExistingIsFlakyImport(sourceFile)).toBe(false);
  });
});

describe('addIsFlakyImport', () => {
  it('should add the import for itFlaky at the top of the file', () => {
    const { sourceFile } = setupProject(nonFlakyTestVitestFileSource);

    const importString = `import { itFlaky } from "@hudl/vitest-config/itFlaky"`;

    expect(sourceFile.getText()).not.toContain(importString);

    addIsFlakyImport(sourceFile);

    expect(sourceFile.getText()).toContain(importString);
  });
});

describe('replaceItWithItFlaky', () => {
  it('should replace the it call with itFlaky for the flaky test', () => {
    const { sourceFile } = setupProject(nonFlakyTestVitestFileSource);

    const itCall = `it(`;
    const itFlakyCall = `itFlaky(`;

    expect(sourceFile.getText()).toContain(itCall);
    expect(sourceFile.getText()).not.toContain(itFlakyCall);

    replaceItWithItFlaky(sourceFile, 'not currently marked as flaky');

    expect(sourceFile.getText()).not.toContain(itCall);
    expect(sourceFile.getText()).toContain(itFlakyCall);
  });
});
