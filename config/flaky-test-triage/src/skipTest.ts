export const shouldSkipTest = (): boolean => {
  // If we're in the merge queue, we should skip flaky tests
  return process.env.CI_ON_MERGE_QUEUE === 'true' || process.env.CI_ON_MAIN === 'true';
};

export const skipTest = (name: string, testFn: (name: string, fn: () => void) => void): void => {
  return testFn(`[SKIPPED] ${name}`, () => {
    // Use process.stderr.write to print log when using vitest's `--silent` option
    process.stderr.write(`[SKIPPED] Skipping flaky test: "${name}"\n`);
  });
};
