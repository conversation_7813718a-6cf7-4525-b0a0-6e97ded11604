{"name": "@hudl/vitest-config", "version": "1.0.0", "private": true, "contributors": [{"name": "Mandalorians", "url": "https://hudl.slack.com/archives/C050MC1USQ5", "channel": "#ask-front-end-platform", "qaChannel": "#squad-mandalorians"}], "exports": {"./reporters": {"import": "./dist/reporters.js", "require": "./dist/reporters.js", "types": "./dist/reporters.d.ts"}, "./itFlaky": {"import": "./dist/retryReporter/itFlaky.js", "require": "./dist/retryReporter/itFlaky.js", "types": "./dist/retryReporter/itFlaky.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "vite build", "clean": "rimraf dist node_modules/.cache", "dev": "vite build --watch", "lint": "eslint --ext .js,.ts,.jsx,.tsx src/ --quiet", "lint:fix": "eslint --ext .js,.ts,.jsx,.tsx src/ --quiet --fix", "nuke": "pnpm run clean && rimraf node_modules", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "release_REMOVE_TO_ENABLE": "release-package", "test": "vitest --passWithNoTests", "test:ci": "vitest --passWithNoTests --no-cache --silent", "test:nowatch": "vitest --passWithNoTests --watch=false", "test:update": "vitest --passWithNoTests --no-cache --update", "types": "tsc --project tsconfig-declarations.json --sourceRoot $PWD/src", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src"}, "dependencies": {"@snowplow/node-tracker": "4.3.1"}, "devDependencies": {"@hudl/eslint-config": "workspace:*", "@hudl/flaky-test-triage": "workspace:*", "@hudl/vite-config": "workspace:*", "config": "workspace:*", "eslint": "8.45.0", "github-actions": "workspace:*", "vite-tsconfig-paths": "4.3.2"}, "peerDependencies": {"vite": "^5.0.0", "vitest": "^3.0.0"}}