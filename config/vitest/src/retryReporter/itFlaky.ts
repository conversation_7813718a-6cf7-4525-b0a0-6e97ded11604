import type { TestFunction, TestOptions } from 'vitest';

import { shouldSkipTest, skipTest } from '@hudl/flaky-test-triage';

/**
 * This is a wrapper around `it` that skips flaky tests when running in the merge queue or on main.
 */
export const itFlaky = (name: string, fn: TestFunction<object> | undefined, options?: number | TestOptions): void => {
  const shouldSkip = shouldSkipTest();
  if (shouldSkip) {
    return skipTest(name, it);
  }

  // eslint-disable-next-line vitest/expect-expect -- This is a wrapper function so doesn't need an expect
  return it(`${name}`, fn, options);
};
