import type { TestCase } from 'vitest/node.js';
import { DefaultReporter } from 'vitest/reporters';

import { markTestAsFlaky } from '@hudl/flaky-test-triage';

import { sendFlakySlackMessage } from './sendFlakySlackMessage.js';

/**
 * <PERSON><PERSON>Reporter extends Vitest's DefaultReporter to handle flaky tests
 * Flaky tests are defined as tests that pass after one or more retries
 * Use process.stderr.write to print log when using vitest's `--silent` option
 */
export class RetryReporter extends DefaultReporter {
  public async onTestCaseResult(test: TestCase): Promise<void> {
    super.onTestCaseResult(test);

    // If not in the merge queue or on main, and the test is flaky, mark it as flaky
    if (process.env.CI_ON_MERGE_QUEUE !== 'true' && process.env.CI_ON_MAIN !== 'true' && test.diagnostic()?.flaky) {
      process.stderr.write(`\n[RETRY REPORTER] Flaky test detected: "${test.fullName}"`);
      const retryCount = test.diagnostic()?.retryCount ?? 0;
      process.stderr.write(
        `\n[RETRY REPORTER] Test passed after ${retryCount} ${retryCount === 1 ? 'retry' : 'retries'}`
      );
      const filePath = test.module.moduleId;
      const hasMarkedTest = await markTestAsFlaky(filePath, test.name);

      if (process.env.SLACK_BOT_TOKEN && hasMarkedTest) {
        await sendFlakySlackMessage(process.env.SLACK_BOT_TOKEN, test.fullName);
      }
    }
  }
}
