{"name": "@hudl/uniform-web-forms", "version": "7.1.0", "contributors": [{"name": "Mandalorians", "url": "https://hudl.slack.com/archives/C050MC1USQ5", "channel": "#ask-front-end-platform", "qaChannel": "#squad-mandalorians"}], "sideEffects": ["**/*.css"], "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./server": {"import": "./dist/server.mjs", "require": "./dist/server.js", "types": "./dist/server.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "vite build", "clean": "rimraf dist node_modules/.cache storybook-static", "dev": "vite build --watch", "lint": "eslint --ext .js,.ts,.jsx,.tsx src/ --quiet && pnpm lint:styles", "lint:fix": "eslint --ext .js,.ts,.jsx,.tsx src/ --quiet --fix && pnpm lint:styles:fix", "lint:styles": "stylelint './src/**/*.css' --rdd --risd --rd --config './stylelint.config.js'", "lint:styles:fix": "stylelint './src/**/*.css' --rdd --risd --rd --fix --config './stylelint.config.js'", "nuke": "pnpm run clean && rimraf node_modules", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "release": "release-package", "test": "vitest --passWithNoTests", "test:ci": "vitest --no-cache --passWithNoTests --silent", "test:nowatch": "vitest --passWithNoTests --watch=false", "test:update": "vitest --no-cache --update", "types": "tsc --project tsconfig-declarations.json --sourceRoot $PWD/src", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src"}, "dependencies": {"@hudl/uniform-shared": "workspace:*", "@hudl/uniform-web-button": "workspace:*", "@hudl/uniform-web-icons": "workspace:*", "@hudl/uniform-web-type": "workspace:*", "classnames": "2.3.1", "immutable": "4.2.2", "react-aria-components": "1.8.0", "react-select": "5.8.0"}, "devDependencies": {"@hudl/eslint-config": "workspace:*", "@hudl/stylelint-config": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "config": "workspace:*", "eslint": "8.45.0", "jsdom": "24.0.0", "stylelint": "16.13.0", "vite": "5.4.7", "vite-tsconfig-paths": "4.3.2"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0"}}