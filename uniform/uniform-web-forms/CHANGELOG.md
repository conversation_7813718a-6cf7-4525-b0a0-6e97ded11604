# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [7.1.0]

- Adds `isRequired` and `hasError` props to `Checkbox`
- Updates border styling for `Select` error state
- Updates placeholder text styling for `Input` error state

## [7.0.1]

- Updates `ReactElement` types to either `ReactNode` or `JSX.Element`

## [7.0.0]

- Updates `@hudl/uniform-web-button` to `7.0.0`

## [6.4.0]

- Fixes height of `Textarea` when `autoheight` is enabled for sizes other than `medium`

## [6.3.0]

- Bumps version of `@hudl/uniform-web-button` to v6.2.0

## [6.2.0]

- Updates `react-aria-component`

## [6.1.1]

- Fixed a bug in `<Input>` where type wasn't passed to underlying component

## [6.1.0]

- Custom `Select` option labels utilizing the `Text` element now change size based off the `formSize` property.

## [6.0.0]

With our upgrade to [`react-aria-components`](https://react-spectrum.adobe.com/react-aria/TextField.html) `<TextField>`, the following props are no longer supported in the `<Input>` & `<SearchInput>` component:

- **`onKeyPress`** – This event has been deprecated in React. Use `onKeyDown` or `onKeyUp` instead.
- **`disabled`** – Replaced with `isDisabled`, which aligns with React Aria’s API.

The `defaultValues` prop is still supported but now follows React Aria's API:

Instead of accepting a value of `string | number | readonly string[] | undefined`, it now only accepts a value of type `string | undefined`.

This ensures compatibility with React Aria’s controlled input model.

The `spellCheck` prop is still supported but now follows React Aria's API:

Instead of accepting a value of `Booleanish | undefined`, it now only accepts a value of type `string | undefined`.

This ensures compatibility with React Aria’s controlled input model.

Example Migration:

If you previously used:

```tsx
<Input spellCheck={true} />
<Input spellCheck={false} />
<SearchInput spellCheck={false} />
<SearchInput spellCheck={false} />
```

You should update your code to:

```tsx
<Input spellCheck="true" />
<Input spellCheck="false" />
<SearchInput spellCheck="false" />
<SearchInput spellCheck="false" />
```

The `onChange` prop is still supported but now follows React Aria's API:

Instead of receiving a native event, it now directly provides the new value when the input changes.

Example Migration:

If you previously used:

```tsx
<Input onChange={(e) => console.log(e.target.value)} />
```

You should update your code to:

```tsx
<Input onChange={(value) => console.log(value)} />
```

- Removes deprecated `hasError` prop from `SearchInput` component.

## [5.60.0]

- Adds Forms components.
- Hides `SearchInput` dismiss button when `onDismissClick` is not set.
