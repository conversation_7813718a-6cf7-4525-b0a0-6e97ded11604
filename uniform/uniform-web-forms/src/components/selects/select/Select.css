.u-select--cursor-container {
  cursor: not-allowed;
}

.u-select-container {
  .u-form__item--medium &,
  .u-form--medium & {
    .u-select__control {
      min-height: 2.5rem;
    }

    .u-select__value-container {
      padding: 0 var(--u-space-one) 0 var(--u-space-one);
      font-size: var(--u-font-size-default);
    }

    .u-select__clear-indicator {
      padding: 0 var(--u-space-half);
    }

    .u-select__dropdown-arrow {
      padding: 0 var(--u-space-one) 0 var(--u-space-half);
    }
  }

  .u-form__item--xsmall &,
  .u-form--xsmall & {
    .u-select__control {
      min-height: 1.5rem;
    }

    .u-select__value-container {
      padding: 0 var(--u-space-half) 0 var(--u-space-half);
      font-size: var(--u-font-size-micro);
    }

    .u-select__clear-indicator {
      padding: 0 var(--u-space-quarter);
    }

    .u-select__dropdown-arrow {
      padding: 0 var(--u-space-half) 0 var(--u-space-half);
    }
  }

  .u-form__item--small &,
  .u-form--small & {
    .u-select__control {
      min-height: 2rem;
    }

    .u-select__value-container {
      padding: 0 var(--u-space-half) 0 var(--u-space-half);
      font-size: var(--u-font-size-small);
    }

    .u-select__clear-indicator {
      padding: 0 var(--u-space-quarter);
    }

    .u-select__dropdown-arrow {
      padding: 0 var(--u-space-half) 0 var(--u-space-half);
    }
  }

  .u-form__item--large &,
  .u-form--large & {
    .u-select__control {
      min-height: 3rem;
    }

    .u-select__value-container {
      padding: 0 var(--u-space-one) 0 var(--u-space-one);
      font-size: var(--u-font-size-default);
    }

    .u-select__clear-indicator {
      padding: 0 var(--u-space-half);
    }

    .u-select__dropdown-arrow {
      padding: 0 var(--u-space-one) 0 var(--u-space-one);
    }
  }

  &.u-select--readonly {
    pointer-events: none;
    cursor: not-allowed;

    .u-select__control {
      cursor: not-allowed;
      background-color: var(--u-color-background-callout);
      opacity: 0.8;

      &:not(.u-select__control--is-focused) {
        border-color: var(--u-color-line-subtle);
      }

      .u-form__item--is-error & {
        border-color: var(--u-color-alert-foreground);
      }
    }

    .u-select__value-container .u-select__input > input {
      cursor: not-allowed;
      color: transparent !important;
    }
  }

  :focus-visible {
    outline: none;
  }

  .u-select__control {
    border-radius: var(--u-border-radius-small);
    min-height: 2.5rem;
    background-color: var(--u-color-background-popover);
    border-color: var(--u-color-line-subtle);

    @media (prefers-reduced-motion: no-preference) {
      transition: border-color var(--u-animation-duration-fast) var(--u-animation-easing-static);
    }

    &:hover,
    &:focus {
      border-color: var(--u-color-line-subtle);
      box-shadow: none;
    }

    .u-form__item--is-error & {
      border-color: var(--u-color-alert-foreground);
    }

    &.u-select__control--is-focused {
      border-color: var(--u-color-emphasis-background-contrast);
      box-shadow: 0 2px 4px 0 var(--u-color-focus-shadow);
    }
  }

  .u-select__control--menu-is-open {
    border-radius: var(--u-border-radius-small) var(--u-border-radius-small) 0 0;
    border-top: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast) !important;
    border-right: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast) !important;
    border-bottom: calc(1rem / 16) solid var(--u-color-line-subtle) !important;
    border-left: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast) !important;

    & .u-select__dropdown-arrow > .u-icon {
      transform: rotate(180deg);
    }
  }

  .u-select__value-container {
    padding: 0 var(--u-space-one) 0 var(--u-space-one);
    font-size: var(--u-font-size-default);
  }

  .u-select__single-value {
    color: var(--u-color-base-foreground);
    margin-left: 0;
    margin-right: 0;
    padding-bottom: 1px;
    width: calc(100% - var(--u-space-one));
    top: calc(50% + calc(1rem / 16));

    .u-form__item--is-error & {
      color: var(--u-color-alert-foreground);
    }

    & + div {
      margin: 0;
    }
  }

  .u-select__placeholder {
    margin: 0;
    color: var(--u-color-base-foreground-subtle);
    white-space: nowrap;
    text-overflow: ellipsis;
    width: calc(100% - var(--u-space-one));
    overflow: hidden;
    top: calc(50% + calc(1rem / 16));

    .u-form__item--is-error & {
      color: var(--u-color-alert-foreground);
    }

    & + div {
      margin: 0;
      padding: 0;
    }
  }

  .u-select__indicator-separator {
    display: none;
  }

  .u-select__clear-indicator {
    padding: 0 var(--u-space-half);
  }

  input.u-select__input {
    color: var(--u-color-base-foreground) !important;
  }

  .u-select__multi-value {
    align-items: center;
    border-radius: var(--u-border-radius-small);
    margin: var(--u-space-eighth);
    cursor: pointer;

    &,
    .u-select__multi-value__label,
    .u-select__multi-value__remove {
      background-color: var(--u-color-bg-level3);
      color: var(--u-color-base-foreground);

      @media (prefers-reduced-motion: no-preference) {
        transition:
          color var(--u-animation-duration-fast) var(--u-animation-easing-static),
          background-color var(--u-animation-duration-fast) var(--u-animation-easing-static);
      }
    }

    &:hover,
    &:focus {
      background-color: var(--u-color-bg-level3-accent);

      .u-select__multi-value__label,
      .u-select__multi-value__remove {
        background-color: var(--u-color-bg-level3-accent);
      }
    }

    &:first-child {
      margin-left: 0 !important;
    }

    &.u-select__multi-value--has-error {
      &,
      .u-select__multi-value__label,
      .u-select__multi-value__remove {
        color: var(--u-color-button-label-white);
        background-color: var(--u-color-alert-background-contrast);

        &:hover,
        &:focus {
          background-color: var(--u-color-alert-background-contrast-hover);

          .u-select__multi-value__label,
          .u-select__multi-value__remove {
            background-color: var(--u-color-alert-background-contrast-hover);
          }
        }
      }
    }
  }

  .u-form__item--is-error & {
    &:not(.u-select--has-single-error) {
      .u-select__multi-value {
        &,
        .u-select__multi-value__label,
        .u-select__multi-value__remove {
          color: var(--u-color-button-label-white);
          background-color: var(--u-color-alert-background-contrast);

          &:hover,
          &:focus {
            &,
            .u-select__multi-value__label,
            .u-select__multi-value__remove {
              background-color: var(--u-color-alert-background-contrast-hover);
            }
          }
        }
      }
    }
  }

  .u-select__multi-value__label {
    border-radius: var(--u-border-radius-small) 0 0 var(--u-border-radius-small);
    font-size: var(--u-font-size-micro);
    padding: 0.25rem;

    &:focus {
      outline: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast);
      box-shadow: 0 2px 4px 3px var(--u-color-focus-shadow);
      z-index: var(--u-zindex-1);
    }
  }

  .u-select__multi-value__edit {
    border: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast-hover);
    border-radius: 0 var(--u-border-radius-small) var(--u-border-radius-small) 0;
    font-size: var(--u-font-size-micro);
    padding: 0.25rem;
    background-color: var(--u-color-button-label-white);
  }

  .u-select__multi-value__remove {
    height: 1.25rem;
    padding-right: var(--u-space-eighth);
    padding-left: var(--u-space-eighth);
    border-radius: 0 var(--u-border-radius-small) var(--u-border-radius-small) 0;

    &:focus {
      outline: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast);
      box-shadow: 0 2px 4px 3px var(--u-color-focus-shadow);
    }
  }
}

.u-select-container,
body {
  .u-form__item--xsmall &,
  .u-form--xsmall & {
    .u-select__menu-list {
      max-height: 9rem;
    }

    .u-select__option,
    .u-select__option-title {
      font-size: var(--u-font-size-micro);

      .u-text--default {
        font-size: var(--u-font-size-micro);
      }
    }
  }

  .u-form__item--small &,
  .u-form--small & {
    .u-select__menu-list {
      max-height: 10rem;
    }

    .u-select__option,
    .u-select__option-title {
      font-size: var(--u-font-size-small);

      .u-text--default {
        font-size: var(--u-font-size-small);
      }
    }
  }

  .u-form__item--medium &,
  .u-form--medium & {
    .u-select__menu-list {
      max-height: 10.75rem;
    }

    .u-select__option,
    .u-select__option-title {
      font-size: var(--u-font-size-default);

      .u-text--default {
        font-size: var(--u-font-size-default);
      }
    }
  }

  .u-form__item--large &,
  .u-form--large & {
    .u-select__menu-list {
      max-height: 12rem;
    }

    .u-select__option,
    .u-select__option-title {
      font-size: var(--u-font-size-large);

      .u-text--default {
        font-size: var(--u-font-size-large);
      }
    }
  }

  .u-select__option {
    color: var(--u-color-base-foreground);
    position: relative;
    display: flex;
    padding: var(--u-space-quarter) var(--u-space-one);
    cursor: pointer;

    @media (prefers-reduced-motion: no-preference) {
      transition:
        background-color var(--u-animation-duration-fast) var(--u-animation-easing-static),
        color var(--u-animation-duration-fast) var(--u-animation-easing-static);
    }

    &:hover,
    &:focus,
    &.u-select__option--is-focused {
      background-color: var(--u-color-emphasis-background-hover);
      color: var(--u-color-base-foreground-contrast);
    }

    &.u-select__option--is-selected {
      background-color: var(--u-color-emphasis-background);
      color: var(--u-color-base-foreground-contrast);
    }

    &.u-select__option--is-disabled {
      color: var(--u-color-base-foreground);
      pointer-events: none;
      opacity: 0.25;
    }
  }

  .u-select__group {
    padding-top: var(--u-space-half);
  }

  .u-select__option-title {
    display: block;
    color: var(--u-color-base-foreground);
    font-size: var(--u-font-size-default);
  }

  .u-select__option-subtitle {
    display: block;
    margin-top: var(--u-space-eighth);
    color: var(--u-color-base-foreground-subtle);
    font: inherit;
    font-size: var(--u-font-size-micro);
  }

  .u-select__menu-list {
    max-height: 10.75rem;
    padding-top: var(--u-space-half) !important;
    padding-bottom: var(--u-space-half) !important;
    border-radius: 0 0 var(--u-border-radius-small) var(--u-border-radius-small);
    background:
      linear-gradient(var(--u-color-background-popover), var(--u-color-background-popover)),
      linear-gradient(var(--u-color-background-popover), var(--u-color-background-popover)) 0 100%,
      linear-gradient(rgb(0 0 0 / 10%), var(--u-color-background-popover)),
      linear-gradient(var(--u-color-background-popover), rgb(0 0 0 / 10%)) 0 100%;
    background-repeat: no-repeat;
    background-color: var(--u-color-background-popover);
    background-size:
      100% var(--u-space-half),
      100% var(--u-space-half),
      100% var(--u-space-half),
      100% var(--u-space-half);
    background-attachment: local, local, scroll, scroll;

    @media (prefers-reduced-motion: no-preference) {
      transform-origin: top center;
      opacity: 0;
      animation: menu-items-enter var(--u-animation-duration-slow) forwards;
    }
  }

  .u-select__menu {
    margin-top: 0 !important;
    border-radius: 0 0 var(--u-border-radius-small) var(--u-border-radius-small) !important;
    border-top: 0 solid transparent;
    border-right: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast);
    border-bottom: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast);
    border-left: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast);
    box-shadow: 0 2px 4px 0 var(--u-color-focus-shadow) !important;

    @media (prefers-reduced-motion: no-preference) {
      transform-origin: top center;
      transform: translateY(-25%) scale3d(1, 0, 1);
      opacity: 0;
      animation: menu-enter var(--u-animation-duration-fast) var(--u-animation-easing-entering) forwards;
    }

    background-color: var(--u-color-background-popover) !important;
  }

  .u-select--menu-top {
    margin-bottom: 0 !important;
    border-top: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast);
    border-right: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast);
    border-bottom: 0 solid transparent;
    border-left: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast);
    border-radius: var(--u-border-radius-small) var(--u-border-radius-small) 0 0 !important;
    box-shadow: 0 -2px -4px 0 var(--u-color-focus-shadow) !important;
  }

  /* Apply styles to the "Control" with class `u-select__control--menu-is-open` if the sibling "Menu" has
  class `u-select--menu-top`.
  If <Select> prop `menuPosition` has value "fixed", then there will be a "Portal" `<div>` wrapping the "Menu". */
  .u-select__control--menu-is-open {
    &:has(+ .u-select--menu-top),
    &:has(+ div .u-select--menu-top) {
      border-radius: 0 0 var(--u-border-radius-small) var(--u-border-radius-small);
      border-top: calc(1rem / 16) solid var(--u-color-line-subtle) !important;
      border-right: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast) !important;
      border-bottom: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast) !important;
      border-left: calc(1rem / 16) solid var(--u-color-emphasis-background-contrast) !important;
    }
  }
}

@keyframes menu-enter {
  0% {
    transform: scale3d(1, 0.8, 1);
    opacity: 0;
  }

  100% {
    transform: scale3d(1, 1, 1);
    opacity: 1;
  }
}

@keyframes menu-items-enter {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
