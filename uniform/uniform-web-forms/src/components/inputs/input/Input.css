.u-input__wrapper {
  display: flex;

  > .u-input {
    flex: 1;
    text-overflow: ellipsis;
  }
}

.u-environment--dark .u-input--date,
.u-environment--dark .u-input--time {
  color-scheme: dark;
}

.u-environment--light .u-input--date,
.u-environment--light .u-input--time {
  color-scheme: light;
}

.u-input,
.u-input--date,
.u-input--time {
  color: var(--u-color-base-foreground);
  border-color: var(--u-color-line-subtle);
  background-color: var(--u-color-background-container);
  width: 100%;
  border-style: solid;
  border-width: calc(1rem / 16);
  box-shadow: none;
  border-radius: var(--u-border-radius-small);
  outline: 0;
  appearance: none;
  height: 2.5rem;
  padding: 0 var(--u-space-one);

  &:focus,
  &:focus-within {
    border-color: var(--u-color-emphasis-background-contrast);
    box-shadow: 0 2px 4px 0 var(--u-color-focus-shadow);
    outline: 0;
  }

  &::placeholder {
    color: var(--u-color-base-foreground-subtle);
  }

  .u-form__item--is-error & {
    color: var(--u-color-alert-foreground);
    border-color: var(--u-color-alert-foreground);

    &::placeholder {
      color: var(--u-color-alert-foreground);
    }
  }

  .u-form__item--is-disabled & {
    pointer-events: none;
  }

  .u-form__item--medium &,
  .u-form--medium & {
    font-size: var(--u-font-size-default);
    height: 2.5rem;
    padding: 0 var(--u-space-one);
  }

  .u-form__item--large &,
  .u-form--large & {
    font-size: var(--u-font-size-large);
    height: 3rem;
    padding: 0 var(--u-space-one);
  }

  .u-form__item--small &,
  .u-form--small & {
    font-size: var(--u-font-size-small);
    height: 2rem;
    padding: 0 var(--u-space-half);
  }

  .u-form__item--xsmall &,
  .u-form--xsmall & {
    font-size: var(--u-font-size-micro);
    height: 1.5rem;
    padding: 0 var(--u-space-half);
  }

  &:read-only {
    background-color: var(--u-color-background-callout);
    border-color: var(--u-color-line-subtle);
    opacity: 0.8;
    cursor: not-allowed;

    &:focus,
    &:focus-within {
      border-color: var(--u-color-emphasis-background-contrast);
      box-shadow: 0 2px 4px 0 var(--u-color-focus-shadow);
      outline: 0;
    }
  }
}

.u-form__input--file {
  margin-top: var(--u-space-quarter);
  margin-bottom: var(--u-space-quarter);

  .u-form--compact & {
    margin-top: var(--u-space-quarter);
    margin-bottom: var(--u-space-quarter);
  }
}

.u-form__input--range {
  width: 100%;

  &:focus {
    outline: 0;
  }
}
