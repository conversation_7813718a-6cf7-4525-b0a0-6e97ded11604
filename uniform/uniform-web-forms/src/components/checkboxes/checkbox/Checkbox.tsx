import type { ChangeEvent, InputHTMLAttributes } from 'react';

import classNames from 'classnames';

import { type ClassNameProps, type QaIdProps, useId } from '@hudl/uniform-shared';

import { Required } from '../../../partials/required/Required';
import type { InputDisplayTypeProps } from '../../../PropTypes';

import './Checkbox.css';

export interface CheckboxProps
  extends InputHTMLAttributes<HTMLInputElement>,
    ClassNameProps,
    InputDisplayTypeProps,
    QaIdProps {
  /**
   * Determines whether the checkbox is checked.
   */
  isChecked?: boolean;
  /**
   * Determines whether the checkbox is disabled.
   */
  isDisabled?: boolean;
  /**
   * Determines whether the checkbox is readonly.
   */
  isReadOnly?: boolean;
  /**
   * Determines whether the required asterisk is shown.
   */
  isRequired?: boolean;
  /**
   * Determines whether to apply error styling to the input.
   */
  hasError?: boolean;
  /**
   * Determines whether the checkbox should be styled as a child in a `CheckboxGroup`.
   */
  isNested?: boolean;
  /**
   * Determines whether the checkbox is partially checked (indeterminate).
   */
  isPartiallyChecked?: boolean;
  /**
   * The label that appears to the right of the checkbox.
   */
  label?: string;
  /**
   * The value of the checkbox when submitting the form, if the checkbox is currently toggled on.
   */
  value?: string | number | string[];
  /**
   * Called when checkbox is checked or unchecked.
   */
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  /**
   * A unique string to target the checkbox in automated tests. The qaId is also passed down to the following elements inside the component:
   * - qaId-label
   */
  qaId?: string;
}

/**
 * Let users pick one or more options from a short list.
 */
export function Checkbox({
  displayType = 'default',
  label,
  isNested,
  isPartiallyChecked,
  isChecked,
  isDisabled,
  isReadOnly,
  isRequired,
  hasError,
  qaId,
  className,
  ...props
}: CheckboxProps): React.JSX.Element {
  const defaultId = useId();
  const defaultName = useId('uniName_');

  const elementId = props.id ?? defaultId;
  const checkboxName = props.name ?? defaultName;
  const isCheckboxChecked = isChecked && !(isDisabled && isNested);

  const classes = classNames(
    'u-form__check-item',
    `u-form__check-item--${displayType}`,
    {
      'u-form__check-item--child': isNested,
      'u-form__check-item--is-checked': isCheckboxChecked,
      'u-form__check-item--is-indeterminate': !isCheckboxChecked && isPartiallyChecked,
      'u-form__check-item--is-disabled': isDisabled,
      'u-form__check-item--is-readonly': isReadOnly,
      'u-form__check-item--is-error': hasError,
    },
    className
  );

  const labelQaId = qaId ? `${qaId}-label` : undefined;

  return (
    <div className={classes}>
      <label className="u-form__label--check" htmlFor={elementId} data-qa-id={labelQaId}>
        {displayType === 'default' && (
          <svg width="16px" height="16px" className="u-form__check-indicator" viewBox="0 0 16 16">
            <rect className="u-form__check-indicator__background" x="2" y="2" width="12" height="12" rx="2" />
            <rect className="u-form__check-indicator__indeterminate" x="5" y="7" width="6" height="2" rx="0.5" />
            <path
              className="u-form__check-indicator__check"
              d="M11.3756706,4.23018776 L6.43160893,10.1230579 L4.46475452,8.47354152 C4.1980624,8.24916113
            3.79097226,8.28249764 3.56338643,8.55303629 L3.1511676,9.04539098 C2.92165851,9.31913506 2.95820046,
            9.71981433 3.2287391,9.94675907 L6.17837963,12.4219953 C6.31172569,12.5335444 6.48033153,12.581626
            6.64444976,12.5675221 L6.64573193,12.5688042 C6.80920908,12.5540592 6.96691644,12.4764877 7.08038881,
            12.3412184 L12.8488882,5.46620318 C13.0783972,5.19374127 13.0418553,4.79049765 12.7706756,4.56355291
            L12.278962,4.1500519 C12.0052179,3.9211839 11.6032564,3.95900803 11.3756706,4.23018776 Z"
            />
          </svg>
        )}
        <input
          {...props}
          id={elementId}
          name={checkboxName}
          className="u-form__input--check"
          type="checkbox"
          checked={isChecked}
          disabled={isDisabled}
          readOnly={isReadOnly}
          data-qa-id={qaId}
        />

        <span>
          {label}
          {isRequired && <Required qaId={qaId} />}
        </span>
      </label>
    </div>
  );
}
