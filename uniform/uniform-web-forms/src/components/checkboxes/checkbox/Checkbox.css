@import '@hudl/uniform-web-button/Button.css';

.u-form__check-item {
  position: relative;

  &.u-form__check-item--default {
    &:focus {
      outline: 0;
    }

    .u-form__item--is-error & {
      .u-form__label--check {
        color: var(--u-color-alert-foreground);
      }
    }
  }

  .u-form__label--check {
    color: var(--u-color-base-foreground);
    display: flex;
    align-items: center;
    cursor: pointer;

    .u-form__item--medium &,
    .u-form--medium & {
      font-size: var(--u-font-size-default);
    }

    .u-form__item--xsmall &,
    .u-form--xsmall & {
      font-size: var(--u-font-size-micro);
    }

    .u-form__item--small &,
    .u-form--small & {
      font-size: var(--u-font-size-small);
    }

    .u-form__item--large &,
    .u-form--large & {
      font-size: var(--u-font-size-large);
    }
  }

  .u-form__input--check {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    cursor: pointer;
  }

  &.u-form__check-item--child {
    margin-top: var(--u-space-one);
  }

  &.u-form__check-item--is-readonly {
    opacity: 0.8;

    .u-form__check-indicator {
      opacity: 0.6;
    }
  }

  &.u-form__check-item--is-disabled {
    opacity: 0.3;

    .u-form__check-indicator {
      opacity: 1; /* safeguard against lower opacity if readonly is also true */
    }
  }

  &.u-form__check-item--is-disabled,
  &.u-form__check-item--is-readonly,
  &.u-form__item--is-readonly .u-form__check-item {
    cursor: not-allowed;

    .u-form__label--check,
    input.u-form__input--check {
      cursor: unset;
      pointer-events: none;
      user-select: none;
    }
  }

  &.u-form__check-item--is-error {
    .u-form__label--check {
      color: var(--u-color-alert-foreground);
    }
  }
}

.u-form__check-indicator {
  position: relative;
  margin-right: var(--u-space-half);
  overflow: visible;
  transform-origin: center;
  min-width: 1rem;
  min-height: 1rem;

  .u-form__check-item:not(.u-form__check-item--is-disabled, .u-form__check-item--is-readonly):active & {
    @media (prefers-reduced-motion: no-preference) {
      transform: scale3d(0.9, 0.9, 0.9);
    }
  }

  .u-form__check-item:focus &,
  .u-form__check-item:focus-within & {
    outline: calc(1rem / 16) solid var(--u-color-emphasis-foreground);
    filter: drop-shadow(0 var(--u-space-eighth) 0.25rem var(--u-color-focus-shadow));
  }

  .u-form__check-indicator__background {
    stroke: var(--u-color-line);
    fill: var(--u-color-background-container);
    stroke-width: var(--u-space-eighth);

    @media (prefers-reduced-motion: no-preference) {
      transition:
        fill var(--u-animation-duration-fast) var(--u-animation-easing-static),
        stroke var(--u-animation-duration-fast) var(--u-animation-easing-static);
    }

    .u-form__check-item:not(.u-form__check-item--is-disabled, .u-form__check-item--is-readonly):hover &,
    .u-form__check-item:not(.u-form__check-item--is-disabled, .u-form__check-item--is-readonly):focus & {
      stroke: var(--u-color-base-foreground-contrast);
    }

    .u-form__check-item:focus &,
    .u-form__check-item:focus-within & {
      stroke: var(--u-color-emphasis-foreground) !important;
    }

    .u-form__check-item--is-checked &,
    .u-form__check-item--is-indeterminate & {
      stroke: var(--u-color-base-foreground-contrast);
      fill: var(--u-color-base-foreground-contrast);
    }

    .u-form__check-item--is-checked:focus &,
    .u-form__check-item--is-checked:focus-within &,
    .u-form__check-item--is-indeterminate:focus &,
    .u-form__check-item--is-indeterminate:focus-within & {
      fill: var(--u-color-emphasis-foreground);
    }

    .u-form__item--is-error &,
    .u-form__item--is-error .u-form__check-item:hover &,
    .u-form__item--is-error .u-form__check-item:focus &,
    .u-form__item--is-error .u-form__check-item:focus-within &,
    .u-form__check-item--is-error &,
    .u-form__check-item--is-error:hover &,
    .u-form__check-item--is-error:focus &,
    .u-form__check-item--is-error:focus-within & {
      stroke: var(--u-color-alert-foreground);
    }

    .u-form__item--is-error .u-form__check-item--is-checked &,
    .u-form__item--is-error .u-form__check-item--is-indeterminate &,
    .u-form__check-item--is-error.u-form__check-item--is-checked &,
    .u-form__check-item--is-error.u-form__check-item--is-indeterminate & {
      stroke: var(--u-color-alert-foreground);
      fill: var(--u-color-alert-foreground);
    }
  }

  .u-form__check-indicator__indeterminate {
    fill: var(--u-color-background-container);
    transform-origin: bottom left;
    transform: scale3d(0, 0, 0);
    visibility: hidden;

    .u-form__check-item--is-indeterminate & {
      transform: scale3d(1, 1, 1);

      @media (prefers-reduced-motion: no-preference) {
        transition: transform var(--u-animation-duration-fast) 0.1s cubic-bezier(0.23, 1, 0.32, 1);
      }

      visibility: visible;
    }

    .u-form__item--is-error & {
      fill: var(--u-color-background-container);
    }
  }

  .u-form__check-indicator__check {
    fill: var(--u-color-background-container);
    transform-origin: bottom left;
    transform: scale3d(0, 0, 0);
    opacity: 0;

    .u-form__check-item--is-checked & {
      transform: scale3d(1, 1, 1);
      opacity: 1;

      @media (prefers-reduced-motion: no-preference) {
        transition:
          transform var(--u-animation-duration-fast) var(--u-animation-easing-moving),
          opacity var(--u-animation-duration-fast) var(--u-animation-easing-static);
      }
    }

    .u-form__item--is-error & {
      fill: var(--u-color-background-container);
    }
  }
}
