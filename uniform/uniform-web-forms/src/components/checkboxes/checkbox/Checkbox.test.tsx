import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Checkbox } from './Checkbox';

describe('Checkbox', () => {
  it('Sets the label correctly', () => {
    render(<Checkbox label="Label here" />);
    expect(screen.getByRole('checkbox')).toBeDefined();
    expect(screen.getByLabelText('Label here')).toBeDefined();
  });

  it('Sets the value correctly', () => {
    render(<Checkbox value="CircusOfValue" />);
    expect(screen.getByDisplayValue('CircusOfValue')).toBeDefined();
  });

  it('Sets the id correctly', () => {
    render(<Checkbox id="CircusOfValue" />);
    expect(screen.getByRole('checkbox')).toHaveAttribute('id', 'CircusOfValue');
  });

  it('Should be able to be checked', async () => {
    const user = userEvent.setup();
    render(<Checkbox isChecked />);
    expect(screen.getByRole('checkbox')).toBeChecked();

    // The checkbox stays clicked after clicking on it because the isChecked attribute is passed in.
    await user.click(screen.getByRole('checkbox'));
    expect(screen.getByRole('checkbox')).toBeChecked();
  });

  it('Should be able to be disabled', async () => {
    const user = userEvent.setup();
    const onClick = vi.fn();
    render(<Checkbox isDisabled onClick={onClick} />);
    expect(screen.getByRole('checkbox')).toBeDisabled();
    await user.click(screen.getByRole('checkbox'));
    expect(onClick).toHaveBeenCalledTimes(0);
  });

  /* The Checkbox component does not set this prop correctly, so targeting the
  component using userEvent functionality bypasses the attribute. The functionality
  should be changed to apply the isReadOnly prop to the correct level in the DOM. */
  it('Should be able to be read only', () => {
    const { container } = render(<Checkbox isReadOnly />);
    expect(container.querySelector('.u-form__check-item--is-readonly')).not.toBeNull();
  });

  it('Calls the `onChange` function appropriately', async () => {
    const user = userEvent.setup();
    const onChangeFunc = vi.fn();
    render(<Checkbox onChange={onChangeFunc} />);
    await user.click(screen.getByRole('checkbox'));
    expect(onChangeFunc).toHaveBeenCalledTimes(1);
  });

  it('Sets the `data-qa-id` attribute if `qaId` is passed', () => {
    render(<Checkbox qaId="unique-id" />);
    expect(screen.getByTestId('unique-id')).toBeDefined();
  });

  it('Adds required indicator if `isRequired` is true', () => {
    render(<Checkbox isRequired qaId="unique-id" />);
    expect(screen.getByTestId('unique-id-required-indicator')).toBeDefined();
  });
});
