# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [7.1.0]

- Bumps version of `@hudl/uniform-web-forms` to v7.1.0
- Updates `Checkbox` story to include new `hasError` and `isRequired` props

## [7.0.3]

- Updates `SearchInput` storybook implementation `onDismissClick()` & `onClick()` defaults

## [7.0.2]

- Bumps version of `@hudl/uniform-tokens` to v2.0.1

## [7.0.1]

- Updates `ReactElement` types to either `ReactNode` or `JSX.Element`

## [7.0.0]

- Bumps version of `@hudl/uniform-tokens` to v2.0.0
- Bumps version of `@hudl/uniform-web-icons` to v7.0.0
- Adds new type component stories
  - `Display`
  - `Title`
  - `Lead`

## [6.10.0]

- Bumps version of `@hudl/uniform-web-forms` to v6.4.0

## [6.9.0]

- Bumps version of `@hudl/uniform-web-dialogs` to v6.3.0

## [6.8.0]

- Bumps version of `@hudl/uniform-web-actions` to v6.3.0

## [6.7.0]

### Added

- Extended `AccordionContentProps` to include all props from the underlying Radix UI component.

## [6.6.0]

- Bumps version of `@hudl/uniform-web-actions` to v6.2.0
- Bumps version of `@hudl/uniform-web-avatar` to v6.1.0
- Bumps version of `@hudl/uniform-web-button` to v6.2.0
- Bumps version of `@hudl/uniform-web-card` to v6.2.0
- Bumps version of `@hudl/uniform-web-dialogs` to v6.2.0
- Bumps version of `@hudl/uniform-web-forms` to v6.3.0
- Bumps version of `@hudl/uniform-web-notifications` to v6.3.0

## [6.5.0]

- Bumps version of `@hudl/uniform-web-card` to v6.1.0
- Bumps version of `@hudl/uniform-web-dialogs` to v6.1.0
- Bumps version of `@hudl/uniform-web-notifications` to v6.2.0
- Fixes inconsistent versions of `react-aria-components` in external usage of `uniform-web`

## [6.4.2]

- Bumps version of `@hudl/uniform-web-actions` to v6.1.0

## [6.4.1]

### Fixed

- Fixed flickering issues in accordion animations during open/close transitions

## [6.4.0]

- Updates `react-aria-component`, `@hudl/uniform-web-button`, `@hudl/uniform-web-forms`

## [6.3.2]

- Bumps version of `@hudl/uniform-web-form` to v6.1.1

## [6.3.1](https://2a7806d--63dd878b6e1bac956cef23ec.chromatic.com/)

- Bumps version of `@hudl/uniform-web-dialogs` to v6.0.1

## [6.3.0](https://be6ea62--63dd878b6e1bac956cef23ec.chromatic.com/)

- Bumps version of `@hudl/uniform-web-notifications` to v6.1.0

## [6.2.1](https://d179244--63dd878b6e1bac956cef23ec.chromatic.com/)

### Fixed

- Fixed a bug in the `Onboarding` component, where the dismiss cross button did not match the tooltip's text color in dark mode.

## [6.2.0](https://eeded77--63dd878b6e1bac956cef23ec.chromatic.com/)

- Bumps version of `@hudl/uniform-web-forms` to v6.1.0
- Bumps version of `@hudl/uniform-web-type` to v5.56.0

## [6.1.0](https://bc954b0--63dd878b6e1bac956cef23ec.chromatic.com/)

- Added `isTransparent` prop to `Scrim`

## [6.0.0](https://1ed2eb4--63dd878b6e1bac956cef23ec.chromatic.com/)

### Changed

- Bumps version of `@hudl/uniform-web-button` to v6.0.0
- Bumps version of `@hudl/uniform-web-actions` to v6.0.0
- Bumps version of `@hudl/uniform-web-avatar` to v6.0.0
- Bumps version of `@hudl/uniform-web-card` to v6.0.0
- Bumps version of `@hudl/uniform-web-dialogs` to v6.0.0
- Bumps version of `@hudl/uniform-web-forms` to v6.0.0
- Bumps version of `@hudl/uniform-web-notifications` to v6.0.0
- Removes deprecated `text` prop from `ActionBar` component
- Removes `ActionListAction`. This is replaced by `MenuAction`
- Removes `ActionListActionGroup`. This is replaced by `MenuActionGroup`

## [5.67.0](https://e616d73--63dd878b6e1bac956cef23ec.chromatic.com/)

- Moved all card components into `@hudl/uniform-web-card`

## [5.66.0]

- Moves `Alert`, `Modal`, `Overlay` components to `@hudl/uniform-web-dialogs`

## [5.65.0]

- Moves `Note`, `Notice`, `Toast` components into `@hudl/uniform-web-notifications`

## [5.64.0]

- Moved all actions components into `@hudl/uniform-web-actions`

## [5.63.0]

- Moved all theme components into `@hudl/uniform-web-theme`

## [5.62.0]

- Updated `@hudl/uniform-tokens`

## [5.61.0]

### Added

- Added new `Scrim` component.

### Updated

- Update the `Onboarding` component to use the new animated `Scrim` component.

## [5.60.0]

### Changed

- Moved all forms components into `@hudl/uniform-web-forms`

## [5.59.0]

### Fixed

- Fixed a bug in the `Notice` component causing flickering when width was around the breakpoint

## [5.58.0]

### Changed

- Updated `@hudl/uniform-web-tooltip` to v5.50.0
- Updated the `Onboarding` component to only pass through the `triggerClassName` on open

## [5.57.0]

### Changed

- Moved Type components into `@hudl/uniform-web-type`

## [5.56.0]

### Changed

- Moved `Dismiss` component, `IconSize` and `IconColor` types.
- Changes to `@hudl/uniform-web-button` and `@hudl/uniform-web-icons`

## [5.55.0]

### Changed

- Version bump to make sure reverted code gets published outside the monorepo.

## [5.53.0]

### Changed

- Refactored `Avatar` component to improve accessibility
  - Set the `Avatar` `sport` prop type to the exported type `AvatarTeamSport`
  - Added the `aria-label` prop to be used with clickable avatars
  - Renamed the `u-avatar__initials` classname to `u-avatar__text` to support more generic use case
- Added the `storybook-addon-pseudo-states` Storybook addon for testing focus and active states
- Bumped version of `react-aria-components`

## [5.52.0]

### Changed

- Moved Button component into separate package
- Refactors references to Button CSS

## [5.51.0]

### Changed

- Moved Spinner component into separate package

## [5.50.0]

### Changed

- Moved Avatar components into separate package

## [5.49.0]

### Changed

- Moved `Tooltip` component into separate package

## [5.48.0]

### Changed

- Moved icons into separate package

## [5.47.0]

### Fixed

- Fixed `PortalProvider` not re-rendering when provided `containerRef` changed

## [5.46.0]

### Added

- Added `IconTicketNav` and `IconPass` icons

## [5.45.0]

### Updated

- Use updated Vite config with `vite-plugin-lib-inject-css`.

## [5.44.0]

### Changed

- Added the option to use `ReactElement` types as column headers in the DataTable

## [5.43.1]

### Added

- Added `z-index` to `DataTable` sticky first row to prevent overlap with relatively positioned elements

## [5.43.0]

### Updated

- Updated Vite config.

## [5.42.0]

### Changed

- Vite config to use common.

## [5.41.0]

### Changed

- Added `word-wrap: break-word` to Tooltip content styles to handle large unbreakable words

## [5.40.1]

### Fixed

- `Avatar` components without `onClick` are removed from the tab navigation

## [5.40.0]

### Added

- Add new `IconFavorite` and `IconFavoriteOff` icons

### Changed

- Update `IconNotifications` and `IconNotificationsOff` to a new design

## [5.39.7]

### Fixed

- Keyframe naming lint errors.

## [5.39.6]

### Fixed

- Number precision lint errors.

## [5.39.5]

### Fixed

- Media query lint errors.

## [5.39.4]

### Fixed

- `Select` menu styling now handles dynamic menu position when using value "auto" for prop `menuPlacement`.

## [5.39.3]

### Updated

- Forced color scheme for native input elements in light mode.

## [5.39.2]

### Fixed

- Several lint errors.

## [5.39.1]

### Fixed

- Several lint errors.

## [5.39.0]

### Added

- Add the `Menu` component split out from `ActionList`.

## [5.38.5]

### Fixed

- Incorrect styling of native input elements in dark mode.

## [5.38.4]

### Fixed

- Dependency vulnerability with legacy-swc-helpers.

## [5.38.3]

### Fixed

- Fixed styling of Uniform components within a Tooltip's content.

## [5.38.2]

### Fixed

- Fixed bug where checkboxes inside a disabled `CheckboxGroup` were still interactive.

## [5.38.1]

### Fixed

- Fixed an issue where the event was not passed to `SelectMark`'s `onClick`

## [5.38.0]

- Add `isDisabled` to `ActionList`

## [5.37.0]

### Added

- Add the `PriceInput` component

## [5.36.0]

### Added

- Add `isDisabled` prop to `SelectMark` component

## [5.35.1]

### Fixed

- Several style lint rule errors.

## [5.35.0]

### Added

- Adds the `Accordion` component

## [5.34.1]

### Fixed

- Fixed bug in `Select` and `CreatableSelect` components where letters that extend below the baseline of a font (eg. `g` and `j`) were being cut off.

## [5.34.0]

### Added

- `tooltipText` prop to `Avatar`, `AvatarUser`, `AvatarTeam`, `AvatarGroup`, and `AvatarOrg`

## [5.33.1]

- Make sure `Link` and `KeyboardShortcut` import CSS for `Button` and `Text`, respsectively
- Improve specificity of CSS for `KeyboardShortcut`

## [5.33.0]

- Upgrade Vite to `5.3.1`
- Upgrade @vitejs/plugin-react to `4.3.1`

## [5.32.1]

### Removed

- Generic Icon descriptions

## [5.32.0]

### Added

- New icons!
  - `IconClock` and `IconTicket`.

## [5.31.0]

### Updated

- Updated to use vitest instead of jest.
- Updated `IconOnBase` to import shared config from `uniform-shared`.

## [5.30.0]

### Updated

- Module resolution was changed from `node` to `bundler`.
- React Select module augmentation was updated to use `react-select/base`.

## [5.29.0]

### Updated

- Changes to ActionBar
  - Added a children prop (but kept the text prop for backward compatibility)
  - Added an optional overflow (three dot) menu
  - Added `buttonLocation` to align buttons left, right, or in overflow
  - Added responsiveness - if buttons do not fit they move into the overflow menu
  - Allow buttons to be customized with `buttonType` and `buttonStyle`
  - Made `onDismiss` optional - when undefined the dismiss button will not be shown
  - Added a size option for a `small` ActionBar
  - Updated CSS for better organization and customizability

## [5.28.0]

### Added

- Added `tooltipClassName` prop to `Tooltip` component to allow for more custom tooltip styling.

## [5.27.0]

### Added

- New `Angle7`, `Angle8`, and `Angle9` icons.

## [5.26.0]

### Added

- New `IconOnBase` icon, which includes a `runners` prop to dictate which bases should be filled.

## [5.25.1]

### Fixed

- Vertical alignment of text in Select component.

## [5.25.0]

### Added

- Added `PortalProvider` to determine the portal context for components when rendered in full-screen mode.

## [5.24.0]

### Added

- Released new Tabs components: `Tabs`, `TabList`, `Tab`, and `TabPanel`.

## [5.23.3]

### Fixed

- Alignment of `FormItems` in multiple columns.

## [5.23.2]

### Updated

- Added more specific CSS selectors to `SearchInput` component.

## [5.23.1]

### Added

- Remove unused prop from `DefaultAvatarIcon`.

## [5.23.0]

### Added

- `DefaultIcon` svg component renamed and exported as `DefaultAvatarIcon`.

## [5.22.3]

### Fixed

- removed `outer-region` div on ActionList
- Added ref on action list div and added event listener to check for mouse down events

## [5.22.2]

### Changed

- Bumped version of `react-select` to `5.8.0`.

### Fixed

- `Checkbox` components inside `ParentCheckbox` by ensuring `qaId` is properly set.

## [5.22.1]

### Added

- Added `parentSelector` prop to Alert component
- Added `prefers-reduced-motion` CSS options to dialogs to remove animation

## [5.22.0]

### Fixed

- Reverted `Onboarding` tooltip margins change from v5.15.0
- Replaced with `collisionPadding` prop on underlying `Tooltip` component

## [5.21.4]

### Fixed

- `CreateableSelect.qaId` is properly applied to all partials.

## [5.21.3]

### Fixed

- `Input.isRequired` to properly set `required` attribute.

## [5.21.2]

### Fixed

- Mobile CSS for `Overlay` and `Headline`.

## [5.21.1]

### Fixed

- Replaced `pxToRem` calcuations in `Select` component styles with equivalent hardcoded values.

## [5.21.0]

### Added

- New icon
  - `IconEffectFormat`

## [5.20.0]

### Added

- New types to extend `Action` type:
  - `ActionBarAction`
  - `ActionListAction`
  - `AlertAction`
  - `CardAction`
  - `ModalAction`

### Changed

- Made the `AvatarGroup.groupCount` optional. A fallback UI is already in place if no `groupCount` is provided.
- Default `AvatarTeam.sport` to `"othersport"` to show correct fallback UI.

### Fixed

- `FormModifier` CSS grid styling when `templateGrid` prop is supplied.
- Styles for `FormModifier` children form elements when `formSize` is set to `"xsmall"` or `"small"`.

### Removed

- **BREAKING** `onChange` prop from `ParentCheckboxProps`. This was marked for internal use only, so I moved it to a new interface that is not exported from the package.
  - Since this was never meant for public use, we will not do a major version bump even though this is technically a breaking change.
- **BREAKING** `displayType` prop from `CheckboxParentGroupProps`. The checkbox parent group does not support button display types.
  - Since this feature was never supported, we will not do a major version bump even though this is technically a breaking change.

## [5.19.0]

### Added

- More new icons!
  - `IconUserBan`
  - `IconUserRemove`
  - `IconAttach`
  - `IconBolt`
  - `IconTapHoldFastForward`
  - `IconTapHoldRewind`
  - `IconClipCount`
  - `IconSkipForward`
  - `IconSkipBackward`
  - `IconVideoCast`

`IconTapHoldFastForward` and `IconTapHoldRewind` are new wide icons. These are twice the width of regular icons.

## [5.18.0]

### Added

- New icons!
  - `IconProvideFeedback`, `IconSlowMotion`, and `IconSpotShadow`.

## [5.17.0]

### Added

- New utility types to use with Uniform `Select` and `CreatableSelect` components:
  - `SelectSingleValue`
  - `SelectMultiValue`
  - `SelectSingleOrMultiValue`
  - `SelectGroupBase`

### Fixed

- **BREAKING**: The `react-select` TypeScript implementation was previously broken, and caused various issues in projects that we tried to fix with type assertions, but it got out of hand. This version actually fixes the problem by:
  - Removing `React.forwardRef` usages in the Uniform `Select` and `CreatableSelect`. Due to this, the `ref` prop has been renamed `selectRef` in both components.
  - Properly passing generics to the `react-select` components. The `Option`, `IsMulti`, and `Group` generics are now correctly passed through the Uniform `SelectProps` and `CreatableSelectProps` to the `react-select` interfaces, which fixes issues with props like `onChange` not being aware of the `isMulti` prop value.

## [5.16.0]

### Added

- `Text` supports `id` prop

## [5.15.0]

### Added

- `Onboarding` tooltip has margin on sides

## [5.14.0]

### Added

- `Tooltip` supports `triggerClassName` prop

### Fixed

- `Onboarding` respects the `portalContainer` prop
- `Onboarding` trigger stylings now have a higher z-index to show the trigger element while the onboarding is visible
- `Onboarding` will correctly render 1 scrim instead of 2

## [5.13.0]

### Added

- New icons!
  - `IconUiNavigationBackArrow` and `IconUiNavigationForwardArrow`

### Changed

- Icon updates to `IconReports` and `IconVideo`

## [5.12.0]

### Added

- `SearchInput` supports `helpText` prop

## [5.11.0]

### Changed

- The Data Table `ElementCell` type now allows `string | number` for the `value` field, which enables sorting columns by numeric value.

### Fixed

- Issue preventing default sorting on the first column of Data Tables.

## [5.10.1]

### Fixed

- React key prop warning in `Modal`.

## [5.10.0]

### Added

- `SelectOnChangeValue` type to make the `Select.onChange` and `Select.onEdit` props easier to type check

### Fixed

- Types passed to `react-select` for `SelectProps` and `CreatableSelectProps`.

## [5.9.0]

### Added

- New icon!
  - `IconUnrestricted`.

## [5.8.0]

### Added

- New icons!
  - `IconCalendar`, `Icon4G0`, `Icon4G25`, `Icon4G50`, `Icon4G75`, `Icon4G100`, `IconUiAutoScroll`, and `IconUiViewList`.

### Fixed

- Direction of `IconFieldHockey` (flipped to better represent how a field hockey stick is actually used).

## [5.7.0]

### Changed

- `usePrimarySecondaryColorTheme` to `useTeamColorTheme`.
- `ThemeProvider` to include environment prop.

## [5.6.0]

### Added

- `/server` export for SSR-compatible components.
- SSR-compatible component documentation in Storybook.

## [5.5.6]

### Fixed

- Missing `@hudl/uniform-shared` version from `5.5.5` release. Version `1.4.0` of `@hudl/uniform-shared` is published and used in this version.

## [5.5.5]

### Added

- Use `QaIdProps` interface for qaId prop in Uniform components.

## [5.5.4]

### Fixed

- Uniform Notice component button layout fixed when `isTight` is set to false

## [5.5.3]

### Fixed

- Use `tabIndex` to enable keyboard navigation in the Avatar and Card components.

## [5.5.2]

### Fixed

- Use `--u-color-content-subtle` for all form input placeholder text for better color contrast.

## [5.5.1]

### Changed

- Build config to prevent minifying identifiers. Snapshot tests and React Devtools will now show the Uniform component name, rather than a single character. e.g. `Button` instead of `z`. Snapshots tests using Uniform components should be more stable.

## [5.5.0]

### Added

- The Onboarding component, which is a styled and controlled Tooltip under-the-hood.
- CSS animation to the Tooltip component, which can be disabled using the new `isAnimated` prop.
- `max-width` of `11.25rem` for Tooltips (matches version 4 value).

### Fixed

- Tooltip no longer overrides custom properties such as `data-qa-id` on trigger elements when `asChild={true}`.
- Tooltip trigger no longer receives `data-qa-id` if the `Tooltip.qaId` is `undefined`.
- `onOpenChange` is now called when `isOpen` is defined and updated.

## [5.4.1]

### Fixed

- Missing `IconProps` export after version `5.0.0`.

## [5.4.0]

### Added

- Dark and light theme support using the `data-theme` property ([`@hudl/uniform-tokens@1.1.0`](../uniform-tokens/CHANGELOG.md#110))
- `sideEffects` for `*.css` files in [`package.json`](./package.json).

## [5.3.0]

### Added

- Added `IconSubtract` to the interactions icons

## [5.2.0]

### Added

- `getContentColor` to exported functions (from `@hudl/uniform-shared`).

### Changed

- `usePrimarySecondaryColorTheme` to use accessible button label colors.

### Fixed

- `Avatar` components that weren't properly setting their secondary/background color.

## [5.1.1]

### Fixed

- Updated Select import for compatibility in @hudl/permissions to fix module interoperability issue

## [5.1.0]

### Added

- `ThemeProvider` component to allow customizing Uniform component colors. Currently supports Uniform Buttons and custom style properties.
- `usePrimarySecondaryColor` hook to generate a theme based on a team's primary and secondary colors; used with `ThemeProvider`.

## [5.0.0]

### Changed

- This is the official stable release of `@hudl/uniform-web` at version `5.0.0` 🎉 (no changes since `5.0.0-beta.2`).

## [5.0.0-beta.2]

### Added

- All Uniform Web `PropTypes` are now exported from the package root.

### Fixed

- Hide triangle from Tooltip unless `type` equals `"instruction"`.
- Occasional undefined error in Select component `selectProps`.
- Toast Messenger component is now included in the package bundle.

## [5.0.0-beta.1]

### Changed

- Upgraded package to `5.0.0-beta`! We plan to have a short testing period to verify components and styles load before creating an `RC` branch.

## [5.0.0-alpha.12]

### Changed

- Upgraded `@hudl/uniform-tokens` to version `1.0.0-alpha.2`. [See `@hudl/uniform-tokens` CHANGELOG](../uniform-tokens/CHANGELOG.md)
- All `uniform-` CSS class name prefixes to `u-`

## [5.0.0-alpha.11]

### Changed

- The first major version of `@hudl/uniform-tokens` to version `1.0.0-alpha.1`.

## [5.0.0-alpha.10]

### Changed

- The core Uniform stylesheet, imported from `@hudl/uniform-web/style.css`, is now built in the `@hudl/uniform-tokens` package, and re-exported from this package.

## [5.0.0-alpha.9]

### Changed

- Build config to externalize all dependencies when bundling.
- Upgraded `react-select` dependency to fix issue with importing `react-select/creatable` in ES Modules.

## [5.0.0-alpha.8]

### Removed

- `@sheerun/mutationobserver-shim`, since it is no longer needed with our current Jest version.

## [5.0.0-alpha.7]

### Changed

- Extend `IconProps` from `AriaAttributes` and apply to all icons.
- `Icon` component no longer incorrectly uses HTML `title` element. `aria-label` is used instead.
- `Icon` component does not receive `aria-label` if `aria-hidden` is set to true.

## [5.0.0-alpha.6]

### Removed

- `resize-observer-polyfill` dependency from Notice component.

## [5.0.0-alpha.5]

### Added

- Tooltip component with new `@radix-ui/react-tooltip` library.

## [5.0.0-alpha.4]

### Added

- `CreatableSelect` and `Select` components with upgraded `react-modal` library.

## [5.0.0-alpha.3]

### Added

- Dialog components with upgraded `react-select` library.
  - `Alert`, `Modal`, `Overlay`

## [5.0.0-alpha.2]

### Changed

- Styling method from `SCSS` to `CSS` with `PostCSS` plugins.

## [5.0.0-alpha.1]

### Added

- Initial setup for the `@hudl/uniform-web` package with Vite build toolchain.
- All components, other than `Alert`, `CreatableSelect`, `Modal`, `Overlay`, `Select`, and `Tooltip` from Version 4 package.
- `addCssImports()` Vite plugin to support CSS code splitting.
