import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite';

import {
  Checkbox,
  CheckboxGroup,
  type CheckboxGroupProps,
  CheckboxParentGroup,
  type CheckboxProps,
  ParentCheckbox,
} from '@hudl/uniform-web-forms';

import { Environment, Level } from '../../../../../index';
import CheckboxStoriesMeta from '../checkbox/stories/Checkbox.stories';

import './Checkboxes.stories.css';

export default {
  ...CheckboxStoriesMeta,
  title: 'Chromatic Tests/Checkboxes',
  parameters: {
    chromatic: { disableSnapshot: false },
    controls: { disable: true },
  },
} as Meta<typeof Checkbox>;

function CheckboxRowVariations(args: Partial<CheckboxGroupProps>) {
  return (
    <>
      <CheckboxGroup onChange={() => null} {...args}>
        <Checkbox value="t-1" label="Test 1" />
        <Checkbox value="t-2" label="Test 2" isReadOnly />
        <Checkbox value="t-3" label="Test 3" isDisabled />
        <Checkbox value="t-4" label="Test 4" isRequired />
        <Checkbox value="t-5" label="Test 5" hasError />
      </CheckboxGroup>
      <CheckboxGroup onChange={() => null} valuesChecked={['t-1', 't-2', 't-3']} {...args}>
        <Checkbox value="t-1" label="Test 1" />
        <Checkbox value="t-2" label="Test 2" isReadOnly />
        <Checkbox value="t-3" label="Test 3" isDisabled />
        <Checkbox value="t-4" label="Test 4" isRequired />
        <Checkbox value="t-5" label="Test 5" hasError />
      </CheckboxGroup>
      <CheckboxParentGroup>
        <ParentCheckbox value="p-1" label="Parent 1">
          <Checkbox value="t-1" label="Test 1" />
          <Checkbox value="t-2" label="Test 2" isReadOnly />
          <Checkbox value="t-3" label="Test 3" isDisabled />
          <Checkbox value="t-4" label="Test 4" isRequired />
          <Checkbox value="t-5" label="Test 5" hasError />
        </ParentCheckbox>
      </CheckboxParentGroup>
    </>
  );
}

function AllCheckboxVariations() {
  return (
    <div className="u-sb-checkbox-container">
      <CheckboxRowVariations />
      <CheckboxRowVariations header="Testing" helpText="Tested." isRequired />
      <CheckboxRowVariations header="Testing" helpText="Tested." hasError />
      <CheckboxRowVariations isReadOnly />
      <CheckboxRowVariations isDisabled />
      <CheckboxRowVariations displayType="button" />
      <CheckboxRowVariations displayType="button-minimal" />
      <CheckboxRowVariations orientation="horizontal" />
      <CheckboxRowVariations orientation="horizontal" displayType="button" />
      <CheckboxRowVariations orientation="horizontal" displayType="button-minimal" />
    </div>
  );
}

export const CheckboxVariations: StoryObj<CheckboxProps> = {
  render: () => (
    <>
      <Environment environment="light">
        <Level level="2">
          <AllCheckboxVariations />
        </Level>
      </Environment>
      <Environment environment="dark">
        <Level level="2">
          <AllCheckboxVariations />
        </Level>
      </Environment>
    </>
  ),
};
