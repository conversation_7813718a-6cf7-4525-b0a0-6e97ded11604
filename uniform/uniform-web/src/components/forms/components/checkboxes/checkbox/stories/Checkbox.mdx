import { <PERSON><PERSON>, <PERSON>a, Source } from '@storybook/addon-docs/blocks';
import { DocumentationTemplate } from 'config/storybook/DocumentationTemplate';

import { Checkbox } from '@hudl/uniform-web-forms';

import * as Stories from './Checkbox.stories';
import * as ExampleStories from './CheckboxExamples.stories';

<Meta of={Stories} />

<DocumentationTemplate hideDemo>

<Source
  dark
  langauge="tsx"
  code={`
import { Checkbox } from '@hudl/uniform-web';
    `}
/>

## Skip Ahead

- **[Usage Guidelines](#usage-guidelines)**: [Selection](#selection), [Display Type](#display-type), [State](#state)
- **[Component API](#component-api)**

## Usage Guidelines

### Selection

When used outside of a [checkbox group](#checkbox-group), the `isChecked` and `onChange` props control the selected state of the checkbox.

<Source
  dark
  langauge="tsx"
  code={`
const [isChecked, setIsChecked] = React.useState<CheckboxProps['isChecked']>(true);
return <Checkbox isChecked={isChecked} onChange={e => setIsChecked(e.target.checked)} {...args} />
    `}
/>

<Canvas of={ExampleStories.Selection} />

### Display Type

The `displayType` prop controls the style of the checkbox. The `"button"` and `"button-minimal"` styles can be used to make checkboxes appear as [buttons](/docs/components-buttons--docs).

<Canvas of={ExampleStories.DisplayType} />

### State

Checkboxes have six states: checked, unchecked, disabled, readonly, error, and required. The `isChecked`, `isDisabled`, `isReadOnly`, `hasError`, and `isRequired` props control the state.

<Canvas of={ExampleStories.State} />

</DocumentationTemplate>
