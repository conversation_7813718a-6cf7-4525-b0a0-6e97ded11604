import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/react-vite';

import { Checkbox, type CheckboxProps } from '@hudl/uniform-web-forms';

import CheckboxStoriesMeta from './Checkbox.stories';

import '../../stories/Checkboxes.stories.css';

export default {
  ...CheckboxStoriesMeta,
  title: 'Examples/Forms/Checkboxes',
} as Meta<CheckboxProps>;

export const Selection: StoryObj<CheckboxProps> = {
  render: (args) => {
    const [isChecked, setIsChecked] = useState<CheckboxProps['isChecked']>(true);
    return <Checkbox isChecked={isChecked} onChange={(e) => setIsChecked(e.target.checked)} {...args} />;
  },
  args: {
    label: 'Checkbox label',
  },
};

export const DisplayType: StoryObj<CheckboxProps> = {
  render: () => {
    const [defaultIsChecked, setDefaultIsChecked] = useState<CheckboxProps['isChecked']>(false);
    const [buttonIsChecked, setButtonIsChecked] = useState<CheckboxProps['isChecked']>(false);
    const [buttonMinimalIsChecked, setButtonMinimalIsChecked] = useState<CheckboxProps['isChecked']>(false);

    return (
      <div className="u-sb-checkbox-container align-center">
        <Checkbox
          isChecked={defaultIsChecked}
          onChange={(e) => setDefaultIsChecked(e.target.checked)}
          label="default"
          displayType="default"
        />
        <Checkbox
          isChecked={buttonIsChecked}
          onChange={(e) => setButtonIsChecked(e.target.checked)}
          label="button"
          displayType="button"
        />
        <Checkbox
          isChecked={buttonMinimalIsChecked}
          onChange={(e) => setButtonMinimalIsChecked(e.target.checked)}
          label="button-minimal"
          displayType="button-minimal"
        />
      </div>
    );
  },
};

export const State: StoryObj<CheckboxProps> = {
  render: () => {
    return (
      <>
        <div className="u-sb-checkbox-container">
          <Checkbox isChecked label="Checked" />
          <Checkbox isChecked={false} label="Unchecked" />
          <Checkbox isChecked isDisabled label="Disabled" />
          <Checkbox isDisabled label="Disabled" />
          <Checkbox isChecked isReadOnly label="Readonly" />
          <Checkbox isReadOnly label="Readonly" />
          <Checkbox isChecked hasError label="Error" />
          <Checkbox hasError label="Error" />
          <Checkbox isRequired label="Required" />
        </div>
      </>
    );
  },
};
