{"name": "@hudl/uniform-web", "version": "7.1.0", "private": false, "description": "Uniform Web Component Library", "homepage": "https://uniform.hudl.com", "repository": {"url": "git+https://github.com/hudl/hudl-frontends.git", "types": "git"}, "license": "MIT", "contributors": [{"name": "Mandalorians", "url": "https://hudl.slack.com/archives/C0KBB5FKN", "channel": "#uniform", "qaChannel": "#squad-mandalorians"}], "sideEffects": ["*.css"], "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./style.css": "./dist/style.css", "./server": {"import": "./dist/server.mjs", "require": "./dist/server.js", "types": "./dist/server.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "*.md"], "scripts": {"betterer": "betterer --cache", "betterer:ci": "betterer ci", "betterer:merge": "betterer merge", "betterer:precommit": "betterer precommit", "betterer:update": "betterer --update", "build": "vite build --logLevel error", "build-storybook": "storybook build --stats-json", "chromatic": "chromatic --exit-once-uploaded --storybook-build-dir storybook-static --storybook-base-dir uniform/uniform-web --project-token $UNIFORM_V5_CHROMATIC_PROJECT_TOKEN", "chromatic:skip": "chromatic --skip --project-token $UNIFORM_V5_CHROMATIC_PROJECT_TOKEN", "clean": "rimraf dist node_modules/.cache storybook-static", "dev": "vite build --watch", "lint": "eslint --ext .js,.ts,.jsx,.tsx src/ && pnpm lint:styles", "lint:fix": "eslint --ext .js,.ts,.jsx,.tsx src/ --fix && pnpm lint:styles:fix", "lint:styles": "stylelint './src/**/*.css' --rdd --risd --rd --config './stylelint.config.js'", "lint:styles:fix": "stylelint './src/**/*.css' --rdd --risd --rd --fix --config './stylelint.config.js'", "nuke": "pnpm run clean && rimraf node_modules", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "release": "release-package", "storybook": "storybook dev -p 9008", "test": "vitest", "test-cov": "vitest --no-cache --coverage", "test:ci": "vitest --no-cache --silent", "test:nowatch": "vitest --watch=false", "test:update": "vitest --no-cache --update", "types": "tsc --project tsconfig-declarations.json --sourceRoot $PWD/src", "types:check": "tsc --noEmit --sourceRoot $PWD/src"}, "dependencies": {"@hudl/uniform-shared": "workspace:*", "@hudl/uniform-tokens": "workspace:*", "@hudl/uniform-web-actions": "workspace:*", "@hudl/uniform-web-avatar": "workspace:*", "@hudl/uniform-web-button": "workspace:*", "@hudl/uniform-web-card": "workspace:*", "@hudl/uniform-web-dialogs": "workspace:*", "@hudl/uniform-web-forms": "workspace:*", "@hudl/uniform-web-grid": "workspace:*", "@hudl/uniform-web-icons": "workspace:*", "@hudl/uniform-web-notifications": "workspace:*", "@hudl/uniform-web-portal": "workspace:*", "@hudl/uniform-web-spinner": "workspace:*", "@hudl/uniform-web-theme": "workspace:*", "@hudl/uniform-web-tooltip": "workspace:*", "@hudl/uniform-web-type": "workspace:*", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-tooltip": "^1.0.3", "@types/react-modal": "^3.13.1", "classnames": "2.3.1", "immutable": "4.2.2", "react-aria-components": "1.8.0", "react-modal": "3.16.1", "resize-observer-polyfill": "1.5.1"}, "devDependencies": {"@betterer/betterer": "5.4.0", "@betterer/cli": "5.4.0", "@betterer/eslint": "5.4.0", "@hudl/eslint-config": "workspace:*", "@hudl/stylelint-config": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "@vitejs/plugin-react": "4.3.1", "config": "workspace:*", "eslint": "8.45.0", "jsdom": "24.0.0", "stylelint": "16.13.0", "vite": "5.3.1"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0"}, "publishConfig": {"registry": "http://npm-push.hudltools.com"}}