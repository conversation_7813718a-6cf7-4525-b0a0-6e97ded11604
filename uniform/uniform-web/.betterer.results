// BETTERER RESULTS V2.
// 
// If this file contains merge conflicts, use `betterer merge` to automatically resolve them:
// https://phenomnomnominal.github.io/betterer/docs/results-file/#merge
//
exports[`Enable eslint rules`] = {
  value: `{
    "src/components/action-bar/stories/ActionBarExamples.stories.tsx:2356385670": [
      [40, 8, 41, "JSX props should not use arrow functions", "709547965"],
      [48, 12, 35, "JSX props should not use arrow functions", "1964165250"],
      [51, 10, 42, "Visible, non-interactive elements with click handlers must have at least one keyboard listener.", "4082225324"],
      [51, 10, 42, "Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element.", "4082225324"],
      [51, 16, 35, "JSX props should not use arrow functions", "1964165250"],
      [55, 12, 35, "JSX props should not use arrow functions", "1965570635"],
      [58, 10, 42, "Visible, non-interactive elements with click handlers must have at least one keyboard listener.", "3824998725"],
      [58, 10, 42, "Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element.", "3824998725"],
      [58, 16, 35, "JSX props should not use arrow functions", "1965570635"],
      [95, 8, 41, "JSX props should not use arrow functions", "709547965"],
      [103, 12, 35, "JSX props should not use arrow functions", "1964165250"],
      [106, 10, 42, "Visible, non-interactive elements with click handlers must have at least one keyboard listener.", "4082225324"],
      [106, 10, 42, "Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element.", "4082225324"],
      [106, 16, 35, "JSX props should not use arrow functions", "1964165250"],
      [110, 12, 35, "JSX props should not use arrow functions", "1965570635"],
      [113, 10, 42, "Visible, non-interactive elements with click handlers must have at least one keyboard listener.", "3824998725"],
      [113, 10, 42, "Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element.", "3824998725"],
      [113, 16, 35, "JSX props should not use arrow functions", "1965570635"],
      [117, 12, 35, "JSX props should not use arrow functions", "1955508819"],
      [120, 10, 42, "Visible, non-interactive elements with click handlers must have at least one keyboard listener.", "3530906205"],
      [120, 10, 42, "Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element.", "3530906205"],
      [120, 16, 35, "JSX props should not use arrow functions", "1955508819"]
    ],
    "src/components/data-visualization/data-table/DataTable.tsx:3837733527": [
      [93, 24, 3, "Unexpected any. Specify a different type.", "193409811"],
      [94, 18, 16, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "3951127206"],
      [94, 18, 14, "Unsafe member access .toString on an \`any\` value.", "1833623463"],
      [94, 18, 14, "Unsafe call of an \`any\` typed value.", "1833623463"],
      [94, 55, 14, "Unsafe member access .toString on an \`any\` value.", "1833623463"],
      [94, 55, 14, "Unsafe call of an \`any\` typed value.", "1833623463"],
      [97, 29, 3, "Unexpected any. Specify a different type.", "193409811"],
      [97, 37, 3, "Unexpected any. Specify a different type.", "193409811"],
      [99, 20, 1, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "177604"],
      [99, 38, 1, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "177607"],
      [102, 8, 37, "Unsafe assignment of an \`any\` value.", "3538133017"],
      [102, 17, 26, "Unsafe member access .toLowerCase on an \`any\` value.", "2195330023"],
      [102, 17, 26, "Unsafe call of an \`any\` typed value.", "2195330023"],
      [102, 17, 11, "Unsafe member access .toString on an \`any\` value.", "1820855803"],
      [102, 17, 11, "Unsafe call of an \`any\` typed value.", "1820855803"],
      [103, 8, 37, "Unsafe assignment of an \`any\` value.", "47124249"],
      [103, 17, 26, "Unsafe member access .toLowerCase on an \`any\` value.", "1134147652"],
      [103, 17, 26, "Unsafe call of an \`any\` typed value.", "1134147652"],
      [103, 17, 11, "Unsafe member access .toString on an \`any\` value.", "1004160952"],
      [103, 17, 11, "Unsafe call of an \`any\` typed value.", "1004160952"],
      [211, 12, 72, "JSX props should not use arrow functions", "11101054"],
      [240, 8, 51, "JSX props should not use arrow functions", "144659057"],
      [268, 12, 54, "JSX props should not use arrow functions", "1727228006"],
      [361, 39, 3, "Unexpected any. Specify a different type.", "193409811"],
      [363, 24, 6, "Unsafe member access .id on an \`any\` value.", "2126506700"],
      [364, 29, 6, "Unsafe assignment of an \`any\` value.", "2126506700"],
      [364, 29, 6, "Unsafe member access .id on an \`any\` value.", "2126506700"],
      [365, 35, 6, "Unsafe member access .id on an \`any\` value.", "2126506700"],
      [367, 78, 6, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "2126506700"],
      [367, 78, 6, "Unsafe member access .id on an \`any\` value.", "2126506700"],
      [373, 17, 8, "Unsafe member access .data on an \`any\` value.", "778242225"],
      [373, 17, 12, "Unsafe call of an \`any\` typed value.", "1161979523"],
      [373, 81, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`{ id: string; data: (string | Element | null)[]; }\`.", "193432495"]
    ],
    "src/components/data-visualization/data-table/stories/DataTable.stories.tsx:2127271663": [
      [61, 10, 49, "JSX props should not use arrow functions", "4045066004"],
      [66, 12, 2, "JSX props should not use arrow functions", "5861220"],
      [79, 12, 2, "JSX props should not use arrow functions", "5861220"]
    ],
    "src/components/data-visualization/data-table/stories/DataTable.storyutils.tsx:1455504571": [
      [408, 24, 55, "JSX props should not use arrow functions", "936924583"],
      [430, 8, 67, "JSX props should not use arrow functions", "3725606508"],
      [456, 8, 69, "JSX props should not use arrow functions", "3646026092"],
      [481, 8, 67, "JSX props should not use arrow functions", "3831709351"],
      [510, 8, 81, "JSX props should not use arrow functions", "3156591724"]
    ],
    "src/components/dialogs/alert/stories/AlertExamples.stories.tsx:3390204106": [
      [21, 16, 34, "JSX props should not use arrow functions", "1553309781"],
      [44, 16, 34, "JSX props should not use arrow functions", "1553309781"]
    ],
    "src/components/dialogs/modal/stories/Modal.stories.tsx:2733768233": [
      [38, 54, 45, "JSX props should not use arrow functions", "4219680147"]
    ],
    "src/components/dialogs/modal/stories/ModalExamples.stories.tsx:657655266": [
      [49, 18, 41, "JSX props should not use arrow functions", "3000415899"],
      [50, 18, 39, "JSX props should not use arrow functions", "871014989"],
      [56, 10, 42, "JSX props should not use arrow functions", "45891585"],
      [62, 73, 40, "JSX props should not use arrow functions", "2117623959"],
      [101, 16, 44, "JSX props should not use arrow functions", "1113730057"],
      [127, 10, 21, "JSX props should not use functions", "1349553205"],
      [128, 10, 2, "JSX props should not use arrow functions", "5861220"]
    ],
    "src/components/dialogs/overlay/stories/Overlay.stories.tsx:2743079512": [
      [22, 27, 45, "JSX props should not use arrow functions", "4219680147"]
    ],
    "src/components/dialogs/overlay/stories/OverlayExamples.stories.tsx:2385002871": [
      [21, 16, 36, "JSX props should not use arrow functions", "3355605409"],
      [22, 56, 37, "JSX props should not use arrow functions", "2961195547"]
    ],
    "src/components/dialogs/stories/Dialogs.chromatic.stories.tsx:3665922945": [
      [27, 8, 65, "JSX props should not use arrow functions", "2229889961"],
      [52, 8, 65, "JSX props should not use arrow functions", "2229889961"],
      [75, 46, 65, "JSX props should not use arrow functions", "2229889961"]
    ],
    "src/components/dialogs/stories/DialogsExamples.stories.tsx:1668185704": [
      [54, 34, 35, "JSX props should not use arrow functions", "2791224874"],
      [59, 38, 37, "JSX props should not use arrow functions", "2961195547"],
      [65, 18, 34, "JSX props should not use arrow functions", "1553309781"],
      [66, 18, 34, "JSX props should not use arrow functions", "959434032"],
      [67, 18, 36, "JSX props should not use arrow functions", "3355605409"]
    ],
    "src/components/dialogs/stories/DialogsMobile.chromatic.stories.tsx:2336545854": [
      [25, 46, 65, "JSX props should not use arrow functions", "2229889961"]
    ],
    "src/components/forms/components/checkboxes/checkbox-group/stories/CheckboxGroup.stories.tsx:1172117577": [
      [38, 66, 23, "JSX props should not use arrow functions", "2946834037"]
    ],
    "src/components/forms/components/checkboxes/checkbox-group/stories/CheckboxGroupExamples.stories.tsx:3187113899": [
      [50, 10, 55, "JSX props should not use arrow functions", "1927760274"],
      [61, 10, 54, "JSX props should not use arrow functions", "2664453610"],
      [72, 10, 55, "JSX props should not use arrow functions", "1095379560"],
      [83, 10, 54, "JSX props should not use arrow functions", "2634455816"],
      [108, 31, 23, "JSX props should not use arrow functions", "2946834037"],
      [130, 31, 23, "JSX props should not use arrow functions", "2946834037"],
      [163, 10, 49, "JSX props should not use arrow functions", "3854667848"],
      [163, 42, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "193428222"],
      [174, 10, 51, "JSX props should not use arrow functions", "4161605860"],
      [174, 42, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "193428222"],
      [209, 10, 49, "JSX props should not use arrow functions", "3854667848"],
      [209, 42, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "193428222"],
      [220, 10, 51, "JSX props should not use arrow functions", "4161605860"],
      [220, 42, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "193428222"]
    ],
    "src/components/forms/components/checkboxes/checkbox-parent-group/stories/CheckboxParentGroup.stories.tsx:3987406853": [
      [36, 72, 23, "JSX props should not use arrow functions", "2946834037"]
    ],
    "src/components/forms/components/checkboxes/checkbox-parent-group/stories/CheckboxParentGroupExamples.stories.tsx:3188129350": [
      [31, 67, 23, "JSX props should not use arrow functions", "2946834037"],
      [60, 31, 23, "JSX props should not use arrow functions", "2946834037"],
      [93, 10, 49, "JSX props should not use arrow functions", "3854667848"],
      [93, 42, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "193428222"],
      [104, 10, 51, "JSX props should not use arrow functions", "4161605860"],
      [104, 42, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "193428222"],
      [139, 10, 49, "JSX props should not use arrow functions", "3854667848"],
      [139, 42, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "193428222"],
      [150, 10, 51, "JSX props should not use arrow functions", "4161605860"],
      [150, 42, 3, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "193428222"]
    ],
    "src/components/forms/components/checkboxes/checkbox/stories/CheckboxExamples.stories.tsx:1967545550": [
      [18, 43, 48, "JSX props should not use arrow functions", "2364602771"],
      [35, 10, 55, "JSX props should not use arrow functions", "1725241016"],
      [41, 10, 54, "JSX props should not use arrow functions", "4068122117"],
      [47, 10, 61, "JSX props should not use arrow functions", "3878561030"]
    ],
    "src/components/forms/components/checkboxes/stories/Checkboxes.chromatic.stories.tsx:3654235736": [
      [28, 21, 21, "JSX props should not use arrow functions", "4282629760"],
      [35, 21, 21, "JSX props should not use arrow functions", "4282629760"]
    ],
    "src/components/forms/components/form-modifier/stories/FormModifier.chromatic.stories.tsx:2435165245": [
      [33, 58, 21, "JSX props should not use arrow functions", "4282629760"],
      [37, 55, 21, "JSX props should not use arrow functions", "4282629760"]
    ],
    "src/components/forms/components/form-modifier/stories/FormModifier.stories.tsx:3298895617": [
      [67, 38, 3, "Unexpected any. Specify a different type.", "193409811"],
      [68, 32, 5, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "189936718"],
      [71, 35, 16, "Unsafe return of an \`any[]\` typed value.", "61796667"],
      [87, 90, 28, "JSX props should not use arrow functions", "3905788556"]
    ],
    "src/components/forms/components/form-modifier/stories/FormModifierExamples.stories.tsx:563926782": [
      [59, 38, 3, "Unexpected any. Specify a different type.", "193409811"],
      [60, 32, 5, "Unsafe argument of type \`any\` assigned to a parameter of type \`string\`.", "189936718"],
      [63, 35, 16, "Unsafe return of an \`any[]\` typed value.", "61796667"],
      [79, 90, 28, "JSX props should not use arrow functions", "3905788556"]
    ],
    "src/components/forms/components/inputs/search-input/stories/SearchInputExamples.stories.tsx:390841708": [
      [51, 8, 47, "JSX props should not use arrow functions", "2733532403"],
      [52, 8, 37, "JSX props should not use arrow functions", "1570708116"]
    ],
    "src/components/forms/components/radios/radio-group/stories/RadioGroup.stories.tsx:2962346380": [
      [35, 61, 23, "JSX props should not use arrow functions", "2946834037"]
    ],
    "src/components/forms/components/radios/radio-group/stories/RadioGroupExamples.stories.tsx:625486064": [
      [45, 10, 64, "JSX props should not use arrow functions", "2760274509"],
      [56, 10, 63, "JSX props should not use arrow functions", "2813919061"],
      [67, 10, 64, "JSX props should not use arrow functions", "1830745079"],
      [78, 10, 63, "JSX props should not use arrow functions", "3156708023"],
      [101, 28, 23, "JSX props should not use arrow functions", "2946834037"],
      [121, 28, 23, "JSX props should not use arrow functions", "2946834037"],
      [150, 10, 49, "JSX props should not use arrow functions", "3854667848"],
      [161, 10, 51, "JSX props should not use arrow functions", "4161605860"],
      [192, 10, 49, "JSX props should not use arrow functions", "3854667848"],
      [203, 10, 51, "JSX props should not use arrow functions", "4161605860"]
    ],
    "src/components/forms/components/radios/radio/stories/RadioExamples.stories.tsx:1768614971": [
      [18, 40, 48, "JSX props should not use arrow functions", "2364602771"],
      [35, 10, 55, "JSX props should not use arrow functions", "1725241016"],
      [41, 10, 54, "JSX props should not use arrow functions", "4068122117"],
      [47, 10, 61, "JSX props should not use arrow functions", "3878561030"]
    ],
    "src/components/forms/components/radios/stories/Radios.chromatic.stories.tsx:1727704456": [
      [21, 18, 21, "JSX props should not use arrow functions", "4282629760"],
      [25, 18, 21, "JSX props should not use arrow functions", "4282629760"]
    ],
    "src/components/forms/components/selects/creatable-select/stories/CreatableSelect.stories.tsx:2148056383": [
      [31, 31, 3, "Unexpected any. Specify a different type.", "193409811"],
      [31, 36, 3, "Unexpected any. Specify a different type.", "193409811"],
      [31, 41, 3, "Unexpected any. Specify a different type.", "193409811"],
      [33, 49, 3, "Unexpected any. Specify a different type.", "193409811"],
      [33, 54, 3, "Unexpected any. Specify a different type.", "193409811"],
      [33, 59, 3, "Unexpected any. Specify a different type.", "193409811"]
    ],
    "src/components/forms/components/selects/select/stories/Select.stories.tsx:1430546556": [
      [35, 22, 3, "Unexpected any. Specify a different type.", "193409811"],
      [35, 27, 3, "Unexpected any. Specify a different type.", "193409811"],
      [35, 32, 3, "Unexpected any. Specify a different type.", "193409811"],
      [37, 40, 3, "Unexpected any. Specify a different type.", "193409811"],
      [37, 45, 3, "Unexpected any. Specify a different type.", "193409811"],
      [37, 50, 3, "Unexpected any. Specify a different type.", "193409811"]
    ],
    "src/components/icons/stories/Icon.stories.tsx:2196926605": [
      [52, 12, 25, "Unsafe assignment of an \`any\` value.", "519134689"],
      [53, 12, 22, "Unsafe assignment of an \`any\` value.", "3154099990"],
      [101, 21, 53, "JSX props should not use arrow functions", "389511612"]
    ],
    "src/components/icons/stories/Icons.chromatic.stories.tsx:753217480": [
      [36, 8, 30, "Unsafe assignment of an \`any\` value.", "133201156"]
    ],
    "src/components/notifications/toast/stories/ToastMessenger.stories.tsx:2227096735": [
      [22, 50, 65, "JSX props should not use arrow functions", "1832223191"],
      [25, 16, 37, "JSX props should not use arrow functions", "222586651"]
    ],
    "src/components/onboarding/stories/OnboardingExamples.stories.tsx:1588632014": [
      [36, 16, 31, "JSX props should not use arrow functions", "3218444321"],
      [58, 16, 31, "JSX props should not use arrow functions", "3218444321"],
      [80, 16, 31, "JSX props should not use arrow functions", "3218444321"],
      [102, 16, 31, "JSX props should not use arrow functions", "3218444321"],
      [124, 16, 31, "JSX props should not use arrow functions", "3218444321"]
    ],
    "src/components/select-mark/SelectMark.test.tsx:1038075006": [
      [7, 57, 18, "JSX props should not use arrow functions", "3906676021"],
      [12, 49, 18, "JSX props should not use arrow functions", "3906676021"]
    ],
    "src/components/select-mark/SelectMark.tsx:4294915041": [
      [93, 6, 31, "JSX props should not use arrow functions", "2030430645"],
      [94, 6, 23, "JSX props should not use arrow functions", "1291654836"],
      [95, 6, 25, "JSX props should not use arrow functions", "159542549"]
    ],
    "src/components/select-mark/stories/SelectMark.stories.tsx:4090035349": [
      [23, 63, 21, "JSX props should not use arrow functions", "913031477"]
    ],
    "src/components/select-mark/stories/SelectMarkExamples.stories.tsx:1343295542": [
      [23, 63, 21, "JSX props should not use arrow functions", "913031477"],
      [42, 10, 54, "JSX props should not use arrow functions", "1042189527"],
      [49, 10, 56, "JSX props should not use arrow functions", "1462323159"],
      [56, 10, 54, "JSX props should not use arrow functions", "3290503959"]
    ],
    "src/components/tabs/stories/Tabs.stories.tsx:2328573151": [
      [118, 22, 39, "JSX props should not use arrow functions", "4236050870"]
    ],
    "src/stories/utilties/code-helpers/space/SpaceTable.tsx:1237935219": [
      [35, 9, 16, "Unsafe array destructuring of a tuple element with an \`any\` value.", "2130854537"],
      [55, 8, 57, "JSX props should not use arrow functions", "3492653360"]
    ]
  }`
};
