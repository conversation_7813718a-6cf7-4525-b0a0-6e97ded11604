{"version": 1.1, "pre-load-external-sets": ["seating-charts"], "sets": {"fan": {"keys": ["fan.schedule.date-range.reset-button-text.today", "fan.schedule.date-range.reset-button-text.this-week", "fan.schedule.date-range.reset-button-text.this-month", "fan.overview.schedule.flag.watch", "fan.overview.schedule.flag.live", "fan.overview.schedule.flag.preview", "fan.overview.live-now", "fan.overview.live-and-upcoming", "fan.overview.all-live", "fan.overview.highlights.header", "fan.overview.highlights.viewAll", "fan.overview.pastLivestreams.header", "fan.overview.pastLivestreams.viewAll", "fan.schedule.schedule-entry-content.game-highlights", "fan.schedule.schedule-entry-content.past-game-final", "fan.schedule.schedule-entry-content.broadcasted-by", "fan.schedule.schedule-entry-content.versus", "fan.schedule.schedule-entry-content.at", "fan.teams.team-data-card.team-schedule-link", "fan.teams.team-data-card.team-roster-link", "fan.teams.team-data-card.team-highlights-link", "fan.teams.team-data-card.team-video-link", "fan.teams.team-data-card.team-tickets-link", "fan.video.thumbnail.live", "fan.video.thumbnail.login", "fan.video.thumbnail.minutes", "fan.video.thumbnail.month-day.dateFormat", "fan.video.thumbnail.month-day.time", "fan.video.thumbnail.paidAccess", "fan.video.thumbnail.seconds", "fan.video.thumbnail.time.dateFormat", "fan.video.thumbnail.weekday.dateFormat", "fan.video.thumbnail.weekday.time", "fan.video.thumbnail.year.dateFormat", "fan.video.thumbnail.yesterday.time", "fan.video.video-card.now-playing", "fan.video.video-card.views", "fan.video.video-landing.recent", "fan.video.video-landing.highlights", "fan.video.video-landing.livestreams", "fan.video.video-landing.pastLivestreams", "fan.video.video-landing.viewAll", "fan.video.video-landing.allEvents", "fan.video.video-landing.allLivestreams", "fan.video.video-landing.allPastLivestreams", "fan.video.video-landing.allHighlights", "fan.video.video-landing.live", "fan.video.video-landing.live-and-upcoming", "fan.video.video-landing.all-live-and-upcoming", "fan.event-viewer.watch.full-game", "fan.event-viewer.watch.game-highlights", "fan.event-viewer.watch.recommended-highlights", "fan.event-viewer.watch.empty-highlights", "fan.event-viewer.watch.featured-games", "fan.event-viewer.watch.top-videos", "fan.event-viewer.watch.views", "fan.event-viewer.watch.published", "fan.event-viewer.watch.video-not-found", "fan.event-viewer.watch.on-demand", "fan.account.personal-info.personalInformation", "fan.account.personal-info.privacyInformation", "fan.account.personal-info.firstName", "fan.account.personal-info.lastName", "fan.account.personal-info.email", "fan.account.password-security.title", "fan.account.password-security.password.title", "fan.account.password-security.password.reset-password", "fan.account.password-security.password.body", "fan.account.password-security.password.social-linked", "fan.account.password-security.password.social-reset", "fan.account.password-security.password.success-toast", "fan.account.password-security.password.error-toast", "fan.account.personal-info.deleteAccount.confirmTitle", "fan.account.personal-info.deleteAccount.title", "fan.account.personal-info.deleteAccount.body", "fan.account.personal-info.deleteAccount.buttonText", "fan.account.personal-info.deleteAccount.modalTitle", "fan.account.personal-info.deleteAccount.errorToast", "fan.account.personal-info.deleteAccount.modalText", "fan.highlight.views", "fan.highlight.emptyState", "fan.highlight.recentHighlights", "fan.schedules.emptyState", "fan.schedule.emptyStateDay", "fan.schedule.emptyStateWeek", "fan.schedule.emptyStateMonth", "fan.schedule.content.emptyState", "fan.schedule.content.emptyStateSub", "fan.schedule.content.emptyStateNoVideos", "fan.video.emptyState", "fan.checkForUpdates", "fan.video.team.emptyState", "fan.video.team.checkForUpdates", "fan.account.reset-password-email.header", "fan.account.reset-password-email.message", "fan.account.verify-email.success-header", "fan.account.verify-email.success-message", "fan.account.verify-email.error-header", "fan.account.verify-email.error-message", "fan.account.verify-email.access-expired-header", "fan.account.verify-email.access-expired-message", "fan.account.verify-email.resend-success-header", "fan.account.verify-email.resend-success-message", "fan.account.verify-email.resend-error", "fan.account.account-created.toast-message", "fan.school-page.edit-button.text", "fan.pageHeaderInfo-component.edit-button.text", "fan.pageHeaderInfo-component.share-button.text", "fan.share-component.clipboard-copy.toast-success", "fan.school-page.feedback-modal.first-paragraph", "fan.school-page.feedback-modal.second-paragraph", "fan.teams.team-level-filter.all", "fan.teams.team-level-filter.varsity", "fan.teams.team-level-filter.junior-varsity", "fan.teams.team-level-filter.sophomore", "fan.teams.team-level-filter.freshman", "fan.teams.team-level-filter.other", "fan.team.select-season", "fan.team.home.schedule-tab.past", "fan.team.home.schedule-tab.upcoming", "fan.team.home.schedule-tab.full-schedule", "fan.team.home.roster-tab.full-roster", "fan.team.empty-state.other-seasons", "fan.team.roster.empty-state.empty-roster-season", "fan.basic-text.with", "fan.basic-text.and", "fan.video.video-filter.all", "fan.video.video-filter.live-and-upcoming", "fan.video.video-filter.live-now", "fan.video.video-filter.recent", "fan.video.video-filter.highlights", "fan.video.video-filter.livestreams", "fan.video.video-filter.pastLivestreams", "fan.sport.australian-rules-football", "fan.sport.australian-rules-football-recruiting", "fan.sport.badminton", "fan.sport.badminton-recruiting", "fan.sport.baseball", "fan.sport.baseball-recruiting", "fan.sport.basketball", "fan.sport.basketball-recruiting", "fan.sport.cheer-and-spirit", "fan.sport.cheer-and-spirit-recruiting", "fan.sport.cricket", "fan.sport.cricket-recruiting", "fan.sport.cross-country", "fan.sport.cross-country-recruiting", "fan.sport.cycling", "fan.sport.cycling-recruiting", "fan.sport.dance-and-drill", "fan.sport.dance-and-drill-recruiting", "fan.sport.fencing", "fan.sport.fencing-recruiting", "fan.sport.field-hockey", "fan.sport.field-hockey-recruiting", "fan.sport.football", "fan.sport.football-recruiting", "fan.sport.golf", "fan.sport.golf-recruiting", "fan.sport.gymnastics", "fan.sport.gymnastics-recruiting", "fan.sport.handball", "fan.sport.handball-recruiting", "fan.sport.ice-hockey", "fan.sport.ice-hockey-recruiting", "fan.sport.lacrosse", "fan.sport.lacrosse-recruiting", "fan.sport.netball", "fan.sport.netball-recruiting", "fan.sport.no-sport", "fan.sport.other", "fan.sport.performing-arts", "fan.sport.performing-arts-recruiting", "fan.sport.rugby", "fan.sport.rugby-league", "fan.sport.rugby-league-recruiting", "fan.sport.rugby-recruiting", "fan.sport.rugby-union", "fan.sport.rugby-union-recruiting", "fan.sport.sailing-and-yachting", "fan.sport.sailing-and-yachting-recruiting", "fan.sport.soccer", "fan.sport.soccer-recruiting", "fan.sport.softball", "fan.sport.softball-recruiting", "fan.sport.squash", "fan.sport.squash-recruiting", "fan.sport.surfing", "fan.sport.surfing-recruiting", "fan.sport.swimming-and-diving", "fan.sport.swimming-and-diving-recruiting", "fan.sport.tennis", "fan.sport.tennis-recruiting", "fan.sport.tenpin-bowling", "fan.sport.tenpin-bowling-recruiting", "fan.sport.track", "fan.sport.track-recruiting", "fan.sport.volleyball", "fan.sport.volleyball-recruiting", "fan.sport.water-polo", "fan.sport.water-polo-recruiting", "fan.sport.wrestling", "fan.sport.wrestling-recruiting", "fan.order.free", "fan.order.quantity-indicator", "fan.button.back", "fan.button.continue", "fan.button.transfer", "fan.button.get-share-link", "fan.button.cancel", "fan.button.select-all", "fan.button.deselect-all", "fan.button.okay", "fan.ticketing.discover.title", "fan.ticketing.discover.description", "fan.ticketing.discover.buttonText", "fan.ticketing.order.details.team-count-in-pass-label", "fan.ticketing.order.details.schools-included-in-pass-label", "fan.ticketing.order.details.date-context-tickets", "fan.ticketing.order.details.date-context-passes", "fan.ticketing.order.details.date-context-valid-for-passes", "fan.ticketing.order.item-selection.select-items-tickets", "fan.ticketing.order.item-selection.select-items-passes", "fan.ticketing.order.item-selection.additional-fees-applied", "fan.ticketing.order.item-selection.fees-included", "fan.ticketing.order.item-selection.additional-fees-applied-non-refundable", "fan.ticketing.order.item-selection.fees-included-non-refundable", "fan.ticketing.order.item-selection.non-refundable", "fan.ticketing.order.selected-items-for-review", "fan.ticketing.order.selected-seats-for-review", "fan.ticketing.order.item-selection.max-items-per-order-tickets", "fan.ticketing.order.item-selection.max-items-per-order-passes", "fan.ticketing.order.item-selection.max-items-per-order", "fan.ticketing.order.item-selection.max-items-per-item-type-tickets", "fan.ticketing.order.item-selection.max-items-per-item-type-passes", "fan.ticketing.order.item-selection.sold-out", "fan.ticketing.order.item-selection.passes-left", "fan.ticketing.order.item-selection.tickets-left", "fan.ticketing.order.seat-selection.select-seats", "fan.ticketing.order.seat-selection.select-renewal-seats", "fan.ticketing.order.seat-selection.select-seats-tip", "fan.ticketing.order.seat-selection.select-renewal-seats-tip", "fan.ticketing.order.seat-summary.section", "fan.ticketing.order.seat-summary.row", "fan.ticketing.order.seat-summary.seat", "fan.ticketing.order.seat-summary.generalAdmissionArea", "fan.ticketing.order.seat-summary.table", "fan.ticketing.order.review.review-order", "fan.ticketing.order.review.button.review-order", "fan.ticketing.order.review.reserve", "fan.ticketing.order.review.continue", "fan.ticketing.order.review.total-includes-fees", "fan.ticketing.order.review.total-includes-fees-non-refundable", "fan.ticketing.order.review.order-processing-fees", "fan.ticketing.order.review.error-loading-review", "fan.ticketing.order.review.error-loading-review-hudl-support-button", "fan.ticketing.order.order-sum-tickets", "fan.ticketing.order.order-sum-passes", "fan.ticketing.order.order-sum-tickets-plural", "fan.ticketing.order.order-sum-passes-plural", "fan.ticketing.order.error-finding-ticketed-event", "fan.ticketing.order.error-finding-pass", "fan.ticketing.order.confirmation.view-tickets-button", "fan.ticketing.order.confirmation.view-passes-button", "fan.ticketing.order.generic-error", "fan.ticketing.order.back-to-tickets", "fan.ticketing.order.reload-page", "fan.ticketing.order.checkout.checkout-header", "fan.ticketing.order.checkout.first-name", "fan.ticketing.order.checkout.first-name-placeholder", "fan.ticketing.order.checkout.last-name", "fan.ticketing.order.checkout.last-name-placeholder", "fan.ticketing.order.checkout.email", "fan.ticketing.order.checkout.payment-information", "fan.ticketing.order.checkout.contact-information", "fan.ticketing.order.checkout.email-placeholder", "fan.ticketing.order.checkout.confirm-email", "fan.ticketing.order.checkout.confirm-email-placeholder", "fan.ticketing.order.checkout.button.place-order", "fan.ticketing.order.checkout.email-error-help-text", "fan.ticketing.order.checkout.confirm-email-error-help-text", "fan.ticketing.order.checkout.first-name-error-help-text", "fan.ticketing.order.checkout.last-name-error-help-text", "fan.ticketing.order.checkout.toast-error-retry", "fan.ticketing.order.checkout.toast-error-message", "fan.ticketing.order.checkout.read-terms-of-service", "fan.ticketing.order.checkout.i-agree", "fan.ticketing.order.checkout.read-refund-exchange-policy", "fan.ticketing.order.checkout.place-order-again-error-message", "fan.ticketing.order.checkout.error-checking-out-contact-support", "fan.ticketing.order.checkout.error-making-payment-contact-support", "fan.ticketing.order.confirmation.details-order-headline", "fan.ticketing.order.confirmation.details-purchase-headline", "fan.ticketing.order.confirmation.confirmation-description-with-support-tickets", "fan.ticketing.order.confirmation.confirmation-description-with-support-passes", "fan.ticketing.order.confirmation.confirmation-description-with-support-pass-renewal", "fan.ticketing.order.confirmation.details-tickets", "fan.ticketing.order.confirmation.details-passes", "fan.ticketing.order.confirmation.customer-information", "fan.ticketing.order.confirmation.payment-information", "fan.ticketing.order.payment-info-text", "fan.ticketing.order.confirmation.non-refundable", "fan.ticketing.order.confirmation.order-summary", "fan.ticketing.order.confirmation.feedback", "fan.ticketing.order.confirmation.give-feedback", "fan.ticketing.order.confirmation.feedback-detail", "fan.ticketing.order.confirmation.organization-info-primary", "fan.ticketing.order.confirmation.organization-info-primary-renewal", "fan.ticketing.order.confirmation.organization-info-secondary", "fan.ticketing.order.confirmation.use-mobile", "fan.ticketing.order.confirmation.use-mobile-help-text-tickets", "fan.ticketing.order.confirmation.use-mobile-help-text-passes", "fan.ticketing.order.confirmation.print-item-tickets", "fan.ticketing.order.confirmation.print-item-passes", "fan.ticketing.order.confirmation.print-item-help-text-tickets", "fan.ticketing.order.confirmation.print-item-help-text-passes", "fan.ticketing.order.confirmation.scan-entry-tickets", "fan.ticketing.order.confirmation.scan-entry-passes", "fan.ticketing.order.confirmation.scan-entry-help-tickets", "fan.ticketing.order.confirmation.scan-entry-help-passes", "fan.ticketing.order.registration.login-to-order", "fan.ticketing.order.registration.join-hudl-perks", "fan.ticketing.order.registration.login", "fan.ticketing.order.registration.create-account", "fan.ticketing.order.registration.continue-as-guest", "fan.ticketing.order.error-pass-config-expired", "fan.ticketing.order.does-not-exist", "fan.ticketing.order.back-to-hudl-fan", "fan.ticketing.tab.buy-tickets", "fan.ticketing.tab.get-tickets", "fan.ticketing.tab.buy-passes", "fan.ticketing.tab.get-passes", "fan.ticketing.tab.pass-details", "fan.ticketing.tab.event-info", "fan.ticketing.tab.your-pass", "fan.ticketing.tab.your-passes", "fan.ticketing.my.tickets-not-yet-available", "fan.ticketing.my.passes-not-yet-available", "fan.ticketing.my.header", "fan.ticketing.my.tickets-tab-title", "fan.ticketing.my.passes-tab-title", "fan.ticketing.my.details-header-tickets", "fan.ticketing.my.details-header-passes", "fan.ticketing.my.ticketing-orders.header", "fan.ticketing.my.ticketing-orders.num-tickets", "fan.ticketing.my.ticketing-orders.num-passes", "fan.ticketing.my.ticketing-orders.pass-valid-for", "fan.ticketing.my.ticketing-orders.valid-for", "fan.ticketing.my.ticketing-orders.description", "fan.ticketing.my.ticketing-orders.no-passes-purchased", "fan.ticketing.my.ticketing-orders.no-passes-purchased-help", "fan.ticketing.my.ticketing-orders.no-tickets-purchased", "fan.ticketing.my.ticketing-orders.no-tickets-purchased-help", "fan.ticketing.my.ticketing-orders.error-loading-tickets", "fan.ticketing.my.ticketing-orders.error-loading-passes", "fan.ticketing.tab.enjoy-event-banner.tickets-remaining", "fan.ticketing.tab.enjoy-event-banner.tickets-in-use", "fan.ticketing.tab.enjoy-event-banner.passes-remaining", "fan.ticketing.tab.enjoy-event-banner.passes-in-use", "fan.ticketing.tab.activated-on-date", "fan.ticketing.tab.used-on-date", "fan.ticketing.tab.shared-on-date", "fan.ticketing.tab.no-ticketed-events-published", "fan.ticketing.tab.no-ticketed-events-published-help-text", "fan.ticketing.tab.no-ticketed-events-published-help-text.league", "fan.ticketing.tab.no-ticketed-events-enabled", "fan.ticketing.tab.no-ticketed-events-enabled-help-text", "fan.ticketing.tab.no-ticketing-entities-published.team", "fan.ticketing.tab.no-ticketed-events-button.team", "fan.ticketing.tab.upcoming-events", "fan.ticketing.tab.passes", "fan.ticketing.tab.tickets", "fan.ticketing.tab.single-event", "fan.ticketing.tab.load-more-events", "fan.ticketing.tab.error-loading-events", "fan.ticketing.tab.view-all-passes", "fan.ticketing.ticket.ticket-footer-text", "fan.ticketing.ticket.ticket-footer-text-new", "fan.ticketing.ticket.ticket-number", "fan.ticketing.ticket.ticket-section", "fan.ticketing.ticket.ticket-row", "fan.ticketing.ticket.ticket-seat", "fan.ticketing.ticket.ticket-zone", "fan.ticketing.ticket.ticket-table", "fan.ticketing.pass.pass-number", "fan.ticketing.ticket.qr-code-alt-text", "fan.ticketing.pass.qr-code-alt-text", "fan.ticketing.pass.renewal.go-to-tickets", "fan.ticketing.pass.renewal.renewal-period-ended", "fan.ticketing.reload", "fan.ticketing.reserved-seats-timer-display", "fan.ticketing.reserved-seats-timeout-text", "fan.ticketing.reserved-seats-your-seats", "fan.ticketing.reserved-seats-no-seats-selected", "fan.ticketing.start-search", "fan.ticketing.how-to-use.headline", "fan.ticketing.how-to-use.headline-tickets", "fan.ticketing.how-to-use.headline-passes", "fan.ticketing.how-to-use.scanned-tickets", "fan.ticketing.how-to-use.scanned-passes", "fan.ticketing.how-to-use.show-staff", "fan.ticketing.how-to-use.show-staff-tickets", "fan.ticketing.how-to-use.show-staff-passes", "fan.ticketing.how-to-use.at-event", "fan.ticketing.how-to-use.contact-support", "fan.ticketing.how-to-use.contact-support-passes", "fan.ticketing.how-to-use.contact-support-tickets", "fan.ticketing.disabled-qr-code.help-text-bold-tickets", "fan.ticketing.disabled-qr-code.help-text-bold-passes", "fan.ticketing.disabled-qr-code.help-text-tickets", "fan.ticketing.disabled-qr-code.help-text-passes", "fan.ticketing.disabled-qr-code.qr-code-text", "fan.ticketing.transfer.problem-transferring-passes", "fan.ticketing.transfer.problem-transferring-tickets", "fan.ticketing.transfer.once-transferred-passes", "fan.ticketing.transfer.once-transferred-tickets", "fan.ticketing.transfer.add-recipient-information-passes", "fan.ticketing.transfer.add-recipient-information-tickets", "fan.ticketing.transfer.select-to-transfer-passes", "fan.ticketing.transfer.select-to-transfer-tickets", "fan.ticketing.transfer.successfully-transferred-passes", "fan.ticketing.transfer.successfully-transferred-tickets", "fan.ticketing.transfer.number-selected-passes", "fan.ticketing.transfer.number-selected-tickets", "fan.ticketing.event-today.help-text-bold-tickets", "fan.ticketing.event-today.help-text-tickets", "fan.ticketing.event-today.help-text-bold-passes", "fan.ticketing.event-today.help-banner-text-tickets", "fan.ticketing.event-today.help-banner-text-bold-tickets", "fan.ticketing.event-today.help-banner-text-bold-passes", "fan.ticketing.event-today.help-banner-text-passes", "fan.ticketing.redemption.already-used-tickets", "fan.ticketing.redemption.already-used-passes", "fan.ticketing.redemption.number-selected-tickets", "fan.ticketing.redemption.number-selected-passes", "fan.ticketing.redemption.select-for-entry-tickets", "fan.ticketing.redemption.select-for-entry-passes", "fan.ticketing.redemption.follow-staff-instructions", "fan.ticketing.redemption.use-at-event-tickets", "fan.ticketing.redemption.use-at-event-passes", "fan.ticketing.redemption.enjoy-the-event", "fan.ticketing.redemption.mobile-header-tickets", "fan.ticketing.redemption.mobile-header-passes", "fan.ticketing.redemption.use-for-entry-tickets", "fan.ticketing.redemption.use-for-entry-passes", "fan.ticketing.redemption.go-to-my-tickets", "fan.ticketing.redemption.go-to-my-passes", "fan.ticketing.redemption.ready-to-use-tickets", "fan.ticketing.redemption.ready-to-use-passes", "fan.ticketing.redemption.used-for-entry-tickets", "fan.ticketing.redemption.used-for-entry-passes", "fan.ticketing.redemption.used-for-entry-with-name-tickets", "fan.ticketing.redemption.used-for-entry-with-name-passes", "fan.ticketing.redemption.problem-using-tickets", "fan.ticketing.redemption.problem-using-passes", "fan.ticketing.redemption.show-device-to-staff-bold-tickets", "fan.ticketing.redemption.show-device-to-staff-tickets", "fan.ticketing.share.mobile-header-tickets", "fan.ticketing.share.number-selected-tickets", "fan.ticketing.share.select-for-share-tickets", "fan.ticketing.share.copy-the-link-tickets", "fan.ticketing.share.copy-link", "fan.ticketing.share.link-copied", "fan.ticketing.share.problem-sharing-tickets", "fan.ticketing.share.done", "fan.ticketing.share.share", "fan.ticketing.events-list.no-events", "fan.ticketing.events-list.no-events-today.active-pass", "fan.ticketing.events-list.today", "fan.ticketing.rebook-seats.error-message", "fan.ticketing.rebook-seats.error-message-select-seats", "fan.ticketing.event.does-not-exist", "fan.ticketing.event.event-does-not-exist", "fan.ticketing.event.is-past", "fan.ticketing.event.does-not-exist.go-to-tickets", "fan.ticketing.pass.in-renewal-no-access-error", "fan.ticketing.form-fields.additional-information", "fan.ticketing.form-fields.additional-information-needed", "fan.ticketing.form-fields.field-required-error", "fan.ticketing.form-fields.character-limit-error", "fan.ticketing.transparent-fees.total-tickets", "fan.ticketing.transparent-fees.total-passes", "fan.ticketing.transparent-fees.transaction-fees", "fan.ticketing.transparent-fees.service-fee-tickets", "fan.ticketing.transparent-fees.service-fee-passes", "fan.ticketing.transparent-fees.transaction-fee", "fan.ticketing.transparent-fees.transaction-fee-help-text", "fan.ticketing.transparent-fees.service-fee-help-text-tickets", "fan.ticketing.transparent-fees.service-fee-help-text-passes", "fan.ticketing.transparent-fees.service-fee-help-text", "fan.ticketing.transparent-fees.error-message", "fan.ticketing.transparent-fees.error-checkout", "fan.ticketing.league-pass-display.school", "fan.ticketing.league-pass-display.schools", "fan.ticketing.league-pass-display.club", "fan.ticketing.league-pass-display.clubs", "fan.ticketing.league-pass-display.member", "fan.ticketing.league-pass-display.members", "fan.ticketing.league-pass-event-filter.schools", "fan.ticketing.league-pass-event-filter.all-schools", "fan.ticketing.league-pass-event-filter.x-schools", "fan.ticketing.league-pass-event-filter.gender", "fan.ticketing.league-pass-event-filter.level", "fan.ticketing.league-pass-event-filter.sport", "fan.ticketing.league-pass-event-filter.filter-slash-schools", "fan.ticketing.league-pass-event-filter.search-for-school", "fan.ticketing.league-pass-schools-list.view-schools-and-teams", "fan.ticketing.league-pass-schools-list.included-in-pass", "fan.ticketing.league-pass-event-filter.all-league-type", "fan.ticketing.league-pass-event-filter.x-league-type", "fan.ticketing.league-pass-event-filter.filter-slash-league-type", "fan.ticketing.league-pass-event-filter.search-for-league-type", "fan.ticketing.filter-container.clear-all", "fan.ticketing.filter-container.select-all", "fan.ticketing.filter-container.apply-filter", "fan.ticketing.filter-container.filter", "fan.ticketing.filter-container.filters", "fan.ticketing.modal-or-mobile.close", "fan.ticketing.teams-list.view-teams", "fan.ticketing.league-pass.select-school.header", "fan.ticketing.league-pass.select-school.description", "fan.ticketing.league-pass.select-school.placeholder", "fan.ticketing.league-pass.select-school.no-results", "fan.ticketing.league-pass.select-school.continue-purchase-pass", "fan.ticketing.league-passes.go-to-entity", "fan.ticketing.league-pass-schools-list.view-display-name-and-teams", "fan.search-bar.empty-state", "fan.search-bar.error-state", "fan.search-bar.reload", "fan.search-bar.see-all-results-v1", "fan.search-bar.placeholder", "fan.search-bar.placeholder-text", "fan.search-bar.search-hudl-fan", "fan.search-bar.search-for-v1", "fan.search-bar.search-for-mobile-v1", "fan.search-bar.athletes-v1", "fan.search-bar.schools-v1", "fan.search-bar.clubs-v1", "fan.search-bar.networks-v1", "fan.nav-bar.watch-now", "fan.nav-bar.get-help", "fan.nav-bar.tutorials", "fan.nav-bar.livestream-purchases", "fan.nav-bar.tickets-and-passes", "fan.nav-bar.account-settings", "fan.nav-bar.log-out", "fan.ssr.organization.metadata.home.tab-title", "fan.ssr.organization.metadata.schedule.tab-title", "fan.ssr.organization.metadata.video.tab-title", "fan.ssr.organization.metadata.tickets.tab-title", "fan.ssr.organization.metadata.teams.tab-title", "fan.ssr.organization.metadata.event-viewer.tab-title", "fan.ssr.organization.metadata.home.description", "fan.ssr.organization.metadata.schedule.description", "fan.ssr.organization.metadata.video.description", "fan.ssr.organization.metadata.teams.description", "fan.ssr.organization.metadata.tickets.description", "fan.ssr.organization.metadata.event-viewer.description", "fan.ssr.organization.metadata.location-description", "fan.ssr.team.metadata.home.tab-title", "fan.ssr.team.metadata.schedule.tab-title", "fan.ssr.team.metadata.roster.tab-title", "fan.ssr.team.metadata.video.tab-title", "fan.ssr.team.metadata.event-viewer.tab-title", "fan.ssr.team.metadata.home.description", "fan.ssr.team.metadata.schedule.description", "fan.ssr.team.metadata.roster.description", "fan.ssr.team.metadata.video.description", "fan.ssr.team.metadata.event-viewer.description", "fan.ssr.team.metadata.location-description", "fan.ssr.league.metadata.affiliates.tab-title", "fan.ssr.league.metadata.home.description.v1", "fan.ssr.league.metadata.affiliates.description.v1", "fan.ssr.league.metadata.location-description", "fan.simple-webnav.watch-now", "fan.simple-webnav.create-account", "fan.simple-webnav.login", "fan.simple-webnav.support", "fan.homepage.spotlight.text", "fan.homepage.spotlight.subtext", "fan.homepage.spotlight.description", "fan.homepage.spotlight.heading.partial-first", "fan.homepage.spotlight.heading.partial-stacked-top", "fan.homepage.spotlight.heading.partial-stacked-bottom", "fan.homepage.spotlight.heading.partial-last", "fan.homepage.ad.presented-by", "fan.footer.solutions.heading", "fan.footer.solutions.high-school", "fan.footer.solutions.club", "fan.footer.solutions.college", "fan.footer.solutions.ncaa-d1", "fan.footer.solutions.youth-football", "fan.footer.solutions.professional", "fan.footer.solutions.licensing-suite", "fan.footer.products.heading", "fan.footer.products.hudl", "fan.footer.products.sportscode", "fan.footer.products.insight", "fan.footer.products.studio", "fan.footer.products.coda", "fan.footer.products.wyscout", "fan.footer.products.gamepass", "fan.footer.products.assist", "fan.footer.products.focus", "fan.footer.products.focus-indoor", "fan.footer.products.focus-outdoor", "fan.footer.products.focus-flex", "fan.footer.products.sideline", "fan.footer.products.replay", "fan.footer.products.volleymetrics", "fan.footer.products.hudltv", "fan.footer.products.wimu", "fan.footer.products.instat", "fan.footer.products.release-notes", "fan.footer.athletes-and-fans.heading", "fan.footer.athletes-and-fans.highlights", "fan.footer.about.heading", "fan.footer.about.advertise", "fan.footer.about.press", "fan.footer.about.company-news", "fan.footer.about.careers", "fan.footer.contact.heading", "fan.footer.contact.support", "fan.footer.contact.blog", "fan.footer.contact.pay", "fan.footer.contact.hudl-accessories", "fan.footer.login", "fan.footer.request-demo", "fan.footer.privacy-policy", "fan.footer.terms", "fan.footer.license-agreement", "fan.footer.privacy-rights", "fan.footer.cookies", "fan.footer.security", "fan.footer.copyright", "fan.graphql-error-state", "fan.graphql-error-state.reload", "fan.highlights.disabled", "fan.homepage.fansearch-error.heading", "fan.homepage.fansearch-error.text-top", "fan.homepage.fansearch-error.text-bottom", "fan.homepage.fansearch-error.hudl-support-text", "fan.league.live-schools", "fan.league.all-live-schools", "fan.league.live-and-upcoming", "fan.league.all-live-and-upcoming", "fan.league.past-livestreams", "fan.league.all-past-livestreams", "fan.org-card.live", "fan.dialog.share.social-share", "fan.dialog.share.social-share.facebook", "fan.dialog.share.social-share.twitter", "fan.dialog.share.social-share.post", "fan.dialog.share.copy-url", "fan.dialog.share.copy", "fan.dialog.share.twitter-text", "fan.dialog.share.share-via", "fan.dialog.share.copy-link", "fan.page-not-found.heading", "fan.page-not-found.sub-text", "fan.page-not-found.contact-support", "fan.page-not-found.go-home", "fan.org-profile.error-state-heading", "fan.org-profile.error-state-text", "fan.graphql-error-state.go-to-home", "fan.hudl-registration-purchase-terms", "fan.hudl-support-text", "fan.okay", "fan.dismiss", "fan.of", "fan.contact-support-text", "fan.hudl-tickets-purchase-terms", "fan.privacy-policy", "fan.refund-exchange-policy", "fan.contact-support-text-upper", "fan.roster.weight-abbreviation", "fan.roster.name", "fan.roster.position", "fan.roster.jersey-number", "fan.roster.height", "fan.roster.weight", "fan.roster.graduation-year", "fan.roster.view-all", "fan.roster.view-less", "fan.roster.empty-state-text", "fan.roster.empty-state-sub-text", "fan.schedule-entry-location.home", "fan.schedule-entry-location.away", "fan.schedule-entry-outcome.no-score-reported", "fan.studios-content.top-videos", "fan.studios-content.featured-games", "fan.shared.carousel.slide-number", "fan.date.today", "fan.date.at", "fan.back", "fan.enter", "fan.refresh", "fan.graphql-no-refetch-state", "fan.organization-card.schedule-button-text", "fan.organization-card.teams-button-text", "fan.organization-card.video-button-text", "fan.organization-card.tickets-button-text", "fan.smart-app-banner.title.main", "fan.smart-app-banner.title.secondary", "fan.smart-app-banner.description", "fan.seating-type.general-admission", "fan.seating-type.reserved-seating", "fan.ssr.league.metadata.schedule.tab-title", "fan.ssr.league.metadata.schedule.description.v1", "fan.league.schedule.live-now", "fan.league.schedule.final", "fan.league.schedule.vs", "fan.league.schedule.at", "fan.league.schedule.video-button.watch", "fan.league.schedule.video-button.preview", "fan.league.schedule.video-button.recap", "fan.league.schedule.get-tickets", "fan.league.schedule.schedule-card-group-display-name.today", "fan.league.home.latest-results", "fan.league.home.latest-results.all-events", "fan.search-bar.recent-searches", "fan.ssr.league.metadata.members.tab-title", "fan.ssr.league.metadata.members.description.v1", "fan.league.competition-filter.all", "fan.league.recent-highlights", "fan.league.all-highlights", "fan.search-bar.filter-empty-state.title", "fan.search-bar.filter-empty-state.description", "fan.search-bar.filter-empty-state.clear-button-text", "fan.org-programs.athlete-selection.title", "fan.org-programs.athlete-selection.description", "fan.org-programs.footer.back", "fan.org-programs.footer.continue", "fan.org-programs.footer.place-order", "fan.org-programs.registration-card.eligibility", "fan.org-programs.registration-card.birthdate-from", "fan.org-programs.registration-card.birthdate-to", "fan.org-programs.registration-card.boys", "fan.org-programs.registration-card.boys-and-girls", "fan.org-programs.registration-card.girls", "fan.org-programs.registration-card.gender", "fan.org-programs.registration-card.grade", "fan.org-programs.registration-card.free", "fan.org-programs.registration-card.full-price", "fan.org-programs.registration-card.payment-options", "fan.org-programs.registration-card.details", "fan.org-programs.registration-card.status.none", "fan.org-programs.registration-card.status.ineligible", "fan.org-programs.registration-card.status.eligible-athlete", "fan.org-programs.registration-card.status.eligible-athletes", "fan.org-programs.registration-card.register", "fan.org-programs.registration-card.registered", "fan.org-programs.list.registrations", "fan.org-programs.header.read-more-button", "fan.org-programs.header.read-less-button", "fan.org-programs.status-card.unauthenticated.title", "fan.org-programs.status-card.unauthenticated.description", "fan.org-programs.status-card.unauthenticated.log-in", "fan.org-programs.status-card.incomplete-athletes.title", "fan.org-programs.status-card.incomplete-athletes.description", "fan.org-programs.status-card.incomplete-athletes.add-athlete-info", "fan.org-programs.status-card.no-athletes.title", "fan.org-programs.status-card.no-athletes.description", "fan.org-programs.status-card.no-athletes.add-athlete", "fan.org-programs.status-card.unauthenticated.create-account", "fan.org-programs.status-card.unauthenticated.create-account-link", "fan.org-programs.order-summary.title", "fan.org-programs.order-summary.registration-total", "fan.org-programs.order-summary.total", "fan.org-programs.order-summary.transaction-fee", "fan.registration-participant.form-item.first-name", "fan.registration-participant.form-item.first-name-placeholder", "fan.registration-participant.form-item.last-name", "fan.registration-participant.form-item.last-name-placeholder", "fan.registration-participant.form-item.date-of-birth", "fan.registration-participant.form-item.gender", "fan.registration-participant.form-item.gender-placeholder", "fan.registration-participant.form-item.gender-male", "fan.registration-participant.form-item.gender-female", "fan.registration-participant.form-item.guardian-checkbox", "fan.registration-participant.form-item.grade", "fan.registration-participant.form-item.grade-placeholder", "fan.registration-participant.form-item.grad-year", "fan.registration-participant.form-item.grade-1st", "fan.registration-participant.form-item.grade-2nd", "fan.registration-participant.form-item.grade-3rd", "fan.registration-participant.form-item.grade-4th", "fan.registration-participant.form-item.grade-5th", "fan.registration-participant.form-item.grade-6th", "fan.registration-participant.form-item.grade-7th", "fan.registration-participant.form-item.grade-8th", "fan.registration-participant.form-item.grade-9th", "fan.registration-participant.form-item.grade-10th", "fan.registration-participant.form-item.grade-11th", "fan.registration-participant.form-item.grade-12th", "fan.registration-participant.form-item.add-athletes-header", "fan.registration-participant.form-item.add-athletes-description", "fan.registration-participant.form-item.add-another-athlete-button", "fan.registration-participant.form-item.finish-button", "fan.registration-participant.form-item.save-and-add-another-athlete-button", "fan.registration-participant.form-item.save-and-finish-button", "fan.registration-participant.form-item.new-player-placeholder", "fan.registration-participant.form-item.grade-academic-year", "fan.registration-participant.form-item.participant-name", "fan.registration-participant.submission-error-message"]}}, "base-language": {"fan.schedule.date-range.reset-button-text.today": "Today", "fan.schedule.date-range.reset-button-text.this-week": "This Week", "fan.schedule.date-range.reset-button-text.this-month": "This Month", "fan.overview.schedule.flag.watch": "Watch", "fan.overview.schedule.flag.live": "LIVE", "fan.overview.schedule.flag.preview": "Preview", "fan.overview.live-now": "Live Now", "fan.overview.live-and-upcoming": "All Live & Upcoming", "fan.overview.all-live": "All Live", "fan.overview.highlights.header": "Top Highlights", "fan.overview.highlights.viewAll": "All Highlights", "fan.overview.pastLivestreams.header": "Past Livestreams", "fan.overview.pastLivestreams.viewAll": "All Past Livestreams", "fan.schedule.schedule-entry-content.game-highlights": "GAME HIGHLIGHTS", "fan.schedule.schedule-entry-content.past-game-final": "FINAL", "fan.schedule.schedule-entry-content.broadcasted-by": "Broadcasted by ", "fan.schedule.schedule-entry-content.versus": "VS", "fan.schedule.schedule-entry-content.at": "AT", "fan.teams.team-data-card.team-schedule-link": "Schedule", "fan.teams.team-data-card.team-roster-link": "<PERSON><PERSON><PERSON>", "fan.teams.team-data-card.team-highlights-link": "Highlights", "fan.teams.team-data-card.team-video-link": "Video", "fan.teams.team-data-card.team-tickets-link": "Tickets", "fan.video.thumbnail.live": "LIVE", "fan.video.thumbnail.login": "Log In", "fan.video.thumbnail.minutes": "{minuteCount} {minuteCount, plural, one {minute} other {minutes}} ago", "fan.video.thumbnail.month-day.dateFormat": "MMM d", "fan.video.thumbnail.month-day.time": "{month}, {time}", "fan.video.thumbnail.paidAccess": "Paid Access", "fan.video.thumbnail.seconds": "{secondCount} {secondCount, plural, one {second} other {seconds}} ago", "fan.video.thumbnail.time.dateFormat": "h:mmaaa", "fan.video.thumbnail.weekday.dateFormat": "EEEE", "fan.video.thumbnail.weekday.time": "{weekday} at {time}", "fan.video.thumbnail.year.dateFormat": "PP", "fan.video.thumbnail.yesterday.time": "Yesterday at {time}", "fan.video.video-card.now-playing": "Now Playing", "fan.video.video-card.views": "{viewCount, plural, one { View} other { Views}}", "fan.video.video-landing.recent": "Recent", "fan.video.video-landing.highlights": "Highlights", "fan.video.video-landing.livestreams": "Livestreams", "fan.video.video-landing.pastLivestreams": "Past Livestreams", "fan.video.video-landing.viewAll": "View All", "fan.video.video-landing.allEvents": "All Events", "fan.video.video-landing.allLivestreams": "All Livestreams", "fan.video.video-landing.allPastLivestreams": "All Past Livestreams", "fan.video.video-landing.allHighlights": "All Highlights", "fan.video.video-landing.live": "Live Now", "fan.video.video-landing.live-and-upcoming": "Live & Upcoming", "fan.video.video-landing.all-live-and-upcoming": "All Live & Upcoming", "fan.event-viewer.watch.full-game": "FULL GAME", "fan.event-viewer.watch.game-highlights": "GAME HIGHLIGHTS", "fan.event-viewer.watch.recommended-highlights": "RECOMMENDED HIGHLIGHTS", "fan.event-viewer.watch.empty-highlights": "We'll display more highlights here as your team produces content.", "fan.event-viewer.watch.featured-games": "Featured Games", "fan.event-viewer.watch.top-videos": "Top Videos", "fan.event-viewer.watch.views": "{viewCount, plural, one { View} other { Views}}", "fan.event-viewer.watch.published": "Published {date}", "fan.event-viewer.watch.video-not-found": "Could not find video! Check your URL and try again.", "fan.event-viewer.watch.on-demand": "This Livestream will be available on-demand once it's uploaded to Hudl", "fan.account.personal-info.personalInformation": "Personal Information", "fan.account.personal-info.privacyInformation": "We'll use the information below to provide updates about your account and other Hudl products. You can check our Privacy Policy for more information.", "fan.account.personal-info.firstName": "First Name", "fan.account.personal-info.lastName": "Last Name", "fan.account.personal-info.email": "Email", "fan.account.password-security.title": "Password & Security", "fan.account.password-security.password.title": "Password", "fan.account.password-security.password.reset-password": "Reset Password", "fan.account.password-security.password.body": "Reset your Hudl account password.", "fan.account.password-security.password.social-linked": "Linked Account: {email}", "fan.account.password-security.password.social-reset": "To reset your password, visit your {social} account settings.", "fan.account.password-security.password.success-toast": "A reset password link has been sent to your email address.", "fan.account.password-security.password.error-toast": "We’re having some issues sending the reset password email. Please try again later. If the problem continues, contact support.", "fan.account.personal-info.deleteAccount.confirmTitle": "Delete Account", "fan.account.personal-info.deleteAccount.title": "Delete Account", "fan.account.personal-info.deleteAccount.body": "Delete your Hudl account.", "fan.account.personal-info.deleteAccount.buttonText": "Delete Account", "fan.account.personal-info.deleteAccount.modalTitle": "Delete Account?", "fan.account.personal-info.deleteAccount.errorToast": "We're having some issues deleting the account. Please try again later. If the problem continues, contact support.", "fan.account.personal-info.deleteAccount.modalText": "Are you sure you want to permanently delete your Hudl account?\n\nAll of your data will be erased. You will be unsubscribed from any active subscriptions and lose access to all media through purchases made on this account.\n\nAny related accounts linked to your email address will also be deleted. This action cannot be undone.", "fan.highlight.views": "{viewCount, plural, one { View} other { Views}}", "fan.highlight.emptyState": "No Highlights Published Yet", "fan.highlight.recentHighlights": "Recent Highlights", "fan.schedules.emptyState": "No Events Scheduled Yet", "fan.schedule.emptyStateDay": "No Events Scheduled This Day", "fan.schedule.emptyStateWeek": "No Events Scheduled This Week", "fan.schedule.emptyStateMonth": "No Events Scheduled This Month", "fan.schedule.content.emptyState": "No Event Summary Available", "fan.schedule.content.emptyStateSub": "Check back later for updates \nor select another event. ", "fan.schedule.content.emptyStateNoVideos": "No Video Published Yet", "fan.video.emptyState": "No Videos Published Yet", "fan.checkForUpdates": "Check back later for updates.", "fan.video.team.emptyState": "No Video Published This Season", "fan.video.team.checkForUpdates": "Check out this team’s previous seasons.", "fan.account.reset-password-email.header": "Check Your Email", "fan.account.reset-password-email.message": "If you have an account, a reset password link has been sent to your email.", "fan.account.verify-email.success-header": "You’re all set!", "fan.account.verify-email.success-message": "Thanks for verifying your email.\nYou’ll be redirected to Hudl.com in {seconds} seconds...", "fan.account.verify-email.error-header": "Something went wrong", "fan.account.verify-email.error-message": "Contact support if you have any questions.", "fan.account.verify-email.access-expired-header": "Verification Link Expired", "fan.account.verify-email.access-expired-message": "Let us know if you'd like us to send you a new one.", "fan.account.verify-email.resend-success-header": "Check Your Email", "fan.account.verify-email.resend-success-message": "If you have an account, a new verification link has been sent to your email address.", "fan.account.verify-email.resend-error": "We’re having some issues sending the email. Please try again later. If the problem continues, contact support.", "fan.account.account-created.toast-message": "Your account has been created. Use the verification link sent to your email address to verify your account.", "fan.school-page.edit-button.text": "Edit Profile", "fan.pageHeaderInfo-component.share-button.text": "Share", "fan.pageHeaderInfo-component.edit-button.text": "Edit Profile", "fan.share-component.clipboard-copy.toast-success": "{PageType} link copied to clipboard", "fan.school-page.feedback-modal.first-paragraph": "This profile contains everything your fans need to follow along—teams, schedules, scores, highlights and livestreams all in one place.", "fan.school-page.feedback-modal.second-paragraph": "These profiles won't be publicly available until the Fall of 2023, but we need your feedback to improve them for you and your fans. As you continue, a survey will launch for you to fill out alongside your new profile.", "fan.teams.team-level-filter.all": "All", "fan.teams.team-level-filter.varsity": "Varsity", "fan.teams.team-level-filter.junior-varsity": "Junior Varsity", "fan.teams.team-level-filter.sophomore": "Sophomore", "fan.teams.team-level-filter.freshman": "Freshman", "fan.teams.team-level-filter.other": "Other", "fan.team.select-season": "Select a Season", "fan.team.home.schedule-tab.past": "Past", "fan.team.home.schedule-tab.upcoming": "Upcoming", "fan.team.home.schedule-tab.full-schedule": "Full Schedule", "fan.team.home.roster-tab.full-roster": "Full Roster", "fan.team.empty-state.other-seasons": "Check out this team’s other seasons.", "fan.team.roster.empty-state.empty-roster-season": "No Roster Added This Season", "fan.basic-text.with": "with", "fan.basic-text.and": "and", "fan.video.video-filter.all": "All", "fan.video.video-filter.live-and-upcoming": "Live & Upcoming", "fan.video.video-filter.live-now": "Live Now", "fan.video.video-filter.recent": "Recent", "fan.video.video-filter.highlights": "Highlights", "fan.video.video-filter.livestreams": "Livestreams", "fan.video.video-filter.pastLivestreams": "Past Livestreams", "fan.sport.australian-rules-football": "Australian Rules Football", "fan.sport.australian-rules-football-recruiting": "Australian Rules Football Recruiting", "fan.sport.badminton": "Bad<PERSON>ton", "fan.sport.badminton-recruiting": "Bad<PERSON>ton Recruiting", "fan.sport.baseball": "Baseball", "fan.sport.baseball-recruiting": "Baseball Recruiting", "fan.sport.basketball": "Basketball", "fan.sport.basketball-recruiting": "Basketball Recruiting", "fan.sport.cheer-and-spirit": "Cheer And Spirit", "fan.sport.cheer-and-spirit-recruiting": "Cheer And Spirit Recruiting", "fan.sport.cricket": "Cricket", "fan.sport.cricket-recruiting": "Cricket Recruiting", "fan.sport.cross-country": "Cross Country", "fan.sport.cross-country-recruiting": "Cross Country Recruiting", "fan.sport.cycling": "Cycling", "fan.sport.cycling-recruiting": "Cycling Recruiting", "fan.sport.dance-and-drill": "Dance And Drill", "fan.sport.dance-and-drill-recruiting": "Dance And Drill Recruiting", "fan.sport.fencing": "Fencing", "fan.sport.fencing-recruiting": "Fencing Recruiting", "fan.sport.field-hockey": "Field Hockey", "fan.sport.field-hockey-recruiting": "Field Hockey Recruiting", "fan.sport.football": "Football", "fan.sport.football-recruiting": "Football Recruiting", "fan.sport.golf": "Golf", "fan.sport.golf-recruiting": "Golf Recruiting", "fan.sport.gymnastics": "Gymnastics", "fan.sport.gymnastics-recruiting": "Gymnastics Recruiting", "fan.sport.handball": "Handball", "fan.sport.handball-recruiting": "Handball Recruiting", "fan.sport.ice-hockey": "Ice Hockey", "fan.sport.ice-hockey-recruiting": "Ice Hockey Recruiting", "fan.sport.lacrosse": "Lacrosse", "fan.sport.lacrosse-recruiting": "Lacrosse Recruiting", "fan.sport.netball": "Netball", "fan.sport.netball-recruiting": "Netball Recruiting", "fan.sport.no-sport": "No Sport", "fan.sport.other": "Other", "fan.sport.performing-arts": "Performing Arts", "fan.sport.performing-arts-recruiting": "Performing Arts Recruiting", "fan.sport.rugby": "Rugby", "fan.sport.rugby-league": "Rugby League", "fan.sport.rugby-league-recruiting": "Rugby League Recruiting", "fan.sport.rugby-recruiting": "Rugby Recruiting", "fan.sport.rugby-union": "Rugby Union", "fan.sport.rugby-union-recruiting": "Rugby Union Recruiting", "fan.sport.sailing-and-yachting": "Sailing And Yachting", "fan.sport.sailing-and-yachting-recruiting": "Sailing And Yachting Recruiting", "fan.sport.soccer": "Soccer", "fan.sport.soccer-recruiting": "Soccer Recruiting", "fan.sport.softball": "Softball", "fan.sport.softball-recruiting": "Softball Recruiting", "fan.sport.squash": "Squash", "fan.sport.squash-recruiting": "Squash Recruiting", "fan.sport.surfing": "Surfing", "fan.sport.surfing-recruiting": "Surfing Recruiting", "fan.sport.swimming-and-diving": "Swimming And Diving", "fan.sport.swimming-and-diving-recruiting": "Swimming And Diving Recruiting", "fan.sport.tennis": "Tennis", "fan.sport.tennis-recruiting": "Tennis Recruiting", "fan.sport.tenpin-bowling": "Tenpin Bowling", "fan.sport.tenpin-bowling-recruiting": "Tenpin Bowling Recruiting", "fan.sport.track": "Track", "fan.sport.track-recruiting": "Track Recruiting", "fan.sport.volleyball": "Volleyball", "fan.sport.volleyball-recruiting": "Volleyball Recruiting", "fan.sport.water-polo": "Water Polo", "fan.sport.water-polo-recruiting": "Water Polo Recruiting", "fan.sport.wrestling": "Wrestling", "fan.sport.wrestling-recruiting": "Wrestling Recruiting", "fan.order.free": "Free", "fan.order.quantity-indicator": "x", "fan.button.back": "Back", "fan.button.cancel": "Cancel", "fan.button.continue": "Continue", "fan.button.transfer": "Transfer", "fan.button.get-share-link": "Get Shareable Link", "fan.button.select-all": "Select All", "fan.button.deselect-all": "Deselect All", "fan.button.okay": "Okay", "fan.ticketing.discover.title": "Discover More Events at {orgName}", "fan.ticketing.discover.description": "Want to find more events to support {orgName}? Go to their \"Tickets\" page to see what's coming up.", "fan.ticketing.discover.buttonText": "Go to {orgName}'s Profile", "fan.ticketing.order.details.team-count-in-pass-label": "{numberOfTeams, plural, one {# Team Included In Pass} other {# Teams Included In Pass}}", "fan.ticketing.order.details.schools-included-in-pass-label": "Schools Included In Pass:", "fan.ticketing.order.details.date-context-tickets": "{dateString}", "fan.ticketing.order.details.date-context-passes": "Pass Valid For: {dateString}", "fan.ticketing.order.details.date-context-valid-for-passes": "Pass Valid For:", "fan.ticketing.order.item-selection.select-items-tickets": "Select Ticket(s)", "fan.ticketing.order.item-selection.select-items-passes": "Select Pass(es)", "fan.ticketing.order.item-selection.additional-fees-applied": "Additional fees will be applied at checkout.", "fan.ticketing.order.item-selection.fees-included": "All fees are included in this order total.", "fan.ticketing.order.item-selection.additional-fees-applied-non-refundable": "Additional fees will be applied at checkout. All purchases are non-refundable.", "fan.ticketing.order.item-selection.non-refundable": "All purchases are non-refundable.", "fan.ticketing.order.item-selection.fees-included-non-refundable": "All fees are included in this order total. All purchases are non-refundable.", "fan.ticketing.order.selected-items-for-review": "Item{numberOfItems, plural, =1 {} other {s}}", "fan.ticketing.order.selected-seats-for-review": "{numberOfItems} x Reserved Seating", "fan.ticketing.order.item-selection.max-items-per-order-tickets": "{maxNumItems} tickets max per purchase", "fan.ticketing.order.item-selection.max-items-per-order-passes": "{maxNumItems} passes max per purchase", "fan.ticketing.order.item-selection.max-items-per-item-type-tickets": "{maxNumItems} tickets max per ticket type", "fan.ticketing.order.item-selection.max-items-per-item-type-passes": "{maxNumItems} passes max per order", "fan.ticketing.order.item-selection.sold-out": "sold out", "fan.ticketing.order.item-selection.passes-left": "pass{numberOfItems, plural, =1 {} other {es}} left", "fan.ticketing.order.item-selection.tickets-left": "ticket{numberOfItems, plural, =1 {} other {s}} left", "fan.ticketing.order.seat-selection.select-seats": "Select Seat(s)", "fan.ticketing.order.seat-selection.select-renewal-seats": "Select Your Renewal Seat(s)", "fan.ticketing.order.seat-selection.select-seats-tip": "Use seat map below to select your seats. {maxSeats} tickets max per order.", "fan.ticketing.order.seat-selection.select-renewal-seats-tip": "Using the seat map below, select seats you would like to renew from your previous reservation.", "fan.ticketing.order.seat-summary.section": "Section", "fan.ticketing.order.seat-summary.row": "Row", "fan.ticketing.order.seat-summary.seat": "<PERSON><PERSON>", "fan.ticketing.order.seat-summary.generalAdmissionArea": "Zone", "fan.ticketing.order.seat-summary.table": "Table", "fan.ticketing.order.review.review-order": "Review Order", "fan.ticketing.order.review.button.review-order": "Review Order", "fan.ticketing.order.review.reserve": "Reserve", "fan.ticketing.order.review.continue": "Continue", "fan.ticketing.order.review.total-includes-fees": "Total includes all fees.", "fan.ticketing.order.review.total-includes-fees-non-refundable": "Total includes all fees. All purchases are non-refundable.", "fan.ticketing.order.review.order-processing-fees": "Order Processing Fees", "fan.ticketing.order.review.error-loading-review": "We're having trouble loading this content. Try reloading or <a>contact support.</a>", "fan.ticketing.order.review.error-loading-review-hudl-support-button": "We're having trouble loading this content. Try reloading or {hudlSupport}", "fan.ticketing.order.order-sum-tickets": "Order Total: {lineItemConfigQuantitiesSum} ticket", "fan.ticketing.order.order-sum-tickets-plural": "Order Total: {lineItemConfigQuantitiesSum} tickets", "fan.ticketing.order.order-sum-passes": "Order Total: {lineItemConfigQuantitiesSum} pass", "fan.ticketing.order.order-sum-passes-plural": "Order Total: {lineItemConfigQuantitiesSum} passes", "fan.ticketing.order.error-finding-ticketed-event": "We're having trouble loading this event page. Please try reloading the page.", "fan.ticketing.order.error-finding-pass": "We're having trouble loading this pass. Please try reloading the page.", "fan.ticketing.order.error-pass-config-expired": "This pass is no longer available for purchase. Visit the tickets page for more options.", "fan.ticketing.order.generic-error": "We're having trouble loading this page. Please try reloading the page.", "fan.ticketing.order.back-to-tickets": "Back to Tickets", "fan.ticketing.order.reload-page": "Reload", "fan.ticketing.order.checkout.checkout-header": "Checkout", "fan.ticketing.order.checkout.first-name": "First Name", "fan.ticketing.order.checkout.first-name-placeholder": "Enter first name", "fan.ticketing.order.checkout.last-name": "Last Name", "fan.ticketing.order.checkout.last-name-placeholder": "Enter last name", "fan.ticketing.order.checkout.email": "Email Address", "fan.ticketing.order.checkout.payment-information": "Payment Information", "fan.ticketing.order.checkout.contact-information": "Contact Information", "fan.ticketing.order.checkout.email-placeholder": "<EMAIL>", "fan.ticketing.order.checkout.confirm-email": "Confirm Email Address", "fan.ticketing.order.checkout.confirm-email-placeholder": "<EMAIL>", "fan.ticketing.order.checkout.button.place-order": "Place Order", "fan.ticketing.order.checkout.email-error-help-text": "Please enter a valid email address.", "fan.ticketing.order.checkout.confirm-email-error-help-text": "Email address does not match.", "fan.ticketing.order.checkout.first-name-error-help-text": "Please provide your first name.", "fan.ticketing.order.checkout.last-name-error-help-text": "Please provide your last name.", "fan.ticketing.order.checkout.toast-error-retry": "Retry", "fan.ticketing.order.checkout.toast-error-message": "Something went wrong. Please try again.", "fan.ticketing.order.checkout.read-terms-of-service": "By clicking 'I agree' you acknowledge you have read and agree to be bound by the {termsOfService} and {privacyPolicy}, including those additional terms and conditions referenced therein and/or available by hyperlink.", "fan.ticketing.order.checkout.i-agree": "I agree", "fan.ticketing.order.checkout.place-order-again-error-message": "Something went wrong. Please try placing your order again.", "fan.ticketing.order.checkout.error-checking-out-contact-support": "We received your order, but we're having trouble processing it. Contact support to make sure you receive your tickets.", "fan.ticketing.order.checkout.error-making-payment-contact-support": "We are having issues placing your order. Contact support for further assistance.", "fan.ticketing.order.confirmation.non-refundable": "Note: This purchase is non-refundable.", "fan.ticketing.order.confirmation.details-order-headline": "Order Confirmation", "fan.ticketing.order.confirmation.details-purchase-headline": "Purchase Confirmation", "fan.ticketing.order.confirmation.confirmation-description-with-support-tickets": "Your ticket{numberOfEntities, plural, =1 {} other {s}} {numberOfEntities, plural, =1 {has} other {have}} been sent to {emailAddress}. If you don’t receive an email after a few minutes, check your spam folder or {hudlSupport}", "fan.ticketing.order.confirmation.confirmation-description-with-support-passes": "Your pass{numberOfEntities, plural, =1 {} other {es}} {numberOfEntities, plural, =1 {has} other {have}} been sent to {emailAddress}. If you don’t receive an email after a few minutes, check your spam folder or {hudlSupport}", "fan.ticketing.order.confirmation.confirmation-description-with-support-pass-renewal": "You've renewed your pass{numberOfPasses, plural, =1 {} other {es}} and kept your seat{numberOfSeats, plural, =1 {} other {e}}! Your renewal pass{numberOfPasses, plural, =1 {} other {es}} {numberOfPasses, plural, =1 {has} other {have}} been sent to {emailAddress}. If you don’t receive an email after a few minutes, check your spam folder or {hudlSupport}", "fan.ticketing.order.confirmation.details-contact-link": "contact support", "fan.ticketing.order.confirmation.details-tickets": "Event Details", "fan.ticketing.order.confirmation.details-passes": "Pass Details", "fan.ticketing.order.confirmation.view-tickets-button": "View Tickets", "fan.ticketing.order.confirmation.view-passes-button": "View Passes", "fan.ticketing.order.confirmation.customer-information": "Customer Information", "fan.ticketing.order.confirmation.payment-information": "Payment Information", "fan.ticketing.order.payment-info-text": "Charges will appear on your card statement as Hudl Tickets.", "fan.ticketing.order.confirmation.order-summary": "Order Summary", "fan.ticketing.order.confirmation.feedback": "<PERSON><PERSON><PERSON>", "fan.ticketing.order.confirmation.give-feedback": "<PERSON>", "fan.ticketing.order.confirmation.feedback-detail": "How was your purchase experience? We would love to hear about it—we’re always looking to make improvements!", "fan.ticketing.order.confirmation.organization-info-primary": "You're going!", "fan.ticketing.order.confirmation.organization-info-primary-renewal": "You're all set!", "fan.ticketing.order.confirmation.organization-info-secondary": "Order # {ticketGroupReference}", "fan.ticketing.order.confirmation.use-mobile": "Use Mobile Device", "fan.ticketing.order.confirmation.use-mobile-help-text-tickets": "Your phone is your ticket. Open your tickets using the button above for entry to the event.", "fan.ticketing.order.confirmation.use-mobile-help-text-passes": "Your phone is your pass. Open your passes using the button above for entry to the event.", "fan.ticketing.order.confirmation.print-item-tickets": "Access Tickets Early", "fan.ticketing.order.confirmation.print-item-passes": "Access Passes Early", "fan.ticketing.order.confirmation.print-item-help-text-tickets": "Open your tickets in advance in case cell service is slow at the venue.", "fan.ticketing.order.confirmation.print-item-help-text-passes": "Open your passes in advance in case cell service is slow at the venue.", "fan.ticketing.order.confirmation.scan-entry-tickets": "Show Tickets at Event", "fan.ticketing.order.confirmation.scan-entry-passes": "Show Passes at Event", "fan.ticketing.order.confirmation.scan-entry-help-tickets": "Authorized staff will scan your tickets at the event.", "fan.ticketing.order.confirmation.scan-entry-help-passes": "Authorized staff will scan your passes at the event.", "fan.ticketing.order.registration.login-to-order": "Log In to Place Order", "fan.ticketing.order.registration.join-hudl-perks": "Join <PERSON> to enjoy all the perks of staying connected to your favorite teams and athletes.", "fan.ticketing.order.registration.login": "Log In", "fan.ticketing.order.registration.continue-as-guest": "Continue as Guest", "fan.ticketing.order.registration.create-account": "Create Account", "fan.ticketing.order.does-not-exist": "This order does not exist. If you have any questions", "fan.ticketing.order.back-to-hudl-fan": "Back to Hudl Fan", "fan.ticketing.tab.buy-tickets": "Buy Tickets", "fan.ticketing.tab.get-tickets": "Get Tickets", "fan.ticketing.tab.buy-passes": "Buy Passes", "fan.ticketing.tab.get-passes": "Get Passes", "fan.ticketing.tab.pass-details": "Pass Details", "fan.ticketing.tab.event-info": "Event Info", "fan.ticketing.tab.your-pass": "Your Pass", "fan.ticketing.tab.your-passes": "Your Pass{numberOfPasses, plural, =1 {} other {es}}", "fan.ticketing.tab.enjoy-event-banner.tickets-remaining": " {remainingTickets} ticket{remainingTickets, plural, =1 {} other {s}} ha{remainingTickets, plural, =1 {s} other {ve}} not been used.", "fan.ticketing.tab.enjoy-event-banner.tickets-in-use": "You used {numberRedeemed} ticket{numberRedeemed, plural, =1 {} other {s}} at entry for this event. ", "fan.ticketing.tab.enjoy-event-banner.passes-remaining": "{remainingPasses} pass{remainingPasses, plural, =1 {} other {es}} ha{remainingPasses, plural, =1 {s} other {ve}} not been used.", "fan.ticketing.tab.enjoy-event-banner.passes-in-use": "You used {numberRedeemed} pass{numberRedeemed, plural, =1 {} other {es}} at entry for this event. ", "fan.ticketing.tab.used-on-date": "Used {usedTime}", "fan.ticketing.tab.shared-on-date": "Shared {sharedTime}", "fan.ticketing.tab.no-ticketed-events-published": "No Tickets Published Yet", "fan.ticketing.tab.no-ticketed-events-published-help-text": "Once tickets are available, you'll be able to purchase them here.", "fan.ticketing.tab.no-ticketed-events-published-help-text.league": "Once tickets are available for this league, you'll be able to purchase them here. Go to your school's profile page to find more upcoming events.", "fan.ticketing.tab.no-ticketed-events-enabled": "No Tickets Available", "fan.ticketing.tab.no-ticketed-events-enabled-help-text": "{orgName} hasn't set up digital tickets yet. Let them know you're interested in purchasing tickets on Hudl!", "fan.ticketing.tab.no-ticketed-events-enabled-help-text.team": "Once tickets are available for this team, you'll be able to purchase them here. Want to find more events to support {orgName}? Go to their \"Tickets\" page to see what’s coming up.", "fan.ticketing.tab.no-ticketing-entities-published.team": "Once tickets are available for this team, you'll be able to purchase them here. Want to find more events to support {orgName}? Go to their \"Tickets\" page to see what's coming up.", "fan.ticketing.tab.no-ticketed-events-button.team": "Find More Events", "fan.ticketing.tab.passes": "Passes", "fan.ticketing.tab.single-event": "Single Event", "fan.ticketing.my.tickets-not-yet-available": "Tickets are being generated. Try reloading the page to see if the tickets are ready.", "fan.ticketing.my.passes-not-yet-available": "Passes are being generated. Try reloading the page to see if the passes are ready.", "fan.ticketing.my.header": "My Tickets", "fan.ticketing.my.tickets-tab-title": "Tickets", "fan.ticketing.my.passes-tab-title": "Passes", "fan.ticketing.my.details-header-tickets": "Event Details", "fan.ticketing.my.details-header-passes": "Pass Details", "fan.ticketing.my.ticketing-orders.header": "Tickets & Passes", "fan.ticketing.my.ticketing-orders.num-tickets": "{numberOfTickets} ticket{numberOfTickets, plural, =1 {} other {s}}", "fan.ticketing.my.ticketing-orders.num-passes": "{numberOfPasses} pass{numberOfPasses, plural, =1 {} other {es}}", "fan.ticketing.my.ticketing-orders.pass-valid-for": "Pass Valid For:", "fan.ticketing.my.ticketing-orders.valid-for": "Valid For: {formattedPassDate}", "fan.ticketing.my.ticketing-orders.description": "Description:", "fan.ticketing.my.ticketing-orders.error-loading-tickets": "We're having trouble loading your tickets. Try reloading the page or {hudlSupport}", "fan.ticketing.my.ticketing-orders.error-loading-passes": "We're having trouble loading your passes. Try reloading the page or {hudlSupport}", "fan.ticketing.my.ticketing-orders.no-passes-purchased": "No Passes Purchased Yet", "fan.ticketing.my.ticketing-orders.no-passes-purchased-help": "Search for a school or club to find available passes, and directly support your favorite teams.", "fan.ticketing.my.ticketing-orders.no-tickets-purchased": "No Tickets Purchased Yet", "fan.ticketing.my.ticketing-orders.no-tickets-purchased-help": "Search for a school or club to find available tickets, and directly support your favorite teams.", "fan.ticketing.tab.upcoming-events": "Upcoming Events", "fan.ticketing.tab.tickets": "Tickets", "fan.ticketing.tab.load-more-events": "Load More", "fan.ticketing.tab.error-loading-events": "Something went wrong. Please try refreshing the page.", "fan.ticketing.tab.view-all-passes": "View all", "fan.ticketing.ticket.ticket-footer-text": "Ticket must be scanned for entry.", "fan.ticketing.ticket.ticket-footer-text-new": "Ticket must be used on site for entry.", "fan.ticketing.ticket.ticket-number": "({ticketNumber} of {totalTicketsNumber})", "fan.ticketing.ticket.ticket-section": "Section", "fan.ticketing.ticket.ticket-row": "Row", "fan.ticketing.ticket.ticket-seat": "<PERSON><PERSON>", "fan.ticketing.ticket.ticket-zone": "Zone", "fan.ticketing.ticket.ticket-table": "Table", "fan.ticketing.pass.pass-number": "({passNumber} of {totalPassesNumber})", "fan.ticketing.ticket.qr-code-alt-text": "The QR Code for ticket {ticketNumber} of {totalTicketsNumber}", "fan.ticketing.pass.qr-code-alt-text": "The QR Code for pass {passNumber} of {totalPassesNumber}", "fan.ticketing.pass.renewal.go-to-tickets": "Go to Tickets", "fan.ticketing.pass.renewal.renewal-period-ended": "Renewal period for this pass has ended. Go to Tickets to check out passes available for purchase.", "fan.ticketing.reload": "Reload", "fan.ticketing.reserved-seats-timer-display": "Holding Seats For", "fan.ticketing.reserved-seats-timeout-text": "The hold on your seats has expired. You'll need to select your seats again.", "fan.ticketing.reserved-seats-your-seats": "Your Seat(s)", "fan.ticketing.reserved-seats-no-seats-selected": "No Seats Selected", "fan.ticketing.start-search": "Start a Search", "fan.ticketing.how-to-use.headline": "How To Use Your Tickets", "fan.ticketing.how-to-use.headline-tickets": "How To Use Your Tickets", "fan.ticketing.how-to-use.headline-passes": "How To Use Your Passes", "fan.ticketing.how-to-use.scanned-tickets": "● Tickets can be scanned or activated for entry.", "fan.ticketing.how-to-use.scanned-passes": "● Passes can be scanned or activated for entry.", "fan.ticketing.how-to-use.show-staff": "● To scan your tickets, show them to staff member at entry.", "fan.ticketing.how-to-use.show-staff-tickets": "● To scan your tickets, show them to staff member at entry.", "fan.ticketing.how-to-use.show-staff-passes": "● To scan your passes, show them to staff member at entry.", "fan.ticketing.how-to-use.at-event": "● Make sure you are at the event and following the instructions of authorized staff.", "fan.ticketing.how-to-use.contact-support": "● For help with your tickets, {hudlSupport}", "fan.ticketing.how-to-use.contact-support-tickets": "● For help with your tickets, {hudlSupport}", "fan.ticketing.how-to-use.contact-support-passes": "● For help with your passes, {hudlSupport}", "fan.ticketing.disabled-qr-code.help-text-bold-tickets": "Tickets will only be active a few hours before the event starts.", "fan.ticketing.disabled-qr-code.help-text-bold-passes": "Passes will only be active a few hours before the event starts.", "fan.ticketing.disabled-qr-code.help-text-tickets": "{boldText} Screenshots will not be accepted for entry.", "fan.ticketing.disabled-qr-code.help-text-passes": "Screenshots will not be accepted for entry.", "fan.ticketing.event-today.help-banner-text-bold-tickets": "You have an event today!", "fan.ticketing.event-today.help-banner-text-bold-passes": "You have an event today!", "fan.ticketing.event-today.help-banner-text-tickets": "{boldText} Your tickets are ready to be scanned or used for entry.", "fan.ticketing.event-today.help-banner-text-passes": "Your passes are ready to be scanned or used for entry.", "fan.ticketing.disabled-qr-code.qr-code-text": "QR code will be available a few hours before the event starts.", "fan.ticketing.transfer.problem-transferring-passes": "There was a problem transferring your passes. Please try again.", "fan.ticketing.transfer.problem-transferring-tickets": "There was a problem transferring your tickets. Please try again.", "fan.ticketing.transfer.once-transferred-passes": "Once a pass is transferred, it will no longer be available in your account.", "fan.ticketing.transfer.once-transferred-tickets": "Once a ticket is transferred, it will no longer be available in your account.", "fan.ticketing.transfer.add-recipient-information-passes": "Add Pass Recipient Information", "fan.ticketing.transfer.add-recipient-information-tickets": "Add Ticket Recipient Information", "fan.ticketing.transfer.select-to-transfer-passes": "Select the passes you'd like to transfer via email.", "fan.ticketing.transfer.select-to-transfer-tickets": "Select the tickets you'd like to transfer via email.", "fan.ticketing.transfer.successfully-transferred-passes": "{numSelectedEntities} pass{numSelectedEntities, plural, =1 {} other {es}} successfully transferred", "fan.ticketing.transfer.successfully-transferred-tickets": "{numSelectedEntities} ticket{numSelectedEntities, plural, =1 {} other {s}} successfully transferred", "fan.ticketing.transfer.number-selected-passes": "{numSelectedEntities} pass{numSelectedEntities, plural, =1 {} other {es}} selected", "fan.ticketing.transfer.number-selected-tickets": "{numSelectedEntities} ticket{numSelectedEntities, plural, =1 {} other {s}} selected", "fan.ticketing.redemption.already-used-tickets": "This ticket has already been used.", "fan.ticketing.redemption.already-used-passes": "This pass has already been used.", "fan.ticketing.redemption.number-selected-tickets": "{numberOfTickets} Ticket{numberOfTickets, plural, =1 {} other {s}} Selected", "fan.ticketing.redemption.number-selected-passes": "{numberOfTickets} Pass{numberOfTickets, plural, =1 {} other {es}} Selected", "fan.ticketing.redemption.select-for-entry-tickets": "Select Tickets to Use for Entry", "fan.ticketing.redemption.select-for-entry-passes": "Select Passes to Use for Entry", "fan.ticketing.redemption.follow-staff-instructions": "Make sure you're following the instructions of authorized staff at entry.", "fan.ticketing.redemption.use-at-event-tickets": "Only use your tickets if you're at the event and have been instructed by authorized staff. Ticket usage for entry cannot be undone.", "fan.ticketing.redemption.use-at-event-passes": "Only use your passes if you're at the event and have been instructed by authorized staff. Pass usage for entry cannot be undone.", "fan.ticketing.redemption.enjoy-the-event": "You're all set. Enjoy the event and thanks for your support!", "fan.ticketing.redemption.mobile-header-tickets": "Tickets", "fan.ticketing.redemption.mobile-header-passes": "Passes", "fan.ticketing.redemption.use-for-entry-tickets": "Use Tickets for Entry", "fan.ticketing.redemption.use-for-entry-passes": "Use Passes for Entry", "fan.ticketing.redemption.go-to-my-tickets": "Go To My Tickets", "fan.ticketing.redemption.go-to-my-passes": "Go To My Passes", "fan.ticketing.redemption.ready-to-use-tickets": "Ready to Use {numberOfTickets} Ticket{numberOfTickets, plural, =1 {} other {s}}?", "fan.ticketing.redemption.ready-to-use-passes": "Ready to Use {numberOfTickets} Pass{numberOfTickets, plural, =1 {} other {es}}?", "fan.ticketing.redemption.used-for-entry-tickets": "{numberOfTickets} Ticket{numberOfTickets, plural, =1 {} other {s}} Used for Entry", "fan.ticketing.redemption.used-for-entry-passes": "{numberOfTickets} Pass{numberOfTickets, plural, =1 {} other {es}} Used for Entry", "fan.ticketing.redemption.used-for-entry-with-name-tickets": "{numberOfTickets} Ticket{numberOfTickets, plural, =1 {} other {s}} Used for {entityName}", "fan.ticketing.redemption.used-for-entry-with-name-passes": "{numberOfTickets} Pass{numberOfTickets, plural, =1 {} other {es}} Used for {entityName}", "fan.ticketing.redemption.problem-using-tickets": "There was a problem using your tickets for entry. Please try again.", "fan.ticketing.redemption.problem-using-passes": "There was a problem using your passes for entry. Please try again.", "fan.ticketing.redemption.show-device-to-staff-bold-tickets": "Show your device", "fan.ticketing.redemption.show-device-to-staff-tickets": "{boldText} to authorized staff to use tickets for entry.", "fan.ticketing.share.mobile-header-tickets": "Share Tickets", "fan.ticketing.share.number-selected-tickets": "{numberOfTickets} Ticket{numberOfTickets, plural, =1 {} other {s}} Selected", "fan.ticketing.share.select-for-share-tickets": "Select the tickets you'd like to share.", "fan.ticketing.share.copy-the-link-tickets": "Copy the link to share your tickets.", "fan.ticketing.share.copy-link": "Copy Link", "fan.ticketing.share.link-copied": "<PERSON> copied.", "fan.ticketing.share.problem-sharing-tickets": "There was a problem sharing your tickets. Please try again.", "fan.ticketing.share.done": "Done", "fan.ticketing.share.share": "Share", "fan.ticketing.events-list.no-events": "No upcoming events at this time. Check back later for new events that get added to this pass.", "fan.ticketing.events-list.no-events-today.active-pass": "No events today", "fan.ticketing.events-list.today": "Today", "fan.ticketing.rebook-seats.error-message": "Something went wrong while trying to book your seats. Please re-select your seats and try again.", "fan.ticketing.rebook-seats.error-message-select-seats": "Select Seats", "fan.ticketing.event.does-not-exist": "This event no longer exists. Go to Tickets to find upcoming events.", "fan.ticketing.event.event-does-not-exist": "This event does not exist. Go to Tickets to find upcoming events.", "fan.ticketing.event.is-past": "This event has already passed. Go to Tickets to find upcoming events.", "fan.ticketing.event.does-not-exist.go-to-tickets": "Go to Tickets", "fan.ticketing.pass.in-renewal-no-access-error": "This pass is currently only available for renewal. If you are a returning pass holder, please use the link provided in your email to renew your seats.", "fan.ticketing.form-fields.additional-information": "Additional Information", "fan.ticketing.form-fields.additional-information-needed": "Additional Information Needed", "fan.ticketing.form-fields.field-required-error": "This information is required.", "fan.ticketing.form-fields.character-limit-error": "Character count is over the limit. {characterCount}/{characterLimit}", "fan.ticketing.transparent-fees.total-tickets": "Ticket Total", "fan.ticketing.transparent-fees.total-passes": "Pass Total", "fan.ticketing.transparent-fees.transaction-fees": "transaction fees", "fan.ticketing.transparent-fees.service-fee-tickets": "This includes the base ticket price and a {hudlFeeInCents} service fee for each ticket. Your purchase supports the school and Hudl's ticketing platform.", "fan.ticketing.transparent-fees.service-fee-passes": "This includes the base pass price and a {hudlFeeInCents} service fee for each pass. Your purchase supports the school and Hudl's ticketing platform.", "fan.ticketing.transparent-fees.transaction-fee": "Order Transaction Fee", "fan.ticketing.transparent-fees.transaction-fee-help-text": "The fee is collected by Stripe, Hudl's payment provider, to process your order.", "fan.ticketing.transparent-fees.service-fee-help-text-tickets": "Includes {hudlFeeInCents} service fee per ticket", "fan.ticketing.transparent-fees.service-fee-help-text-passes": "Includes {hudlFeeInCents} service fee per pass", "fan.ticketing.transparent-fees.service-fee-help-text": "Includes {hudlFeeInCents} service fee", "fan.ticketing.transparent-fees.error-message": "There was a problem calculating the order total. Refresh the page to try again.", "fan.ticketing.transparent-fees.error-checkout": "There was a problem calculating the order total.", "fan.ticketing.league-pass-display.school": "School", "fan.ticketing.league-pass-display.schools": "Schools", "fan.ticketing.league-pass-display.club": "Club", "fan.ticketing.league-pass-display.clubs": "Clubs", "fan.ticketing.league-pass-display.member": "Member", "fan.ticketing.league-pass-display.members": "Members", "fan.ticketing.league-pass-event-filter.schools": "Schools", "fan.ticketing.league-pass-event-filter.all-schools": "All Schools", "fan.ticketing.league-pass-event-filter.all-league-type": "All {displayName}", "fan.ticketing.league-pass-event-filter.x-schools": "{schoolCount, plural, one {# School Selected} other {# Schools Selected}}", "fan.ticketing.league-pass-event-filter.x-league-type": "{count, plural, one {# {displayName} Selected} other {# {displayNamePlural} Selected}}", "fan.ticketing.league-pass-event-filter.gender": "Gender", "fan.ticketing.league-pass-event-filter.level": "Level", "fan.ticketing.league-pass-event-filter.sport": "Sport", "fan.ticketing.league-pass-event-filter.filter-slash-schools": "Filter / Schools", "fan.ticketing.league-pass-event-filter.filter-slash-league-type": "Filter / {displayName}", "fan.ticketing.league-pass-event-filter.search-for-school": "Search for a school...", "fan.ticketing.league-pass-event-filter.search-for-league-type": "Search for a {displayName}...", "fan.ticketing.league-pass-schools-list.view-schools-and-teams": "View Schools & Teams", "fan.ticketing.league-pass-schools-list.view-display-name-and-teams": "View {displayName} & Teams", "fan.ticketing.league-pass-schools-list.included-in-pass": "{entityCount} {entityLabel}, {teamCount, plural, one {# Team} other {# Teams}} Included In Pass", "fan.ticketing.filter-container.clear-all": "Clear All", "fan.ticketing.filter-container.select-all": "Select All", "fan.ticketing.filter-container.apply-filter": "Apply Filter", "fan.ticketing.filter-container.filter": "Filter", "fan.ticketing.filter-container.filters": "Filters", "fan.ticketing.modal-or-mobile.close": "Close", "fan.ticketing.teams-list.view-teams": "View Teams", "fan.ticketing.league-pass.select-school.header": "Select Your School", "fan.ticketing.league-pass.select-school.description": "Search for your school to view and purchase passes. Your purchase helps support the school.", "fan.ticketing.league-pass.select-school.placeholder": "Search for...", "fan.ticketing.league-pass.select-school.no-results": "No results found", "fan.ticketing.league-pass.select-school.continue-purchase-pass": "Continue to Purchase Pass", "fan.ticketing.league-passes.go-to-entity": "Go to {entityName}", "fan.search-bar.empty-state": "No results found", "fan.search-bar.error-state": "We're having trouble loading this content. Try reloading the page.", "fan.search-bar.reload": "Reload", "fan.search-bar.see-all-results-v1": "See All", "fan.search-bar.placeholder": "Search for a school or club", "fan.search-bar.placeholder-text": "Search for a school, club or athlete", "fan.search-bar.search-hudl-fan": "Search Hudl Fan", "fan.search-bar.search-for-v1": "Search for an athlete, school, club, association or league", "fan.search-bar.search-for-mobile-v1": "Search for an athlete, school, club and more", "fan.search-bar.athletes-v1": "Athletes", "fan.search-bar.schools-v1": "Schools", "fan.search-bar.clubs-v1": "Clubs", "fan.search-bar.networks-v1": "Networks", "fan.nav-bar.watch-now": "Watch Now", "fan.simple-webnav.support": "Support", "fan.nav-bar.get-help": "Get Help", "fan.nav-bar.tutorials": "Tutorials", "fan.nav-bar.livestream-purchases": "Livestream Purchases", "fan.nav-bar.tickets-and-passes": "Tickets & Passes", "fan.nav-bar.account-settings": "Account <PERSON><PERSON>", "fan.nav-bar.log-out": "Log Out", "fan.ssr.organization.metadata.home.tab-title": "Home", "fan.ssr.organization.metadata.schedule.tab-title": "Schedule", "fan.ssr.organization.metadata.video.tab-title": "Video", "fan.ssr.organization.metadata.teams.tab-title": "Teams", "fan.ssr.organization.metadata.tickets.tab-title": "Tickets", "fan.ssr.organization.metadata.event-viewer.tab-title": "Watch Now", "fan.ssr.organization.metadata.home.description": "Watch livestreams, see the latest highlights and find upcoming events.", "fan.ssr.organization.metadata.schedule.description": "Find scheduled events, watch livestreams and explore rosters.", "fan.ssr.organization.metadata.video.description": "Watch live events, past livestreams and recent highlights.", "fan.ssr.organization.metadata.teams.description": "Explore their teams’ livestreams, highlights, rosters and more.", "fan.ssr.organization.metadata.tickets.description": "Buy digital tickets to attend upcoming events.", "fan.ssr.organization.metadata.event-viewer.description": "Watch live events, past livestreams and recent highlights.", "fan.ssr.organization.metadata.location-description": "Located in {city}, {subdivision}. ", "fan.ssr.team.metadata.home.tab-title": "Home", "fan.ssr.team.metadata.schedule.tab-title": "Schedule", "fan.ssr.team.metadata.roster.tab-title": "<PERSON><PERSON><PERSON>", "fan.ssr.team.metadata.video.tab-title": "Video", "fan.ssr.team.metadata.event-viewer.tab-title": "Watch Now", "fan.ssr.team.metadata.home.description": "View livestreams, highlights and upcoming events", "fan.ssr.team.metadata.schedule.description": "Find scheduled events, livestreams and recent scores", "fan.ssr.team.metadata.roster.description": "Explore players, positions and metrics", "fan.ssr.team.metadata.video.description": "Watch live events, past livestreams and highlights", "fan.ssr.team.metadata.event-viewer.description": "Watch live events, past livestreams and recent highlights", "fan.ssr.team.metadata.location-description": " for {schoolName} in {city}, {subdivision}.", "fan.ssr.league.metadata.affiliates.tab-title": "Affiliates", "fan.ssr.league.metadata.home.description.v1": "Watch livestreams and explore past videos.", "fan.ssr.league.metadata.affiliates.description.v1": "Explore livestreams, recent highlights and team rosters.", "fan.ssr.league.metadata.location-description": "Located in {location}.", "fan.simple-webnav.watch-now": "Watch Now", "fan.simple-webnav.create-account": "Create Account", "fan.simple-webnav.login": "Log In", "fan.homepage.spotlight.text": "Find livestreams, highlights, rosters, and more from all your favorites.", "fan.homepage.spotlight.subtext": "Find livestreams, highlights, rosters, and more.", "fan.homepage.spotlight.description": " Search for a school, club or athlete.", "fan.homepage.spotlight.heading.partial-first": "Catch", "fan.homepage.spotlight.heading.partial-stacked-top": "all", "fan.homepage.spotlight.heading.partial-stacked-bottom": "the", "fan.homepage.spotlight.heading.partial-last": "action", "fan.homepage.ad.presented-by": "Presented by", "fan.footer.solutions.heading": "Solutions", "fan.footer.solutions.high-school": "High School", "fan.footer.solutions.club": "Club", "fan.footer.solutions.college": "Collegiate", "fan.footer.solutions.ncaa-d1": "Division I Colleges", "fan.footer.solutions.youth-football": "Youth Football", "fan.footer.solutions.professional": "Professional", "fan.footer.solutions.licensing-suite": "Licensing Suite", "fan.footer.products.heading": "Products", "fan.footer.products.hudl": "<PERSON><PERSON>", "fan.footer.products.sportscode": "Sportscode", "fan.footer.products.insight": "Insight", "fan.footer.products.studio": "Studio", "fan.footer.products.coda": "Coda", "fan.footer.products.wyscout": "Wyscout", "fan.footer.products.gamepass": "GamePass", "fan.footer.products.assist": "Assist", "fan.footer.products.focus": "Focus", "fan.footer.products.focus-indoor": "Focus Indoor", "fan.footer.products.focus-outdoor": "Focus Outdoor", "fan.footer.products.focus-flex": "Focus Flex", "fan.footer.products.sideline": "Sideline", "fan.footer.products.replay": "Replay", "fan.footer.products.volleymetrics": "Volleymetrics", "fan.footer.products.hudltv": "Hudl TV", "fan.footer.products.wimu": "WIMU", "fan.footer.products.instat": "Instat", "fan.footer.products.release-notes": "Release Notes", "fan.footer.athletes-and-fans.heading": "Athletes and Fans", "fan.footer.athletes-and-fans.highlights": "Hudl Highlights", "fan.footer.about.heading": "About", "fan.footer.about.advertise": "Advertise", "fan.footer.about.press": "Press", "fan.footer.about.company-news": "Company News", "fan.footer.about.careers": "Careers", "fan.footer.contact.heading": "Contact Us", "fan.footer.contact.support": "Support", "fan.footer.contact.blog": "Blog", "fan.footer.contact.pay": "Pay by Credit Card", "fan.footer.contact.hudl-accessories": "Hudl Accessories", "fan.footer.login": "<PERSON><PERSON>", "fan.footer.request-demo": "Request a Free Demo", "fan.footer.privacy-policy": "Privacy Policy", "fan.footer.terms": "Terms & Conditions", "fan.footer.license-agreement": "Software License Agreement", "fan.footer.privacy-rights": "Do Not Sell My Personal Information", "fan.footer.cookies": "Cookies", "fan.footer.security": "Security", "fan.footer.copyright": "Hudl is a product and service of Agile Sports Technologies, Inc. All text and design ©2007-{currentYear}. All rights reserved.", "fan.graphql-error-state": "We're having trouble loading this content. Try reloading.", "fan.graphql-error-state.reload": "Reload", "fan.highlights.disabled": "We're having trouble loading this content. Try again later.", "fan.homepage.fansearch-error.heading": "Sorry, we ran into an error.", "fan.homepage.fansearch-error.text-top": "It's not your fault! We screwed up. Something is wrong with this page, but we're working on fixing it now.", "fan.homepage.fansearch-error.text-bottom": "Please check back soon, or hit up {hudlSupport} if the issue persists.", "fan.homepage.fansearch-error.hudl-support-text": "Hudl Support", "fan.league.live-schools": "Live Schools", "fan.league.all-live-schools": "All Schools", "fan.league.live-and-upcoming": "Live & Upcoming", "fan.league.all-live-and-upcoming": "All Live & Upcoming", "fan.league.past-livestreams": "Past Livestreams", "fan.league.all-past-livestreams": "All Past Livestreams", "fan.org-card.live": "Live", "fan.dialog.share.social-share": "Share on {social}", "fan.dialog.share.social-share.facebook": "Facebook", "fan.dialog.share.social-share.twitter": "Twitter", "fan.dialog.share.social-share.post": "Post", "fan.dialog.share.copy-url": "<PERSON><PERSON> the URL", "fan.dialog.share.copy": "Copy", "fan.dialog.share.twitter-text": "Check out {profileName} on @Hudl", "fan.dialog.share.share-via": "Share via", "fan.dialog.share.copy-link": "Or copy link", "fan.page-not-found.heading": "Sorry, we couldn't find that page.", "fan.page-not-found.sub-text": "This usually means something's changed or been deleted.", "fan.page-not-found.contact-support": "If you're sure something should be here, feel free to contact <a>Hudl support</a>.", "fan.page-not-found.go-home": "Go to Home", "fan.org-profile.error-state-heading": "Sorry, we couldn't load this page.", "fan.org-profile.error-state-text": "Try reloading the page or try again later. If you continue having trouble, hit up {hudlSupport}.", "fan.graphql-error-state.go-to-home": "Go to Home", "fan.hudl-registration-purchase-terms": "Hudl Registration Purchase Terms", "fan.hudl-support-text": "Hudl Support", "fan.okay": "Okay", "fan.dismiss": "<PERSON><PERSON><PERSON>", "fan.of": "of", "fan.contact-support-text": "contact support.", "fan.hudl-tickets-purchase-terms": "Hudl Tickets Purchase Terms", "fan.privacy-policy": "Hudl Privacy Policy", "fan.refund-exchange-policy": "refund and exchange policy", "fan.contact-support-text-upper": "Contact Support", "fan.roster.weight-abbreviation": "lbs", "fan.roster.name": "Name", "fan.roster.position": "Position", "fan.roster.jersey-number": "Jersey #", "fan.roster.height": "Height", "fan.roster.weight": "Weight", "fan.roster.graduation-year": "Class of", "fan.roster.view-all": "View All", "fan.roster.view-less": "View Less", "fan.roster.empty-state-text": "No Roster Added Yet", "fan.roster.empty-state-sub-text": "Check back later for updates.", "fan.schedule-entry-location.home": "Home", "fan.schedule-entry-location.away": "Away", "fan.schedule-entry-outcome.no-score-reported": "No score reported", "fan.studios-content.top-videos": "Top Videos", "fan.studios-content.featured-games": "Featured Games", "fan.shared.carousel.slide-number": "{slideNumber} of {totalSlidesNumber}", "fan.date.today": "Today", "fan.date.at": "@", "fan.back": "Back", "fan.enter": "Enter", "fan.refresh": "Refresh", "fan.graphql-no-refetch-state": "We're having trouble loading this content.", "fan.organization-card.schedule-button-text": "Schedule", "fan.organization-card.teams-button-text": "Teams", "fan.organization-card.video-button-text": "Video", "fan.organization-card.tickets-button-text": "Tickets", "fan.smart-app-banner.title.main": "Hudl Fan:", "fan.smart-app-banner.title.secondary": "Find. Watch. Follow.", "fan.smart-app-banner.description": "Teams, scores, streams & more.", "fan.seating-type.general-admission": "General Admission", "fan.seating-type.reserved-seating": "Reserved Seating", "fan.ssr.league.metadata.schedule.tab-title": "Schedule", "fan.ssr.league.metadata.schedule.description.v1": "Find scheduled events, livestreams and recent scores.", "fan.league.schedule.live-now": "Live Now", "fan.league.schedule.final": "Final", "fan.league.schedule.vs": "vs", "fan.league.schedule.at": "at", "fan.league.schedule.video-button.watch": "Watch", "fan.league.schedule.video-button.preview": "Preview", "fan.league.schedule.video-button.recap": "Recap", "fan.league.schedule.get-tickets": "Get Tickets", "fan.league.schedule.schedule-card-group-display-name.today": "Today", "fan.league.home.latest-results": "Latest Results", "fan.league.home.latest-results.all-events": "All Events", "fan.search-bar.recent-searches": "Recent Searches", "fan.ssr.league.metadata.members.tab-title": "Members", "fan.ssr.league.metadata.members.description.v1": "Explore livestreams, recent highlights and team rosters.", "fan.league.competition-filter.all": "All", "fan.league.recent-highlights": "Recent Highlights", "fan.league.all-highlights": "All Highlights", "fan.search-bar.filter-empty-state.title": "No Matching Results", "fan.search-bar.filter-empty-state.description": "Try adjusting your filters or search terms.", "fan.search-bar.filter-empty-state.clear-button-text": "Clear All Filters", "fan.org-programs.athlete-selection.title": "Select Athlete", "fan.org-programs.athlete-selection.description": "You're registering for", "fan.org-programs.footer.back": "Back", "fan.org-programs.footer.continue": "Continue", "fan.org-programs.footer.place-order": "Place Order", "fan.org-programs.registration-card.eligibility": "Eligibility", "fan.org-programs.registration-card.birthdate-from": "Birthdate From", "fan.org-programs.registration-card.birthdate-to": "Birthdate To", "fan.org-programs.registration-card.boys": "Boys", "fan.org-programs.registration-card.boys-and-girls": "Boys and Girls", "fan.org-programs.registration-card.girls": "Girls", "fan.org-programs.registration-card.gender": "Gender", "fan.org-programs.registration-card.grade": "Grade", "fan.org-programs.registration-card.free": "Free", "fan.org-programs.registration-card.full-price": "Full Payment", "fan.org-programs.registration-card.payment-options": "Payment Options", "fan.org-programs.registration-card.details": "Details", "fan.org-programs.registration-card.status.none": "Not Eligible", "fan.org-programs.registration-card.status.ineligible": "No eligible athletes found.", "fan.org-programs.registration-card.status.eligible-athlete": "Eligible Athlete", "fan.org-programs.registration-card.status.eligible-athletes": "Eligible Athletes", "fan.org-programs.registration-card.register": "Register", "fan.org-programs.registration-card.registered": "Registered", "fan.org-programs.list.registrations": "Registrations", "fan.org-programs.header.read-more-button": "read more", "fan.org-programs.header.read-less-button": "read less", "fan.org-programs.status-card.unauthenticated.title": "Log in or create an account to register an athlete.", "fan.org-programs.status-card.unauthenticated.description": "We’ll match your athlete to eligible registrations to help you get started faster.", "fan.org-programs.status-card.unauthenticated.log-in": "Log in", "fan.org-programs.status-card.incomplete-athletes.title": "Add athlete information to view eligible registrations.", "fan.org-programs.status-card.incomplete-athletes.description": "We use age, grade, and gender to determine what's available for your athlete.", "fan.org-programs.status-card.incomplete-athletes.add-athlete-info": "Add Athlete Info", "fan.org-programs.status-card.no-athletes.title": "Add your athletes to get started.", "fan.org-programs.status-card.no-athletes.description": "We'll use their details to show the registrations they're eligible for.", "fan.org-programs.status-card.no-athletes.add-athlete": "Add Athletes", "fan.org-programs.status-card.unauthenticated.create-account": "Don't have an account?", "fan.org-programs.status-card.unauthenticated.create-account-link": "Create account", "fan.org-programs.order-summary.title": "Order Summary", "fan.org-programs.order-summary.registration-total": "Registration Total", "fan.org-programs.order-summary.total": "Total", "fan.org-programs.order-summary.transaction-fee": "Transaction Fee", "fan.registration-participant.form-item.new-player": "New Player", "fan.registration-participant.form-item.first-name": "First Name", "fan.registration-participant.form-item.last-name": "Last Name", "fan.registration-participant.form-item.date-of-birth": "Date of Birth", "fan.registration-participant.form-item.gender": "Gender", "fan.registration-participant.form-item.gender-male": "Male", "fan.registration-participant.form-item.gender-female": "Female", "fan.registration-participant.form-item.guardian-checkbox": "I am the parent or legal guardian of the athlete listed above. You must be a parent or legal guardian to register someone under 18 years old. I acknowledge that the participant and I, the parent/legal guardian, agree to abide by the organization's rules and policies.", "fan.registration-participant.form-item.grade": "Grade", "fan.registration-participant.form-item.grad-year": "Graduation Year", "fan.registration-participant.form-item.grade-1st": "1st Grade", "fan.registration-participant.form-item.grade-2nd": "2nd Grade", "fan.registration-participant.form-item.grade-3rd": "3rd Grade", "fan.registration-participant.form-item.grade-4th": "4th Grade", "fan.registration-participant.form-item.grade-5th": "5th Grade", "fan.registration-participant.form-item.grade-6th": "6th Grade", "fan.registration-participant.form-item.grade-7th": "7th Grade", "fan.registration-participant.form-item.grade-8th": "8th Grade", "fan.registration-participant.form-item.grade-9th": "9th Grade", "fan.registration-participant.form-item.grade-10th": "10th Grade", "fan.registration-participant.form-item.grade-11th": "11th Grade", "fan.registration-participant.form-item.grade-12th": "12th Grade", "fan.registration-participant.form-item.add-athletes-header": "Add Your Athletes", "fan.registration-participant.form-item.add-athletes-description": "Enter your athlete’s information so we can match them to eligible registrations.", "fan.registration-participant.form-item.add-another-athlete-button": "Add Another Athlete", "fan.registration-participant.form-item.finish-button": "Finish", "fan.registration-participant.form-item.save-and-add-another-athlete-button": "Save & Add Another Athlete", "fan.registration-participant.form-item.save-and-finish-button": "Save & Finish", "fan.registration-participant.form-item.new-player-placeholder": "New Player", "fan.registration-participant.form-item.grade-academic-year": "Your athlete’s grade for the {academicYear} academic year.", "fan.registration-participant.form-item.participant-name": "{firstName} {lastName}", "fan.registration-participant.submission-error-message": "We couldn’t save your athlete information. Please try again."}}