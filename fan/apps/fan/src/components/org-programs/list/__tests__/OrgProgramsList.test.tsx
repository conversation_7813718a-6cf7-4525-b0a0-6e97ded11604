import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';

import { isAthleteEligible } from '../../../../utils/athleteEligibilityUtils';
import { mapProgramRegistrationToRegistration } from '../../../../utils/mappingRegistrationUtils';
import { OrgProgramsProvider, useOrgPrograms } from '../../OrgProgramsContext';
import { Athlete } from '../../types/athlete';
import { GenderType, Grade } from '../../types/registrations';
import { OrgProgramsList } from '../OrgProgramsList';
import {
  FeeResponsibility,
  Grade as GraphQLGrade,
  ProgramState,
  RegistrationGender,
} from '@/graphql/public/graphqlTypes';

function mockFormat(key: string): string {
  const translations: Record<string, string> = {
    'fan.org-programs.header.read-more': 'read more',
    'fan.org-programs.header.read-less': 'read less',
    'fan.org-programs.status-card.unauthenticated.title': 'Log In',
    'fan.org-programs.status-card.incomplete-athletes.title': 'Add athlete information to view eligible registrations.',
    'fan.org-programs.status-card.no-athletes.title': 'Add your athletes to get started.',
    'fan.org-programs.list.registrations': 'Registrations',
    'fan.org-programs.registration-card.free': 'FREE',
  };
  return translations[key] || key;
}

vi.mock('../../OrgProgramsContext', () => ({
  OrgProgramsProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useOrgPrograms: vi.fn(),
}));

vi.mock('date-fns', () => ({
  format: () => 'Jan 1, 2024',
}));

vi.mock('frontends-i18n', () => ({
  useFormat: () => mockFormat,
}));

const mockAthletes: Athlete[] = [
  {
    firstName: 'Kate',
    lastName: 'Mak',
    birthdate: '2000-01-01',
    gender: GenderType.Female,
    grade: Grade.Ninth,
    id: 'athlete-1',
  },
  {
    firstName: 'Jennifer',
    lastName: 'Kelly',
    birthdate: '2005-01-01',
    gender: GenderType.Female,
    grade: Grade.Ninth,
    id: 'athlete-2',
  },
  {
    firstName: 'Justin',
    lastName: 'Mock',
    birthdate: '2000-01-01',
    gender: GenderType.Male,
    grade: Grade.Ninth,
    id: 'athlete-3',
  },
];

const mockSetOrgProgramsData = vi.fn();
const mockGoToStep = vi.fn();
const mockProgram = {
  __typename: 'OrganizationProgram' as const,
  id: '1',
  orgId: '1',
  state: ProgramState.PUBLISHED,
  timeZoneIdentifier: 'America/Chicago',
  feeResponsibility: FeeResponsibility.ORGANIZATION,
  title: 'Test Program',
  description: 'Test Description',
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  type: {
    __typename: 'EmbeddedOrganizationProgramType' as const,
    id: '1',
    name: 'Tryout',
  },
  registrationsForProgram: [
    {
      __typename: 'ProgramRegistration' as const,
      id: '1',
      title: 'Registration 1',
      priceInBaseDenomination: 100,
      currencyCode: 'USD',
      description: 'Test Registration',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      timeZoneIdentifier: 'America/Chicago',
      maxCapacity: 100,
      isWaitlistEnabled: false,
      eligibility: {
        __typename: 'RegistrationEligibilityOutput' as const,
        birthDateFrom: '2000-01-01',
        birthDateTo: '2010-12-31',
        gender: RegistrationGender.ANY,
        grades: [GraphQLGrade.NINTH, GraphQLGrade.TENTH],
      },
    },
    {
      __typename: 'ProgramRegistration' as const,
      id: '2',
      title: 'Advanced Registration',
      priceInBaseDenomination: 25000,
      currencyCode: 'USD',
      description: 'Advanced level registration with extended benefits',
      startDate: '2024-02-01',
      endDate: '2024-11-30',
      timeZoneIdentifier: 'America/Chicago',
      maxCapacity: 50,
      isWaitlistEnabled: true,
      eligibility: {
        __typename: 'RegistrationEligibilityOutput' as const,
        birthDateFrom: '1998-01-01',
        birthDateTo: '2008-12-31',
        gender: RegistrationGender.ANY,
        grades: [GraphQLGrade.ELEVENTH, GraphQLGrade.NINTH],
      },
    },
    {
      __typename: 'ProgramRegistration' as const,
      id: '3',
      title: 'Beginner Registration',
      priceInBaseDenomination: 5000,
      currencyCode: 'USD',
      description: 'Entry level registration for new participants',
      startDate: '2024-03-01',
      endDate: '2024-10-31',
      timeZoneIdentifier: 'America/Chicago',
      maxCapacity: 200,
      isWaitlistEnabled: false,
      eligibility: {
        __typename: 'RegistrationEligibilityOutput' as const,
        birthDateFrom: '2000-01-01',
        birthDateTo: '2016-12-31',
        gender: RegistrationGender.ANY,
        grades: [GraphQLGrade.SIXTH, GraphQLGrade.SEVENTH, GraphQLGrade.EIGHTH, GraphQLGrade.NINTH],
      },
    },
    {
      __typename: 'ProgramRegistration' as const,
      id: '4',
      title: 'Free Trial Registration',
      priceInBaseDenomination: 0,
      currencyCode: 'USD',
      description: 'Free trial registration with limited access',
      startDate: '2024-04-01',
      endDate: '2024-09-30',
      timeZoneIdentifier: 'America/Chicago',
      maxCapacity: 75,
      isWaitlistEnabled: true,
      eligibility: {
        __typename: 'RegistrationEligibilityOutput' as const,
        birthDateFrom: '1999-01-01',
        birthDateTo: '2014-12-31',
        gender: RegistrationGender.ANY,
        grades: [GraphQLGrade.SEVENTH, GraphQLGrade.EIGHTH, GraphQLGrade.NINTH],
      },
    },
  ],
};

const mockProgramTwo = {
  __typename: 'OrganizationProgram' as const,
  id: '1',
  orgId: '1',
  state: ProgramState.PUBLISHED,
  timeZoneIdentifier: 'America/Chicago',
  feeResponsibility: FeeResponsibility.ORGANIZATION,
  title: 'Test Program',
  description: 'Test Description',
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  type: {
    __typename: 'EmbeddedOrganizationProgramType' as const,
    id: '1',
    name: 'Tryout',
  },
  registrationsForProgram: [
    {
      __typename: 'ProgramRegistration' as const,
      id: '1',
      title: 'Registration 1',
      priceInBaseDenomination: 100,
      currencyCode: 'USD',
      description: 'Test Registration',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      timeZoneIdentifier: 'America/Chicago',
      maxCapacity: 100,
      isWaitlistEnabled: false,
      eligibility: {
        __typename: 'RegistrationEligibilityOutput' as const,
        birthDateFrom: '2000-01-01',
        birthDateTo: '2010-12-31',
        gender: RegistrationGender.ANY,
        grades: [GraphQLGrade.NINTH, GraphQLGrade.TENTH],
      },
    },
    {
      __typename: 'ProgramRegistration' as const,
      id: '2',
      title: 'Advanced Registration',
      priceInBaseDenomination: 25000,
      currencyCode: 'USD',
      description: 'Advanced level registration with extended benefits',
      startDate: '2024-02-01',
      endDate: '2024-11-30',
      timeZoneIdentifier: 'America/Chicago',
      maxCapacity: 50,
      isWaitlistEnabled: true,
      eligibility: {
        __typename: 'RegistrationEligibilityOutput' as const,
        birthDateFrom: '1998-01-01',
        birthDateTo: '2008-12-31',
        gender: RegistrationGender.ANY,
        grades: [GraphQLGrade.ELEVENTH, GraphQLGrade.NINTH],
      },
    },
    {
      __typename: 'ProgramRegistration' as const,
      id: '3',
      title: 'Beginner Registration',
      priceInBaseDenomination: 5000,
      currencyCode: 'USD',
      description: 'Entry level registration for new participants',
      startDate: '2024-03-01',
      endDate: '2024-10-31',
      timeZoneIdentifier: 'America/Chicago',
      maxCapacity: 200,
      isWaitlistEnabled: false,
      eligibility: {
        __typename: 'RegistrationEligibilityOutput' as const,
        birthDateFrom: '2000-01-01',
        birthDateTo: '2016-12-31',
        gender: RegistrationGender.ANY,
        grades: [GraphQLGrade.SIXTH, GraphQLGrade.SEVENTH, GraphQLGrade.EIGHTH],
      },
    },
    {
      __typename: 'ProgramRegistration' as const,
      id: '4',
      title: 'Free Trial Registration',
      priceInBaseDenomination: 0,
      currencyCode: 'USD',
      description: 'Free trial registration with limited access',
      startDate: '2024-04-01',
      endDate: '2024-09-30',
      timeZoneIdentifier: 'America/Chicago',
      maxCapacity: 75,
      isWaitlistEnabled: true,
      eligibility: {
        __typename: 'RegistrationEligibilityOutput' as const,
        birthDateFrom: '1999-01-01',
        birthDateTo: '2014-12-31',
        gender: RegistrationGender.ANY,
        grades: [GraphQLGrade.SEVENTH, GraphQLGrade.EIGHTH, GraphQLGrade.NINTH],
      },
    },
  ],
};

describe('OrgProgramsList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useOrgPrograms as any).mockReturnValue([
      {
        userId: 'user123',
        program: mockProgram,
        wizard: {
          goToStep: mockGoToStep,
        },
      },
      mockSetOrgProgramsData,
    ]);
  });

  const renderWithContext = (ui: React.JSX.Element) => {
    return render(<OrgProgramsProvider>{ui}</OrgProgramsProvider>);
  };

  it('displays program header when showHeader is true', () => {
    renderWithContext(
      <OrgProgramsList showHeader program={mockProgram} isAuthenticated={true} athletes={mockAthletes} />
    );

    expect(screen.getByText('Test Program')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    expect(screen.getByText('Tryout')).toBeInTheDocument();
  });

  it('does not display program header when showHeader is false', () => {
    renderWithContext(
      <OrgProgramsList showHeader={false} program={mockProgram} isAuthenticated={true} athletes={mockAthletes} />
    );

    expect(screen.queryByText('Test Program')).not.toBeInTheDocument();
  });

  it('displays status card when isAuthenticated is false', () => {
    renderWithContext(
      <OrgProgramsList showHeader program={mockProgram} isAuthenticated={false} athletes={mockAthletes} />
    );
    expect(screen.getByText('Log In')).toBeInTheDocument();
  });

  it('displays registrations section with correct title and description', () => {
    renderWithContext(<OrgProgramsList program={mockProgram} isAuthenticated={true} athletes={mockAthletes} />);

    expect(screen.getByText('Registrations')).toBeInTheDocument();
  });

  it('displays registration details with dates and prices', () => {
    renderWithContext(<OrgProgramsList program={mockProgram} isAuthenticated={true} athletes={mockAthletes} />);

    expect(screen.getByText('Registration 1')).toBeInTheDocument();
    expect(screen.getByText('Advanced Registration')).toBeInTheDocument();
    expect(screen.getByText('Beginner Registration')).toBeInTheDocument();
    expect(screen.getByText('Free Trial Registration')).toBeInTheDocument();
    expect(screen.getAllByText('$1.00')).toHaveLength(1);
    expect(screen.getAllByText('$250.00')).toHaveLength(1);
    expect(screen.getAllByText('$50.00')).toHaveLength(1);
    expect(screen.getAllByText('FREE')).toHaveLength(1);
  });

  it('displays registrations in alphabetical order by title when eligible for all registrations', () => {
    renderWithContext(<OrgProgramsList program={mockProgram} isAuthenticated={true} athletes={mockAthletes} />);

    const registrationTitles = screen.getAllByText(
      /^(Advanced Registration|Beginner Registration|Free Trial Registration|Registration 1)$/
    );

    const titleTexts = registrationTitles.map((title) => title.textContent);

    expect(titleTexts).toEqual([
      'Advanced Registration',
      'Beginner Registration',
      'Free Trial Registration',
      'Registration 1',
    ]);
  });

  it('displays eligible registrations first in alphabetical order by title and then ineligible registrations in alphabetical order', () => {
    renderWithContext(<OrgProgramsList program={mockProgramTwo} isAuthenticated={true} athletes={mockAthletes} />);

    const registrationTitles = screen.getAllByText(
      /^(Advanced Registration|Beginner Registration|Free Trial Registration|Registration 1)$/
    );

    const titleTexts = registrationTitles.map((title) => title.textContent);

    expect(titleTexts).toEqual([
      'Advanced Registration',
      'Free Trial Registration',
      'Registration 1',
      'Beginner Registration',
    ]);
  });

  // TODO: Add test for no eligible athletes message for second registration when we have a real program
});

describe('isAthleteEligible', () => {
  it('returns true if athlete is eligible', () => {
    const athlete = mockAthletes[0];
    const registration = mapProgramRegistrationToRegistration('1', mockProgram.registrationsForProgram[0]);
    expect(isAthleteEligible(athlete, registration)).toBe(true);
  });
});
