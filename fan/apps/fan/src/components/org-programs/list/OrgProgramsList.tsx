import React, { useCallback, useEffect, useState } from 'react';

import { useIntl } from 'react-intl';

import { Text } from '@hudl/uniform-web';
import { useFormat } from 'frontends-i18n';

import { isAthleteEligible } from '../../../utils/athleteEligibilityUtils';
import { formatDateLocalized } from '../../../utils/formatDateLocalizedUtil';
import { mapProgramRegistrationToRegistration } from '../../../utils/mappingRegistrationUtils';
import { OrgProgramsHeader } from '../header/OrgProgramsHeader';
import { useOrgPrograms } from '../OrgProgramsContext';
import RegistrationCard from '../RegistrationCard/RegistrationCard';
import { Athlete } from '../types/athlete';
import { Registration } from '../types/registrations';
import { WebFanQueryProgramByIdR1Query } from '@/graphql/public/graphqlTypes';

import styles from './OrgProgramsList.module.scss';

//temporary athlete interface for testing purposes, 404 implementation needed for getting athlete from user

interface OrgProgramsListProps {
  showHeader?: boolean;
  program: WebFanQueryProgramByIdR1Query['school']['organizationProgram'];
  isAuthenticated: boolean;

  //temporary athletes for testing purposes, 404 implementation needed for getting athlete from user
  athletes: Athlete[];
}

const OrgProgramsList = ({ showHeader, program, isAuthenticated, athletes }: OrgProgramsListProps) => {
  const [orgProgramsData, setOrgProgramsData] = useOrgPrograms();
  const [sortedRegistrations, setSortedRegistrations] = useState<
    Array<{
      registrationStatus: 'eligible' | 'ineligible';
      registration: Registration;
    }>
  >([]);
  // TODO: Note for 404: add 'incomplete-athletes' status when athletes show as registered/complete
  const accountAthleteStatus = orgProgramsData.user?.athletes?.length ? 'athletes' : 'no-athletes';

  const userLocale = useIntl().locale;
  const format = useFormat();

  const onRegister = useCallback(
    (props: {
      registrationId: string;
      eligibleAthletes: Athlete[];
      amount: number;
      currency: string;
      country: string;
    }) => {
      const { registrationId, eligibleAthletes, amount, currency, country } = props;
      setOrgProgramsData((prev) => ({
        ...prev,
        checkout: {
          selectedRegistration: prev.program?.registrationsForProgram.find((r) => r.id === registrationId),
          eligibleAthletes: eligibleAthletes,
          paymentData: {
            amountInBaseDenomination: amount,
            currency,
            country,
          },
        },
        wizard: {
          ...prev.wizard,
          isCurrentStepValid: true,
          processingStatus: 'success',
        },
      }));
      orgProgramsData.wizard.goToStep('athlete-selection');
    },
    [orgProgramsData]
  );

  //In the future, add 'registered' as a status as well.

  useEffect(() => {
    if (isAuthenticated && athletes) {
      const eligibleRegistrations = program.registrationsForProgram
        .filter((registration) => {
          return athletes.some((athlete) =>
            isAthleteEligible(athlete, mapProgramRegistrationToRegistration(program.id, registration))
          );
        })
        .sort((a, b) => {
          return a.title.localeCompare(b.title);
        })
        .map((registration) => ({
          registrationStatus: 'eligible' as const,
          registration: mapProgramRegistrationToRegistration(program.id, registration),
        }));

      const ineligibleRegistrations = program.registrationsForProgram
        .filter((registration) => {
          return !eligibleRegistrations.some((eligible) => eligible.registration.id === registration.id);
        })
        .sort((a, b) => {
          return a.title.localeCompare(b.title);
        })
        .map((registration) => ({
          registrationStatus: 'ineligible' as const,
          registration: mapProgramRegistrationToRegistration(program.id, registration),
        }));

      setSortedRegistrations([...eligibleRegistrations, ...ineligibleRegistrations]);
    } else {
      setSortedRegistrations(
        program.registrationsForProgram
          .map((registration) => mapProgramRegistrationToRegistration(program.id, registration))
          .sort((a, b) => {
            return a.title.localeCompare(b.title);
          })
          .map((registration) => ({
            registrationStatus: 'ineligible' as const,
            registration,
          }))
      );
    }
  }, [isAuthenticated, orgProgramsData.program, athletes, program.registrationsForProgram]);
  return (
    <>
      {showHeader && (
        <OrgProgramsHeader
          isAuthenticated={isAuthenticated}
          accountAthleteStatus={isAuthenticated ? accountAthleteStatus : undefined}
          title={program.title}
          headerInfo={[
            program.type.name,
            <strong>{`${formatDateLocalized(program.startDate, userLocale)} - ${formatDateLocalized(program.endDate, userLocale)}`}</strong>,
          ]}
          description={program.description}
        >
          <Text>{program.description}</Text>
        </OrgProgramsHeader>
      )}
      <div className={styles.registrationsSection}>
        <div className={styles.registrationsSectionHeader}>
          <Text size="default">{format('fan.org-programs.list.registrations')}</Text>
        </div>
        <div className={styles.registrationList}>
          {sortedRegistrations.map(({ registrationStatus, registration }) => (
            <RegistrationCard
              key={registration.id}
              registration={registration}
              status={registrationStatus}
              athletes={athletes}
              onRegister={onRegister}
            />
          ))}
        </div>
      </div>
    </>
  );
};

export { OrgProgramsList };
