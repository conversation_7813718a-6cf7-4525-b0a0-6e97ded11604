import React from 'react';

import { Meta, StoryObj } from '@storybook/react-vite';

import { OrgProgramsContext } from '../OrgProgramsContext';
import { Athlete } from '../types/athlete';
import { GenderType, Grade as LocalGrade } from '../types/registrations';
import { OrgProgramsList } from './OrgProgramsList';
import { FeeResponsibility, Grade as GqlGrade, ProgramState, RegistrationGender } from '@/graphql/public/graphqlTypes';

const meta = {
  title: 'Org Programs/OrgProgramsList',
  component: OrgProgramsList,
} as Meta<typeof OrgProgramsList>;

export default meta;

const mockProgram = {
  id: 'prog1',
  orgId: 'org1',
  state: ProgramState.PUBLISHED,
  title: 'Soccer Program',
  type: { name: 'Club' },
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  description: 'A great soccer program for all ages.',
  timeZoneIdentifier: 'America/New_York',
  feeResponsibility: FeeResponsibility.ORGANIZATION,
  registrationsForProgram: [
    {
      id: 'reg1',
      title: 'Registration 1',
      priceInBaseDenomination: 5000,
      startDate: '2024-01-01',
      endDate: '2024-06-30',
      currencyCode: 'USD',
      description: 'Registration 1 description',
      timeZoneIdentifier: 'America/New_York',
      maxCapacity: 100,
      isWaitlistEnabled: false,
      eligibility: {
        birthDateFrom: '2024-01-01',
        birthDateTo: '2024-12-31',
        gender: RegistrationGender.MALE,
        grades: [GqlGrade.KINDERGARTEN],
      },
    },
    {
      id: 'reg2',
      title: 'Registration 2',
      priceInBaseDenomination: 7000,
      startDate: '2024-07-01',
      endDate: '2024-12-31',
      currencyCode: 'USD',
      description: 'Registration 2 description',
      timeZoneIdentifier: 'America/New_York',
      maxCapacity: 100,
      isWaitlistEnabled: false,
      eligibility: {
        birthDateFrom: '2013-01-01',
        birthDateTo: '2015-12-31',
        gender: RegistrationGender.FEMALE,
        grades: [GqlGrade.KINDERGARTEN],
      },
    },
  ],
};

// Add mock athletes data
const mockAthletes: Athlete[] = [
  {
    id: 'athlete1',
    firstName: 'John',
    lastName: 'Doe',
    birthdate: '2015-06-15',
    gender: GenderType.Male,
    grade: LocalGrade.Kindergarten,
  },
  {
    id: 'athlete2',
    firstName: 'Jane',
    lastName: 'Smith',
    birthdate: '2014-03-22',
    gender: GenderType.Female,
    grade: LocalGrade.Kindergarten,
  },
  {
    id: 'athlete3',
    firstName: 'Jim',
    lastName: 'Beam',
    birthdate: '2013-06-15',
    gender: GenderType.Male,
    grade: LocalGrade.Kindergarten,
  },
];

const mockContextValue = [
  {
    userId: 'user1',
    program: mockProgram,
    wizard: { goToStep: () => {}, isCurrentStepValid: true },
    checkout: {},
  },
  () => {},
];

export const Default: StoryObj<typeof OrgProgramsList> = {
  render: () => {
    return (
      <OrgProgramsContext.Provider value={mockContextValue as any}>
        <OrgProgramsList program={mockProgram} isAuthenticated={true} athletes={mockAthletes} />
      </OrgProgramsContext.Provider>
    );
  },
  args: {
    showHeader: true,
    program: mockProgram,
    isAuthenticated: true,
    athletes: mockAthletes,
  },
};
