import React from 'react';

import { Divider, Text } from '@hudl/uniform-web';
import { useFormat } from 'frontends-i18n';

import { useOrgPrograms } from '../OrgProgramsContext';
import { getFormattedPrice } from '@/utils/numberFormatterUtils';

import styles from './OrderSummary.module.scss';

const OrderSummary = () => {
  const [orgProgramData] = useOrgPrograms();
  const format = useFormat();
  // TODO: Implement fee calculation/query
  // EX: const computedFee = priceInBaseDenomination * 0.0299 + 30;

  return (
    <section className={styles.section}>
      <div className={styles.orderSummary}>
        <Text size="large" className={styles.orderSummaryTitle} qaId="order-summary-title">
          {format('fan.org-programs.order-summary.title')}
        </Text>
        <div className={styles.orderSummaryHeader}>
          <Text color="contrast" qaId="order-summary-program-title">
            {orgProgramData.program?.title ?? ''}
          </Text>
          <Text color="contrast" qaId="order-summary-registration-title">
            {orgProgramData.checkout?.selectedRegistration?.title ?? ''}
          </Text>
          <Text color="contrast" qaId="order-summary-athlete-name">
            {orgProgramData.checkout?.selectedAthlete?.firstName ?? ''}{' '}
            {orgProgramData.checkout?.selectedAthlete?.lastName ?? ''}
          </Text>
        </div>

        <div className={styles.orderTotal}>
          <div className={`${styles.orderTotalTitle} ${styles.orderLineItem}`} data-qa-id="order-summary-total">
            <Text size="large" color="contrast">
              {format('fan.org-programs.order-summary.total')}
            </Text>
            <Text size="large" color="contrast">
              {getFormattedPrice(orgProgramData.checkout?.selectedRegistration?.priceInBaseDenomination ?? 0)}
            </Text>
          </div>
          <div className={styles.orderLineItems}>
            <div className={styles.orderLineItem} data-qa-id="order-summary-registration-total">
              <Text>{format('fan.org-programs.order-summary.registration-total')}</Text>
              <Text>
                {getFormattedPrice(orgProgramData.checkout?.selectedRegistration?.priceInBaseDenomination ?? 0)}
              </Text>
            </div>
            <div className={styles.orderLineItem} data-qa-id="order-summary-transaction-fee">
              <Text>{format('fan.org-programs.order-summary.transaction-fee')}</Text>
              <Text>{getFormattedPrice(0)}</Text>
            </div>
          </div>
        </div>
      </div>

      <Divider color="level3-accent" />
    </section>
  );
};

export { OrderSummary };
