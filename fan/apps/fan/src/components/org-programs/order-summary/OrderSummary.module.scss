@use '../../../shared/commonStyles.module.scss' as CommonStyles;

.section {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one-and-three-quarter);
}

.orderSummary {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one-and-quarter);
}

.orderSummaryTitle {
  font-weight: var(--u-font-weight-bold);
}

.orderSummaryHeader {
  display: inline;

  > * {
    display: inline;
  }

  > *:not(:last-child)::after {
    content: '·';
    padding: 0 var(--u-space-half);
    font-size: var(--u-font-size-default);
  }
}

.orderTotalTitle * {
  font-weight: var(--u-font-weight-bold);
}

.orderLineItems {
  display: flex;
  flex-direction: column;
}

.orderLineItem {
  display: flex;
  justify-content: space-between;
}

.orderLineItemDescription {
  display: flex;
  gap: var(--u-space-quarter);
  align-items: center;
}
