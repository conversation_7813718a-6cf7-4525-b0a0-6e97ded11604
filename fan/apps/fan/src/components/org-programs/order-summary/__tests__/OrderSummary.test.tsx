import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';

import { OrgProgramsProvider, useOrgPrograms } from '../../OrgProgramsContext';
import { OrderSummary } from '../OrderSummary';
import { getFormattedPrice } from '@/utils/numberFormatterUtils';

// Mock the getFormattedPrice utility
vi.mock('@/utils/numberFormatterUtils', () => ({
  getFormattedPrice: vi.fn((price) => `$${price.toFixed(2)}`),
}));

// Mock the OrgProgramsContext
vi.mock('../../OrgProgramsContext', () => ({
  OrgProgramsProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useOrgPrograms: vi.fn(),
}));

// Mock the useFormat hook
vi.mock('frontends-i18n', () => ({
  useFormat: () => (key: string) => {
    const translations: Record<string, string> = {
      'fan.org-programs.order-summary.title': 'Order Summary',
      'fan.org-programs.order-summary.total': 'Total',
      'fan.org-programs.order-summary.registration-total': 'Registration Total',
      'fan.org-programs.order-summary.transaction-fee': 'Transaction Fee',
    };
    return translations[key] || key;
  },
}));

const createMockOrgProgramsData = (overrides = {}) => ({
  program: {
    title: 'Test Program',
  },
  checkout: {
    selectedAthlete: {
      firstName: 'John',
      lastName: 'Doe',
      grade: 'NINTH',
    },
    selectedRegistration: {
      title: 'Test Registration',
      priceInBaseDenomination: 100,
    },
  },
  ...overrides,
});

describe('OrderSummary', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderWithContext = (ui: React.JSX.Element) => {
    return render(<OrgProgramsProvider>{ui}</OrgProgramsProvider>);
  };

  it('displays complete order information when all data is available', () => {
    (useOrgPrograms as any).mockReturnValue([createMockOrgProgramsData()]);
    renderWithContext(<OrderSummary />);

    expect(screen.getByText('Order Summary')).toBeInTheDocument();
    expect(screen.getByText('Test Program')).toBeInTheDocument();
    expect(screen.getByText('Test Registration')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Total')).toBeInTheDocument();
    expect(screen.getByText('Registration Total')).toBeInTheDocument();
    expect(screen.getAllByText('$100.00')).toHaveLength(2);
    expect(screen.getByText('Transaction Fee')).toBeInTheDocument();
    expect(screen.getByText('$0.00')).toBeInTheDocument();
  });

  it('displays nothing for athlete name when athlete name is missing', () => {
    (useOrgPrograms as any).mockReturnValue([
      createMockOrgProgramsData({
        checkout: {
          selectedRegistration: {
            title: 'Test Registration',
            priceInBaseDenomination: 100,
          },
        },
      }),
    ]);
    renderWithContext(<OrderSummary />);

    expect(screen.getByText('Test Program')).toBeInTheDocument();
    expect(screen.getByText('Test Registration')).toBeInTheDocument();
    expect(screen.getByTestId('order-summary-athlete-name').textContent).toBe(' ');
  });

  it('displays undefined for registration title and zero price when registration data is missing', () => {
    (useOrgPrograms as any).mockReturnValue([
      createMockOrgProgramsData({
        checkout: {
          selectedAthlete: {
            firstName: 'John',
            lastName: 'Doe',
            grade: 'NINTH',
          },
        },
      }),
    ]);
    renderWithContext(<OrderSummary />);

    expect(screen.getByText('Test Program')).toBeInTheDocument();
    expect(screen.getByTestId('order-summary-registration-title')).toBeEmptyDOMElement();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getAllByText('$0.00')).toHaveLength(3);
  });

  it('displays nothing for program title when program data is missing', () => {
    (useOrgPrograms as any).mockReturnValue([
      createMockOrgProgramsData({
        program: undefined,
        checkout: {
          selectedAthlete: {
            firstName: 'John',
            lastName: 'Doe',
            grade: 'NINTH',
          },
          selectedRegistration: {
            title: 'Test Registration',
            priceInBaseDenomination: 100,
          },
        },
      }),
    ]);
    renderWithContext(<OrderSummary />);

    expect(screen.getByTestId('order-summary-program-title')).toBeEmptyDOMElement();
    expect(screen.getByText('Test Registration')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getAllByText('$100.00')).toHaveLength(2);
  });

  it('displays nothing for all fields and zero price when checkout data is missing', () => {
    (useOrgPrograms as any).mockReturnValue([
      createMockOrgProgramsData({
        checkout: undefined,
      }),
    ]);
    renderWithContext(<OrderSummary />);

    expect(screen.getByText('Test Program')).toBeInTheDocument();
    expect(screen.queryByText('undefined')).toBeNull();
    expect(screen.queryByText('undefined')).toBeNull();
    expect(screen.getAllByText('$0.00')).toHaveLength(3);
  });

  it('formats price correctly', () => {
    (useOrgPrograms as any).mockReturnValue([
      createMockOrgProgramsData({
        checkout: {
          selectedRegistration: {
            priceInBaseDenomination: 1234.56,
          },
        },
      }),
    ]);
    renderWithContext(<OrderSummary />);

    expect(getFormattedPrice).toHaveBeenCalledWith(1234.56);
    expect(screen.getAllByText('$1234.56')).toHaveLength(2);
  });

  it('handles zero price correctly', () => {
    (useOrgPrograms as any).mockReturnValue([
      createMockOrgProgramsData({
        checkout: {
          selectedRegistration: {
            priceInBaseDenomination: 0,
          },
        },
      }),
    ]);
    renderWithContext(<OrderSummary />);

    expect(getFormattedPrice).toHaveBeenCalledWith(0);
    expect(screen.getAllByText('$0.00')).toHaveLength(3);
  });
});
