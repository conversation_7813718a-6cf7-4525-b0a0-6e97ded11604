import React, { useLayoutEffect, useRef, useState } from 'react';

import { Text, Title } from '@hudl/uniform-web';
import { useFormat } from 'frontends-i18n';

import { useWindowWidth } from '../../../utils/getWindowWidthUtil';
import { useOrgPrograms } from '../OrgProgramsContext';
import { StatusCard } from '../status-card/StatusCard';
import { type accountAthleteStatus } from '../types/accountAthleteStatus';

import styles from './OrgProgramsHeader.module.scss';

export interface OrgProgramsHeaderProps extends React.PropsWithChildren {
  isAuthenticated: boolean;
  accountAthleteStatus?: accountAthleteStatus;
  title: string;
  headerInfo?: React.ReactNode[];
  description?: string;
}

const OrgProgramsHeader = ({
  isAuthenticated,
  accountAthleteStatus,
  title,
  headerInfo,
  description,
}: OrgProgramsHeaderProps) => {
  const format = useFormat();
  const [orgProgramsData, setOrgProgramsData] = useOrgPrograms();
  const windowWidth = useWindowWidth();

  // TODO: Add auth status from 404
  const headerInfoArray = headerInfo ? (Array.isArray(headerInfo) ? headerInfo : [headerInfo]) : [];
  const [isExpanded, setIsExpanded] = useState(false);
  const [hasOverflow, setHasOverflow] = useState(false);
  const descriptionRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (descriptionRef.current && !isExpanded) {
      const element = descriptionRef.current;

      const fullHeight = element.scrollHeight;

      // Compare with current height to detect overflow
      const currentHeight = element.clientHeight;

      const tolerance = 2.5;

      setHasOverflow(fullHeight > currentHeight + tolerance);
    } else if (descriptionRef.current && isExpanded) {
      const element = descriptionRef.current;

      const maxHeight = parseInt(window.getComputedStyle(element).maxHeight, 10);

      // Compare with current height to detect overflow
      const currentHeight = element.clientHeight;

      if (maxHeight === currentHeight) {
        setHasOverflow(false);
        setIsExpanded(false);
      }
    }
  }, [description, windowWidth, isExpanded]);

  return (
    <div className={styles.eventHeader} data-qa-id="org-programs-header">
      {isAuthenticated ? <StatusCard accountAthleteStatus={accountAthleteStatus} /> : <StatusCard />}
      <div className={styles.eventHeaderContent}>
        {title && (
          <Title as="h2" size="xxlarge" className={styles.title} data-qa-id="org-programs-header-title">
            {title}
          </Title>
        )}
        {headerInfoArray.length > 0 && (
          <div className={styles.dotDelimitedContent}>
            {headerInfoArray.map((info, index) => (
              <Text key={index} size="small" color="subtle">
                {info}
              </Text>
            ))}
          </div>
        )}
        {description && (
          <div className={styles.descriptionContainer} data-qa-id="org-programs-header-description">
            <div ref={descriptionRef} className={`${styles.description} ${isExpanded ? styles.expanded : ''}`}>
              <Text size="small" color="subtle">
                {description}
              </Text>
            </div>
            {hasOverflow && (
              <button
                className={styles.readMoreButton}
                onClick={() => setIsExpanded(!isExpanded)}
                data-qa-id="org-programs-header-read-more-button"
              >
                {isExpanded
                  ? format('fan.org-programs.header.read-less-button')
                  : format('fan.org-programs.header.read-more-button')}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export { OrgProgramsHeader };
