import React from 'react';

import { Meta, StoryObj } from '@storybook/react-vite';

import { OrgProgramsHeader, OrgProgramsHeaderProps } from './OrgProgramsHeader';

export default {
  component: OrgProgramsHeader,
  title: 'Org Programs/OrgProgramsHeader',
} as Meta<OrgProgramsHeaderProps>;

export const Header: StoryObj<OrgProgramsHeaderProps> = {
  render: (args) => {
    const accountAthleteStatus = args.isAuthenticated ? args.accountAthleteStatus : undefined;
    return <OrgProgramsHeader {...args} accountAthleteStatus={accountAthleteStatus} />;
  },
  args: {
    isAuthenticated: true,
    title: 'Sample Program Title',
    headerInfo: ['Soccer', <strong key="date">Jan 1, 2024 - Dec 31, 2024</strong>],
    description: 'This is a description of the program.',
    accountAthleteStatus: 'athletes',
  },
};
