import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';

import { OrgProgramsProvider, useOrgPrograms } from '../../OrgProgramsContext';
import { OrgProgramsHeader } from '../OrgProgramsHeader';
import { FeeResponsibility, Grade, ProgramState, RegistrationGender } from '@/graphql/public/graphqlTypes';

function mockFormat(key: string): string {
  const translations: Record<string, string> = {
    'fan.org-programs.header.read-more': 'read more',
    'fan.org-programs.header.read-less': 'read less',
    'fan.org-programs.status-card.unauthenticated.log-in': 'Log In',
    'fan.org-programs.status-card.incomplete-athletes.title': 'Add athlete information to view eligible registrations.',
    'fan.org-programs.status-card.no-athletes.title': 'Add your athletes to get started.',
  };
  return translations[key] || key;
}

vi.mock('../../OrgProgramsContext', () => ({
  OrgProgramsProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useOrgPrograms: vi.fn(),
}));

vi.mock('date-fns', () => ({
  format: () => 'Jan 1, 2024',
}));

vi.mock('frontends-i18n', () => ({
  useFormat: () => mockFormat,
}));

describe('OrgProgramsHeader', () => {
  const mockSetOrgProgramsData = vi.fn();

  const mockGoToStep = vi.fn();
  const mockProgramOne = {
    __typename: 'OrganizationProgram' as const,
    id: '1',
    orgId: '1',
    state: ProgramState.PUBLISHED,
    timeZoneIdentifier: 'America/Chicago',
    feeResponsibility: FeeResponsibility.ORGANIZATION,
    title: 'Test Program',
    description: 'Test Description',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    type: {
      __typename: 'EmbeddedOrganizationProgramType' as const,
      id: '1',
      name: 'Tryout',
    },
    registrationsForProgram: [
      {
        __typename: 'ProgramRegistration' as const,
        id: '1',
        title: 'Registration 1',
        priceInBaseDenomination: 100,
        currencyCode: 'USD',
        description: 'Test Registration',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        timeZoneIdentifier: 'America/Chicago',
        maxCapacity: 100,
        isWaitlistEnabled: false,
        eligibility: {
          __typename: 'RegistrationEligibilityOutput' as const,
          birthDateFrom: '2000-01-01',
          birthDateTo: '2010-12-31',
          gender: RegistrationGender.ANY,
          grades: [Grade.NINTH, Grade.TENTH],
        },
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useOrgPrograms as any).mockReturnValue([
      {
        userId: 'user123',
        program: mockProgramOne,
        wizard: {
          goToStep: mockGoToStep,
        },
      },
      mockSetOrgProgramsData,
    ]);
  });

  const renderWithContext = (ui: React.ReactElement) => {
    return render(<OrgProgramsProvider>{ui}</OrgProgramsProvider>);
  };

  it('displays program header', () => {
    renderWithContext(
      <OrgProgramsHeader
        isAuthenticated={true}
        accountAthleteStatus="incomplete-athletes"
        title="Test Program"
        headerInfo={['Tryout', 'Jan 1, 2024 - Dec 31, 2024']}
      />
    );

    expect(screen.getByText('Test Program')).toBeInTheDocument();
    expect(screen.getByText('Tryout')).toBeInTheDocument();
  });

  it('displays the unauthenticated status card when the user is not authenticated', () => {
    renderWithContext(
      <OrgProgramsHeader
        isAuthenticated={false}
        accountAthleteStatus={undefined}
        title="Test Program"
        headerInfo={['Tryout', 'Jan 1, 2024 - Dec 31, 2024']}
      />
    );

    expect(screen.getByText('Log In')).toBeInTheDocument();
  });

  it('does not display the status card when the user is authenticated with athletes', () => {
    renderWithContext(
      <OrgProgramsHeader
        isAuthenticated={true}
        accountAthleteStatus="athletes"
        title="Test Program"
        headerInfo={['Tryout', 'Jan 1, 2024 - Dec 31, 2024']}
      />
    );

    expect(screen.queryByText('Log In')).not.toBeInTheDocument();
    expect(screen.queryByText('Add athlete information to view eligible registrations.')).not.toBeInTheDocument();
    expect(screen.queryByText('Add your athletes to get started.')).not.toBeInTheDocument();
  });
});
