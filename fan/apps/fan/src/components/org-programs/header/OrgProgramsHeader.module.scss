@import '../../../shared/commonStyles.module';

.eventHeader {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-three);
}

.title {
  color: var(--u-color-base-foreground);
  font-style: normal;
  font-weight: var(--u-font-weight-bold);
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.eventHeaderContent {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-half);
}

.dotDelimitedContent {
  display: flex;
  gap: var(--u-space-half);

  > *:not(:last-child)::after {
    content: '·';
    padding-left: var(--u-space-half);
    font-size: var(--u-font-size-small);
  }
}

.description {
  position: relative;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;

  &.expanded {
    display: block;
    overflow: visible;
    max-height: none;
  }
}

.descriptionContainer {
  display: flex;
  flex-wrap: wrap;
  align-self: baseline;
  margin-top: var(--u-space-half);
}

.readMoreButton {
  color: var(--u-color-base-foreground);
  background: none;
  border: none;
  padding: 0;
  font-size: var(--u-font-size-small);
  font-weight: var(--u-font-weight-bold);
  cursor: pointer;
}
