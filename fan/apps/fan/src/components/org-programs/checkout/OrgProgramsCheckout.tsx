import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { useForm } from 'react-hook-form';

import {
  MetadataMap,
  PaymentForm,
  PaymentsElements,
  PaymentsGatewayProvider,
  StripePaymentMethod,
  TransactionLineItem,
  useProcessTransaction,
} from '@hudl/payment-elements';
import { FormModifier, Link, Text } from '@hudl/uniform-web';
import { useFormat, useFormatMessage } from 'frontends-i18n';

import { ControlledField } from '../controlled-field/ControlledField';
import { OrgProgramsHeader } from '../header/OrgProgramsHeader';
import { OrderSummary } from '../order-summary/OrderSummary';
import { useOrgPrograms } from '../OrgProgramsContext';
import { ClubRegistrationTryoutMetadata } from '../types/clubRegistrationTryoutMetadata';
import { FormField } from '../types/formField';
import { useWebFanUpsertRegistrantsR1Mutation } from '@/graphql/public/graphqlTypes';

import styles from './OrgProgramsCheckout.module.scss';

interface OrgProgramsCheckoutFormProps {
  showHeader?: boolean;
  paymentAmount: number;
  currency: string;
  country: string;
}

const OrgProgramsCheckoutForm = ({ showHeader, paymentAmount, currency, country }: OrgProgramsCheckoutFormProps) => {
  const format = useFormat();
  const formatMessage = useFormatMessage();
  const processPayment = useProcessTransaction();
  const [upsertRegistrants] = useWebFanUpsertRegistrantsR1Mutation();
  const [orgProgramsData, setOrgProgramsData] = useOrgPrograms();
  const [paymentFormValid, setPaymentFormValid] = useState(false);

  // TODO: Revisit for appropriate values & cleanup
  const toGQLId = useCallback((id: string, type: string) => Buffer.from(`${type}${id}`, 'utf8').toString('base64'), []);
  const fromGQLId = useCallback(
    (id: string, type: string) => Buffer.from(id, 'base64').toString('utf8').slice(type.length),
    []
  );
  const guardianId = useMemo(() => toGQLId(orgProgramsData?.user?.id ?? '', 'User'), [orgProgramsData?.user?.id]);
  // const installmentPlanId = useMemo(() => toGQLId('682379f2aa7d9091e9ff50c7', 'InstallmentPlan'), []);
  const paymentEntityType = 'HudlOrganization';

  // TODO: Revisit `useForm` once we move away from react-hook-form
  const {
    control,
    handleSubmit,
    formState: { isValid: isCustomFormValid },
    watch,
  } = useForm({
    mode: 'all',
  });

  const handlePaymentFormCompleted = useCallback(
    (value: boolean) => {
      setPaymentFormValid(value);
    },
    [setPaymentFormValid]
  );

  const onSuccess = useCallback((message: string) => {
    setOrgProgramsData((prev) => ({
      ...prev,
      wizard: {
        ...prev.wizard,
        processingStatus: 'success',
      },
    }));
    orgProgramsData.wizard.goToStep('confirmation');
  }, []);

  const onError = useCallback((message: string) => {
    setOrgProgramsData((prev) => ({
      ...prev,
      wizard: {
        ...prev.wizard,
        processingStatus: 'failure',
      },
    }));
  }, []);

  const processRegistrants = useCallback(
    async (formData: any) => {
      const result = await upsertRegistrants({
        variables: {
          input: {
            // TODO: These will potentially get removed with registrants/404 work.
            id: orgProgramsData?.checkout?.upsertedRegistrant?.id,
            firstName: orgProgramsData?.checkout?.selectedAthlete?.firstName ?? '',
            lastName: orgProgramsData?.checkout?.selectedAthlete?.lastName ?? '',
            registrationId: orgProgramsData?.checkout?.selectedRegistration?.id ?? '',
            email: formData['email-address'],
            installmentPlanId: undefined,
            guardianIds: [guardianId],
          },
        },
      });

      const upsertedRegistrant = result?.data?.upsertRegistrants[0];
      if (!upsertedRegistrant) {
        throw new Error(result?.errors?.join('\n') ?? 'Error upserting registrant');
      }
      setOrgProgramsData((prev) => ({
        ...prev,
        checkout: {
          ...prev.checkout,
          upsertedRegistrant,
        },
      }));

      return {
        registrantId: upsertedRegistrant.id,
        transactionId: upsertedRegistrant.installments?.[0]?.transactions?.[0].id,
      };
    },
    [orgProgramsData?.checkout?.upsertedRegistrant, guardianId]
  );

  const onSubmit = useCallback(
    async (data: any) => {
      setOrgProgramsData((prev) => ({
        ...prev,
        wizard: {
          ...prev.wizard,
          processingStatus: 'spinning',
        },
        checkout: {
          ...prev.checkout,
          additionalData: data,
        },
      }));

      try {
        const selectedRegistration = orgProgramsData.checkout?.selectedRegistration;
        if (!selectedRegistration) {
          throw Error('Invalid registration');
        }

        const { registrantId, transactionId } = await processRegistrants(data);
        const metadata: ClubRegistrationTryoutMetadata = {
          ...data, // Form data - not accessible in a type-safe manner
          registrationId: selectedRegistration.id,
          registrantId,
        };
        const result = await processPayment({
          isFormValid: paymentFormValid && isCustomFormValid,
          amountInLowestUnits: paymentAmount,
          currency,
          country,
          paymentDescription: selectedRegistration.title,
          cardholderName: data['cardholder-name'],
          paymentMethodType: StripePaymentMethod.CreditCard,
          lineItems: [
            {
              name: 'OrgProgramRegistrationPayment',
              amount: paymentAmount,
              quantity: 1,
              metadata: MetadataMap.fromObject(metadata),
              internalReferenceId: fromGQLId(transactionId, 'Transaction'),
            },
          ] as TransactionLineItem[],
          multiPartyPaymentOptions: {
            paymentEntityId: orgProgramsData.organizationId,
            paymentEntityType,
            // TODO: Review security of such data being passed from the frontend
            paymentEntityAmountInSmallestCurrencyUnit: Math.round(paymentAmount * 0.9),
          },
        });

        if (!result) {
          return;
        }
        if (result.success) {
          onSuccess(result.message);
        } else {
          onError(result.message);
        }
      } catch (e: any) {
        onError(e.message);
      }
    },
    [paymentFormValid, isCustomFormValid, paymentAmount, currency, country, onSuccess, onError, processRegistrants]
    // Removed useProcessTransaction as it's not properly memoized internally in payment-elements library
  );

  useEffect(() => {
    setOrgProgramsData((prev) => ({
      ...prev,
      wizard: {
        ...prev.wizard,
        isCurrentStepValid: isCustomFormValid && paymentFormValid,
        processingStatus: undefined,
      },
    }));
  }, [isCustomFormValid, paymentFormValid]);

  useEffect(() => {
    setOrgProgramsData((prev) => ({
      ...prev,
      wizard: {
        ...prev.wizard,
        overrideNextStep: handleSubmit(onSubmit),
      },
    }));
  }, [handleSubmit, onSubmit]);

  // TODO: Form fields expected to be removed from here & rendered via custom forms flow
  const paymentInfoFields: FormField[] = useMemo(
    () => [
      {
        name: 'cardholder-name',
        fieldType: 'input',
        fieldProps: {
          label: 'Name on Card',
          placeholder: 'Enter name on card',
          type: 'text',
          helpText: 'Please enter the name on the card.',
          isRequired: true,
        },
        controllerProps: {
          rules: { required: true },
        },
      },
    ],
    []
  );
  const paymentAuthFields: FormField[] = useMemo(
    () => [
      {
        name: 'terms-of-service',
        fieldType: 'checkbox',
        fieldProps: {
          label: format('fan.ticketing.order.checkout.i-agree'),
          qaId: 'terms-of-service-check-box',
        },
        additionalProps: {
          helpText: 'Required',
          wrapperClassName: styles.tosCheckbox,
        },
        controllerProps: {
          defaultValue: false,
          rules: { required: true },
        },
      },
    ],
    [format]
  );

  return (
    <>
      {/* TODO: Currently assuming that the user will always be authenticated with athletes if they are at the checkout page, but checks should be implemented (404) */}
      {showHeader && <OrgProgramsHeader isAuthenticated={true} accountAthleteStatus="athletes" title="Checkout" />}
      <div className={styles.checkout}>
        <OrderSummary />
        <form onSubmit={handleSubmit(onSubmit)} data-qa-id="payment-form" className={styles.checkoutForm}>
          <section className={styles.paymentContainer}>
            <Text size="large">
              <strong>{format('fan.ticketing.order.checkout.payment-information')}</strong>
            </Text>
            <FormModifier templateColumns="1fr" className={styles.paymentFormModifier}>
              {paymentInfoFields.map((formField) => {
                formField.controllerProps.control = control;
                return <ControlledField key={formField.name} {...formField} />;
              })}
              <PaymentForm
                paymentFormComplete={handlePaymentFormCompleted}
                onPaymentMethodChange={() => {}}
                customPaymentMethodOrder={[StripePaymentMethod.CreditCard]}
                // customPaymentFormValidationErrorMessage={customStripePaymentFormValidationErrorMessage}
              />
            </FormModifier>
          </section>
          <div className={styles.tosContainer}>
            <Text color="subtle">
              {formatMessage(
                { id: 'fan.ticketing.order.checkout.read-terms-of-service' },
                {
                  termsOfService: (
                    <Link
                      href="#"
                      qaId="registerations-terms-of-service-link"
                      className={styles.termsOfServiceLink}
                      type="article"
                      isExternal
                    >
                      {format('fan.hudl-registration-purchase-terms')}
                    </Link>
                  ),
                  privacyPolicy: (
                    <Link
                      href="https://www.hudl.com/privacy"
                      qaId="ticketing-privacy-policy-link"
                      className={styles.termsOfServiceLink}
                      type="article"
                      isExternal
                    >
                      {format('fan.privacy-policy')}
                    </Link>
                  ),
                }
              )}
            </Text>
            {paymentAuthFields.map((formField) => {
              formField.controllerProps.control = control;
              return <ControlledField key={formField.name} {...formField} />;
            })}
          </div>
        </form>
      </div>
    </>
  );
};

export const OrgProgramsCheckout = ({ showHeader }: { showHeader?: boolean }) => {
  const [orgProgramsData] = useOrgPrograms();
  const paymentData = orgProgramsData?.checkout?.paymentData;
  if (!paymentData) {
    return null;
  }

  const country = paymentData?.country || 'US';
  const currency = paymentData?.currency || 'USD';
  const amount = paymentData?.amountInBaseDenomination || '0';
  const paymentAmount = Number(amount);

  return (
    <PaymentsGatewayProvider billingCountry={country} isConnect={true}>
      <input type="submit" className={styles.hidden} />
      <PaymentsElements
        paymentAmountInLowestUnit={paymentAmount}
        currency={currency}
        appearance={{
          variables: {
            gridColumnSpacing: '1.25rem',
            gridRowSpacing: '1.25rem',
          },
          rules: {
            '.Label': {
              fontSize: '0.75rem',
              fontWeight: '700',
            },
          },
        }}
      >
        <OrgProgramsCheckoutForm
          country={country}
          currency={currency}
          paymentAmount={paymentAmount}
          showHeader={showHeader}
        />
      </PaymentsElements>
    </PaymentsGatewayProvider>
  );
};
