import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useForm } from 'react-hook-form';
import { vi } from 'vitest';

import { PaymentForm, useProcessTransaction } from '@hudl/payment-elements';

import { OrgProgramsProvider, useOrgPrograms } from '../../OrgProgramsContext';
import { OrgProgramsCheckout } from '../OrgProgramsCheckout';
import { useWebFanUpsertRegistrantsR1Mutation } from '@/graphql/public/graphqlTypes';

// Mock react-hook-form
vi.mock('react-hook-form', () => ({
  useForm: vi.fn().mockReturnValue({
    control: {},
    handleSubmit: (callback: any) => callback,
    formState: { isValid: true },
    watch: vi.fn(),
  }),
  Controller: ({ name, render: renderField }: { name: string; render: any }) =>
    renderField({
      field: {
        onChange: vi.fn(),
        value: '',
        name,
      },
      fieldState: {
        invalid: false,
        error: undefined,
      },
    }),
}));

vi.mock('@hudl/payment-elements', () => ({
  useProcessTransaction: vi.fn(),
  PaymentFlow: {
    MultiPartyPayment: 'MultiPartyPayment',
  },
  StripePaymentMethod: {
    CreditCard: 'CreditCard',
  },
  MetadataMap: {
    fromObject: (obj: any) => obj,
  },
  PaymentsGatewayProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  PaymentForm: ({ paymentFormComplete }: { paymentFormComplete: (value: boolean) => void }) => (
    <div data-testid="payment-form">
      <button onClick={() => paymentFormComplete(true)}>Complete Payment</button>
    </div>
  ),
  PaymentsElements: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock('@/graphql/public/graphqlTypes', () => ({
  useWebFanUpsertRegistrantsR1Mutation: vi.fn(),
}));

vi.mock('../../OrgProgramsContext', () => ({
  OrgProgramsProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useOrgPrograms: vi.fn(),
}));

const mockProcessTransaction = vi.fn();
const mockUpsertRegistrants = vi.fn();

// Mock data
const createMockOrgProgramsData = (overrides = {}) => ({
  user: {
    id: 'user123',
  },
  checkout: {
    paymentData: {
      amountInBaseDenomination: 100,
      currency: 'USD',
      country: 'US',
    },
    selectedRegistration: {
      id: 'reg123',
      title: 'Test Registration',
      priceInBaseDenomination: 100,
      type: {
        name: 'tryout',
      },
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      currencyCode: 'USD',
      description: 'Test registration description',
      timeZoneIdentifier: 'America/New_York',
      maxCapacity: 100,
      isWaitlistEnabled: false,
    },
    selectedAthlete: {
      id: 'athlete123',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      year: 2010,
    },
    upsertedRegistrant: {
      id: '123',
      registrationId: 'reg123',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
  },
  athletes: [
    {
      id: 'athlete123',
      firstName: 'John',
      lastName: 'Doe',
      birthdate: '2010-01-01',
      gender: 'Male',
      grade: 'Ninth',
      email: '<EMAIL>',
    },
  ],
  organizationId: 'org123',
  program: {
    id: 'prog123',
    orgId: 'org123',
    title: 'Test Program',
    type: {
      name: 'tryout',
    },
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: 'Test program description',
    timeZoneIdentifier: 'America/New_York',
  },
  wizard: {
    currentStep: 'checkout',
    goToStep: vi.fn(),
    goToNextStep: vi.fn(),
    isCurrentStepValid: true,
    processingStatus: undefined,
  },
  ...overrides,
});

describe('OrgProgramsCheckout', () => {
  const user = userEvent.setup();
  const mockSetOrgProgramsData = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useProcessTransaction as any).mockReturnValue(mockProcessTransaction);
    (useWebFanUpsertRegistrantsR1Mutation as any).mockReturnValue([mockUpsertRegistrants, { loading: false }]);

    const mockData = createMockOrgProgramsData();
    (useOrgPrograms as any).mockReturnValue([mockData, mockSetOrgProgramsData]);
  });

  const renderWithContext = (ui: React.JSX.Element) => {
    return render(<OrgProgramsProvider>{ui}</OrgProgramsProvider>);
  };

  describe('Rendering', () => {
    it('should not render when payment data is missing', () => {
      (useOrgPrograms as any).mockReturnValueOnce([
        createMockOrgProgramsData({ checkout: {} }),
        mockSetOrgProgramsData,
      ]);

      renderWithContext(<OrgProgramsCheckout />);
      expect(screen.queryByText('fan.ticketing.order.checkout.contact-information')).not.toBeInTheDocument();
    });

    it('should render header when showHeader is true', () => {
      renderWithContext(<OrgProgramsCheckout showHeader />);
      expect(screen.getByText('Checkout')).toBeInTheDocument();
    });
  });

  describe('Payment Processing', () => {
    it('should set up form submission handler when form is valid', async () => {
      renderWithContext(<OrgProgramsCheckout />);

      // Complete payment form
      await user.click(screen.getByText('Complete Payment'));

      // The component uses setState updater functions, so we need to check the calls
      const calls = mockSetOrgProgramsData.mock.calls;

      // Find the call that sets isCurrentStepValid to true
      const validationCall = calls.find((call) => {
        if (typeof call[0] === 'function') {
          const mockPrevState = createMockOrgProgramsData();
          const result = call[0](mockPrevState);
          return result?.wizard?.isCurrentStepValid === true;
        }
        return false;
      });

      expect(validationCall).toBeDefined();

      // Find the call that sets overrideNextStep
      const overrideCall = calls.find((call) => {
        if (typeof call[0] === 'function') {
          const mockPrevState = createMockOrgProgramsData();
          const result = call[0](mockPrevState);
          return result?.wizard?.overrideNextStep !== undefined;
        }
        return false;
      });

      expect(overrideCall).toBeDefined();
    });

    it('should process registrants and payment when form is submitted', async () => {
      // Override useForm mock to return valid form state
      (useForm as any).mockReturnValueOnce({
        control: {},
        handleSubmit: (callback: any) => callback,
        formState: { isValid: true },
        watch: vi.fn(),
      });

      renderWithContext(<OrgProgramsCheckout />);

      // Complete payment form
      await user.click(screen.getByText('Complete Payment'));

      // Wait for state updates to complete
      await vi.waitFor(() => {
        const calls = mockSetOrgProgramsData.mock.calls;
        // Check that the state was updated with paymentFormValid = true
        const validCall = calls.find((call) => {
          if (typeof call[0] === 'function') {
            const mockPrevState = createMockOrgProgramsData();
            const result = call[0](mockPrevState);
            return result?.wizard?.isCurrentStepValid === true;
          }
          return false;
        });
        expect(validCall).toBeDefined();
      });

      // Find the overrideNextStep function from the setState calls
      let overrideNextStep;
      const calls = mockSetOrgProgramsData.mock.calls;
      for (const call of calls) {
        if (typeof call[0] === 'function') {
          const mockPrevState = createMockOrgProgramsData();
          const result = call[0](mockPrevState);
          if (result?.wizard?.overrideNextStep) {
            overrideNextStep = result.wizard.overrideNextStep;
            break;
          }
        }
      }

      expect(overrideNextStep).toBeDefined();

      // Setup mocks for the next steps
      const mockRegistrantId = 'new-registrant-123';
      const mockTransactionId = Buffer.from('Transactiontransaction-123').toString('base64');
      mockUpsertRegistrants.mockResolvedValueOnce({
        data: {
          upsertRegistrants: [
            {
              id: mockRegistrantId,
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              registrationId: 'reg123',
              installments: [
                {
                  transactions: [{ id: mockTransactionId }],
                },
              ],
            },
          ],
        },
      });
      mockProcessTransaction.mockResolvedValueOnce({ success: true, message: 'Payment successful' });

      // Call the callback with form data
      await overrideNextStep({
        'email-address': '<EMAIL>',
        'cardholder-name': 'John Doe',
      });

      // Verify upsertRegistrants was called with correct data from context + form
      expect(mockUpsertRegistrants).toHaveBeenCalledWith({
        variables: {
          input: {
            id: '123',
            firstName: 'John',
            lastName: 'Doe',
            registrationId: 'reg123',
            email: '<EMAIL>',
            installmentPlanId: undefined,
            guardianIds: ['VXNlcnVzZXIxMjM='],
          },
        },
      });

      // Verify the component updated with the new registrant data through setState callback
      const updateCall = mockSetOrgProgramsData.mock.calls.find((call) => {
        if (typeof call[0] === 'function') {
          const mockPrevState = createMockOrgProgramsData();
          const result = call[0](mockPrevState);
          return result?.checkout?.upsertedRegistrant?.id === mockRegistrantId;
        }
        return false;
      });

      expect(updateCall).toBeDefined();

      // Verify payment processing was called with correct data
      // Note: isFormValid is false because we're calling the handler directly without the full component lifecycle
      expect(mockProcessTransaction).toHaveBeenCalledWith({
        isFormValid: false,
        amountInLowestUnits: 100,
        currency: 'USD',
        country: 'US',
        paymentDescription: 'Test Registration',
        cardholderName: 'John Doe',
        paymentMethodType: 'CreditCard',
        lineItems: [
          {
            name: 'OrgProgramRegistrationPayment',
            amount: 100,
            quantity: 1,
            metadata: {
              registrationId: 'reg123',
              registrantId: mockRegistrantId,
              'email-address': '<EMAIL>',
              'cardholder-name': 'John Doe',
            },
            internalReferenceId: 'transaction-123',
          },
        ],
        multiPartyPaymentOptions: {
          paymentEntityId: 'org123',
          paymentEntityType: 'HudlOrganization',
          paymentEntityAmountInSmallestCurrencyUnit: 90,
        },
      });
    });

    it('should not set isCurrentStepValid when form is invalid', async () => {
      // Override useForm mock for this test
      (useForm as any).mockReturnValueOnce({
        control: {},
        handleSubmit: (callback: any) => (e: any) => {
          e?.preventDefault?.();
          return callback({});
        },
        formState: { isValid: false },
        watch: vi.fn(),
      });

      renderWithContext(<OrgProgramsCheckout />);

      // Check that the state was updated with isCurrentStepValid: false
      await vi.waitFor(() => {
        const calls = mockSetOrgProgramsData.mock.calls;

        // Find the call that sets isCurrentStepValid to false
        const invalidCall = calls.find((call) => {
          if (typeof call[0] === 'function') {
            const mockPrevState = createMockOrgProgramsData();
            const result = call[0](mockPrevState);
            return result?.wizard?.isCurrentStepValid === false;
          }
          return false;
        });

        expect(invalidCall).toBeDefined();

        // Also check that overrideNextStep was set
        const overrideCall = calls.find((call) => {
          if (typeof call[0] === 'function') {
            const mockPrevState = createMockOrgProgramsData();
            const result = call[0](mockPrevState);
            return result?.wizard?.overrideNextStep !== undefined;
          }
          return false;
        });

        expect(overrideCall).toBeDefined();
      });
    });
  });
});
