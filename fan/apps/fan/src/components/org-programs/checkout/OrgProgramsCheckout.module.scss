.checkout {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-two);
  padding-bottom: var(--u-space-one);
}

.paymentContainer {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one-and-quarter);
}

.checkoutForm {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one-and-quarter);
}

.paymentFormModifier {
  display: grid; // FormModified removes u-form--grid in mobile view. Forcing it for grid-gap.
  grid-gap: var(--u-space-one-and-quarter) var(--u-space-one-and-quarter);
}

.tosContainer {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-three-quarter);
  justify-content: flex-start;
}

.tosCheckbox {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-quarter);

  label {
    width: fit-content; // So the clickable area is not the entire row

    &::after {
      color: var(--u-color-utility-critical-text);
      content: '*';
      padding-left: var(--u-space-quarter);
    }
  }
}

.hidden {
  display: none;
}
