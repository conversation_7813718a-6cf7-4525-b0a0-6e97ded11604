import React, { useCallback, useEffect, useState } from 'react';

import { Button, ButtonStatus } from '@hudl/uniform-web';
import { useFormat } from 'frontends-i18n';

import { useOrgPrograms } from '../OrgProgramsContext';

import styles from './OrgProgramsFooter.module.scss';

const OrgProgramsFooter = () => {
  const [orgProgramsData, setOrgProgramsData] = useOrgPrograms();
  const [isCurrentStepValid, setIsCurrentStepValid] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<ButtonStatus | null>(null);
  const currentStep = orgProgramsData.wizard.currentStep;

  const format = useFormat();

  const backText = format('fan.org-programs.footer.back');

  const continueText =
    currentStep === 'checkout'
      ? format('fan.org-programs.footer.place-order')
      : format('fan.org-programs.footer.continue');

  const onBackClick = useCallback(() => {
    if (
      orgProgramsData.wizard.currentStep === 'questions' &&
      orgProgramsData.checkout?.eligibleAthletes?.length === 1
    ) {
      setOrgProgramsData((prev) => ({
        ...prev,
        checkout: {
          ...prev.checkout,
          selectedAthlete: undefined,
        },
      }));
      orgProgramsData.wizard.goToPreviousStep?.();
    } else if (orgProgramsData.wizard.currentStep === 'questions') {
      orgProgramsData.wizard.goToPreviousStep?.();
      setOrgProgramsData((prev) => ({
        ...prev,
        wizard: {
          ...prev.wizard,
          isCurrentStepValid: true,
        },
      }));
    } else {
      orgProgramsData.wizard.goToPreviousStep?.();
    }
  }, [orgProgramsData]);

  const onContinueClick = useCallback(() => {
    setOrgProgramsData((prev) => ({
      ...prev,
      wizard: {
        ...prev.wizard,
        previousStep: orgProgramsData.wizard.currentStep,
      },
    }));
    orgProgramsData.wizard.goToNextStep?.();
  }, [orgProgramsData]);

  useEffect(() => {
    if (orgProgramsData.wizard.isCurrentStepValid !== undefined) {
      setIsCurrentStepValid(orgProgramsData.wizard.isCurrentStepValid);
    }
  }, [orgProgramsData, orgProgramsData.wizard.isCurrentStepValid]);

  if (currentStep === 'list' || currentStep === 'confirmation') {
    return null;
  }

  return (
    <div className={styles.footer}>
      <Button buttonType="subtle" size="medium" onPress={onBackClick} isDisabled={processingStatus === 'spinning'}>
        {backText}
      </Button>
      <Button
        buttonType="primary"
        size="medium"
        onPress={onContinueClick}
        isDisabled={!isCurrentStepValid || processingStatus === 'spinning'}
        status={processingStatus || undefined}
      >
        {continueText}
      </Button>
    </div>
  );
};

export { OrgProgramsFooter };
