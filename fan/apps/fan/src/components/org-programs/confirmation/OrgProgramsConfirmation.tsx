import React, { useCallback, useEffect } from 'react';

import { format } from 'date-fns';

import { Button, Text } from '@hudl/uniform-web';

import { OrgProgramsHeader } from '../header/OrgProgramsHeader';
import { OrderSummary } from '../order-summary/OrderSummary';
import { useOrgPrograms } from '../OrgProgramsContext';

import styles from './OrgProgramsConfirmation.module.scss';

interface OrgProgramsConfirmationProps {
  showHeader?: boolean;
}

const OrgProgramsConfirmation = ({ showHeader }: OrgProgramsConfirmationProps) => {
  const dateFormat = 'MMM d, yyyy';
  const [orgProgramsData, setOrgProgramsData] = useOrgPrograms();
  const firstName = orgProgramsData.checkout?.selectedAthlete?.firstName;
  const email = orgProgramsData.checkout?.additionalData?.['email-address'];
  const selectedRegistration = orgProgramsData.checkout?.selectedRegistration;

  if (!selectedRegistration) {
    return <Text>Select a registration.</Text>;
  }

  const { startDate, endDate, title: registrationTitle, description } = selectedRegistration;

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const onReturnClick = useCallback(() => {
    setOrgProgramsData((prev) => ({
      ...prev,
      checkout: {
        ...prev.checkout,
        selectedRegistration: undefined,
        selectedAthlete: undefined,
        paymentData: undefined,
        additionalData: undefined,
      },
      wizard: {
        ...prev.wizard,
        currentStep: 'list',
        processingStatus: undefined,
      },
    }));
  }, [setOrgProgramsData]);

  return (
    <>
      {showHeader && (
        <OrgProgramsHeader isAuthenticated={true} title="Order Confirmation" headerInfo={[]}>
          <Text>
            Thank you for registering {firstName}.{' '}
            <strong>Your payment of has been processed and a confirmation email has been sent to {email}</strong>.
            Please check your inbox for additional details.
          </Text>
          <Button buttonType="primary" size="medium" onPress={onReturnClick} className={styles.returnButton}>
            Return to Program
          </Button>
        </OrgProgramsHeader>
      )}
      <OrderSummary />
      <section className={styles.section}>
        <Text level="large">
          <strong>Event Details</strong>
        </Text>
        <div>
          <div className={styles.dotDelimitedContent}>
            <Text>{orgProgramsData.program?.title}</Text>
            <Text>{registrationTitle}</Text>
          </div>
          <Text>
            {format(new Date(startDate), dateFormat)} - {format(new Date(endDate), dateFormat)}
          </Text>
        </div>
        {description && description.length > 0 && <Text className={styles.eventNotes}>{description}</Text>}
      </section>
    </>
  );
};

export { OrgProgramsConfirmation };
