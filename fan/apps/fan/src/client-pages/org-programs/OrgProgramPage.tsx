import { useCallback, useEffect, useMemo } from 'react';

import { useReactiveVar } from '@apollo/client';
import { useParams } from 'react-router-dom';

import { AvatarOrg, Spinner, Title } from '@hudl/uniform-web';

import { OrgProgramsAthleteSelection } from '../../components/org-programs/athleteSelection/OrgProgramsAthleteSelection';
import { PageNotFound } from '../pageNotFound/PageNotFound';
import { useAuth } from '@/components/auth/HudlAuthContext';
import { OrgProgramsConfirmation, OrgProgramsFooter, OrgProgramsList } from '@/components/org-programs';
import { OrgProgramsCheckout } from '@/components/org-programs/checkout/OrgProgramsCheckout';
import { OrgProgramsForms } from '@/components/org-programs/forms/OrgProgramsForms';
import { mockAthletes } from '@/components/org-programs/mockData/mockData';
import { OrgProgramsProvider, useOrgPrograms } from '@/components/org-programs/OrgProgramsContext';
import { useWebFanQueryProgramByIdR1 } from '@/graphql/public/graphqlTypes';
import { DeviceType } from '@/utils/enums';
import { currentScreenDeviceType } from '@/utils/sharedStateVars';
import { getUserId } from '@/utils/userUtils';

import commonStyles from '../../shared/commonStyles.module.scss';
import styles from './OrgProgramPage.module.scss';

const confirmationIcon = (
  <svg
    width="59"
    height="58"
    viewBox="0 0 59 58"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    data-qa-id="confirmation-icon"
  >
    <circle cx="29.5" cy="29" r="27.5" stroke="#A9C94D" strokeWidth="3" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M27.3552 36.2628C27.6725 36.5291 28.1455 36.4877 28.4118 36.1704L38.2495 24.4463C38.5158 24.129 38.4744 23.6559 38.1571 23.3897L37.008 22.4255C36.6907 22.1592 36.2176 22.2006 35.9514 22.5179L27.5698 32.5067L23.9463 29.4661C23.629 29.1999 23.1559 29.2413 22.8897 29.5586L21.9255 30.7077C21.6592 31.0249 21.7006 31.498 22.0179 31.7643L26.0996 35.1892C26.1307 35.2285 26.1662 35.2652 26.2061 35.2986L27.3552 36.2628Z"
      fill="#A9C94D"
    />
  </svg>
);

export const OrgProgramsPageInternal = () => {
  const { user } = useAuth();
  const { organizationId, programId } = useParams();
  const [orgProgramsData, setOrgProgramsData] = useOrgPrograms();
  const screenDeviceType = useReactiveVar(currentScreenDeviceType);
  const isMobile = screenDeviceType === DeviceType.Mobile;
  const step = orgProgramsData.wizard.currentStep;

  const { data, loading } = useWebFanQueryProgramByIdR1({
    variables: {
      orgId: organizationId!,
      programId: programId!,
    },
  });

  useEffect(() => {
    setOrgProgramsData((prevData) => ({
      ...prevData,
      organizationId,
      user: {
        id: getUserId(user),
        athletes: mockAthletes,
        // Mock athletes being used because registrants assigned athletes will be handled in future work by 404
      },
      program: data?.school?.organizationProgram,
    }));
  }, [organizationId, programId, user, data, setOrgProgramsData]);

  const school = data?.school;

  const renderAvatar = useCallback(() => {
    const size = isMobile ? 'medium' : 'large';
    const qaId = school?.profileImageUri ? 'avatar-profile-image' : 'avatar-abbreviation';

    return (
      <AvatarOrg
        size={size}
        imageUrl={school?.profileImageUri}
        primaryColor={school?.primaryColor || undefined}
        secondaryColor={school?.secondaryColor || undefined}
        initials={school?.abbreviation || undefined}
        qaId={qaId}
      />
    );
  }, [isMobile, school]);

  if (loading) {
    return (
      <div className={commonStyles.spinnerContainer} data-qa-id="tickets-tab-loading-spinner">
        <Spinner size="medium" />
      </div>
    );
  }

  if (!school?.organizationProgram) {
    return <PageNotFound />;
  }

  const isConfirmation = step === 'confirmation';

  return (
    <>
      <div className={styles.pageWrapper}>
        <div className={styles.content}>
          {/* Inspired by PageHeader and PageHeaderInfo components */}
          <div className={styles.headerContainer}>
            {isConfirmation ? confirmationIcon : renderAvatar()}
            <Title size="large" as="h1" className={styles.profileBannerTitle}>
              {isConfirmation ? 'Registration Complete!' : school.fullName}
            </Title>
          </div>
          <section className={`${styles.section}`}>
            {step === 'list' && (
              <OrgProgramsList
                showHeader
                program={school.organizationProgram}
                isAuthenticated={!!orgProgramsData.user?.id}
                athletes={mockAthletes}
              />
            )}
            {step === 'athlete-selection' && <OrgProgramsAthleteSelection />}
            {step === 'questions' && <OrgProgramsForms />}
            {step === 'checkout' && <OrgProgramsCheckout showHeader />}
            {step === 'confirmation' && <OrgProgramsConfirmation showHeader />}
          </section>
        </div>
      </div>
      <OrgProgramsFooter />
    </>
  );
};

const OrgProgramPage = () => {
  return (
    <OrgProgramsProvider>
      <OrgProgramsPageInternal />
    </OrgProgramsProvider>
  );
};

export default OrgProgramPage;
