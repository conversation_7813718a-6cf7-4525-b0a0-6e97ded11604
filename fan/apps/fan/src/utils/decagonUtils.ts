import { Location } from 'react-router-dom';

// <PERSON><PERSON> name remains as <PERSON> due to existing usage in bf-swift-app
const HIDE_DECAGON_FLAG = 'hideEinstein';
const denyListUrlRegex = [/^\/tickets\/(?!view\/|activate\/|share\/).*/, /^\/passes\/view\/.*/, /^\/org\/.*/];

export function shouldHideDecagon(isMobile: boolean, location: Location): boolean {
  const queryParams = new URLSearchParams(location.search);

  const isExplicitlyHidden = queryParams.get(HIDE_DECAGON_FLAG) === 'true';
  const isDenyListed = isMobile && denyListUrlRegex.some((regex) => regex.test(location.pathname));

  return isExplicitlyHidden || isDenyListed;
}
