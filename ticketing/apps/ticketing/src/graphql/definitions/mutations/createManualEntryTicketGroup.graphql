mutation Web_Ticketing_CreateManualEntryTicketGroup_r1($input: CreateManualEntryTicketGroupInput!) {
  createManualEntryTicketGroup(createManualEntryTicketGroupInput: $input) {
    tickets {
      id
      ticketedEventId
      redeemedAt
      refundable
      ticketStatus
      ticketTypeId
      paymentType
      source
    }
    passes {
      id
      passConfigId
      source
    }
    id
    ticketGroupReference
    orderTotalInCents
    currency
    source
    paymentType
  }
}
