query Web_Fan_GetTicketGroupsForPassConfigs_r1($input: TicketGroupsForPassConfigsInput!) {
  ticketGroupsForPassConfigs(input: $input) {
    edges {
      cursor
      node {
        createdAt
        deletedAt
        email
        firstName
        id
        lastName
        passes {
          createdAt
          deletedAt
          email
          firstName
          id
          lastName
          passConfigId
          updatedAt
          userId
          reservedSeats {
            seatIdentifier
            section
            row
            seat
            table
            generalAdmissionArea
          }
        }
        ticketGroupReference
        tickets {
          createdAt
          deletedAt
          email
          firstName
          id
          lastName
          redeemedAt
          refundable
          ticketedEventId
          ticketStatus
          ticketTypeId
          updatedAt
          userId
          reservedSeat {
            seatIdentifier
            section
            row
            seat
            table
            generalAdmissionArea
          }
        }
        updatedAt
        userId
        source
        paymentType
        formFieldResponses {
          formFieldId
          response
        }
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    totalCount
  }
}
