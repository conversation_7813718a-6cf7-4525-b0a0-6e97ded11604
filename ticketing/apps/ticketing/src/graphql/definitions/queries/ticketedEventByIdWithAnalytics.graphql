query Web_Fan_TicketedEventByIdWithAnalytics_r1($ticketedEventId: ID!) {
  ticketedEventById(ticketedEventId: $ticketedEventId) {
    createdAt
    date
    deletedAt
    description
    eventStatus
    formFields {
      id
      label
    }
    visibility
    gender
    id
    linkedEntries {
      id
      type
    }
    name
    organizationId
    participatingTeamIds
    refundable
    seatingChartEventId
    sport
    ticketedEventAnalytics {
      ticketCountForTicketType {
        ticketCount
        ticketTypeId
        ticketCountByPaymentType {
          paymentType
          ticketCount
        }
      }
      ticketCountByPaymentType {
        paymentType
        ticketCount
      }
      totalTicketCount
      complimentaryTicketCountForTicketType {
        ticketCount
        ticketTypeId
      }
      totalComplimentaryTicketCount
    }
    ticketedEventAttendance {
      redeemedCountForRedemptionType {
        id
        lineItemType
        redeemedCount
      }
      totalRedeemedCount
    }
    ticketTypeReferences {
      priceOverride
      quantityOverride
      ticketTypeId
    }
    ticketTypes {
      createdAt
      deletedAt
      id
      name
      organizationId
      priceInCents
      quantity
      updatedAt
    }
    timeZoneIdentifier
    updatedAt
    venueConfigurationId
  }
}
