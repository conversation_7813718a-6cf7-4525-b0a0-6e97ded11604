import { formatInTimeZone } from 'date-fns-tz';
import { LinkifyText } from 'ticketing-shared';

import { DataTable, Divider, Headline, ItemTitle, Link, Rows, Subhead, Text } from '@hudl/uniform-web';
import { formatMessage } from 'frontends-i18n';

import { BadgeType, PaymentType } from '../../enums/shared';
import { TicketCountForTicketType, TicketedEvent } from '../../graphql/generated/graphqlTypes';
import { formatCurrencyFromCents } from '../../utility/currencyUtils';
import { getPaymentPlatformLoginUrl } from '../../utility/paymentPlatformUrlUtils';
import { getTotalSalesRevenueByAllPaymentTypes } from '../../utility/ticketGroupUtils';
import {
  buildTicketTypesWithOverrides,
  getPriceOfTicketTypeWithOverrides,
} from '../../utility/ticketTypeOverrideUtils';
import EntityBadge from '../EntityBadge/EntityBadge';
import LastUpdated from '../LastUpdated/LastUpdated';

import styles from './TicketedEventDetailsOverview.module.scss';

type Props = {
  ticketedEvent: TicketedEvent;
  organizationId: string | undefined;
  lastUpdated?: Date;
};

function TicketedEventDetailsOverview({ ticketedEvent, organizationId, lastUpdated = new Date() }: Props) {
  const ticketCountForTicketType = ticketedEvent?.ticketedEventAnalytics?.ticketCountForTicketType;
  const compTicketCountForTicketType = ticketedEvent?.ticketedEventAnalytics?.complimentaryTicketCountForTicketType;
  const redeemedCountForRedemptionType = ticketedEvent?.ticketedEventAttendance?.redeemedCountForRedemptionType;
  const ticketTypesWithOverridesDict = Object.fromEntries(
    buildTicketTypesWithOverrides(ticketedEvent?.ticketTypes, ticketedEvent?.ticketTypeReferences)?.map((tt) => [
      tt.id,
      tt,
    ]) || []
  );
  const ticketTypeCountDict: Record<string, TicketCountForTicketType> = Object.fromEntries(
    ticketCountForTicketType?.map((tt) => [tt.ticketTypeId, tt]) || []
  );

  const compTicketTypeCountDict = Object.fromEntries(
    compTicketCountForTicketType?.map((tt) => [tt.ticketTypeId, tt.ticketCount]) || []
  );

  const redeemedCountForRedemptionTypeDict = Object.fromEntries(
    redeemedCountForRedemptionType?.map((t) => [t.id, t.redeemedCount]) || []
  );

  const totalTicketSoldCountString = ticketedEvent?.ticketedEventAnalytics?.totalTicketCount?.toString();
  const totalRedeemedCountString = ticketedEvent?.ticketedEventAttendance?.totalRedeemedCount?.toString();

  const { revenueByPaymentType, totalRevenue } = getTotalSalesRevenueByAllPaymentTypes(
    ticketCountForTicketType,
    ticketTypesWithOverridesDict
  );
  const totalRevenueString = formatCurrencyFromCents(totalRevenue ?? 0);

  const totalDigitalSalesRevenue = formatCurrencyFromCents(revenueByPaymentType[PaymentType.Electronic]);
  const totalCashSalesRevenue = formatCurrencyFromCents(revenueByPaymentType[PaymentType.External]);

  const getEstimatedRevenueTableRows = (): Rows => {
    const ticketTypeRows: Rows =
      ticketedEvent?.ticketTypes?.map((tt) => {
        const ticketTypePriceInCents = getPriceOfTicketTypeWithOverrides(ticketTypesWithOverridesDict[tt.id]);
        const ticketTypePrice = formatCurrencyFromCents(ticketTypePriceInCents ?? 0);

        const ticketTypeCount = ticketTypeCountDict[tt.id]?.ticketCount ?? 0;
        const ticketTypeCountString = ticketTypeCount.toString();

        const compTicketTypeCount = compTicketTypeCountDict[tt.id] ?? 0;
        const compTicketTypeCountString = compTicketTypeCount.toString();

        const getTicketCountByPaymentType = (ticketTypeId: string, paymentType: PaymentType) => {
          const ticketCount =
            ticketTypeCountDict[ticketTypeId]?.ticketCountByPaymentType?.find((t) => t.paymentType === paymentType)
              ?.ticketCount ?? 0;
          return formatCurrencyFromCents(ticketCount * (ticketTypePriceInCents ?? 0));
        };

        const digitalSalesRevenueString = getTicketCountByPaymentType(tt.id, PaymentType.Electronic);
        const cashSalesRevenueString = getTicketCountByPaymentType(tt.id, PaymentType.External);

        const ticketTypeRevenueInCents = ticketTypePriceInCents ? ticketTypeCount * ticketTypePriceInCents : 0;
        const ticketTypeRevenue = formatCurrencyFromCents(ticketTypeRevenueInCents ?? 0);

        const ticketTypeQuantity = ticketTypesWithOverridesDict[tt.id]?.quantityOverride;
        const remainingQuantityString =
          !ticketTypeQuantity && ticketTypeQuantity !== 0
            ? formatMessage({ id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.no-limit' })
            : Math.max(ticketTypeQuantity - ticketTypeCount - compTicketTypeCount, 0).toString();
        return {
          id: `${tt.id}-revenue`,
          data: [
            tt.name,
            ticketTypePrice,
            ticketTypeCountString,
            compTicketTypeCountString,
            remainingQuantityString,
            digitalSalesRevenueString,
            cashSalesRevenueString,
            ticketTypeRevenue,
          ],
        };
      }) ?? [];

    ticketTypeRows.push({
      id: 'totalRevenue',
      data: [
        formatMessage({
          id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.footer.total-revenue',
        }),
        '-',
        totalTicketSoldCountString ?? '-',
        '-',
        '-',
        totalDigitalSalesRevenue,
        totalCashSalesRevenue,
        totalRevenueString,
      ],
    });

    return ticketTypeRows;
  };

  const renderEventInfo = () => {
    const eventInfoDateFormat = 'EEEE MMM d, y @ h:mm a zzz';
    const eventDate = new Date(ticketedEvent.date);
    return (
      <div className={styles.eventInfoContainer}>
        <Subhead className={styles.eventInfoSubhead}>
          {formatMessage({ id: 'ticketing.ticketed-event-details.event-info.header' })}
        </Subhead>
        <div className={styles.visibility}>
          <Text className={styles.visibilityText}>{`${formatMessage({ id: 'ticketing.visibility' })}:`}</Text>
          {ticketedEvent.visibility && (
            <EntityBadge badgeType={ticketedEvent.visibility as BadgeType} referenceId={ticketedEvent.id} />
          )}
        </div>
        <Text className={styles.eventInfoDate}>
          {formatMessage({ id: 'ticketing.ticketed-event-details.event-info.date' })}{' '}
          {formatInTimeZone(eventDate, ticketedEvent?.timeZoneIdentifier ?? 'Etc/UTC', eventInfoDateFormat)}
        </Text>
        {ticketedEvent.description && (
          <>
            <Text className={styles.eventInfoDescriptionHeader}>
              {formatMessage({ id: 'ticketing.ticketed-event-details.event-info.description-header' })}
            </Text>
            <LinkifyText
              className={styles.eventInfoDescription}
              qaId="ticketed-event-details-description"
              text={ticketedEvent.description}
            />
          </>
        )}
        <Divider className={styles.divider} />
      </div>
    );
  };

  const renderStatisticBox = (title: string, value: string | undefined, qaId: string) => {
    return (
      <div className={styles.statisticBox}>
        <ItemTitle isCaps className={styles.statisticsBoxTitle}>
          {title}
        </ItemTitle>
        <Headline level="2" className={styles.statisticBoxValue} qaId={qaId}>
          {value ?? '-'}
        </Headline>
      </div>
    );
  };

  const renderTicketingSummary = () => {
    return (
      <div className={styles.ticketingSummaryContainer}>
        <div className={styles.ticketingSummaryHeader}>
          <Subhead>{formatMessage({ id: 'ticketing.ticketed-event-details.ticketing-summary.header' })}</Subhead>
          <LastUpdated lastUpdatedDate={lastUpdated} />
        </div>
        <Text className={styles.ticketingSummaryInfoText}>
          {formatMessage({ id: 'ticketing.ticketed-event-details.ticketing-summary.info-text' })}
        </Text>
        <div className={styles.ticketingSummaryStatisticsContainer}>
          {renderStatisticBox(
            formatMessage({ id: 'ticketing.ticketed-event-details.ticketing-summary.statistics.tickets-sold' }),
            totalTicketSoldCountString,
            'ticketed-event-details-statistic-tickets-sold'
          )}
          {renderStatisticBox(
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.statistics.estimated-revenue',
            }),
            totalRevenueString,
            'ticketed-event-details-statistic-estimated-revenue'
          )}
          {renderStatisticBox(
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.statistics.tickets-scanned',
            }),
            totalRedeemedCountString,
            'ticketed-event-details-statistic-tickets-scanned'
          )}
        </div>
      </div>
    );
  };

  const renderEstimatedRevenueTable = () => {
    return (
      <div className={styles.summaryDetailsTableWrapper} data-qa-id="estimated-revenue-table">
        <DataTable
          className={styles.summaryDetailsTable}
          size="medium"
          columnContentTypes={['text', 'numeric', 'numeric', 'numeric', 'numeric', 'numeric', 'numeric', 'numeric']}
          columnHeaders={[
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.ticket-type',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.price-per-ticket',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.tickets-sold',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.comp-tickets',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.quantity-available',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.digital-sales',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.cash-sales',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.revenue-table.header.revenue',
            }),
          ]}
          freezeFirst="row"
          rows={getEstimatedRevenueTableRows()}
        />
      </div>
    );
  };

  const renderEstimatedRevenue = () => {
    return (
      <div className={styles.summaryDetailsContainer}>
        <div className={styles.estimatedRevenueHeaderContainer}>
          <Text className={styles.summaryDetailsHeader}>
            {formatMessage({ id: 'ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.header' })}
          </Text>
        </div>
        <Text className={styles.estimatedRevenueInfoText}>
          {formatMessage(
            { id: 'ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.info-text' },
            {
              stripeDashboardLink: (
                <Link type="article" href={getPaymentPlatformLoginUrl(organizationId!)} target="_blank">
                  {formatMessage({
                    id: 'ticketing.ticketed-event-details.ticketing-summary.estimated-revenue.stripe-dashboard-link',
                  })}
                </Link>
              ),
            }
          )}
        </Text>
        {renderEstimatedRevenueTable()}
      </div>
    );
  };

  const getAttendanceTableRows = (): Rows => {
    const ticketTypeRows: Rows =
      ticketedEvent?.ticketTypes?.map((tt) => {
        const ticketTypeCount = ticketTypeCountDict[tt.id]?.ticketCount ?? 0;
        const redeemedCount = redeemedCountForRedemptionTypeDict[tt.id] ?? 0;

        return {
          id: `${tt.id}-attendance`,
          data: [tt.name ?? '', redeemedCount.toString(), ticketTypeCount.toString()],
        };
      }) ?? [];

    ticketTypeRows.push({
      id: 'pass-holders',
      data: [
        formatMessage({
          id: 'ticketing.ticketed-event-details.ticketing-summary.attendance-table.rows.pass-holder',
        }),
        redeemedCountForRedemptionTypeDict.Pass?.toString() ?? '0',
        '-',
      ],
    });

    ticketTypeRows.push({
      id: 'total-tickets-scanned',
      data: [
        formatMessage({
          id: 'ticketing.ticketed-event-details.ticketing-summary.attendance-table.footer.total-tickets-scanned',
        }),
        totalRedeemedCountString ?? '-',
        totalTicketSoldCountString ?? '-',
      ],
    });

    return ticketTypeRows;
  };

  const renderAttendanceTable = () => {
    return (
      <div className={styles.summaryDetailsTableWrapper} data-qa-id="attendance-table">
        <DataTable
          className={styles.summaryDetailsTable}
          size="medium"
          columnContentTypes={['text', 'numeric', 'numeric', 'numeric', 'numeric']}
          columnHeaders={[
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.ticket-type',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.tickets-scanned',
            }),
            formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.attendance-table.header.tickets-sold',
            }),
          ]}
          freezeFirst="row"
          rows={getAttendanceTableRows()}
        />
      </div>
    );
  };

  const renderAttendanceDetails = () => {
    return (
      <div className={styles.summaryDetailsContainer}>
        <div className={styles.attendanceDetailsHeaderContainer}>
          <Text className={styles.summaryDetailsHeader}>
            {formatMessage({
              id: 'ticketing.ticketed-event-details.ticketing-summary.attendance.header',
            }).toUpperCase()}
          </Text>
        </div>
        {renderAttendanceTable()}
      </div>
    );
  };

  return (
    <div className={styles.overviewWrapper}>
      {renderEventInfo()}
      {renderTicketingSummary()}
      {renderEstimatedRevenue()}
      {renderAttendanceDetails()}
    </div>
  );
}

export default TicketedEventDetailsOverview;
