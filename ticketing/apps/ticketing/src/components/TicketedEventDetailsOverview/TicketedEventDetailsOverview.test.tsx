import { MockedProvider } from '@apollo/client/testing';
import { screen, within } from '@testing-library/react';

import { ToastMessenger } from '@hudl/uniform-web';

import { PaymentType } from '../../enums/shared';
import {
  aRedeemedCountForRedemptionType,
  aTicketCountForTicketType,
  aTicketedEvent,
  aTicketType,
  aTicketTypeReference,
} from '../../graphql/generated/graphqlMocks';
import {
  TicketedEventAnalytics,
  TicketedEventAttendance,
  TicketType,
  TicketTypeReference,
} from '../../graphql/generated/graphqlTypes';
import { getTicketedEventByIdWithAnalyticsMockedResponse } from '../../mockData/mockedResponses';
import { renderWithOptions } from '../../test/renderHelpers';
import TicketedEventDetailsOverview from './TicketedEventDetailsOverview';

const ticketedEventId = 'ticketedEventId';
const organizationId = 'organizationId';

const ticketedEventAnalytics45TicketsSold: TicketedEventAnalytics = {
  ticketCountForTicketType: [
    aTicketCountForTicketType({
      ticketTypeId: 'ticketTypeId1',
      ticketCount: 25,
      ticketCountByPaymentType: [
        { paymentType: PaymentType.Electronic, ticketCount: 20 },
        { paymentType: PaymentType.External, ticketCount: 5 },
      ],
    }),
    aTicketCountForTicketType({
      ticketTypeId: 'ticketTypeId2',
      ticketCount: 20,
      ticketCountByPaymentType: [
        { paymentType: PaymentType.Electronic, ticketCount: 10 },
        { paymentType: PaymentType.External, ticketCount: 10 },
      ],
    }),
  ],
  ticketCountByPaymentType: [
    { paymentType: PaymentType.Electronic, ticketCount: 30 },
    { paymentType: PaymentType.External, ticketCount: 15 },
  ],
  totalTicketCount: 45,
  totalComplimentaryTicketCount: 0,
  complimentaryTicketCountForTicketType: [],
};

const ticketedEventAttendance45TicketsScanned: TicketedEventAttendance = {
  redeemedCountForRedemptionType: [
    aRedeemedCountForRedemptionType({ id: 'ticketTypeId1', redeemedCount: 25 }),
    aRedeemedCountForRedemptionType({ id: 'ticketTypeId2', redeemedCount: 20 }),
  ],
  totalRedeemedCount: 45,
};

const ticketedEventAnalyticsNoTicketsSold: TicketedEventAnalytics = {
  ticketCountForTicketType: [],
  totalTicketCount: 0,
  totalComplimentaryTicketCount: 0,
  complimentaryTicketCountForTicketType: [],
};

const ticketedEventAttendanceNoTicketsScanned: TicketedEventAttendance = {
  redeemedCountForRedemptionType: [],
  totalRedeemedCount: 0,
};

const ticketTypes: TicketType[] = [
  aTicketType({ id: 'ticketTypeId1', priceInCents: 100, name: 'ticketType1', organizationId }),
  aTicketType({ id: 'ticketTypeId2', priceInCents: 200, name: 'ticketType2', organizationId }),
];

const ticketTypeReferences: TicketTypeReference[] = [
  aTicketTypeReference({ ticketTypeId: 'ticketTypeId1', priceOverride: undefined, quantityOverride: 250 }),
  aTicketTypeReference({ ticketTypeId: 'ticketTypeId2', priceOverride: 300, quantityOverride: undefined }),
];

describe('Ticketed event details overview', () => {
  it('renders the ticketed event details overview with basic info', () => {
    const ticketedEvent = aTicketedEvent({
      organizationId,
      id: ticketedEventId,
      ticketedEventAnalytics: ticketedEventAnalytics45TicketsSold,
      ticketedEventAttendance: ticketedEventAttendance45TicketsScanned,
      ticketTypes,
      ticketTypeReferences,
      timeZoneIdentifier: 'America/Chicago',
    });

    const mockedTicketedEventByIdWithAnalyticsQuery = getTicketedEventByIdWithAnalyticsMockedResponse(
      ticketedEventId,
      ticketedEvent
    );

    renderWithOptions(
      <>
        <MockedProvider mocks={[mockedTicketedEventByIdWithAnalyticsQuery]}>
          <TicketedEventDetailsOverview ticketedEvent={ticketedEvent} organizationId={organizationId} />
        </MockedProvider>
        <ToastMessenger />
      </>,
      {
        withIntlProvider: true,
      }
    );

    expect(screen.getByTestId('ticketed-event-details-description')).toHaveTextContent(ticketedEvent.description!);
    expect(screen.getByTestId('ticketed-event-details-statistic-tickets-sold')).toHaveTextContent('45');
    expect(screen.getByTestId('ticketed-event-details-statistic-estimated-revenue')).toHaveTextContent('$85.00');
    expect(screen.getByTestId('ticketed-event-details-statistic-tickets-scanned')).toHaveTextContent('45');
  });

  it('renders the ticketed event details overview when no tickets have been sold or scanned', () => {
    const ticketedEvent = aTicketedEvent({
      organizationId,
      id: ticketedEventId,
      ticketedEventAnalytics: ticketedEventAnalyticsNoTicketsSold,
      ticketedEventAttendance: ticketedEventAttendanceNoTicketsScanned,
      ticketTypes,
      ticketTypeReferences,
      timeZoneIdentifier: 'America/Chicago',
    });

    const mockedTicketedEventByIdWithAnalyticsQuery = getTicketedEventByIdWithAnalyticsMockedResponse(
      ticketedEventId,
      ticketedEvent
    );

    renderWithOptions(
      <>
        <MockedProvider mocks={[mockedTicketedEventByIdWithAnalyticsQuery]}>
          <TicketedEventDetailsOverview ticketedEvent={ticketedEvent} organizationId={organizationId} />
        </MockedProvider>
        <ToastMessenger />
      </>,
      {
        withIntlProvider: true,
      }
    );

    expect(screen.getByTestId('ticketed-event-details-description')).toHaveTextContent(ticketedEvent.description!);
    expect(screen.getByTestId('ticketed-event-details-statistic-tickets-sold')).toHaveTextContent('0');
    expect(screen.getByTestId('ticketed-event-details-statistic-estimated-revenue')).toHaveTextContent('$0.00');
    expect(screen.getByTestId('ticketed-event-details-statistic-tickets-scanned')).toHaveTextContent('0');
  });

  it('renders the estimated revenue and attendance tables with ticket purchases and scans', () => {
    const ticketedEvent = aTicketedEvent({
      organizationId,
      id: ticketedEventId,
      ticketedEventAnalytics: ticketedEventAnalytics45TicketsSold,
      ticketedEventAttendance: ticketedEventAttendance45TicketsScanned,
      ticketTypes,
      ticketTypeReferences,
      timeZoneIdentifier: 'America/Chicago',
    });

    const mockedTicketedEventByIdWithAnalyticsQuery = getTicketedEventByIdWithAnalyticsMockedResponse(
      ticketedEventId,
      ticketedEvent
    );

    renderWithOptions(
      <>
        <MockedProvider mocks={[mockedTicketedEventByIdWithAnalyticsQuery]}>
          <TicketedEventDetailsOverview ticketedEvent={ticketedEvent} organizationId={organizationId} />
        </MockedProvider>
        <ToastMessenger />
      </>,
      {
        withIntlProvider: true,
      }
    );

    const revenueTable = screen.getByTestId('estimated-revenue-table');
    expect(revenueTable).toBeInTheDocument();
    expect(within(revenueTable).getAllByRole('row').length).toBe(4);
    // Row text content will return {ticketTypeName}${pricePerTicket}${ticketsSold}${quantityAvailable}${revenue}
    expect(screen.getByTestId('row-ticketTypeId1-revenue')).toHaveTextContent(
      'ticketType1$1.00250225$20.00$5.00$25.00'
    );
    expect(screen.getByTestId('row-ticketTypeId2-revenue')).toHaveTextContent(
      'ticketType2$3.00200Unlimited$30.00$30.00$60.00'
    );
    expect(screen.getByTestId('row-totalRevenue')).toHaveTextContent('Total Revenue-45--$50.00$35.00$85.00');

    const attendanceTable = screen.getByTestId('attendance-table');
    expect(attendanceTable).toBeInTheDocument();
    expect(within(attendanceTable).getAllByRole('row').length).toBe(5);
    // Row text content will return {ticketTypeName}${pricePerTicket}${ticketsSold}${quantityAvailable}${revenue}
    expect(screen.getByTestId('row-ticketTypeId1-attendance')).toHaveTextContent('ticketType12525');
    expect(screen.getByTestId('row-ticketTypeId2-attendance')).toHaveTextContent('ticketType22020');
    expect(screen.getByTestId('row-total-tickets-scanned')).toHaveTextContent('Total Tickets Scanned4545');
  });

  it('renders the estimated revenue and attendance table with no tickets purchased or scanned', () => {
    const ticketedEvent = aTicketedEvent({
      organizationId,
      id: ticketedEventId,
      ticketedEventAnalytics: ticketedEventAnalyticsNoTicketsSold,
      ticketedEventAttendance: ticketedEventAttendanceNoTicketsScanned,
      ticketTypes,
      ticketTypeReferences,
      timeZoneIdentifier: 'America/Chicago',
    });

    const mockedTicketedEventByIdWithAnalyticsQuery = getTicketedEventByIdWithAnalyticsMockedResponse(
      ticketedEventId,
      ticketedEvent
    );

    renderWithOptions(
      <>
        <MockedProvider mocks={[mockedTicketedEventByIdWithAnalyticsQuery]}>
          <TicketedEventDetailsOverview ticketedEvent={ticketedEvent} organizationId={organizationId} />
        </MockedProvider>
        <ToastMessenger />
      </>,
      {
        withIntlProvider: true,
      }
    );

    const revenueTable = screen.getByTestId('estimated-revenue-table');
    expect(revenueTable).toBeInTheDocument();
    expect(within(revenueTable).getAllByRole('row').length).toBe(4);
    // Row text content will return {ticketTypeName}${pricePerTicket}${ticketsSold}${quantityAvailable}${revenue}
    expect(screen.getByTestId('row-ticketTypeId1-revenue')).toHaveTextContent('ticketType1$1.0000250$0.00');
    expect(screen.getByTestId('row-ticketTypeId2-revenue')).toHaveTextContent('ticketType2$3.0000Unlimited$0.00');
    expect(screen.getByTestId('row-totalRevenue')).toHaveTextContent('Total Revenue-0--$0.00');

    const attendanceTable = screen.getByTestId('attendance-table');
    expect(attendanceTable).toBeInTheDocument();
    expect(within(attendanceTable).getAllByRole('row').length).toBe(5);
    // Row text content will return {ticketTypeName}${pricePerTicket}${ticketsSold}${quantityAvailable}${revenue}
    expect(screen.getByTestId('row-ticketTypeId1-attendance')).toHaveTextContent('ticketType100');
    expect(screen.getByTestId('row-ticketTypeId2-attendance')).toHaveTextContent('ticketType200');
    expect(screen.getByTestId('row-total-tickets-scanned')).toHaveTextContent('Total Tickets Scanned00');
  });

  it('renders the estimated revenue table with remaining quantity', () => {
    const ticketTypeReferencesWithQuantities: TicketTypeReference[] = [
      aTicketTypeReference({ ticketTypeId: 'ticketTypeId1', priceOverride: 100, quantityOverride: 100 }),
      aTicketTypeReference({ ticketTypeId: 'ticketTypeId2', priceOverride: 300, quantityOverride: 50 }),
    ];

    const ticketedEvent = aTicketedEvent({
      organizationId,
      id: ticketedEventId,
      ticketedEventAnalytics: ticketedEventAnalytics45TicketsSold,
      ticketTypes,
      ticketTypeReferences: ticketTypeReferencesWithQuantities,
      timeZoneIdentifier: 'America/Chicago',
    });

    const mockedTicketedEventByIdWithAnalyticsQuery = getTicketedEventByIdWithAnalyticsMockedResponse(
      ticketedEventId,
      ticketedEvent
    );

    renderWithOptions(
      <>
        <MockedProvider mocks={[mockedTicketedEventByIdWithAnalyticsQuery]}>
          <TicketedEventDetailsOverview ticketedEvent={ticketedEvent} organizationId={organizationId} />
        </MockedProvider>
        <ToastMessenger />
      </>,
      {
        withIntlProvider: true,
      }
    );

    const revenueTable = screen.getByTestId('estimated-revenue-table');
    expect(revenueTable).toBeInTheDocument();
    expect(within(revenueTable).getAllByRole('row').length).toBe(4);
    // Row text content will return {ticketTypeName}${pricePerTicket}${ticketsSold}${quantityAvailable}${revenue}
    expect(screen.getByTestId('row-ticketTypeId1-revenue')).toHaveTextContent('ticketType1$1.0025075$20.00$5.00$25.00');
    expect(screen.getByTestId('row-ticketTypeId2-revenue')).toHaveTextContent(
      'ticketType2$3.0020030$30.00$30.00$60.00'
    );
    expect(screen.getByTestId('row-totalRevenue')).toHaveTextContent('Total Revenue-45--$50.00$35.00$85.00');
  });
});
