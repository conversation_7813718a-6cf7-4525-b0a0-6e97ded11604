@use '../../styles/CommonStyles.scss';

.reportCashSalesContainer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.reportCashSalesContent {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: var(--u-space-three);

  @media only screen and (max-width: CommonStyles.$max-mobile-width) {
    padding: var(--u-space-two);
  }
}

.headerContainer {
  width: 100%;
  display: flex;
  gap: var(--u-space-one);
  padding-bottom: var(--u-space-one);
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.headerNameContainer {
  display: flex;
  gap: var(--u-space-one);
  align-items: center;
  max-width: 70%;

  @media only screen and (max-width: CommonStyles.$max-mobile-width) {
    max-width: 100%;
  }
}

.headerTitle {
  overflow-wrap: break-word;
}

.errorAndLoadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  flex-direction: column;
}

.errorNote {
  margin-bottom: var(--u-space-one);
  text-align: center;
}

.helperText {
  margin-bottom: var(--u-space-one);
}

.ticketTypeInputsContainer {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one);
}

.reportCashSalesFooter {
  display: flex;
  justify-content: right;
  width: 100%;
  padding: var(--u-space-three);

  @media only screen and (max-width: CommonStyles.$max-mobile-width) {
    padding: var(--u-space-two);
  }
}

.reportCashSalesButtonContainer {
  display: flex;
  gap: var(--u-space-half);
}
