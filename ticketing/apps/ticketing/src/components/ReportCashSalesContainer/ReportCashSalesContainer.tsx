import { useCallback, useEffect, useMemo, useState } from 'react';

import { useNavigate, useParams } from 'react-router-dom';

import { Logger } from '@hudl/frontends-logging';
import { But<PERSON>, Headline, IconUiNavigationBack, Note, Spinner, Text, ToastMessenger } from '@hudl/uniform-web';
import { formatMessage } from 'frontends-i18n';

import { LoggingAttributes } from '../../enums/loggingAttributes';
import { TicketType } from '../../graphql/generated/graphqlTypes';
import useTicketedEventById from '../../graphql/hooks/useGetTicketedEventById';
import { loggingParams } from '../../utility/constants';
import { formatCurrencyFromCents } from '../../utility/currencyUtils';
import { buildTicketedEventDetailsLink } from '../../utility/urlUtils';
import { reloadPage } from '../../utility/windowUtils';
import ReportCashSalesTicketTypeInput from '../ReportCashSalesTicketTypeInput/ReportCashSalesTicketTypeInput';

import styles from './ReportCashSalesContainer.module.scss';

function ReportCashSalesContainer(): React.JSX.Element {
  const { organizationId, ticketedEventId } = useParams();
  const navigate = useNavigate();
  const logger = useMemo(() => new Logger('Ticketing'), []);
  const { ticketedEvent, ticketedEventLoading, ticketedEventError } = useTicketedEventById(ticketedEventId!, {
    fetchPolicy: 'network-only',
  });
  const [totalQuantity, setTotalQuantity] = useState(0);
  const [totalCashSales, setTotalCashSales] = useState(0);
  const [ticketTypeQuantity, setTicketTypeQuantity] = useState<{ [key: string]: { [quantity: number]: number } }>({});

  const navigateBack = useCallback(() => {
    navigate(buildTicketedEventDetailsLink(organizationId!, ticketedEventId!));
  }, [organizationId, ticketedEventId, navigate]);

  const onQuantityChange = useCallback((ticketTypeId: string, quantity: number, ticketTypePrice: number) => {
    setTicketTypeQuantity((prev) => {
      return { ...prev, [ticketTypeId]: { [quantity]: quantity * ticketTypePrice } };
    });
  }, []);

  const mapTicketType = useCallback(
    (ticketType: TicketType) => {
      return (
        <ReportCashSalesTicketTypeInput
          key={ticketType.id}
          ticketType={ticketType}
          ticketedEvent={ticketedEvent!}
          onQuantityChange={onQuantityChange}
        />
      );
    },
    [ticketedEvent, onQuantityChange]
  );

  const onSubmit = useCallback(() => {
    console.log(ticketTypeQuantity);
    console.log('submit');
  }, [ticketTypeQuantity]);

  const header = useMemo(() => {
    return (
      <div className={styles.headerContainer}>
        <div className={styles.headerNameContainer}>
          <Button
            buttonType="subtle"
            qaId="report-case-sales-back-button"
            icon={<IconUiNavigationBack />}
            onPress={navigateBack}
          />
          <Headline level="1" qaId="report-cash-sales-title" className={styles.headerTitle}>
            {formatMessage({ id: 'ticketing.report-cash-sales.title' })}
          </Headline>
        </div>
      </div>
    );
  }, [navigateBack]);

  const helperText = useMemo(() => {
    return (
      <Text qaId="report-cash-sales-helper-text" className={styles.helperText}>
        {formatMessage({ id: 'ticketing.report-cash-sales.helper-text' })}
      </Text>
    );
  }, []);

  const ticketTypes = useMemo(() => {
    return (
      <div data-qa-id="ticket-type-inputs-container" className={styles.ticketTypeInputsContainer}>
        {ticketedEvent?.ticketTypes?.map(mapTicketType)}
      </div>
    );
  }, [mapTicketType, ticketedEvent]);

  const footer = useMemo(() => {
    return (
      <div className={styles.footerContainer}>
        <div>
          <Text qaId="report-cash-sales-total-cash-sales">
            {/* {formatMessage({ id: 'ticketing.report-cash-sales.totalCashSales', totalCashSales })} */}
            {formatCurrencyFromCents(totalCashSales)}
          </Text>
        </div>
        <div className={styles.reportCashSalesButtonContainer}>
          <Button qaId="report-cash-sales-cancel-button" buttonType="cancel" onPress={navigateBack}>
            {formatMessage({ id: 'ticketing.cancel' })}
          </Button>
          <Button
            qaId="report-cash-sales-submit-button"
            buttonType="primary"
            onPress={onSubmit}
            isDisabled={totalQuantity === 0}
            disabled={totalQuantity === 0}
          >
            {formatMessage({ id: 'ticketing.report-cash-sales.update' })}
          </Button>
        </div>
      </div>
    );
  }, [navigateBack, onSubmit, totalCashSales, totalQuantity]);

  const loader = useMemo(() => {
    return (
      <div className={styles.errorAndLoadingContainer}>
        <Spinner size="large" qaId="event-details-page-spinner" />
      </div>
    );
  }, []);

  const loadingError = useMemo(() => {
    return (
      <div className={styles.errorAndLoadingContainer} data-qa-id="event-details-error">
        <Note type="critical" size="large" className={styles.errorNote}>
          {formatMessage({ id: 'ticketing.ticketed-event-details.error.note' })}
        </Note>
        <Button buttonType="subtle" onPress={reloadPage} qaId="event-details-page-reload-button">
          {formatMessage({ id: 'ticketing.reload' })}
        </Button>
      </div>
    );
  }, []);

  useEffect(() => {
    ToastMessenger.hide();
  }, []);

  useEffect(() => {
    const quantity = Object.values(ticketTypeQuantity).reduce((acc, cur) => {
      return acc + Object.keys(cur).length;
    }, 0);

    const total = Object.values(ticketTypeQuantity).reduce((acc, cur) => {
      return acc + Object.values(cur).reduce((a, c) => a + c, 0);
    }, 0);

    setTotalQuantity(quantity);
    setTotalCashSales(total);
  }, [ticketTypeQuantity]);

  useEffect(() => {
    logger.log('Viewed Report Cash Sales', {
      [LoggingAttributes.FUNC_ATTRIBUTE]: loggingParams.func.view,
      [LoggingAttributes.OP_ATTRIBUTE]: loggingParams.op.reportCashSales,
      [LoggingAttributes.PAGE_ATTRIBUTE]: loggingParams.page.reportCashSalesPage,
      [LoggingAttributes.TICKETED_EVENT_ID]: ticketedEventId,
      [LoggingAttributes.USER_AGENT_ATTRIBUTE]: navigator.userAgent || 'undefined',
    });
  }, [logger, ticketedEventId]);

  if (ticketedEventLoading) return <>{loader}</>;
  if (ticketedEventError) return <>{loadingError}</>;

  return (
    <div className={styles.reportCashSalesContainer} data-qa-id="report-cash-sales-container">
      <div className={styles.reportCashSalesContent}>
        <div data-qa-id="report-cash-sales-header-container">{header}</div>
        <div data-qa-id="report-cash-sales-helper-text-container">{helperText}</div>
        <div data-qa-id="report-cash-sales-ticket-types-container">{ticketTypes}</div>
      </div>
      <div className={styles.reportCashSalesFooter}>{footer}</div>
    </div>
  );
}

export default ReportCashSalesContainer;
