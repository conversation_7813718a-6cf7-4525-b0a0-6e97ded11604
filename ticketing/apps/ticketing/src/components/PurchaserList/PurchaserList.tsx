import { useCallback, useState } from 'react';

import { ApolloError, useReactiveVar } from '@apollo/client';

import { DataTable, Headline, IconDownload, Note, Spinner, Text } from '@hudl/uniform-web';
import { Button } from '@hudl/uniform-web-button-legacy';
import { formatMessage } from 'frontends-i18n';

import { ItemType, PaymentType, TicketGroupSource } from '../../enums/shared';
import { FormField, TicketedEvent, TicketGroup } from '../../graphql/generated/graphqlTypes';
import { EmptyState } from '../../types/shared';
import { formatDateTimeString } from '../../utility/dateTimeUtils';
import { isMediaScreen } from '../../utility/stateVars';
import {
  getFormattedLastFirstName,
  getPaymentTypeDisplayNameKey,
  getReservedSeatStringForTicketGroup,
  getSourceDisplayNameKey,
} from '../../utility/ticketGroupUtils';
import { getTicketTypeSummariesFromTicketGroup } from '../../utility/ticketTypeUtils';
import { reloadPage } from '../../utility/windowUtils';
import DownloadPurchaserListModal from '../DownloadPurchaserListModal/DownloadPurchaserListModal';
import LastUpdated from '../LastUpdated/LastUpdated';

import styles from './PurchaserList.module.scss';

type Props = {
  ticketGroups: TicketGroup[];
  ticketedEvent?: TicketedEvent;
  referenceId: string;
  loading?: boolean;
  error?: ApolloError | undefined;
  lastUpdated?: Date;
  type: ItemType;
  formFields?: FormField[];
  emptyState: EmptyState;
};

type PurchaserListHeader = {
  title: string;
  webSort: number;
  mobileSort: number;
  type: 'text' | 'numeric' | 'element';
};

type TicketTypeSummary = {
  count: number;
  name: string;
};

function PurchaserList({
  ticketGroups,
  ticketedEvent,
  loading = false,
  error,
  lastUpdated = new Date(),
  type,
  formFields,
  referenceId,
  emptyState,
}: Props) {
  const isMobile = useReactiveVar(isMediaScreen);

  const [isDownloadModalOpen, setIsDownloadModalOpen] = useState(false);

  const hasReservedSeats = (): boolean => {
    switch (type) {
      case ItemType.TicketedEvent:
        return ticketGroups?.some((tg) => tg.tickets?.some((t) => t.reservedSeat));
      case ItemType.PassConfig:
        return ticketGroups?.some((tg) => tg.passes?.some((p) => p.reservedSeats));
      default:
        return false;
    }
  };

  const defaultPurchaserListHeaders: PurchaserListHeader[] = [
    {
      title: formatMessage({ id: 'ticketing.ticketed-event-details.purchaser-name' }),
      webSort: 0,
      mobileSort: 0,
      type: 'text',
    },
    {
      title: formatMessage({ id: 'ticketing.ticketed-event-details.purchaser-email' }),
      webSort: 1,
      mobileSort: 3,
      type: 'text',
    },
    {
      title: formatMessage({ id: 'ticketing.ticketed-event-details.purchaser-order-number' }),
      webSort: 2,
      mobileSort: 4,
      type: 'text',
    },
    {
      title: formatMessage({ id: 'ticketing.ticketed-event-details.purchase-source' }),
      webSort: 4,
      mobileSort: 5,
      type: 'text',
    },
    {
      title: formatMessage({ id: 'ticketing.ticketed-event-details.purchaser-payment-type' }),
      webSort: 5,
      mobileSort: 6,
      type: 'text',
    },
    {
      title: formatMessage({ id: 'ticketing.ticketed-event-details.time-of-purchase' }),
      webSort: 6,
      mobileSort: 7,
      type: 'text',
    },
    {
      title:
        type == ItemType.TicketedEvent
          ? formatMessage({ id: 'ticketing.ticketed-event-details.number-tickets' })
          : formatMessage({ id: 'ticketing.ticketed-event-details.number-passes' }),
      webSort: 8,
      mobileSort: 1,
      type: 'numeric',
    },
  ];

  if (hasReservedSeats()) {
    defaultPurchaserListHeaders.push({
      title: formatMessage({ id: 'ticketing.purchaser-list.reserved-seating-header' }),
      webSort: 3,
      mobileSort: 2,
      type: 'text',
    });
  }

  if (type === ItemType.TicketedEvent && ticketedEvent) {
    defaultPurchaserListHeaders.push({
      title: formatMessage({ id: 'ticketing.purchaser-list.ticket-types-purchased-header' }),
      webSort: 7,
      mobileSort: 8,
      type: 'text',
    });
  }

  const ticketGroupsToDisplay =
    ticketGroups?.filter(
      (tg) =>
        tg.source !== TicketGroupSource.Shared &&
        tg.source !== TicketGroupSource.Transfer &&
        ((tg.tickets?.length ?? 0) > 0 || (tg.passes?.length ?? 0) > 0)
    ) ?? [];

  const getHeaders = (): PurchaserListHeader[] => {
    const maxWebSortValue = defaultPurchaserListHeaders.map((h) => h.webSort).reduce((a, b) => Math.max(a, b), 0);
    const maxMobileSortValue = defaultPurchaserListHeaders.map((h) => h.mobileSort).reduce((a, b) => Math.max(a, b), 0);
    const formFieldPurchaseHeaders =
      formFields?.map(
        (formField, index) =>
          ({
            title: formField.label,
            webSort: index + maxWebSortValue,
            mobileSort: index + maxMobileSortValue,
            type: 'text',
          }) as PurchaserListHeader
      ) ?? [];

    return [...defaultPurchaserListHeaders, ...formFieldPurchaseHeaders];
  };

  const renderLoading = () => {
    return (
      <div className={styles.errorAndLoadingContainer}>
        <Spinner size="large" qaId="purchaser-list-spinner" />
      </div>
    );
  };

  const renderError = () => {
    return (
      <div className={styles.errorAndLoadingContainer} data-qa-id="purchaser-list-error">
        <Note type="critical" size="large" className={styles.errorNote}>
          {formatMessage({ id: 'ticketing.ticketed-event-details.error.note' })}
        </Note>
        <Button buttonType="subtle" onClick={reloadPage} qaId="purchaser-list-page-reload-button">
          {formatMessage({ id: 'ticketing.reload' })}
        </Button>
      </div>
    );
  };

  const renderEmptyState = () => {
    return (
      <div className={styles.emptyState}>
        <Headline level="2" qaId="purchaser-list-empty-state-headline">
          {emptyState.header}
        </Headline>
        <Text qaId="purchaser-list-empty-state-help-text">{emptyState.subHeader}</Text>
      </div>
    );
  };

  const renderCount = useCallback((count: string) => {
    return (
      <div>
        <b>{count}</b>
      </div>
    );
  }, []);

  const renderTicketTypes = (ticketTypeSummaries: TicketTypeSummary[]) => {
    return (
      <>
        {ticketTypeSummaries.map((summary, idx) => (
          <span key={summary.name + idx}>
            <b>{summary.count}</b> x {summary.name}
            {idx < ticketTypeSummaries.length - 1 && ', '}
          </span>
        ))}
      </>
    );
  };

  const buildPurchaserListRow = (ticketGroup: TicketGroup) => {
    const name = getFormattedLastFirstName(ticketGroup) || '-';
    const email = ticketGroup.email || '-';
    const dateTime = formatDateTimeString(ticketGroup.createdAt);
    const paymentTypeKey = getPaymentTypeDisplayNameKey(ticketGroup.paymentType as PaymentType);
    const paymentType = paymentTypeKey ? formatMessage({ id: paymentTypeKey }) : '-';

    const count = (
      (type === ItemType.PassConfig ? ticketGroup?.passes?.length : ticketGroup?.tickets?.length) ?? 0
    ).toString();

    const purchaseTypeKey = getSourceDisplayNameKey(ticketGroup.source as TicketGroupSource);
    const purchaseType = purchaseTypeKey ? formatMessage({ id: purchaseTypeKey }) : '-';
    const ticketTypeSummaries =
      (ticketedEvent && getTicketTypeSummariesFromTicketGroup(ticketGroup, ticketedEvent)) ?? [];

    const formFieldResponseData = formFields
      ? formFields.map(
          (formField) =>
            ticketGroup.formFieldResponses?.find((r) => r.formFieldId === formField.id)?.response?.join(', ') ?? '-'
        )
      : [];

    const countElement = {
      value: 'count',
      element: renderCount(count),
    };

    const ticketTypeElement = { value: 'ticket-type-summary', element: renderTicketTypes(ticketTypeSummaries) };

    const data = !isMobile
      ? [
          name,
          email,
          ticketGroup.ticketGroupReference ?? null,
          purchaseType,
          paymentType,
          dateTime,
          ...(ticketedEvent ? [ticketTypeElement] : []),
          countElement,
          ...formFieldResponseData,
        ]
      : [
          name,
          countElement,
          email,
          ticketGroup.ticketGroupReference ?? null,
          purchaseType,
          paymentType,
          dateTime,
          ...(ticketedEvent ? [ticketTypeElement] : []),
          ...formFieldResponseData,
        ];

    if (hasReservedSeats()) {
      if (!isMobile) {
        data.splice(3, 0, getReservedSeatStringForTicketGroup(ticketGroup, type));
      } else {
        data.splice(2, 0, getReservedSeatStringForTicketGroup(ticketGroup, type));
      }
    }

    return {
      id: ticketGroup.id,
      data,
    };
  };

  const getPurchaserListRows = () => ticketGroupsToDisplay.map(buildPurchaserListRow);

  if (loading) return renderLoading();
  if (error) return renderError();

  if (!ticketGroupsToDisplay.length) return renderEmptyState();

  const headerData = getHeaders().sort(
    (a, b) => (isMobile ? a.mobileSort : a.webSort) - (isMobile ? b.mobileSort : b.webSort)
  );

  const headers = headerData.map((header) => header.title);
  const types = headerData.map((header) => header.type);

  const itemType =
    type === ItemType.PassConfig ? formatMessage({ id: 'ticketing.ticket' }) : formatMessage({ id: 'ticketing.pass' });

  const pointOfSaleHelperText = formatMessage({ id: 'ticketing.purchaser-list.point-of-sale-help-text' }, { itemType });

  return (
    <>
      <div className={styles.purchaserListHeaderContainer}>
        <LastUpdated
          lastUpdatedDate={lastUpdated}
          includePeriodOnDate
          additionalText={pointOfSaleHelperText}
          className={styles.lastUpdated}
        />
        <Button
          buttonType="subtle"
          size="small"
          onClick={() => setIsDownloadModalOpen(true)}
          qaId="purchaser-list-download-button"
          icon={<IconDownload />}
        >
          {formatMessage({ id: 'ticketing.purchaser-list.download' })}
        </Button>
      </div>
      <div className={styles.purchaserListTableContainer} data-qa-id="purchaser-list-table">
        <DataTable
          className={styles.purchaserListTable}
          size="medium"
          columnContentTypes={types}
          columnHeaders={headers}
          freezeFirst="row"
          columnShowSortArrows={[true]}
          rows={getPurchaserListRows()}
        />
      </div>
      {isDownloadModalOpen && (
        <DownloadPurchaserListModal
          isOpen
          type={type}
          closeModal={() => setIsDownloadModalOpen(false)}
          referenceId={referenceId}
          isReservedSeating={hasReservedSeats()}
          ticketedEventId={ticketedEvent?.id}
        />
      )}
    </>
  );
}

export default PurchaserList;
