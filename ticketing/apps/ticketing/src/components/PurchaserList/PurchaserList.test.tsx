import { ApolloError } from '@apollo/client';
import { screen } from '@testing-library/react';

import { ItemType, PaymentType, TicketGroupSource } from '../../enums/shared';
import {
  aFormField,
  aFormFieldResponse,
  aPass,
  aReservedSeat,
  aTicket,
  aTicketedEvent,
  aTicketGroup,
  aTicketType,
} from '../../graphql/generated/graphqlMocks';
import { renderWithOptions } from '../../test/renderHelpers';
import { EmptyState } from '../../types/shared';
import { isMediaScreen } from '../../utility/stateVars';
import PurchaserList from './PurchaserList';

beforeEach(() => {
  vi.useFakeTimers({ toFake: ['Date'] });
  vi.setSystemTime(new Date('2023-08-02T20:26:00Z'));
});

afterEach(() => {
  vi.useRealTimers();
  isMediaScreen(false);
});

const ticketGroup = aTicketGroup({
  firstName: 'first',
  lastName: 'last',
  email: '<EMAIL>',
  ticketGroupReference: 'xxx-xxx-xxx',
  tickets: [aTicket({ id: '1', reservedSeat: undefined, ticketTypeId: 'ticketTypeId' })],
  passes: [aPass({ id: '1', reservedSeats: undefined })],
  createdAt: new Date('2023-08-02T20:26:00Z'),
  source: TicketGroupSource.Web,
  paymentType: PaymentType.Electronic,
  formFieldResponses: [],
});

const ticketGroupWithFormFields = aTicketGroup({
  firstName: 'first',
  lastName: 'last',
  email: '<EMAIL>',
  ticketGroupReference: 'xxx-xxx-xxx',
  tickets: [aTicket({ id: '1', reservedSeat: undefined })],
  passes: [aPass({ id: '1', reservedSeats: undefined })],
  createdAt: new Date('2023-08-02T20:26:00Z'),
  source: TicketGroupSource.Web,
  paymentType: PaymentType.Electronic,
  formFieldResponses: [
    aFormFieldResponse({ formFieldId: '1', response: ['bob'] }),
    aFormFieldResponse({ formFieldId: '2', response: ['jones'] }),
  ],
});

const ticketGroupWithReservedSeats = aTicketGroup({
  firstName: 'first',
  lastName: 'last',
  email: '<EMAIL>',
  ticketGroupReference: 'xxx-xxx-xxx',
  tickets: [
    aTicket({
      id: '1',
      reservedSeat: aReservedSeat({
        row: 'A',
        seat: '1',
        generalAdmissionArea: undefined,
        table: undefined,
        section: undefined,
      }),
    }),
    aTicket({
      id: '1',
      reservedSeat: aReservedSeat({
        row: 'A',
        seat: '2',
        generalAdmissionArea: undefined,
        table: undefined,
        section: undefined,
      }),
    }),
  ],
  passes: [
    aPass({
      id: '1',
      reservedSeats: [
        aReservedSeat({ row: 'A', seat: '1', generalAdmissionArea: undefined, table: undefined, section: undefined }),
      ],
    }),
    aPass({
      id: '1',
      reservedSeats: [
        aReservedSeat({ row: 'A', seat: '2', generalAdmissionArea: undefined, table: undefined, section: undefined }),
      ],
    }),
  ],
  createdAt: new Date('2023-08-02T20:26:00Z'),
  source: TicketGroupSource.Web,
  paymentType: PaymentType.Electronic,
});

const displayTypes: ItemType[] = [ItemType.TicketedEvent, ItemType.PassConfig];

displayTypes.forEach((displayType) => {
  describe(`Purchaser list with ${displayType}`, () => {
    it(`renders loading spinner for ${displayType}`, () => {
      renderWithOptions(
        <PurchaserList ticketGroups={[]} loading type={displayType} referenceId="test" emptyState={{} as EmptyState} />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-spinner')).toBeInTheDocument();
    });

    it(`renders error for ${displayType}`, () => {
      const error = new ApolloError({ errorMessage: 'Error' });

      renderWithOptions(
        <PurchaserList
          ticketGroups={[]}
          error={error}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-error')).toBeInTheDocument();
    });

    it(`renders correct empty state`, () => {
      renderWithOptions(
        <PurchaserList
          ticketGroups={[]}
          type={displayType}
          referenceId="test"
          emptyState={{
            header: 'No Tickets Sold Yet',
            subHeader: 'The list will automatically update when tickets have been purchased by fans',
          }}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-empty-state-headline')).toHaveTextContent('No Tickets Sold Yet');
      expect(screen.getByTestId('purchaser-list-empty-state-help-text')).toHaveTextContent(
        'The list will automatically update when tickets have been purchased by fans'
      );
    });

    it(`renders download button for ${displayType}`, () => {
      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroup]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-download-button')).toBeInTheDocument();
    });

    it(`renders purchaser list table for ${displayType}`, () => {
      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroup]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-table')).toBeInTheDocument();
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('last, first');
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('xxx-xxx-xxx');
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('0');
    });

    it(`renders purchaser list with partial POS data for ${displayType}`, () => {
      const posOrderNumber = '123-456-789';
      const webOrderNumber = '987-654-321';
      const ticketGroups = [
        {
          ...ticketGroup,
          source: TicketGroupSource.Web,
          ticketGroupReference: webOrderNumber,
        },
        aTicketGroup({
          source: TicketGroupSource.PointOfSale,
          paymentType: PaymentType.Electronic,
          ticketGroupReference: posOrderNumber,
          firstName: '',
          lastName: '',
          email: '',
          id: 'pos',
          tickets: [aTicket({ id: '1', reservedSeat: undefined })],
          passes: [aPass({ id: '1', reservedSeats: undefined })],
        }),
      ];
      renderWithOptions(
        <PurchaserList
          ticketGroups={ticketGroups}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-table')).toBeInTheDocument();
      expect(screen.getByText(webOrderNumber)).toBeInTheDocument();
      expect(screen.getByText(posOrderNumber)).toBeInTheDocument();

      expect(screen.getByTestId('row-pos')).toHaveTextContent('--123-456-789At EventCard08/02/2023, 8:26 PM1');
    });

    it(`renders purchaser list table in the appropriate order on web for ${displayType}`, () => {
      isMediaScreen(false);
      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroup]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      const columnHeaders = screen.getAllByRole('columnheader');
      expect(columnHeaders).toHaveLength(7);
      expect(columnHeaders[1]).toHaveTextContent('Email');
    });

    it(`renders purchaser list table in the appropriate order on mobile for ${displayType}`, () => {
      isMediaScreen(true);
      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroup]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      const columnHeaders = screen.getAllByRole('columnheader');
      expect(columnHeaders).toHaveLength(7);
      expect(columnHeaders[1]).toHaveTextContent(
        `# of ${displayType === ItemType.TicketedEvent ? 'Tickets' : 'Passes'}`
      );
    });

    it(`tallies the correct quantity for ${displayType}`, () => {
      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroup]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent(
        'last, <EMAIL>-xxx-xxxOnlineCard08/02/2023, 8:26 PM1'
      );
    });

    it(`displays form field responses for ${displayType}`, () => {
      const formField1 = aFormField({ id: '1', label: 'First Name' });
      const formField2 = aFormField({ id: '2', label: 'Last Name' });

      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroupWithFormFields]}
          type={displayType}
          referenceId="test"
          formFields={[formField1, formField2]}
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      const columnHeaders = screen.getAllByRole('columnheader');
      expect(columnHeaders).toHaveLength(9);
      expect(columnHeaders[7]).toHaveTextContent('First Name');
      expect(columnHeaders[8]).toHaveTextContent('Last Name');

      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent(
        'last, <EMAIL>-xxx-xxxOnlineCard08/02/2023, 8:26 PM1bobjones'
      );
    });

    it(`displays ${displayType} count for ${displayType}`, () => {
      const ticketGroupWithOnlyOneEntityType = aTicketGroup({
        firstName: 'first',
        lastName: 'last',
        email: '<EMAIL>',
        ticketGroupReference: 'xxx-xxx-xxx',
        tickets:
          displayType === ItemType.TicketedEvent
            ? [
                aTicket({ reservedSeat: undefined }),
                aTicket({ reservedSeat: undefined }),
                aTicket({ reservedSeat: undefined }),
              ]
            : [],
        passes:
          displayType === ItemType.PassConfig
            ? [
                aPass({ reservedSeats: undefined }),
                aPass({ reservedSeats: undefined }),
                aPass({ reservedSeats: undefined }),
                aPass({ reservedSeats: undefined }),
                aPass({ reservedSeats: undefined }),
              ]
            : [],
        createdAt: new Date('2023-08-02T20:26:00Z'),
        source: TicketGroupSource.Web,
        paymentType: PaymentType.Electronic,
        formFieldResponses: [],
      });
      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroupWithOnlyOneEntityType]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      const expectedCount = displayType === ItemType.TicketedEvent ? '3' : '5';
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent(
        `last, <EMAIL>-xxx-xxxOnlineCard08/02/2023, 8:26 PM${expectedCount}`
      );
    });

    it(`renders reserved seat info for ${displayType}`, () => {
      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroupWithReservedSeats]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-table')).toBeInTheDocument();
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('last, first');
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('xxx-xxx-xxx');
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('0');
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('A-1, A-2');
    });

    it(`filters out shared ticket groups for ${displayType}`, () => {
      const sharedTicketGroup = aTicketGroup({
        id: 'a-shared-ticket-group',
        tickets: [aTicket({ id: '1', reservedSeat: undefined })],
        passes: [aPass({ id: '1', reservedSeats: undefined })],
        source: TicketGroupSource.Shared,
        formFieldResponses: [],
      });

      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroup, sharedTicketGroup]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-table')).toBeInTheDocument();
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toBeInTheDocument();
      expect(screen.queryByTestId(`row-${sharedTicketGroup.id}`)).toBeFalsy();
    });

    it(`filters out ticket groups with no entities for ${displayType}`, () => {
      const emptyTicketGroup = aTicketGroup({
        id: 'an-empty-ticket-group',
        tickets: [],
        passes: [],
        source: TicketGroupSource.Web,
        formFieldResponses: [],
      });

      renderWithOptions(
        <PurchaserList
          ticketGroups={[ticketGroup, emptyTicketGroup]}
          type={displayType}
          referenceId="test"
          emptyState={{} as EmptyState}
        />,
        {
          withIntlProvider: true,
        }
      );

      expect(screen.getByTestId('purchaser-list-table')).toBeInTheDocument();
      expect(screen.getByTestId(`row-${ticketGroup.id}`)).toBeInTheDocument();
      expect(screen.queryByTestId(`row-${emptyTicketGroup.id}`)).toBeFalsy();
    });
  });

  it('renders purchaser list table with ticket types', () => {
    renderWithOptions(
      <PurchaserList
        ticketGroups={[ticketGroup]}
        type={ItemType.TicketedEvent}
        referenceId="test"
        emptyState={{} as EmptyState}
        ticketedEvent={aTicketedEvent({ ticketTypes: [aTicketType({ id: 'ticketTypeId', name: 'Ticket Type' })] })}
      />,
      {
        withIntlProvider: true,
      }
    );

    expect(screen.getByTestId('purchaser-list-table')).toBeInTheDocument();
    expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('last, first');
    expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('<EMAIL>');
    expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('xxx-xxx-xxx');
    expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('0');
    expect(screen.getByTestId(`row-${ticketGroup.id}`)).toHaveTextContent('1 x Ticket Type');
  });
});
