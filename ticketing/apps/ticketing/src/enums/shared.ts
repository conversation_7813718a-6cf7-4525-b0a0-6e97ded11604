export enum ScheduleEntryLocation {
  Neutral = 0,
  Home = 1,
  Away = 2,
}

export enum Gender {
  COED = 'COED',
  MENS = 'MENS',
  WOMENS = 'WOMENS',
}

export enum Sport {
  AUSTRALIAN_RULES_FOOTBALL = 'AUSTRALIAN_RULES_FOOTBALL',
  AUSTRALIAN_RULES_FOOTBALL_RECRUITING = 'AUSTRALIAN_RULES_FOOTBALL_RECRUITING',
  BADMINTON = 'BADMINTON',
  BADMINTON_RECRUITING = 'BADMINTON_RECRUITING',
  BASEBALL = 'BASEBALL',
  BASEBALL_RECRUITING = 'BASEBALL_RECRUITING',
  BASKETBALL = 'BASKETBALL',
  BASKETBALL_RECRUITING = 'BASKETBALL_RECRUITING',
  CHEER_AND_SPIRIT = 'CHEER_AND_SPIRIT',
  CHEER_AND_SPIRIT_RECRUITING = 'CHEER_AND_SPIRIT_RECRUITING',
  CRICKET = 'CRICKET',
  CRICKET_RECRUITING = 'CRICKET_RECRUITING',
  CROSS_COUNTRY = 'CROSS_COUNTRY',
  CROSS_COUNTRY_RECRUITING = 'CROSS_COUNTRY_RECRUITING',
  CYCLING = 'CYCLING',
  CYCLING_RECRUITING = 'CYCLING_RECRUITING',
  DANCE_AND_DRILL = 'DANCE_AND_DRILL',
  DANCE_AND_DRILL_RECRUITING = 'DANCE_AND_DRILL_RECRUITING',
  FENCING = 'FENCING',
  FENCING_RECRUITING = 'FENCING_RECRUITING',
  FIELD_HOCKEY = 'FIELD_HOCKEY',
  FIELD_HOCKEY_RECRUITING = 'FIELD_HOCKEY_RECRUITING',
  FOOTBALL = 'FOOTBALL',
  FOOTBALL_RECRUITING = 'FOOTBALL_RECRUITING',
  GOLF = 'GOLF',
  GOLF_RECRUITING = 'GOLF_RECRUITING',
  GYMNASTICS = 'GYMNASTICS',
  GYMNASTICS_RECRUITING = 'GYMNASTICS_RECRUITING',
  HANDBALL = 'HANDBALL',
  HANDBALL_RECRUITING = 'HANDBALL_RECRUITING',
  ICE_HOCKEY = 'ICE_HOCKEY',
  ICE_HOCKEY_RECRUITING = 'ICE_HOCKEY_RECRUITING',
  LACROSSE = 'LACROSSE',
  LACROSSE_RECRUITING = 'LACROSSE_RECRUITING',
  NETBALL = 'NETBALL',
  NETBALL_RECRUITING = 'NETBALL_RECRUITING',
  NO_SPORT = 'NO_SPORT',
  OTHER = 'OTHER',
  PERFORMING_ARTS = 'PERFORMING_ARTS',
  PERFORMING_ARTS_RECRUITING = 'PERFORMING_ARTS_RECRUITING',
  RUGBY = 'RUGBY',
  RUGBY_LEAGUE = 'RUGBY_LEAGUE',
  RUGBY_LEAGUE_RECRUITING = 'RUGBY_LEAGUE_RECRUITING',
  RUGBY_RECRUITING = 'RUGBY_RECRUITING',
  RUGBY_UNION = 'RUGBY_UNION',
  RUGBY_UNION_RECRUITING = 'RUGBY_UNION_RECRUITING',
  SAILING_AND_YACHTING = 'SAILING_AND_YACHTING',
  SAILING_AND_YACHTING_RECRUITING = 'SAILING_AND_YACHTING_RECRUITING',
  SOCCER = 'SOCCER',
  SOCCER_RECRUITING = 'SOCCER_RECRUITING',
  SOFTBALL = 'SOFTBALL',
  SOFTBALL_RECRUITING = 'SOFTBALL_RECRUITING',
  SQUASH = 'SQUASH',
  SQUASH_RECRUITING = 'SQUASH_RECRUITING',
  SURFING = 'SURFING',
  SURFING_RECRUITING = 'SURFING_RECRUITING',
  SWIMMING_AND_DIVING = 'SWIMMING_AND_DIVING',
  SWIMMING_AND_DIVING_RECRUITING = 'SWIMMING_AND_DIVING_RECRUITING',
  TENNIS = 'TENNIS',
  TENNIS_RECRUITING = 'TENNIS_RECRUITING',
  TENPIN_BOWLING = 'TENPIN_BOWLING',
  TENPIN_BOWLING_RECRUITING = 'TENPIN_BOWLING_RECRUITING',
  TRACK = 'TRACK',
  TRACK_RECRUITING = 'TRACK_RECRUITING',
  VOLLEYBALL = 'VOLLEYBALL',
  VOLLEYBALL_RECRUITING = 'VOLLEYBALL_RECRUITING',
  WATER_POLO = 'WATER_POLO',
  WATER_POLO_RECRUITING = 'WATER_POLO_RECRUITING',
  WRESTLING = 'WRESTLING',
  WRESTLING_RECRUITING = 'WRESTLING_RECRUITING',
}

export enum TeamLevel {
  FRESHMAN = 'FRESHMAN',
  JUNIOR_VARSITY = 'JUNIOR_VARSITY',
  OTHER = 'OTHER',
  OTHER_NON_HS = 'OTHER_NON_HS',
  SOPHOMORE = 'SOPHOMORE',
  VARSITY = 'VARSITY',
}

export enum GraphQLType {
  ScheduleEntryPublicSummary = 'ScheduleEntryPublicSummary',
  School = 'School',
  TeamHeader = 'TeamHeader',
  Team = 'Team',
}

export enum EventStatus {
  Unknown = 'Unknown',
  Draft = 'Draft',
  Upcoming = 'Upcoming',
  Open = 'Open',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Archived = 'Archived',
  Rescheduled = 'Rescheduled',
  Cancelled = 'Cancelled',
}

export enum PassConfigStatus {
  Unknown = 'Unknown',
  Draft = 'Draft',
  Active = 'Active',
  Completed = 'Completed',
}

export enum PassConfigExclusion {
  Unknown = 'Unknown',
  LeaguePass = 'LeaguePass',
}

export enum TicketSortType {
  TicketDate = 'TICKET_DATE',
}

export enum TicketGroupSortType {
  TicketGroupPurchaseDate = 'TICKET_GROUP_PURCHASE_DATE',
  TicketGroupPurchaserLastName = 'TICKET_GROUP_PURCHASER_LAST_NAME',
}

export enum PassSortType {
  PassStartDate = 'START_DATE',
}

export enum ItemType {
  PassConfig = 'Pass',
  TicketedEvent = 'Event',
}

export enum LinkedEntryType {
  Unknown = 'Unknown',
  HudlScheduleEntry = 'HudlScheduleEntry',
}

export enum TicketGroupSource {
  Unknown = 'Unknown',
  Web = 'Web',
  Mobile = 'Mobile',
  PointOfSale = 'POS',
  Complimentary = 'Complimentary',
  Shared = 'Shared',
  Transfer = 'Transfer',
  ManualEntry = 'ManualEntry',
}

export enum PaymentType {
  Unknown = 'Unknown',
  Electronic = 'Electronic',
  External = 'External',
  None = 'None',
}

export enum TicketingEntityVisibility {
  Public = 'Public',
  Private = 'Private',
  NotForSale = 'NotForSale',
}

export enum FormType {
  GeneralAdmission = 'GeneralAdmission',
  ReservedSeating = 'ReservedSeating',
}

export enum FeeStrategy {
  PaidByOrganization = 'PaidByOrganization',
  PaidByCustomer = 'PaidByCustomer',
  ProcessingPaidByOrganization = 'ProcessingPaidByOrganization',
  Unknown = 'Unknown',
}

export enum TicketingEntityType {
  Ticket = 'Ticket',
  Pass = 'Pass',
}

export enum FormFieldSortType {
  CreatedAt = 'CreatedAt',
}

export enum FormFieldType {
  Text = 'Text',
}

export enum PayoutType {
  Check = 'Check',
  DirectDeposit = 'DirectDeposit',
  Unknown = 'Unknown',
}

export enum BadgeType {
  Public = 'Public',
  Private = 'Private',
  League = 'League',
  NotForSale = 'NotForSale',
  Renewal = 'Renewal',
  Unknown = 'Unknown',
}

export enum PassConfigVisibility {
  Public = 'Public',
  Private = 'Private',
  NotForSale = 'NotForSale',
  Renewal = 'Renewal',
}

export enum EventType {
  LinkedEvent = 'LinkedEvent',
  CustomEvent = 'CustomEvent',
}
