import { ItemType, PaymentType, TicketGroupSource } from '../../enums/shared';
import {
  aFormField,
  aFormFieldResponse,
  aPass,
  aReservedSeat,
  aTicket,
  aTicketGroup,
} from '../../graphql/generated/graphqlMocks';
import { TicketGroup } from '../../graphql/generated/graphqlTypes';
import {
  getFormattedLastFirstName,
  getFormFieldResponseString,
  getPaymentTypeDisplayNameKey,
  getReservedSeatStringForTicketGroup,
  getSourceDisplayNameKey,
  splitComplimentaryTicketGroups,
} from '../ticketGroupUtils';

describe('getSourceDisplayNameKey', () => {
  it('returns Mobile for TicketGroupSource.Mobile', () => {
    expect(getSourceDisplayNameKey(TicketGroupSource.Mobile)).toBe('ticketing.source.mobile');
  });

  it('returns Online for TicketGroupSource.Web', () => {
    expect(getSourceDisplayNameKey(TicketGroupSource.Web)).toBe('ticketing.source.web');
  });

  it('returns At Event for TicketGroupSource.PointOfSale', () => {
    expect(getSourceDisplayNameKey(TicketGroupSource.PointOfSale)).toBe('ticketing.source.pos');
  });

  it('returns empty string for unknown TicketGroupSource', () => {
    expect(getSourceDisplayNameKey('' as TicketGroupSource)).toBe('');
  });

  it('returns empty string for undefined TicketGroupSource', () => {
    expect(getSourceDisplayNameKey(undefined as unknown as TicketGroupSource)).toBe('');
  });

  it('returns empty string for null TicketGroupSource', () => {
    expect(getSourceDisplayNameKey(null as unknown as TicketGroupSource)).toBe('');
  });

  it('returns empty string for bad ticket group source', () => {
    expect(getSourceDisplayNameKey('not a source' as unknown as TicketGroupSource)).toBe('');
  });
});

describe('getPaymentTypeDisplayNameKey', () => {
  it('returns Cash for PaymentType.External', () => {
    expect(getPaymentTypeDisplayNameKey(PaymentType.External)).toBe('ticketing.payment-type.external');
  });

  it('returns Card for PaymentType.Electronic', () => {
    expect(getPaymentTypeDisplayNameKey(PaymentType.Electronic)).toBe('ticketing.payment-type.electronic');
  });

  it('returns - for unknown PaymentType', () => {
    expect(getPaymentTypeDisplayNameKey('' as PaymentType)).toBe('-');
  });

  it('returns - for undefined PaymentType', () => {
    expect(getPaymentTypeDisplayNameKey(undefined as unknown as PaymentType)).toBe('-');
  });

  it('returns - for null PaymentType', () => {
    expect(getPaymentTypeDisplayNameKey(null as unknown as PaymentType)).toBe('-');
  });

  it('returns - for bad payment type', () => {
    expect(getPaymentTypeDisplayNameKey('not a payment type' as unknown as PaymentType)).toBe('-');
  });
});

describe('getFormattedLastFirstName', () => {
  it('returns last name, first name when both are provided', () => {
    expect(getFormattedLastFirstName({ lastName: 'last', firstName: 'first' } as TicketGroup)).toBe('last, first');
  });

  it('returns last name when only last name is provided', () => {
    expect(getFormattedLastFirstName({ lastName: 'last' } as TicketGroup)).toBe('last');
  });

  it('returns first name when only first name is provided', () => {
    expect(getFormattedLastFirstName({ firstName: 'first' } as TicketGroup)).toBe('first');
  });

  it('returns empty string when no names are provided', () => {
    expect(getFormattedLastFirstName({} as TicketGroup)).toBe('');
  });

  it('returns empty string when no ticket group is provided', () => {
    expect(getFormattedLastFirstName(undefined as unknown as TicketGroup)).toBe('');
  });
});

describe('getFormFieldResponseString', () => {
  it('formats no responses correctly', () => {
    const formField = aFormField({ id: 'fieldId' });
    const ticketGroup = aTicketGroup({
      formFieldResponses: [],
    });
    expect(getFormFieldResponseString(ticketGroup, formField.id)).toBe('');
  });

  it('formats one response correctly', () => {
    const formField = aFormField({ id: 'fieldId' });
    const ticketGroup = aTicketGroup({
      formFieldResponses: [aFormFieldResponse({ formFieldId: formField.id, response: ['my response'] })],
    });
    expect(getFormFieldResponseString(ticketGroup, formField.id)).toBe('my response');
  });

  it('formats multiple responses correctly', () => {
    const formField = aFormField({ id: 'fieldId' });
    const ticketGroup = aTicketGroup({
      formFieldResponses: [
        aFormFieldResponse({ formFieldId: formField.id, response: ['my response 1', 'my response 2'] }),
      ],
    });
    expect(getFormFieldResponseString(ticketGroup, formField.id)).toBe('my response 1, my response 2');
  });
});

describe('getReservedSeatStringForTicketGroup', () => {
  it('formats no seats correctly for tickets', () => {
    const ticketGroup = aTicketGroup({
      tickets: [aTicket({ reservedSeat: undefined })],
    });
    expect(getReservedSeatStringForTicketGroup(ticketGroup, ItemType.TicketedEvent)).toBe('');
  });

  it('formats no seats correctly for passes', () => {
    const ticketGroup = aTicketGroup({
      passes: [aPass({ reservedSeats: [] })],
    });
    expect(getReservedSeatStringForTicketGroup(ticketGroup, ItemType.PassConfig)).toBe('');
  });

  it('formats the seat correctly for one ticket', () => {
    const ticketGroup = aTicketGroup({
      tickets: [
        aTicket({
          reservedSeat: aReservedSeat({
            section: '106',
            generalAdmissionArea: undefined,
            table: undefined,
            row: 'A',
            seat: '16',
          }),
        }),
      ],
    });
    expect(getReservedSeatStringForTicketGroup(ticketGroup, ItemType.TicketedEvent)).toBe('106-A-16');
  });

  it('formats the seat correctly for one pass', () => {
    const ticketGroup = aTicketGroup({
      passes: [
        aPass({
          reservedSeats: [
            aReservedSeat({
              section: '106',
              generalAdmissionArea: undefined,
              table: undefined,
              row: 'A',
              seat: '16',
            }),
          ],
        }),
      ],
    });
    expect(getReservedSeatStringForTicketGroup(ticketGroup, ItemType.PassConfig)).toBe('106-A-16');
  });

  it('formats the seat correctly for multiple tickets', () => {
    const ticketGroup = aTicketGroup({
      tickets: [
        aTicket({
          reservedSeat: aReservedSeat({
            section: '106',
            generalAdmissionArea: undefined,
            table: undefined,
            row: 'A',
            seat: '16',
          }),
        }),
        aTicket({
          reservedSeat: aReservedSeat({
            section: '106',
            generalAdmissionArea: undefined,
            table: undefined,
            row: 'A',
            seat: '17',
          }),
        }),
      ],
    });
    expect(getReservedSeatStringForTicketGroup(ticketGroup, ItemType.TicketedEvent)).toBe('106-A-16, 106-A-17');
  });

  it('formats the seat correctly for multiple passes', () => {
    const ticketGroup = aTicketGroup({
      passes: [
        aPass({
          reservedSeats: [
            aReservedSeat({
              section: '106',
              generalAdmissionArea: undefined,
              table: undefined,
              row: 'A',
              seat: '16',
            }),
          ],
        }),
        aPass({
          reservedSeats: [
            aReservedSeat({
              section: '106',
              generalAdmissionArea: undefined,
              table: undefined,
              row: 'A',
              seat: '17',
            }),
          ],
        }),
      ],
    });
    expect(getReservedSeatStringForTicketGroup(ticketGroup, ItemType.PassConfig)).toBe('106-A-16, 106-A-17');
  });
});

describe('splitComplimentaryTicketGroups', () => {
  it('returns correct arrays based on source', () => {
    const ticketGroups = [
      aTicketGroup({ source: TicketGroupSource.Complimentary }),
      aTicketGroup({ source: TicketGroupSource.Web }),
      aTicketGroup({ source: TicketGroupSource.Mobile }),
    ];

    const { complimentaryTicketGroups, nonComplimentaryTicketGroups } = splitComplimentaryTicketGroups(ticketGroups);

    expect(complimentaryTicketGroups).toHaveLength(1);
    expect(nonComplimentaryTicketGroups).toHaveLength(2);
  });
});
