import { ItemType, PaymentType, TicketGroupSource } from '../../enums/shared';
import {
  aFormField,
  aFormFieldResponse,
  aReservedSeat,
  aTicket,
  aTicketedEvent,
  aTicketGroup,
  aTicketType,
} from '../../graphql/generated/graphqlMocks';
import { downloadCsv } from '../csvUtils';
import {
  buildFileName,
  buildPurchaserListData,
  buildPurchaserListHeaders,
  DownloadablePurchaserListColumn,
  downloadPurchaserListCsv,
  getDefaultDownloadablePurchaserListColumns,
  sanitizeFilename,
} from '../downloadPurchaserListUtils';

describe('downloadPurchaserListUtils - buildPurchaserListData', () => {
  it('Returns correct information - csv escaped data', () => {
    const formField = aFormField({ id: '1', label: 'Form Field 1' });
    const ticketGroups = [
      aTicketGroup({
        firstName: '<PERSON>',
        lastName: 'Doe',
        email: '<EMAIL>',
        tickets: [
          aTicket({
            ticketTypeId: '1',
            reservedSeat: aReservedSeat({
              section: '106',
              generalAdmissionArea: undefined,
              table: undefined,
              row: 'A',
              seat: '16',
            }),
          }),
        ],
        passes: [],
        ticketGroupReference: '123-abc',
        source: TicketGroupSource.Web,
        paymentType: PaymentType.Electronic,
        createdAt: new Date('2022-01-01T00:00:00Z'),
        formFieldResponses: [aFormFieldResponse({ formFieldId: formField.id, response: ['my response '] })],
      }),
    ];
    const ticketedEvent = aTicketedEvent({ ticketTypes: [aTicketType({ id: '1', name: 'Ticket Type 1' })] });

    const selectedColumns = getDefaultDownloadablePurchaserListColumns(ItemType.TicketedEvent).map((column) => ({
      ...column,
      checked: true,
    }));
    selectedColumns.push({ id: '1', label: 'Form Field 1', checked: true });

    const csvData = buildPurchaserListData(
      ticketGroups,
      ItemType.TicketedEvent,
      selectedColumns,
      false,
      true,
      false,
      ticketedEvent
    );

    expect(csvData).toEqual([
      [
        'John',
        'Doe',
        '<EMAIL>',
        '106-A-16',
        '123-abc',
        'Online',
        'Card',
        '"01/01/2022, 12:00 AM"',
        '1 x Ticket Type 1',
        '1',
        'my response ',
      ],
    ]);
  });

  it('Returns correct information - exclude complimentary', () => {
    const formField = aFormField({ id: '1', label: 'Form Field 1' });
    const ticketGroups = [
      aTicketGroup({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        tickets: [
          aTicket({
            reservedSeat: aReservedSeat({
              section: '106',
              generalAdmissionArea: undefined,
              table: undefined,
              row: 'A',
              seat: '16',
            }),
          }),
        ],
        passes: [],
        ticketGroupReference: '123-abc',
        source: TicketGroupSource.Complimentary,
        createdAt: new Date('2022-01-01T00:00:00Z'),
        formFieldResponses: [aFormFieldResponse({ formFieldId: formField.id, response: ['my response '] })],
      }),
    ];

    const selectedColumns = getDefaultDownloadablePurchaserListColumns(ItemType.TicketedEvent).map((column) => ({
      ...column,
      checked: true,
    }));
    selectedColumns.push({ id: '1', label: 'Form Field 1', checked: true });

    const csvData = buildPurchaserListData(ticketGroups, ItemType.TicketedEvent, selectedColumns, false, true, true);

    expect(csvData).toEqual([]);
  });

  it('Returns correct information - pdf data', () => {
    const formField = aFormField({ id: '1', label: 'Form Field 1' });
    const ticketGroups = [
      aTicketGroup({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        tickets: [
          aTicket({
            ticketTypeId: '1',
            reservedSeat: aReservedSeat({
              section: '106',
              generalAdmissionArea: undefined,
              table: undefined,
              row: 'A',
              seat: '16',
            }),
          }),
        ],
        passes: [],
        ticketGroupReference: '123-abc',
        source: TicketGroupSource.Web,
        paymentType: PaymentType.Electronic,
        createdAt: new Date('2022-01-01T00:00:00Z'),
        formFieldResponses: [aFormFieldResponse({ formFieldId: formField.id, response: ['my response '] })],
      }),
    ];
    const ticketedEvent = aTicketedEvent({ ticketTypes: [aTicketType({ id: '1', name: 'Ticket Type 1' })] });

    const selectedColumns = getDefaultDownloadablePurchaserListColumns(ItemType.TicketedEvent).map((column) => ({
      ...column,
      checked: true,
    }));
    selectedColumns.push({ id: '1', label: 'Form Field 1', checked: true });

    const csvData = buildPurchaserListData(
      ticketGroups,
      ItemType.TicketedEvent,
      selectedColumns,
      false,
      false,
      false,
      ticketedEvent
    );

    expect(csvData).toEqual([
      [
        'John',
        'Doe',
        '<EMAIL>',
        '106-A-16',
        '123-abc',
        'Online',
        'Card',
        '01/01/2022, 12:00 AM',
        '1 x Ticket Type 1',
        '1',
        'my response ',
      ],
    ]);
  });
});

describe('downloadPurchaserListUtils - buildPurchaserListHeaders', () => {
  it('Returns correct headers', () => {
    const selectedColumns: DownloadablePurchaserListColumn[] = getDefaultDownloadablePurchaserListColumns(
      ItemType.TicketedEvent
    ).map((column) => ({
      ...column,
      checked: true,
    }));
    selectedColumns.push({ id: '1', label: 'Form Field 1', checked: true } as DownloadablePurchaserListColumn);

    const headers = buildPurchaserListHeaders(selectedColumns);

    expect(headers).toEqual([
      'First Name',
      'Last Name',
      'Email',
      'Reserved Seats',
      'Order Number',
      'Purchase Source',
      'Payment Type',
      'Time of Purchase',
      'Ticket Types',
      '# of Tickets',
      'Form Field 1',
    ]);
  });
});

describe('sanitizeFilename', () => {
  it('should replace spaces with dashes', () => {
    const input = 'my file name with spaces';
    const result = sanitizeFilename(input);
    expect(result).toBe('my-file-name-with-spaces');
  });

  it('should remove invalid characters', () => {
    const input = 'file@#$%^&*name<>|:"?';
    const result = sanitizeFilename(input);
    expect(result).toBe('filename');
  });

  it('should handle filenames with mixed valid and invalid characters', () => {
    const input = 'valid_file-name@invalid_chars!';
    const result = sanitizeFilename(input);
    expect(result).toBe('valid_file-nameinvalid_chars');
  });

  it('should preserve dots and underscores', () => {
    const input = 'my_file.name_with_dots.txt';
    const result = sanitizeFilename(input);
    expect(result).toBe('my_file.name_with_dots.txt');
  });

  it('should truncate filenames longer than 250 characters', () => {
    const longInput = 'a'.repeat(300); // 300 characters long
    const result = sanitizeFilename(longInput);
    expect(result.length).toBe(100); // Should truncate to 100 characters
  });

  it('should handle filenames with leading and trailing spaces', () => {
    const input = '   myfile   ';
    const result = sanitizeFilename(input);
    expect(result).toBe('-myfile-');
  });

  it('should handle empty input', () => {
    const input = '';
    const result = sanitizeFilename(input);
    expect(result).toBe('');
  });

  it('should convert filename to lowercase', () => {
    const input = 'MyFiLeNaMe';
    const result = sanitizeFilename(input);
    expect(result).toBe('myfilename');
  });

  it('should handle a combination of spaces, invalid characters, and case', () => {
    const input = 'My File @# With ! Invalid$%^&*() Characters';
    const result = sanitizeFilename(input);
    expect(result).toBe('my-file--with--invalid-characters');
  });
});

describe('buildFileName', () => {
  it('should build a filename with reference name, date, and extension', () => {
    const result = buildFileName('eventName', '2024-12-16', 'csv');
    expect(result).toBe('2024-12-16_eventname_purchaser-list.csv');
  });

  it('should handle missing reference name', () => {
    const result = buildFileName(undefined, '2024-12-16', 'csv');
    expect(result).toBe('2024-12-16_purchaser-list.csv');
  });

  it('should handle missing reference date', () => {
    const result = buildFileName('eventName', undefined, 'csv');
    expect(result).toBe('eventname_purchaser-list.csv');
  });

  it('should handle both reference name and date missing', () => {
    const result = buildFileName(undefined, undefined, 'csv');
    expect(result).toBe('purchaser-list.csv');
  });

  it('should sanitize invalid characters in reference name', () => {
    const result = buildFileName('event@name!', '2024-12-16', 'csv');
    expect(result).toBe('2024-12-16_eventname_purchaser-list.csv');
  });

  it('should sanitize invalid characters in reference date', () => {
    const result = buildFileName('eventName', '2024/12/16', 'csv');
    expect(result).toBe('20241216_eventname_purchaser-list.csv');
  });

  it('should convert everything to lowercase', () => {
    const result = buildFileName('EventName', '2024-12-16', 'CSV');
    expect(result).toBe('2024-12-16_eventname_purchaser-list.csv');
  });

  it('should handle a missing extension', () => {
    const result = buildFileName('eventName', '2024-12-16', '');
    expect(result).toBe('2024-12-16_eventname_purchaser-list.');
  });

  it('should handle a complex combination of invalid characters', () => {
    const result = buildFileName('event!@#$%^&*()Name', '2024/12/16', 'csv');
    expect(result).toBe('20241216_eventname_purchaser-list.csv');
  });

  it('should truncate the sanitized filename if necessary', () => {
    const longName = 'a'.repeat(300);
    const result = buildFileName(longName, '2024-12-16', 'csv');
    expect(result.length).toBeLessThanOrEqual(104); // Filename + ".csv" is <= 104
  });
});

vi.mock('../csvUtils', async () => {
  const actualUtils = await vi.importActual('../csvUtils');
  return {
    ...actualUtils,
    downloadCsv: vi.fn(),
  };
});

describe('downloadPurchaserListCsv', () => {
  it('Calls download', () => {
    const formField = aFormField({ id: '1', label: 'Form Field 1' });
    const ticketGroups = [
      aTicketGroup({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        tickets: [
          aTicket({
            reservedSeat: aReservedSeat({
              section: '106',
              generalAdmissionArea: undefined,
              table: undefined,
              row: 'A',
              seat: '16',
            }),
          }),
        ],
        passes: [],
        ticketGroupReference: '123-abc',
        source: TicketGroupSource.Web,
        createdAt: new Date('2022-01-01T00:00:00Z'),
        formFieldResponses: [aFormFieldResponse({ formFieldId: formField.id, response: ['my response '] })],
      }),
    ];

    const selectedColumns = getDefaultDownloadablePurchaserListColumns(ItemType.TicketedEvent).map((column) => ({
      ...column,
      checked: true,
    }));
    selectedColumns.push({ id: '1', label: 'Form Field 1', checked: true });

    downloadPurchaserListCsv(
      ticketGroups,
      ItemType.TicketedEvent,
      selectedColumns,
      'An Event',
      ['12/25/24'],
      'America/Chicago',
      false,
      false
    );

    expect(downloadCsv).toHaveBeenCalled();
  });
});
