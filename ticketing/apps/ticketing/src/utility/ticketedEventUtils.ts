import { EventStatus, ScheduleEntryLocation } from '../enums/shared';
import { ScheduleEntryPublicSummary, TicketedEvent } from '../graphql/generated/graphqlTypes';

export const isScheduleEntryForTicketedEvent = (
  scheduleEntry: ScheduleEntryPublicSummary,
  ticketedEvent: TicketedEvent
) => {
  return ticketedEvent.linkedEntries?.some(
    (l) =>
      scheduleEntry.scheduleEntryLocation === ScheduleEntryLocation.Home &&
      l.type === 'HudlScheduleEntry' &&
      l.id === scheduleEntry.scheduleEntryId
  );
};

export const isTicketedEventUpcoming = (ticketedEvent: TicketedEvent | undefined) => {
  if (!ticketedEvent) {
    return false;
  }

  return ticketedEvent.eventStatus === EventStatus.Upcoming;
};
