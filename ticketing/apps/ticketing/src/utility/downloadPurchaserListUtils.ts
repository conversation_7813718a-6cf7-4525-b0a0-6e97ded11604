import { format } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { jsPDF as JsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

import { formatMessage } from 'frontends-i18n';

import { ItemType, PaymentType as TicketGroupPaymentType, TicketGroupSource } from '../enums/shared';
import { TicketedEvent, TicketGroup } from '../graphql/generated/graphqlTypes';
import { downloadCsv, escapeCsvValue } from './csvUtils';
import { formatDateTimeString, getDate } from './dateTimeUtils';
import {
  getFormFieldResponseString,
  getPaymentTypeDisplayNameKey,
  getReservedSeatStringForTicketGroup,
  getSourceDisplayNameKey,
} from './ticketGroupUtils';
import { getTicketTypeNamesFromTicketGroup } from './ticketTypeUtils';

export type DownloadablePurchaserListColumn = {
  id: string;
  label: string;
  checked: boolean;
};

export enum DownloadablePurchaserListDefaultColumns {
  FirstName = 'FirstName',
  LastName = 'LastName',
  Email = 'Email',
  ReservedSeats = 'ReservedSeats',
  OrderNumber = 'OrderNumber',
  PurchaseSource = 'PurchaseSource',
  PaymentType = 'PaymentType',
  TimeOfPurchase = 'TimeOfPurchase',
  AmountPurchased = 'AmountPurchased',
  TicketTypes = 'TicketTypes',
}

export const getDefaultDownloadablePurchaserListColumns = (itemType: ItemType): DownloadablePurchaserListColumn[] => {
  return [
    {
      id: DownloadablePurchaserListDefaultColumns.FirstName,
      label: formatMessage({ id: 'ticketing.download-purchaser-list-modal.checkbox-label.first-name' }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.LastName,
      label: formatMessage({ id: 'ticketing.download-purchaser-list-modal.checkbox-label.last-name' }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.Email,
      label: formatMessage({ id: 'ticketing.download-purchaser-list-modal.checkbox-label.email' }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.ReservedSeats,
      label: formatMessage({ id: 'ticketing.download-purchaser-list-modal.checkbox-label.reserved-seats' }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.OrderNumber,
      label: formatMessage({ id: 'ticketing.download-purchaser-list-modal.checkbox-label.order-number' }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.PurchaseSource,
      label: formatMessage({ id: 'ticketing.download-purchaser-list-modal.checkbox-label.purchase-source' }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.PaymentType,
      label: formatMessage({ id: 'ticketing.download-purchaser-list-modal.checkbox-label.payment-type' }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.TimeOfPurchase,
      label: formatMessage({ id: 'ticketing.download-purchaser-list-modal.checkbox-label.time-of-purchase' }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.TicketTypes,
      label: formatMessage({
        id: 'ticketing.download-purchaser-list-modal.checkbox-label.ticket-types-purchased',
      }),
      checked: false,
    },
    {
      id: DownloadablePurchaserListDefaultColumns.AmountPurchased,
      label:
        itemType === ItemType.TicketedEvent
          ? formatMessage({ id: 'ticketing.ticketed-event-details.number-tickets' })
          : formatMessage({ id: 'ticketing.ticketed-event-details.number-passes' }),
      checked: false,
    },
  ];
};

const buildTitleDateString = (type: ItemType, referenceDates: string[], timeZoneIdentifier?: string) => {
  switch (type) {
    case ItemType.PassConfig: {
      const startDate = referenceDates[0];
      const endDate = referenceDates[1];

      if (!startDate || !endDate) {
        return '';
      }
      const passTitleDateFormat = 'yyyy/MM/dd';
      const startDateString = format(getDate(startDate), passTitleDateFormat);
      const endDateString = format(getDate(endDate), passTitleDateFormat);
      return `${startDateString} - ${endDateString}`;
    }
    case ItemType.TicketedEvent: {
      const date = referenceDates[0];
      if (!date || !timeZoneIdentifier) {
        return '';
      }
      const eventTitleDateFormat = 'yyyy/MM/dd HH:mm aaa zzz';
      return formatInTimeZone(date, timeZoneIdentifier, eventTitleDateFormat);
    }
    default:
      return '';
  }
};

export const buildFileDateString = (type: ItemType, referenceDates: string[], timeZoneIdentifier?: string) => {
  const filenameDateFormat = 'yyyy-MM-dd';
  switch (type) {
    case ItemType.PassConfig: {
      const startDate = referenceDates[0];
      const endDate = referenceDates[1];

      if (!startDate || !endDate) {
        return '';
      }
      const startDateString = format(getDate(startDate), filenameDateFormat);
      const endDateString = format(getDate(endDate), filenameDateFormat);
      return `${startDateString}_${endDateString}`;
    }
    case ItemType.TicketedEvent: {
      const date = referenceDates[0];
      if (!date || !timeZoneIdentifier) {
        return '';
      }
      return formatInTimeZone(date, timeZoneIdentifier, filenameDateFormat);
    }
    default:
      return '';
  }
};

export const sanitizeFilename = (input: string) => {
  // Replace spaces with underscores
  let sanitized = input.replace(/\s+/g, '-');

  // Remove invalid characters for filenames
  sanitized = sanitized.replace(/[^a-zA-Z0-9._-]/g, '');

  // Optionally, truncate the filename if it exceeds typical limits (e.g., 255 characters for most filesystems)
  if (sanitized.length > 250) {
    sanitized = sanitized.substring(0, 100);
  }

  return sanitized.toLowerCase();
};

export const buildFileName = (
  referenceName: string | undefined,
  referenceDate: string | undefined,
  extension: string
) => {
  const referenceNamePart = referenceName ? `${referenceName}_` : '';
  const referenceDatePart = referenceDate ? `${referenceDate}_` : '';
  const suffix = 'purchaser-list';
  const fileName = `${referenceDatePart}${referenceNamePart}${suffix}`;
  const sanitizedName = sanitizeFilename(fileName);
  return `${sanitizedName}.${extension.toLowerCase()}`;
};

export const buildPurchaserListHeaders = (columnData: DownloadablePurchaserListColumn[]) => {
  return columnData.filter((column) => column.checked).map((column) => column.label);
};

export const buildPurchaserListData = (
  ticketGroups: TicketGroup[],
  type: ItemType,
  columnData: DownloadablePurchaserListColumn[],
  excludeRowsWithoutNameEmail: boolean,
  escapeCsvValues: boolean,
  excludeComplimentary: boolean,
  ticketedEvent?: TicketedEvent
) => {
  const csvData: string[][] = [];
  const checkedColumns = columnData.filter((column) => column.checked);

  const ticketGroupData = ticketGroups.filter((tg) => {
    const hasNameAndEmail = !excludeRowsWithoutNameEmail || (tg.firstName && tg.lastName && tg.email);
    const isNotComplimentary = !excludeComplimentary || tg.source !== TicketGroupSource.Complimentary;
    return hasNameAndEmail && isNotComplimentary;
  });

  ticketGroupData.forEach((ticketGroup) => {
    const row = checkedColumns.map((column) => {
      switch (column.id) {
        case DownloadablePurchaserListDefaultColumns.FirstName:
          return ticketGroup.firstName ?? '';
        case DownloadablePurchaserListDefaultColumns.LastName:
          return ticketGroup.lastName ?? '';
        case DownloadablePurchaserListDefaultColumns.Email:
          return ticketGroup.email ?? '';
        case DownloadablePurchaserListDefaultColumns.ReservedSeats:
          return getReservedSeatStringForTicketGroup(ticketGroup, type);
        case DownloadablePurchaserListDefaultColumns.OrderNumber:
          return ticketGroup.ticketGroupReference ?? '';
        case DownloadablePurchaserListDefaultColumns.PurchaseSource: {
          const purchaseTypeKey = getSourceDisplayNameKey(ticketGroup.source as TicketGroupSource);
          const purchaseType = purchaseTypeKey ? formatMessage({ id: purchaseTypeKey }) : '-';
          return purchaseType;
        }
        case DownloadablePurchaserListDefaultColumns.PaymentType: {
          const paymentTypeKey = getPaymentTypeDisplayNameKey(ticketGroup.paymentType as TicketGroupPaymentType);
          const paymentType = paymentTypeKey ? formatMessage({ id: paymentTypeKey }) : '-';
          return paymentType;
        }
        case DownloadablePurchaserListDefaultColumns.TimeOfPurchase:
          return formatDateTimeString(ticketGroup.createdAt);
        case DownloadablePurchaserListDefaultColumns.AmountPurchased:
          return (
            (type === ItemType.PassConfig ? ticketGroup?.passes?.length : ticketGroup?.tickets?.length) ?? 0
          ).toString();
        case DownloadablePurchaserListDefaultColumns.TicketTypes:
          return ticketedEvent ? getTicketTypeNamesFromTicketGroup(ticketGroup, ticketedEvent) : '';
        default:
          return getFormFieldResponseString(ticketGroup, column.id);
      }
    });

    const rowValues = escapeCsvValues ? row.map(escapeCsvValue) : row;
    csvData.push(rowValues);
  });

  return csvData;
};

export const downloadPurchaserListCsv = (
  ticketGroups: TicketGroup[],
  type: ItemType,
  columnData: DownloadablePurchaserListColumn[],
  referenceName: string | undefined,
  referenceDates: string[],
  referenceTimeZone: string | undefined,
  excludeRowsWithoutNameEmail: boolean,
  excludeComplimentary: boolean,
  ticketedEvent?: TicketedEvent
) => {
  const headers = buildPurchaserListHeaders(columnData);

  const escapedReferenceName = escapeCsvValue(referenceName);
  const escapedReferenceDate = escapeCsvValue(buildTitleDateString(type, referenceDates, referenceTimeZone));

  const titleRow = new Array(headers.length).fill('');
  titleRow[0] = escapedReferenceName;
  titleRow[headers.length - 1] = escapedReferenceDate;

  if (headers.length === 1) {
    titleRow[0] = `${escapedReferenceName} ${escapedReferenceDate}`;
  }

  const data = buildPurchaserListData(
    ticketGroups,
    type,
    columnData,
    excludeRowsWithoutNameEmail,
    true,
    excludeComplimentary,
    ticketedEvent
  );

  downloadCsv(
    [titleRow, [], headers, ...data],
    buildFileName(referenceName, buildFileDateString(type, referenceDates, referenceTimeZone), 'csv')
  );
};

export const downloadPurchaserListPdf = (
  ticketGroups: TicketGroup[],
  type: ItemType,
  columnData: DownloadablePurchaserListColumn[],
  referenceName: string | undefined,
  referenceDates: string[],
  referenceTimeZone: string | undefined,
  excludeRowsWithoutNameEmail: boolean,
  excludeComplimentary: boolean,
  ticketedEvent: TicketedEvent | undefined
) => {
  const headers = buildPurchaserListHeaders(columnData);
  const data = buildPurchaserListData(
    ticketGroups,
    type,
    columnData,
    excludeRowsWithoutNameEmail,
    false,
    excludeComplimentary,
    ticketedEvent
  );

  const titleString = `${referenceName ?? ''} ${buildTitleDateString(type, referenceDates, referenceTimeZone)}`;

  const doc = new JsPDF('landscape');
  doc.setFontSize(16);
  doc.text(titleString, 15, 20);

  autoTable(doc, {
    head: [headers],
    body: data,
    startY: 25,
    styles: {
      cellPadding: 2,
      fontSize: 12,
    },
    theme: 'striped',
    headStyles: { fillColor: [78, 93, 108] },
  });

  doc.save(buildFileName(referenceName, buildFileDateString(type, referenceDates, referenceTimeZone), 'pdf'));
};
