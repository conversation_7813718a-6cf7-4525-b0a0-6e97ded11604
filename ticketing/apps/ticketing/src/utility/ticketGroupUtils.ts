import { ItemType, PaymentType, TicketGroupSource } from '../enums/shared';
import {
  ReservedSeat,
  TicketCountByPaymentType,
  TicketCountForTicketType,
  TicketGroup,
} from '../graphql/generated/graphqlTypes';
import { TicketTypeWithOverrides } from '../types/shared';
import { IntlMessageKeys } from './intlUtils';
import { buildShorthandReservedSeatString } from './reservedSeatStringUtils';
import { getPriceOfTicketTypeWithOverrides } from './ticketTypeOverrideUtils';

export const getSourceDisplayNameKey = (ticketGroupSource: TicketGroupSource | undefined): IntlMessageKeys | '' => {
  switch (ticketGroupSource) {
    case TicketGroupSource.Mobile:
      return 'ticketing.source.mobile';
    case TicketGroupSource.Web:
      return 'ticketing.source.web';
    case TicketGroupSource.PointOfSale:
      return 'ticketing.source.pos';
    case TicketGroupSource.Complimentary:
      return 'ticketing.source.complimentary';
    case TicketGroupSource.Shared:
      return 'ticketing.source.shared';
    case TicketGroupSource.Transfer:
      return 'ticketing.source.transfer';
    case TicketGroupSource.ManualEntry:
      return 'ticketing.source.manual-entry';
    default:
      return '';
  }
};

export const getPaymentTypeDisplayNameKey = (paymentType: PaymentType): string => {
  switch (paymentType) {
    case PaymentType.External:
      return 'ticketing.payment-type.external';
    case PaymentType.Electronic:
      return 'ticketing.payment-type.electronic';
    default:
      return '-';
  }
};

export const getFormattedLastFirstName = (ticketGroup: TicketGroup) => {
  if (!ticketGroup) return '';

  if (ticketGroup.lastName && ticketGroup.firstName) {
    return `${ticketGroup.lastName}, ${ticketGroup.firstName}`;
  }
  return ticketGroup.lastName || ticketGroup.firstName || '';
};

export const getFormFieldResponseString = (ticketGroup: TicketGroup, formFieldId: string) => {
  if (!ticketGroup || !ticketGroup.formFieldResponses) return '';

  const formFieldResponse = ticketGroup.formFieldResponses.find((response) => response.formFieldId === formFieldId);
  return formFieldResponse?.response.join(', ') || '';
};

export const getReservedSeatStringForTicketGroup = (ticketGroup: TicketGroup, type: ItemType): string => {
  let reservedSeats: ReservedSeat[] = [];

  switch (type) {
    case ItemType.TicketedEvent:
      reservedSeats =
        ticketGroup.tickets?.map((t) => t.reservedSeat).filter((rs): rs is ReservedSeat => rs !== undefined) ?? [];
      break;
    case ItemType.PassConfig:
      reservedSeats =
        ticketGroup.passes?.flatMap((p) => p.reservedSeats).filter((rs): rs is ReservedSeat => rs !== undefined) ?? [];
      break;
    default:
      break;
  }

  const reservedSeatString = reservedSeats.map((rs) => rs && buildShorthandReservedSeatString(rs)).join(', ');
  return reservedSeatString;
};

export const splitComplimentaryTicketGroups = (
  ticketGroups: TicketGroup[]
): {
  complimentaryTicketGroups: TicketGroup[];
  nonComplimentaryTicketGroups: TicketGroup[];
} => {
  const [complimentaryTicketGroups, nonComplimentaryTicketGroups] = ticketGroups.reduce(
    ([complimentary, nonComplimentary], ticketGroup) => {
      if (ticketGroup.source === TicketGroupSource.Complimentary) {
        complimentary.push(ticketGroup);
      } else {
        nonComplimentary.push(ticketGroup);
      }
      return [complimentary, nonComplimentary];
    },
    [[], []] as [TicketGroup[], TicketGroup[]]
  );

  return { complimentaryTicketGroups, nonComplimentaryTicketGroups };
};

export const getTotalSalesRevenueByAllPaymentTypes = (
  ticketCountForTicketType: TicketCountForTicketType[] | undefined,
  ticketTypesWithOverridesDict: Record<string, TicketTypeWithOverrides>
) => {
  const revenueByPaymentType: Record<PaymentType, number> = {} as Record<PaymentType, number>;
  let totalRevenue = 0;

  Object.values(PaymentType).forEach((paymentType) => {
    revenueByPaymentType[paymentType] = 0;
  });

  ticketCountForTicketType?.forEach((curr) => {
    const priceInCents = getPriceOfTicketTypeWithOverrides(ticketTypesWithOverridesDict[curr.ticketTypeId!]) ?? 0;

    curr.ticketCountByPaymentType?.forEach((ticketCount: TicketCountByPaymentType) => {
      const salesCount = ticketCount.ticketCount ?? 0;
      const revenue = salesCount * priceInCents;

      if ((ticketCount.paymentType as PaymentType) in revenueByPaymentType) {
        revenueByPaymentType[ticketCount.paymentType as PaymentType] += revenue;
        totalRevenue += revenue;
      }
    });
  });

  return { revenueByPaymentType, totalRevenue };
};
