{"name": "ticketing", "version": "0.1.0", "private": false, "contributors": [{"name": "Fan Team 6", "url": "https://hudl.slack.com/archives/C04JT6E22DA", "channel": "#squad-fan-team-6"}], "scripts": {"antioch": "vite-node ../../../internal/scripts/src/antioch-runner.ts --port 8098 -p 3", "build": "vite build", "build-storybook": "storybook build --stats-json", "build:analyze": "webpack-bundle-analyzer build/stats.json", "build:verify": "node ../../../internal/scripts/verify-bundle-stats.js", "clean": "rimraf build node_modules/.cache storybook-static", "dev": "vite-node ../../../internal/scripts/src/copy-dev-config.ts && concurrently \"pnpm run types:watch\" \"vite\"", "download-screenshots": "vite-node ../../../internal/scripts/src/upload-screenshots.ts --download --app example", "generate-gql": "./src/graphql/codegen/codegen.sh", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "nuke": "pnpm run clean && rimraf node_modules", "playwright": "tsx ../../../internal/scripts/src/playwright-runner.ts --port 8098 -d app", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "preview": "vite preview", "release": "release-vite-app", "sentry:release": ". ../../../internal/config/sentry_release.sh", "storybook": "storybook dev -p 9021", "test": "TZ=UTC vitest", "test:ci": "TZ=UTC vitest --coverage --silent", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src", "upload-screenshots": "vite-node ../../../internal/scripts/src/upload-screenshots.ts --app example", "vr-playwright": "echo VR Playwright has not been enabled for this app. To enable update the playwright script to: vite-node ../../../packages/scripts/src/playwright-runner.ts --config ./playwright/vr-playwright.config.ts"}, "dependencies": {"@apollo/client": "3.7.11", "@formatjs/intl": "2.10.1", "@hudl/analytics": "workspace:*", "@hudl/frontends-environment": "workspace:*", "@hudl/frontends-logging": "workspace:*", "@hudl/seating-charts": "workspace:*", "@hudl/uniform-web": "workspace:*", "@hudl/uniform-web-actions-legacy": "npm:@hudl/uniform-web-actions@5.64.0", "@hudl/uniform-web-button-legacy": "npm:@hudl/uniform-web-button@5.54.0", "@hudl/uniform-web-dialogs-legacy": "npm:@hudl/uniform-web-dialogs@5.68.0", "@hudl/uniform-web-forms-legacy": "npm:@hudl/uniform-web-forms@5.60.0", "@hudl/uniform-web-notifications-legacy": "npm:@hudl/uniform-web-notifications@5.66.0", "@local/webnav": "workspace:*", "@react-hookz/web": "23.1.0", "@sentry/browser": "7.77.0", "@sentry/react": "7.77.0", "@types/papaparse": "5.3.14", "@uppy/core": "4.4.2", "@uppy/dashboard": "4.3.1", "@uppy/drag-drop": "4.1.1", "@uppy/file-input": "4.1.1", "@uppy/progress-bar": "4.2.1", "@uppy/react": "4.2.1", "@uppy/xhr-upload": "4.3.2", "classnames": "2.5.1", "date-fns": "2.29.3", "date-fns-tz": "2.0.0", "frontends-i18n": "workspace:*", "frontends-preload": "workspace:*", "frontends-shared": "workspace:*", "graphql": "16.8.1", "jspdf": "3.0.1", "jspdf-autotable": "3.8.4", "jwt-decode": "3.1.2", "onboarding": "workspace:*", "papaparse": "5.4.1", "react-intl": "6.6.4", "react-router-dom": "6.22.3", "react-select": "5.8.0", "react-timezone-select": "2.1.2", "ticketing-shared": "workspace:*", "upper-case": "^3.0.0"}, "devDependencies": {"@graphql-codegen/add": "4.0.1", "@graphql-codegen/cli": "3.3.1", "@graphql-codegen/client-preset": "3.0.1", "@graphql-codegen/introspection": "3.0.1", "@graphql-codegen/typescript": "4.0.6", "@graphql-codegen/typescript-operations": "4.2.0", "@graphql-codegen/typescript-react-apollo": "3.3.7", "@hudl/eslint-config": "workspace:*", "@hudl/playwright-config": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "@vitest/coverage-v8": "3.2.4", "config": "workspace:*", "eslint": "8.45.0", "graphql-codegen-typescript-mock-data": "3.2.2", "jsdom": "24.0.0", "playwright-shared": "workspace:*", "vite": "5.4.7"}}