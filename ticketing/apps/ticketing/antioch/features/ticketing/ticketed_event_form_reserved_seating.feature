
Feature: Hudl Ticketing - Ticketed Event Form - Reserved Seating Flow

    # ==============
    # Notes
    # ==============

    To use the Reserved Seating workflow, you must select Reserved Seating ticketed event type in the
    first step of the ticketed event form.

    Ticketed Event Form URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}/add-ticketing/{scheduleEntryId}


    # ==============
    # Page Structure
    # ==============

    # ==== Free and Paid Ticketing ==== #

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating - School with No Venues
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/181416/add-ticketing/U2NoZWR1bGVFbnRyeTI1OTI0NTky
        When I load the ticketed event form for organization "181416" and the scheduled event "U2NoZWR1bGVFbnRyeTI1OTI0NTky"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        And the "Select a Type" form step indicator is selected and completed
        And the selected "General Admission" ticketed event type radio option is visible
        And the disabled "Reserved Seating" ticketed event type radio option is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating - School with No Seating Charts
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/181428/add-ticketing/U2NoZWR1bGVFbnRyeTI1OTI1NjEx
        When I load the ticketed event form for organization "181428" and the scheduled event "U2NoZWR1bGVFbnRyeTI1OTI1NjEx"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        And the "Select a Type" form step indicator is selected and completed
        And the selected "General Admission" ticketed event type radio option is visible
        And the disabled "Reserved Seating" ticketed event type radio option is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating - Navigation
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        And the "Select a Type" form step indicator is selected and completed
        And the "Add Event Details" form step indicator is not selected nor completed
        And the "Add Tickets" form step indicator is completed
        And the "Review & Publish" form step indicator is not selected nor completed
        And the selected "General Admission" ticketed event type radio option is visible
        When I click on the "Reserved Seating" ticketed event type radio option
        Then the selected "Reserved Seating" ticketed event type radio option is visible
        And the "Select a Type" form step indicator is selected and completed
        And the "Add Event Details" form step indicator is not selected nor completed
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        And the "Select a Type" form step indicator is completed
        And the "Add Event Details" form step indicator is selected
        And the disabled Continue button is visible
        When I click on the ticketed event form Back button
        Then the "Ticketed Event Type" form step headline is visible
        And the "Select a Type" form step indicator is selected and completed
        And the "General Admission" ticketed event type radio option is visible
        And the selected "Reserved Seating" ticketed event type radio option is visible


    # ==============
    # Ticket Information Interactions
    # ==============

    # ==== Free Ticketing ==== #

    @regression @non_prod @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating Workflow - Free Ticketing
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the "Reserved Seating" ticketed event type radio option
        And I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Alaska option
        And I select the "America/Juneau" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Tickets" form step indicator is selected
        And the Venue select is visible
        And the disabled Seating Layout select is visible
        And the disabled Review Event button is visible
        When I click on the Venue select to open it
        And I select the "UI Automation - Outdoor Venue 1" option in the Venue select
        And I click on the Seating Layout select to open it
        And I select the "Automation - Outdoor Venue Config 2 - With Seating Chart + One Category" option in the Seating Layout select
        Then the Seating Layout option selected is "Automation - Outdoor Venue Config 2 - With Seating Chart + One Category"
        And the seating layout preview is visible
        And the Create Ticket Type button is not visible
        And the disabled Review Event button is visible
        When I click on the Select Ticket Type(s) select to open it
        # This is the Free Admission - $0.00 option in the select (the only option)
        And I select the "VGlja2V0VHlwZTY0M2RiNmMwNzIyMjZjMjY5Njg1NTg5YQ==" option in the Select Ticket Type(s) select
        Then the Select Ticket Type(s) option selected is "Free Admission - $0.00"
        When I click on the Review Event button
        Then the "Review & Publish" form step indicator is selected and completed
        And the active Save as Draft button is visible
        And the active Publish button is visible

    # ==== Paid Ticketing ==== #

    # TODO: Look into the commented out Then step below to format it better
    @regression @non_prod @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating Workflow - Paid Ticketing
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the "Reserved Seating" ticketed event type radio option
        And I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Alaska option
        And I select the "America/Juneau" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Tickets" form step indicator is selected
        And the Venue select is visible
        And the disabled Seating Layout select is visible
        And the disabled Review Event button is visible
        When I click on the Venue select to open it
        And I select the "UI Automation - Paid Ticketing - Outdoor Venue 1" option in the Venue select
        And I click on the Seating Layout select to open it
        And I select the "Venue Config 2 - Seating Chart with One Category" option in the Seating Layout select
        Then the Seating Layout option selected is "Venue Config 2 - Seating Chart with One Category"
        And the seating layout preview is visible
        And the Create Ticket Type button is visible
        And the disabled Review Event button is visible
        When I click on the Select Ticket Type(s) select to open it
        # This is the Multiple Ticket Type 1 - $20.00 option in the select (the only option)
        And I select the "VGlja2V0VHlwZTY0ZWE0YmY4OTAxY2NiNGE0NTNjOTVmZg==" option in the Select Ticket Type(s) select
        # This is the Multiple Ticket Type 3 - $5.00 option in the select (the only option)
        And I select the "VGlja2V0VHlwZTY0ZWE0Y2JhYzc2NjY3OWU3YTgxNDY0Nw==" option in the Select Ticket Type(s) select
        #Then the Select Ticket Type(s) option selected is "Multiple Ticket Type 3 - $5.00 Multiple Ticket Type 1 - $20.00"
        When I click on the Review Event button
        Then the "Review & Publish" form step indicator is selected and completed
        And the active Save as Draft button is visible
        And the active Publish button is visible

    @regression @non_prod @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating Workflow - Paid Ticketing - Add New Ticket Type Modal
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the "Reserved Seating" ticketed event type radio option
        And I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Alaska option
        And I select the "America/Juneau" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Tickets" form step indicator is selected
        And the Venue select is visible
        And the disabled Seating Layout select is visible
        And the disabled Review Event button is visible
        When I click on the Venue select to open it
        And I select the "UI Automation - Paid Ticketing - Outdoor Venue 1" option in the Venue select
        And I click on the Seating Layout select to open it
        And I select the "Venue Config 1 - Seating Chart with Multiple Categories" option in the Seating Layout select
        Then the Seating Layout option selected is "Venue Config 1 - Seating Chart with Multiple Categories"
        And the seating layout preview is visible
        When I click on the Create Ticket Type button
        Then the Add New Ticket Type modal is visible
        And the Ticket Type Name input in the Add New Ticket Type modal is visible
        And the Price input in the Add New Ticket Type modal is visible
        And the Add to Section select in the Add New Ticket Type modal is visible
        And the add ticket type to section note in the Add New Ticket Type modal is visible
        And the disabled Save button in the Add New Ticket Type modal is visible

    # ==== Free and Paid Ticketing ==== #

    @smoke @non_prod @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating Workflow - Venue with Only One Venue Configuration
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the "Reserved Seating" ticketed event type radio option
        And I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Alaska option
        And I select the "America/Juneau" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        When I click on the Venue select to open it
        And I select the "UI Automation - Indoor Venue 1" option in the Venue select
        Then the Seating Layout option selected is "Automation - Indoor Venue Config 1 - With Valid Seating Chart"
        And the seating layout preview is visible
        And the Ticket Types by Section container is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating Workflow - Venue Configuration with No Categories
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the "Reserved Seating" ticketed event type radio option
        And I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Alaska option
        And I select the "America/Juneau" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        When I click on the Venue select to open it
        And I select the "UI Automation - Outdoor Venue 1" option in the Venue select
        And I click on the Seating Layout select to open it
        And I select the "Automation - Outdoor Venue Config 3 - With Seating Chart + No Categories" option in the Seating Layout select
        Then the Seating Layout option selected is "Automation - Outdoor Venue Config 3 - With Seating Chart + No Categories"
        And the No Sections Available state is visible
        And the No Sections Available Contact Support button is clickable

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Reserved Seating Workflow - Venue with No Venue Configuration
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the "Reserved Seating" ticketed event type radio option
        And I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Alaska option
        And I select the "America/Juneau" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        When I click on the Venue select to open it
        And I select the "UI Automation - Indoor Venue 2" option in the Venue select
        Then the error Seating Layout select is visible
