Feature: Hudl Ticketing - Ticketed Event Form - General Admission Flow

    # ==============
    # Notes
    # ==============

    Tests around the ticketed event form for the General Admission ticketed event, where a ticketed event has not been created yet.

    Ticketed Event Form URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}/add-ticketing/{scheduleEntryId}


    # ==============
    # Navigation
    # ==============

    @manual @smoke
    Scenario: Attempt to Load Ticketed Event Form - Logged Out User
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the page is redirected to the Hudl Login page

    @manual @smoke
    Scenario: Attempt to Load Ticketed Event Form - Athlete
        Given I log in to <PERSON><PERSON> through the API as a basketball athlete
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the "You need permission to access this page." 403 forbidden page is visible

    @manual @smoke
    Scenario: Attempt to Load Ticketed Event Form - Coach
        Given I log in to Hudl through the API as a basketball coach
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the "You need permission to access this page." 403 forbidden page is visible

    @manual @smoke
    Scenario: Attempt to Load Ticketed Event Form - Admin
        Given I log in to Hudl through the API as a basketball admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the "You need permission to access this page." 403 forbidden page is visible

    @manual @smoke
    Scenario: Load Ticketed Event Form - Admin
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible

    @manual @smoke
    Scenario: Navigate to the Ticketed Event Form - Admin
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137
        When I load the Hudl Ticketing Management Home Page for organization "174137"
        Then the Hudl Ticketing Management Home Page is visible
        When I click on the Add Ticketing button for scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible

    # ==============
    # Page Structure
    # ==============

    # ==== Free Ticketing ==== #

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Page Structure - Free Ticketing - Ticketed Event Type Step
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        And the "Select a Type" form step indicator is selected and completed
        And the "Add Event Details" form step indicator is not selected nor completed
        And the "Add Tickets" form step indicator is completed
        And the "Review & Publish" form step indicator is not selected nor completed
        And the selected "General Admission" ticketed event type radio option is visible
        And the "Reserved Seating" ticketed event type radio option is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Page Structure - Free Ticketing - Ticket Information Step
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Time Zone select to open it
        # This is the Mountain Time option
        And I select the "America/Boise" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Tickets" form step indicator is selected and completed
        And the ticket pricing description text is not visible
        And the disabled Type select says "Free Admission"
        And the read only Price input says "0.00"
        And the price input help text is not visible
        And the Quantity for Sale input does not have a quantity set by default
        And the Quantity for Sale help text is visible
        And the Add button to add another ticket type selection is not visible
        And the Bundled Fees section is not visible

    # ==== Paid Ticketing ==== #

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Page Structure - Paid Ticketing - Ticket Information Step
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Tickets" form step indicator is selected
        And the ticket pricing description text is visible
        And the Type select says "Select ticket price option"
        And the read only Price input says "0.00"
        And the price input help text is visible
        And the disabled Quantity for Sale input is visible
        And the disabled Quantity for Sale help text is visible
        And the Add button to add another ticket type selection is visible
        And the Bundled Fees section is visible
        And the Your Fans option in the Bundled Fees section is selected
        When I hover over the Your Fans info tooltip icon in the Bundled Fees section
        Then the Your Fans tooltip is visible
        When I hover over the Your Organization info tooltip icon in the Bundled Fees section
        Then the Your Organization tooltip is visible

    # ==== Free and Paid Ticketing ==== #

    @manual @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Page Structure - Free Ticketing - Event Details Step
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        And the "Add Event Details" form step indicator is selected
        And the "Add Tickets" form step indicator is completed
        And the "Review & Publish" form step indicator is not selected nor completed
        And the "Event Details" form step headline is visible
        And the review event information text is visible
        And the disabled Scheduled Event dropdown is visible
        And the Date input is visible
        And the Start Time input is visible
        And the Time Zone select is visible
        And the event details description text is visible
        And the Event Title input is visible
        And the Event Title help text is visible
        And the Event Description is visible
        And the visibility section is visible
        And the form field section is visible
        And the active Add Custom Field button is visible
        And the disabled Continue button is visible
        And the Cancel button is visible

    @regression @migrated @non_automated
    Scenario: Ticketed Event Form - Page Structure - Scheduled Event Info Auto-Populated
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137
        When I load the Hudl Ticketing Management Home Page for organization "174137"
        Then the Hudl Ticketing Management Home Page is visible
        When I click on the Events "Non-Ticketed" menu item
        And I click on the Add Ticketing button for scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Add Event Details" form step indicator is selected
        And the "Event Details" form step headline is visible
        And the Scheduled Event dropdown has the "Men's Varsity Soccer vs Hudl Automation - Reports" event selected
        And the Date input says "12/20/2026"
        # Not including the start time because the timezone on the TC agents are different
        # and the time shown is different. I'll find a way to make it work in the future
        # And the Start Time input says "03:00 PM"
        And the Event Title input says "Men's Varsity Soccer vs Hudl Automation - Reports"
        And the "Public" visibility option is selected

    # ==== Ad Hoc Ticketed Event Ticketing ==== #

    # Investigate
    @broken @regression
    Scenario: Ticketed Event Form - Page Structure - Ad Hoc Ticketed Event
        Given I log in to Hudl Admin through the API as a hudl employee
        And I backdoor in as user "19187634"
        # https://main--latest.app.thorhudl.com/ticketing/174137
        When I load the Hudl Ticketing Management Home Page for organization "174137"
        Then the Hudl Ticketing Management Home Page is visible
        When I click on the Create button for creating an ad hoc ticketed event
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Add Event Details" form step indicator is selected
        And the Scheduled Event dropdown is not visible
        And the Date input has today's date auto-populated
        And the Event Title input is not auto-populated
        And the visibility section is visible
        And the form field section is visible
        And the active Add Custom Field button is visible
        And the disabled Continue button is visible
        And the Cancel button is visible


    # ==============
    # Ticketed Event Form Interactions
    # ==============

    @smoke @non_automated @migrated
    Scenario: Ticketed Event Form - Free Ticketing - Move Between Steps Interactions
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137
        When I load the Hudl Ticketing Management Home Page for organization "174137"
        Then the Hudl Ticketing Management Home Page is visible
        When I click on the Events "Non-Ticketed" menu item
        And I click on the Add Ticketing button for scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the "Select a Type" form step indicator is selected and completed
        And the "Add Event Details" form step indicator is not selected nor completed
        And the "Add Tickets" form step indicator is completed
        And the "Review & Publish" form step indicator is not selected nor completed
        When I click on the Continue button
        Then the "Select a Type" form step indicator is completed
        And the "Add Event Details" form step indicator is selected
        And the "Add Tickets" form step indicator is completed
        And the "Review & Publish" form step indicator is not selected nor completed
        When I click on the Time Zone select to open it
        # This is the Mountain Time option
        And I select the "America/Boise" option in the Time Zone select
        Then the "Add Event Details" form step indicator is selected and completed
        When I click on the "Review & Publish" form step indicator
        Then the "Review & Publish" form step headline is visible
        And the "Select a Type" form step indicator is completed
        And the "Add Event Details" form step indicator is completed
        And the "Add Tickets" form step indicator is completed
        And the "Review & Publish" form step indicator is selected and completed
        When I click on the ticketed event form Back button
        Then the "Ticket Information" form step headline is visible
        And the "Select a Type" form step indicator is completed
        And the "Add Event Details" form step indicator is completed
        And the "Add Tickets" form step indicator is selected and completed
        And the "Review & Publish" form step indicator is not selected nor completed
        When I click on the Review Event button
        Then the "Review & Publish" form step headline is visible
        And the "Review & Publish" form step indicator is selected and completed

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Cancel Add Ticketing - No Changes Made
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137
        When I load the Hudl Ticketing Management Home Page for organization "174137"
        Then the Hudl Ticketing Management Home Page is visible
        When I click on the Events "Non-Ticketed" menu item
        And I click on the Add Ticketing button for scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        And the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Cancel button
        Then the Hudl Ticketing Management Home Page is visible
        And the Events "Non-Ticketed" menu item is selected

    @regression @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Cancel Add Ticketing - Changes Made
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137
        When I load the Hudl Ticketing Management Home Page for organization "174137"
        Then the Hudl Ticketing Management Home Page is visible
        When I click on the Events "Non-Ticketed" menu item
        And I click on the Add Ticketing button for scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        And the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Time Zone select to open it
        # This is the Mountain Time option
        And I select the "America/Boise" option in the Time Zone select
        And I click on the Cancel button
        Then the Save Changes modal is visible
        When I click on the Discard Changes button in the Save Changes modal
        Then the Hudl Ticketing Management Home Page is visible
        And the Events "Non-Ticketed" menu item is selected
        When I click on the Add Ticketing button for scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        And the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        And no time zone is selected in the Time Zone select

    @regression @non_prod @setup.create.schedule.entry.publish.ticketed.event @cleanup.delete.ticketed.event @cleanup.delete.girls.bball.schedule.entry
    Scenario: Ticketed Event Form - Publish Ticketed Event - Edit Ticketed Event Afterwards
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137
        When I load the Hudl Ticketing Management Home Page for organization "174137"
        Then the Hudl Ticketing Management Home Page is visible
        When I click on the Events "Non-Ticketed" menu item
        And I click on the Add Ticketing button for the created scheduled event
        Then the ticketed event form is visible
        And the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        And the "Add Event Details" form step indicator is selected
        When I click on the Time Zone select to open it
        # This is the Mountain Time option
        And I select the "America/Phoenix" option in the Time Zone select
        Then the "Add Event Details" form step indicator is selected and completed
        When I clear the text in the Event Title input
        Then the "Add Event Details" form step indicator is selected
        When I enter "Ticketed Event Form - Publish Ticketed Event - UI Automation" into the Event Title input
        Then the "Add Event Details" form step indicator is selected and completed
        When I enter "This is an automated test creating this ticketed event" into the Event Description textbox
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Tickets" form step indicator is selected and completed
        When I enter "200" into the Quantity for Sale input
        And I click on the Review Event button
        Then the "Review & Publish" form step headline is visible
        And the "Review & Publish" form step indicator is selected and completed
        And the "Add Tickets" form step indicator is completed
        And the "Add Event Details" form step indicator is completed
        When I click on the Publish button
        Then the Hudl Ticketing Management Home Page is visible
        And the Events "Published" menu item is selected
        And the "Success publishing ticketed event" toast is visible
        And the ticketed event details link is visible for the schedule entry
        And the number of tickets sold is "0"
        When I click on the action list button for the ticketed event
        And I click on the Edit action in the list for the ticketed event
        Then the published ticketed event form is visible
        And the "Review & Publish" form step headline is visible
        When I click on the completed "Add Event Details" form step indicator
        Then the "Event Details" form step headline is visible
        And the "Add Event Details" form step indicator is selected and completed
        And the "Add Tickets" form step indicator is completed
        And the "Review & Publish" form step indicator is not selected nor completed
        And the Time Zone selected is "(GMT-7:00) Arizona (MST)"
        And the Event Title input says "Ticketed Event Form - Publish Ticketed Event - UI Automation"
        And the Event Description textbox contains the text "This is an automated test creating this ticketed event"
        When I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Event Details" form step indicator is completed
        And the "Add Tickets" form step indicator is selected and completed
        And the "Review & Publish" form step indicator is not selected nor completed
        And the Quantity for Sale input has a quantity set for "200"
        When I click on the Review Event button
        Then the "Review & Publish" form step headline is visible
        And the "Select a Type" form step indicator is completed
        And the "Add Event Details" form step indicator is completed
        And the "Add Tickets" form step indicator is completed
        And the "Review & Publish" form step indicator is selected and completed
        Then the disabled Update button is visible
        And the Cancel button is visible

    ## Test to save an event as a draft from the ticketed event form and then publish it from the draft ticketed event
    ## form can be found in the `drafted_ticketed_event_form.feature` file

    @regression @migrated @non_automated
    Scenario: Ticketed Event Form - Invalid Event ID
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0fwes
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0fwes"
        Then the Hudl Ticketing Management Home Page is visible
        And the "Error loading ticketed event" critical toast is visible

    # This has to ALWAYS be run non_thor because we do not show restricted backdoor access on thors
    @smoke @non_thor
    Scenario: Ticketed Event Form - Free Ticketed Event - Publish Button Disabled When Backdoored
        Given I log in to Hudl Admin through the API as a hudl employee
        And I backdoor in as user "19187634"
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Time Zone select to open it
        # This is the Alaska option
        And I select the "America/Juneau" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        When I click on the Review Event button
        Then the active Publish button is visible

    # ==== Add Ticketing Form Validation ==== #

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Form Validation - Date in the Past
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I enter "02/18/2024" into the Date input
        Then the Date input label is in red
        And the Date input has a red border
        And the Date input error help text says "Date cannot be in the past." in red

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Form Validation - No Event Title Text
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        Then the "Event Details" form step headline is visible
        And the disabled Continue button is visible
        When I click on the Time Zone select to open it
        # This is the Alaska option
        And I select the "America/Juneau" option in the Time Zone select
        Then the active Continue button is visible
        When I clear the text in the Event Title input
        # Need to click out of the event title to trigger the error
        And I click on the event description input
        Then the Event Title input label is in red
        And the Event Title input has a red border
        And the Event Title input error help text says "Event title is required." in red
        And the disabled Continue button is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Form Validation - Event Title More Than Max Character Limit
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Pacific Time option
        And I select the "America/Los_Angeles" option in the Time Zone select
        Then the active Continue button is visible
        When I enter "Men's Varsity Soccer vs Hudl Automation - Reports Men's Varsity Soccer vs Hudl Automation - Reports Mens" into the Event Title input
        Then the Event Title input label is in red
        And the Event Title input has a red border
        And the Event Title input error help text says "Character limit: 153/150" in red
        And the disabled Continue button is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Form Validation - Event Description More Than Max Character Limit
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        And I enter "Men's Varsity Soccer vs Hudl Automation - Reports" into the Event Description textbox "41" times
        Then the Event Description textbox label is in red
        And the Event Description textbox has a red border
        And the Event Description textbox error help text says "Character limit: 2009/2000" in red
        And the disabled Continue button is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Form Validation - Add Custom Field Name Input More Than Max Character Limit
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Add Custom Field button
        And I enter "Dietary Restrictions / Allergies" into the Add Custom Field modal Name input "5" times
        Then the Add Custom Field modal Name input label is in red
        And the Add Custom Field modal Name input has a red border
        And the Add Custom Field modal Name input error help text says "Character limit: 160/150" in red
        And the disabled Add Custom Field button in the Add Custom Field modal is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Form Validation - Add Custom Field Help Text Input More Than Max Character Limit
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Add Custom Field button
        And I enter "Enter in any Dietary Restriction or Allergies you may have." into the Add Custom Field modal Help Text input "6" times
        Then the Add Custom Field modal Help Text input label is in red
        And the Add Custom Field modal Help Text input has a red border
        Then the Add Custom Field modal Help Text input error help text says "Character limit: 354/300" in red
        And the disabled Add Custom Field button in the Add Custom Field modal is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Form Validation - Quantity for Sale - Less Than 1
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Tickets" form step indicator is selected and completed
        And the active Review Event button is visible
        When I enter "0" into the Quantity for Sale input
        Then the "Add Tickets" form step indicator is selected
        And the Quantity for Sale input label is in red
        And the Quantity for Sale input has a red border
        And the Quantity for Sale input error help text says "Quantity must be greater than 0." in red
        And the disabled Review Event button is visible

    # ==============
    # Add Custom Form Fields
    # ==============

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Add New Custom Field Modal Structure
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        When I click on the Add Custom Field button
        Then the Add Custom Field modal is visible
        And the Add Custom Field Name input in the Add Custom Field modal is visible
        And the Help Text input in the Add Custom Field modal is visible
        And the selected "Yes" radio option in the Add Custom Field modal is visible
        And the "No" radio option in the Add Custom Field modal is visible
        And the Cancel button in the Add Custom Field modal is visible
        And the disabled Add Custom Field button in the Add Custom Field modal is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Add New Custom Field Modal - No Help Text - Optional
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        When I click on the Add Custom Field button
        Then the Add Custom Field modal is visible
        And the disabled Add Custom Field button in the Add Custom Field modal is visible
        When I enter "Optional Snack Preference" into the Add Custom Field modal Name input
        And I click on the "No" radio option in the Add Custom Field modal
        Then the active Add Custom Field button in the Add Custom Field modal is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Add New Custom Field Modal - Help Text - Required
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        When I click on the Add Custom Field button
        Then the Add Custom Field modal is visible
        And the disabled Add Custom Field button in the Add Custom Field modal is visible
        When I enter "Required Snack Preference with Help Text" into the Add Custom Field modal Name input
        And I enter "Let us know what snacks you would like us to provide." into the Add Custom Field modal Help Text input
        Then the active Add Custom Field button in the Add Custom Field modal is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Form Field Item Selection - No Help Text - Optional
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        Then the active Add Custom Field button is visible
        When I click on the Form Field select to open it
        # This is the Dietary Restrictions / Allergies option
        And I select the "Rm9ybUZpZWxkNjcyNTRlYWMyYTFjYzg0MDUzYjEyZjEy" option in the Form Field select
        Then there is "1" form field item selected
        And the "Rm9ybUZpZWxkNjcyNTRlYWMyYTFjYzg0MDUzYjEyZjEy" form field option is visible
        And the "Rm9ybUZpZWxkNjcyNTRlYWMyYTFjYzg0MDUzYjEyZjEy" form field option label is visible
        And the "Rm9ybUZpZWxkNjcyNTRlYWMyYTFjYzg0MDUzYjEyZjEy" form field option required label says "Required: No"

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Form Field Item Selection - Help Text - Required
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        Then the active Add Custom Field button is visible
        When I click on the Form Field select to open it
        # This is the T-Shirt Size option
        And I select the "Rm9ybUZpZWxkNjcyNTRlZDQyYTFjYzg0MDUzYjEyZjE3" option in the Form Field select
        Then there are "1" form field items selected
        And the "Rm9ybUZpZWxkNjcyNTRlZDQyYTFjYzg0MDUzYjEyZjE3" form field option is visible
        And there is "1" form field item selected
        And the "Rm9ybUZpZWxkNjcyNTRlZDQyYTFjYzg0MDUzYjEyZjE3" form field option label is visible
        And the "Rm9ybUZpZWxkNjcyNTRlZDQyYTFjYzg0MDUzYjEyZjE3" form field option help text label says "Help Text: Enter your T-Shirt Size: S, M, L, XL, 2XL"
        And the "Rm9ybUZpZWxkNjcyNTRlZDQyYTFjYzg0MDUzYjEyZjE3" form field option required label says "Required: Yes"

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Form Field Item Selection - Deselect Item
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        And I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        Then the active Add Custom Field button is visible
        When I click on the Form Field select to open it
        # This is the T-Shirt Size option
        And I select the "Rm9ybUZpZWxkNjcyNTRlZDQyYTFjYzg0MDUzYjEyZjE3" option in the Form Field select
        Then there are "1" form field items selected
        When I remove the "Rm9ybUZpZWxkNjcyNTRlZDQyYTFjYzg0MDUzYjEyZjE3" form field option
        Then there are "0" form field items selected

    @smoke @manual @migrated @non_automated
    Scenario: Ticketed Event Form - Free Ticketing - Form Field Item Selection at the Selection Limit
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
        When I load the ticketed event form for organization "174137" and the scheduled event "U2NoZWR1bGVFbnRyeTIyMjUzNjM0"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Time Zone select to open it
        And I select the "America/Phoenix" option in the Time Zone select
        Then the active Add Custom Field button is visible
        When I click on the Form Field select to open it
        And I select 10 form field items
        Then there are "10" form field items selected
        And the form field selection limit error is visible
        And the disabled Add Custom Field button is visible

    # ==============
    # Paid Ticketing - Add Ticket Types
    # ==============

    @manual @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Paid Ticketing - Add New Ticket Type Modal Structure
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        And the "Event Details" form step headline is visible
        When I click on the Time Zone select to open it
        # This is the Arizona option
        And I select the "America/Phoenix" option in the Time Zone select
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        When I click on the Type select
        And I click on the Add New Ticket Type option in the Type select
        Then the Add New Ticket Type modal is visible
        And the Ticket Type Name input is visible
        And the Price input is visible
        And the Cancel button is visible
        And the disabled Save button is visible

    @smoke @broken @migrated @non_automated
    Scenario: Ticketed Event Form - Paid Ticketing - Add New Ticket Type Modal Save Button Behavior
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        When I click on the Type select to open it
        And I enter "Free" into the Type select
        And I click on the Add new ticket type option in the Type select
        Then the Add New Ticket Type modal is visible
        And the disabled Save button in the Add New Ticket Type modal is visible
        And the Ticket Type Name input says "Free"
        When I enter "20" into the Price input in the Add New Ticket Type modal
        Then the active Save button in the Add New Ticket Type modal is visible

    @smoke @migrated @non_automated
    Scenario: Ticketed Event Form - Paid Ticketing - Multiple Ticket Types Behavior
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        And I click on the Continue button
        Then the "Ticket Information" form step headline is visible
        And the "Add Tickets" form step indicator is selected
        And there is "1" ticket type row
        And ticket type number "1"'s remove selected ticket type button is not visible
        And the disabled Review Event button is visible
        When I click on the Type select to open it
        # The option being clicked here is the 'Test Automation' option
        And I click on the "Test Automation" option in the Type select
        Then the Type select says "Test Automation" for ticket type "VGlja2V0VHlwZTY0YzJjNDdhODQyODgzNmFkZjc2OTBhNA=="
        And the Price input says "20.00"
        And the Quantity for Sale input does not have a quantity set by default
        And the "Add Tickets" form step indicator is selected and completed
        And the active Review Event button is visible
        When I click on the Add button to add another ticket type selection
        Then there are "2" ticket type rows
        And ticket type number "1"'s remove selected ticket type button is visible
        And ticket type number "2"'s remove selected ticket type button is visible
        When I click on the Add button to add another ticket type selection
        Then there are "3" ticket type rows
        And ticket type number "3"'s remove selected ticket type button is visible
        And the disabled Review Event button is visible
        And the "Add Tickets" form step indicator is selected
        When I click on ticket type number "3"'s remove selected ticket type button
        And I click on ticket type number "2"'s remove selected ticket type button
        Then there is "1" ticket type row
        And the "Add Tickets" form step indicator is selected and completed
        And the active Review Event button is visible

    # ==============
    # Paid Ticketing - Bundled Fees
    # ==============

    # Needs to be non_prod because we can't go through the UI to access a paid ticketed event since
    # the Stripe onboarding step cannot be completed in prod
    @non_prod @migrated @non_automated
    Scenario: Ticketed Event Form - Paid Ticketing - Bundled Fees - Existing Event - Your Organization Selected
        Given I log in to Hudl through the API as a basketball school admin
        # https://main--latest.app.thorhudl.com/ticketing/174137
        When I load the Hudl Ticketing Management Home Page for organization "175428"
        And I click on the action list button for ticketed event "VGlja2V0ZWRFdmVudDY2YmZjNzE1ZjgwMzBhZTQzMzY1Yjk3NQ=="
        And I click on the Edit action in the list for the ticketed event
        Then the published ticketed event form is visible
        When I click on the completed "Add Tickets" form step indicator
        Then the "Ticket Information" form step headline is visible
        And the Bundled Fees section is visible
        And the Bundled Fees tutorial link is visible
        And the Your Organization option in the Bundled Fees section is selected

    @migrated @non_automated
    Scenario: Ticketed Event Form - Paid Ticketing - Bundled Fees - New Event - Your Fans Default
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Time Zone select to open it
        # This is the Mountain Time option
        And I select the "America/Boise" option in the Time Zone select
        And I click on the Continue button
        Then the Bundled Fees section is visible
        And the Your Fans option in the Bundled Fees section is selected
        When I hover over the Your Fans info tooltip icon in the Bundled Fees section
        Then the Your Fans tooltip is visible

    # This passes locally against thor and prod, but for some reason is failing on Prod in the CI
    # Marking as non_prod for now and can test out later when we move it to PW
    @non_prod @migrated @non_automated
    Scenario: Ticketed Event Form - Paid Ticketing - Bundled Fees - New Event - Free Ticket - Selecting Your Organization Doesn't Error
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Continue button
        And I click on the Type select to open it
        And I click on the "Free Ticket Type" option in the Type select
        Then the Type select says "Free Ticket Type" for ticket type "VGlja2V0VHlwZTY2ZTgyZDE1ZDYxNjAyNmMxZmI0YjVkMQ=="
        And the Price input says "0.00"
        And the Price input does not show an error
        And the active Review Event button is visible
        When I select the Your Organization option in the Bundled Fees section
        Then the Price input does not show an error
        And the active Review Event button is visible

    @migrated @non_automated
    Scenario: Ticketed Event Form - Paid Ticketing - Bundled Fees - New Event - 10 Dollar Ticket - Selecting Your Organization Doesn't Error
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        And I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Continue button
        And I click on the Type select to open it
        And I click on the "Multiple Ticket Type 2" option in the Type select
        Then the Type select says "Multiple Ticket Type 2" for ticket type "VGlja2V0VHlwZTY0ZWE0Yzk3OTAxY2NiNGE0NTNjOTYwMA=="
        And the Price input says "10.00"
        And the Price input does not show an error
        And the active Review Event button is visible
        When I select the Your Organization option in the Bundled Fees section
        Then the Price input does not show an error
        And the active Review Event button is visible

    @migrated @non_automated
    Scenario: Ticketed Event Form - Paid Ticketing - Bundled Fees - New Event - 1 Dollar Ticket - Selecting Your Organization Errors
        Given I log in to Hudl through the API as a paid ticketing school admin
        # https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
        When I load the ticketed event form for organization "175428" and the scheduled event "U2NoZWR1bGVFbnRyeTIyNjc4Mzgz"
        Then the ticketed event form is visible
        When I click on the Continue button
        Then the "Ticketed Event Type" form step headline is visible
        When I click on the Continue button
        Then the "Event Details" form step headline is visible
        When I click on the Continue button
        And I click on the Type select to open it
        And I click on the "1$ Ticket Type" option in the Type select
        Then the Type select says "1$ Ticket Type" for ticket type "VGlja2V0VHlwZTY2ZTgyZDI2ZDYxNjAyNmMxZmI0YjVkMg=="
        And the Price input says "1.00"
        And the Price input does not show an error
        And the active Review Event button is visible
        When I select the Your Organization option in the Bundled Fees section
        Then the Price input shows an error
        And the disabled Review Event button is visible
