import { expect, test } from '../../fixtures/ticketingBackOfficeManagementFixture';

/*
    # ==============
    # Notes
    # ==============

    Hudl Ticketing Management Home Page URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}

    In order to view this page you need to be a School Admin on the school and your school
    must be have product access to ticketing to load this page
*/

/* ==============
   Page Structure
   ============== */

/* ==== Onboarding ==== */

test.describe('Ticketing Management Home - Page Structure - Onboarding', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Ticketing Management Home - Paid Ticketing School - Onboarding - Load Incomplete Onboarding Flow', async ({
    ticketingManagementHomePage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/175422 */
    await ticketingManagementHomePage.page.goto('/ticketing/175422');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Verify the full incomplete onboarding flow is visible
    await ticketingManagementHomePage.isIncompleteOnboardingFlowVisible();
    await expect(ticketingManagementHomePage.completeOnboardingButton('disabled')).toBeVisible();
  });

  test('Ticketing Management Home - Paid Ticketing School - Onboarding - Cannot Publish Ticketed Event Until Onboarding is Complete', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    baseFormStepsPage,
    addTicketsStepPage,
    eventDetailsStepPage,
    eventSelectionPage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/183764 */
    await ticketingManagementHomePage.page.goto('/ticketing/183764');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Verify the full incomplete onboarding flow is visible
    await ticketingManagementHomePage.isIncompleteOnboardingFlowVisible();
    // Go to the non-ticketed events page
    await ticketingManagementHomePage.eventsMenuItem('non-ticketed').click();
    // Click the Create Ad-Hoc Event button and go through custom ticketed event creation
    await ticketingManagementHomePage.ticketingCreateAdHocEventButton.click();
    await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
    await expect(eventSelectionPage.eventSelectionHeader).toBeVisible();
    await baseTicketedEventFormPage.selectEventButton('custom-event').click();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    await eventDetailsStepPage.timeZoneSelect.click();
    // Central Time Zone
    await eventDetailsStepPage.timeZoneSelectOption('America/Chicago').click();
    await eventDetailsStepPage.eventTitleInput.fill('Cannot Publish Ticketed Event Until Onboarding is Complete');
    await baseFormStepsPage.continueButton.click();
    await expect(addTicketsStepPage.ticketTypeSelectDropdown(0)).toBeVisible();
    await addTicketsStepPage.ticketTypeSelectDropdown(0).click();
    await addTicketsStepPage.ticketTypeSelectOption('Ticket Type 1').click();
    await baseFormStepsPage.reviewButton('event').click();
    // Verify the publish button is disabled and the tooltip is visible
    await expect(baseFormStepsPage.disabledPublishButton('ticketed-event')).toBeVisible();
    await baseFormStepsPage.disabledPublishButton('ticketed-event').hover();
    await expect(baseFormStepsPage.onboardingBlockPublishTooltip).toBeVisible();
  });

  test('Ticketing Management Home - Paid Ticketing School - Onboarding - Cannot Publish Pass Config Until Onboarding is Complete', async ({
    ticketingManagementHomePage,
    baseFormStepsPage,
    basePassConfigFormPage,
    passDetailsStepPage,
    addTeamsStepPage,
  }) => {
    const { format, addDays } = require('date-fns');
    const today = format(new Date(), 'yyyy-MM-dd');
    const tomorrow = format(addDays(new Date(), 1), 'yyyy-MM-dd');

    await ticketingManagementHomePage.page.goto('/ticketing/183764');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Verify the full incomplete onboarding flow is visible
    await ticketingManagementHomePage.isIncompleteOnboardingFlowVisible();
    // Go to the current passes page
    await ticketingManagementHomePage.passesMenuItem('current').click();
    // Click the Create Pass Config button and go through pass congig creation
    await ticketingManagementHomePage.createPassConfigButton.click();
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await passDetailsStepPage.passNameInput.fill('Cannot Publish Pass Config Until Onboarding is Complete');
    await passDetailsStepPage.passDateInput('start').fill(today);
    await passDetailsStepPage.passDateInput('end').fill(tomorrow);
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
    await addTeamsStepPage.passTeamMultiSelectDropdown.click();
    await addTeamsStepPage.teamsSelectOption('Ticketing Automation - Canada Handball').click();
    await baseFormStepsPage.reviewButton('pass').click();
    // Verify the publish button is disabled and the tooltip is visible
    await expect(baseFormStepsPage.disabledPublishButton('pass-config')).toBeVisible();
    await baseFormStepsPage.disabledPublishButton('pass-config').hover();
    await expect(baseFormStepsPage.onboardingBlockPublishTooltip).toBeVisible();
  });

  // Cannot submit schedules until the school has completed the onboarding process
  test('Ticketing Management Home - Paid Ticketing School - Onboarding - No Ability to Submit Schedules', async ({
    ticketingManagementHomePage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/175422 */
    await ticketingManagementHomePage.page.goto('/ticketing/175422');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await expect(ticketingManagementHomePage.ticketingSideMenu).toBeVisible();
    await expect(ticketingManagementHomePage.onboardingMenuItem).toBeVisible();
    // Check Manage action List doesn't contain Submit Schedules option
    await ticketingManagementHomePage.eventsMenuItem('published').click();
    await expect(ticketingManagementHomePage.eventsHeadline('upcoming')).toBeVisible();
    await ticketingManagementHomePage.manageActionList.click();
    // This is showing because this user has admin privileges. Will need to make sure we verify
    // that a normal user does not see this option unless you backdoor in on thors
    await expect(ticketingManagementHomePage.manageActionListOption('analytics')).toBeVisible();
    await expect(ticketingManagementHomePage.manageActionListOption('submit-schedules')).toBeHidden();
    await expect(ticketingManagementHomePage.manageActionListOption('ticket-scanning-access')).toBeVisible();
  });
});

/* ==== Empty State ==== */

test.describe('Ticketing Management Home - Page Structure - Empty state', () => {
  test.use({ storageState: 'playwright/.auth/noEventsSchoolAdmin.json' });

  test('Ticketing Management Home - Empty States for Every Menu Item', async ({ ticketingManagementHomePage }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/174138 */
    await ticketingManagementHomePage.page.goto('/ticketing/174138');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await expect(ticketingManagementHomePage.ticketingSideMenu).toBeVisible();
    // Check Events menu items and their empty states
    await ticketingManagementHomePage.isUpcomingEventsEmptyStateVisible();
    await expect(ticketingManagementHomePage.ticketingCreateAdHocEventButton).toBeVisible();
    await expect(ticketingManagementHomePage.manageActionList).toBeVisible();
    await ticketingManagementHomePage.eventsMenuItem('non-ticketed').click();
    await ticketingManagementHomePage.isNonTicketedEventsEmptyStateVisible();
    await ticketingManagementHomePage.eventsMenuItem('draft').click();
    await ticketingManagementHomePage.isDraftEventsEmptyStateVisible();
    await ticketingManagementHomePage.eventsMenuItem('past').click();
    await ticketingManagementHomePage.isPastEventsEmptyStateVisible();
    // Check Passes menu items and their empty states
    await ticketingManagementHomePage.passesMenuItem('current').click();
    await ticketingManagementHomePage.isCurrentPassesEmptyStateVisible();
    await expect(ticketingManagementHomePage.createPassConfigButton).toBeVisible();
    await expect(ticketingManagementHomePage.manageActionList).toBeVisible();
    await ticketingManagementHomePage.passesMenuItem('draft').click();
    await ticketingManagementHomePage.isDraftPassesEmptyStateVisible();
    await ticketingManagementHomePage.passesMenuItem('past').click();
    await ticketingManagementHomePage.isPastPassesEmptyStateVisible();
  });
});

/* ==== Ticketed Events and Pass Configs Exist ==== */

test.describe('Ticketing Management Home - Free Ticketing School - Page Structure - Ticketed Events and Pass Configs', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Ticketing Management Home - Page Structure - Past Ticketed Event', async ({ ticketingManagementHomePage }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/174137 */
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await ticketingManagementHomePage.eventsMenuItem('past').click();
    const ticketedEventId = 'VGlja2V0ZWRFdmVudDY1YWFmYzA0MGM0ZmI2NTkxZWE0OGM4Zg==';
    await expect(ticketingManagementHomePage.ticketedEventItem(ticketedEventId)).toBeVisible();
    await expect(ticketingManagementHomePage.ticketedEventDetailsLink(ticketedEventId)).toBeVisible();
    await expect(ticketingManagementHomePage.ticketedEventDateAndTime(ticketedEventId)).toBeVisible();
    await expect(ticketingManagementHomePage.ticketedEventNumberOfTicketsSold(ticketedEventId)).toBeVisible();
    await expect(ticketingManagementHomePage.ticketedEventActionList(ticketedEventId)).toBeHidden();
  });

  test('Ticketing Management Home - Page Structure - Past Pass Config', async ({ ticketingManagementHomePage }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/174137 */
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await ticketingManagementHomePage.passesMenuItem('past').click();
    const passConfigId = 'UGFzc0NvbmZpZzY1NTM5YjJmYWUyZWI4ZGY5ODcwMDlhZg==';
    await expect(ticketingManagementHomePage.passConfig(passConfigId)).toBeVisible();
    await expect(ticketingManagementHomePage.passConfigDetailsLink(passConfigId)).toBeVisible();
    await expect(ticketingManagementHomePage.passConfigActiveTimePeriod(passConfigId)).toBeVisible();
    await expect(ticketingManagementHomePage.passConfigTeams(passConfigId)).toBeVisible();
    await expect(ticketingManagementHomePage.passConfigTeams(passConfigId)).toBeVisible();
    await expect(ticketingManagementHomePage.passConfigPassesSold(passConfigId)).toBeVisible();
    await expect(ticketingManagementHomePage.passConfigActionList(passConfigId)).toBeHidden();
  });
});

test.describe('Ticketing Management Home - Paid Ticketing School - Page Structure - Ticketed Events and Pass Configs', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Ticketing Management Home - Paid Ticketing School - Ability to Submit Schedules', async ({
    ticketingManagementHomePage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/175428 */
    await ticketingManagementHomePage.page.goto('/ticketing/175428');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Check Manage action List doesn't contain Submit Schedules option
    await ticketingManagementHomePage.manageActionList.click();
    // This is showing because this user has admin privileges. Will need to make sure we verify
    // that a normal user does not see this option unless you backdoor in on thors
    await expect(ticketingManagementHomePage.manageActionListOption('analytics')).toBeVisible();
    await expect(ticketingManagementHomePage.manageActionListOption('submit-schedules')).toBeVisible();
    await expect(ticketingManagementHomePage.manageActionListOption('ticket-scanning-access')).toBeVisible();
  });
});

test.describe('Ticketing Management Home - Active Pass Configs - Pass Badges', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Pass Config - Active Passes - Public, Private and League Pass Config Badge', async ({
    ticketingManagementHomePage,
    sharedComponentsPage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/174137 */
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await ticketingManagementHomePage.passesMenuItem('current').click();
    /* Public pass config */
    await expect(
      ticketingManagementHomePage.passConfig('UGFzc0NvbmZpZzY1OTVjZGQ2YzIxZmViMzE2NjYxMTc4Mw==')
    ).toBeVisible();
    await expect(
      sharedComponentsPage.entityBadge('public', 'UGFzc0NvbmZpZzY1OTVjZGQ2YzIxZmViMzE2NjYxMTc4Mw==')
    ).toBeVisible();
    /* Private pass config */
    await expect(
      ticketingManagementHomePage.passConfig('UGFzc0NvbmZpZzY3MWJmODk3YTQzODhlNmFjYjFjN2RlNw==')
    ).toBeVisible();
    await expect(
      sharedComponentsPage.entityBadge('private', 'UGFzc0NvbmZpZzY3MWJmODk3YTQzODhlNmFjYjFjN2RlNw==')
    ).toBeVisible();
    /* League pass config */
    // await expect(
    //   ticketingManagementHomePage.passConfig('UGFzc0NvbmZpZzY4MDJjNDdhY2I2MDc4MzE0YWRkMjZhMA==')
    // ).toBeVisible();
    // await expect(
    //   sharedComponentsPage.entityBadge('league', 'UGFzc0NvbmZpZzY4MDJjNDdhY2I2MDc4MzE0YWRkMjZhMA==')
    // ).toBeVisible();
  });
});
