import { validateLocalToUtc } from 'playwright-shared';
import { start } from 'repl';

import { expect, test } from '../../fixtures/ticketingBackOfficeManagementFixture';
import { FormSteps } from '../../pages/forms/baseFormStepsPage';

/*
    # ==============
    # Notes
    # ==============

    Tests around the ticketed event form for the General Admission ticketed event, where a ticketed event has not been created yet.

    Ticketed Event Form URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}/add-ticketing/{scheduleEntryId}
*/

/* ==============
   Navigation
   ============== */

/* TESTS NEEDED FOR CUSTOM vs SCHEDULED EVENT CREATION:

Free & Paid Ticketing

Custom Events

// Create button - Form Validation - Move between steps

// Create a custom GA Ticketed Event

// Create a custom RS Ticketed Event

// Create Custom event - Publish - Edit - Publish

// Create Custom Event - Save as Draft - Review - Edit - Publish - Validate Edits were saved

// Create Custom Event - Publish - Edit - Change event to Scheduled - Update - Validate changes were saved

Scheduled Events

// Create a Scheduled Event

// Attempt to Create a Scheduled event empty state - no upcoming scheduled events

// Create Scheduled event - Publish - Edit Schedule Entry - Validate Information - Publish

// Validate Events in Non-Ticketed tab - 
    Create Scheduled Event - Validate available schedule entries in dropdown
    - Publish - Confirm schedule entry no longer visible in the non-ticketed tab 

// Create Scheduled Event - Publish - Edit - Change to Custom - Update

*/

/* # ==============
    # Page Structure
    # ============== */

test.describe('Ticketed Event Form - Free Ticketing', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Page Structure - Free Ticketing - Ticketed Event Type Step',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage }) => {
      /* https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0 */
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      // Ticketed event type step
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      // Step indicators
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
      // Ticket type radio options
      await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'selected');
      await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'enabled');
    }
  );

  test(
    'Ticketed Event Form - Page Structure - Free Ticketing - Ticket Information Step',
    { tag: '@smoke' },
    async ({ eventDetailsStepPage, baseFormStepsPage, baseTicketedEventFormPage, addTicketsStepPage }) => {
      /* https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0 */
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      // Navigate through ticketing event setup pages
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();

      await eventDetailsStepPage.timeZoneSelect.click();
      await eventDetailsStepPage.timeZoneSelectOption('America/Boise').click();
      // Continue to Ticket Information
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.priceInputHelpText).not.toBeVisible();
      await expect(addTicketsStepPage.disabledTicketTypeSelectDropdown).toBeVisible();
      await expect(addTicketsStepPage.selectedTicketTypeDisabled(0)).toHaveText('Free Admission');
      // Validate input states
      await expect(baseFormStepsPage.readOnlyPriceInput).toBeVisible();
      await expect(baseFormStepsPage.readOnlyPriceInput).toHaveValue('0.00');
      await expect(baseFormStepsPage.priceInputHelpText).not.toBeVisible();
      await expect(baseFormStepsPage.quantityForSaleInput).toHaveValue('');
      await expect(baseFormStepsPage.quantityForSaleInputHelpText).toBeVisible();
      await expect(baseTicketedEventFormPage.addTicketTypeSelectionButton).not.toBeVisible();
      await expect(baseFormStepsPage.bundledFeesContainer).not.toBeVisible();
    }
  );
});

test.describe('Ticketed Event Form - Paid Ticketing', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Page Structure - Paid Ticketing - Ticket Information Step',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, addTicketsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
      await baseTicketedEventFormPage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      // Ensure that not selecting a price option locks down quantity inputs
      await expect(baseFormStepsPage.priceInputHelpText).toBeVisible();
      await expect(addTicketsStepPage.ticketTypeSelectDropdown(0)).toHaveText('Select ticket price option');
      await expect(baseFormStepsPage.readOnlyPriceInput).toHaveValue('0.00');
      await expect(baseFormStepsPage.priceInputHelpText).toBeVisible();
      await expect(baseFormStepsPage.disabledQuantityForSale).toBeVisible();
      await expect(baseFormStepsPage.quantityForSaleInputStateHelpText('disabled')).toBeVisible();
      await expect(baseTicketedEventFormPage.addTicketTypeSelectionButton).toBeVisible();
      // Check bundled fees tool tips
      await expect(baseFormStepsPage.bundledFeesContainer).toBeVisible();
      await expect(baseFormStepsPage.selectedBundledFeesOption('your-fans')).toBeVisible();
      await baseFormStepsPage.bundledFeesToolTipIcon('your-fans').hover();
      await expect(baseFormStepsPage.bundledFeesYourFansToolTipText).toBeVisible();
      await baseFormStepsPage.bundledFeesToolTipIcon('your-organization').hover();
      await expect(baseFormStepsPage.bundledFeesYourOrgToolTipText).toBeVisible();
    }
  );
});

test.describe('Ticketed Event Form - Free and Paid Ticketing', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Page Structure - Free Ticketing - Event Details Step',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      // Step indicators
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
      await expect(eventDetailsStepPage.reviewEventInfoText).toBeVisible();
      // Validate Event Detail inputs
      await expect(eventDetailsStepPage.dateInput).toBeVisible();
      await expect(eventDetailsStepPage.startTimeInput).toBeVisible();
      await expect(eventDetailsStepPage.timeZoneSelect).toBeVisible();
      await expect(eventDetailsStepPage.eventDetailsInstructions).toBeVisible();
      await expect(eventDetailsStepPage.eventTitleInput).toBeVisible();
      await expect(eventDetailsStepPage.eventTitleHelpText).toBeVisible();
      await expect(eventDetailsStepPage.eventDescriptionInput).toBeVisible();
      await expect(eventDetailsStepPage.eventVisibilityContainer).toBeVisible();
      await expect(eventDetailsStepPage.formFieldSelectionContainer).toBeVisible();
      await expect(eventDetailsStepPage.addCustomFieldButton).toBeVisible();
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
      await expect(baseFormStepsPage.cancelButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Page Structure - Scheduled Event Info Auto-Populated',
    { tag: '@regression' },
    async ({ baseFormStepsPage, ticketingManagementHomePage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137
      await baseTicketedEventFormPage.page.goto('/ticketing/174137');
      await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
      await ticketingManagementHomePage.eventsMenuItem('non-ticketed').click();
      // This is 'Men's Varsity Soccer vs Hudl Automation - Reports'
      await ticketingManagementHomePage.addTicketingButton('U2NoZWR1bGVFbnRyeTIyMjUzNjM0').click();
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();

      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await expect(eventDetailsStepPage.selectedScheduleEvent).toHaveText(
        "Men's Varsity Soccer vs Hudl Automation - Reports"
      );
      await expect(
        validateLocalToUtc(
          '2026-12-20',
          '15:00',
          'America/Chicago',
          await eventDetailsStepPage.dateInput.inputValue(),
          await eventDetailsStepPage.startTimeInput.inputValue()
        )
      ).toBeTruthy();
      await expect(eventDetailsStepPage.eventTitleInput).toHaveValue(
        "Men's Varsity Soccer vs Hudl Automation - Reports"
      );
      await expect(eventDetailsStepPage.selectedVisibilityOption('public')).toBeVisible();
    }
  );
});

// TODO: Investigate Ad Hoc Ticketed Event Ticketing section (test is broken and requires Hudl employee backdoor auth)

test.describe('Ticketed Event Form Interactions', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Free Ticketing - Move Between Steps Interactions',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, ticketingManagementHomePage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137
      await baseTicketedEventFormPage.page.goto('/ticketing/174137');
      await ticketingManagementHomePage.eventsMenuItem('non-ticketed').click();
      // This is 'Men's Varsity Soccer vs Hudl Automation - Reports'
      await ticketingManagementHomePage.addTicketingButton('U2NoZWR1bGVFbnRyeTIyMjUzNjM0').click();
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Mountain Time option
      await eventDetailsStepPage.timeZoneSelectOption('America/Boise').click();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.AddEventDetails)).toBeVisible();

      await baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish).click();
      await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

      await baseFormStepsPage.backButton('ticketed-event-form').click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

      await baseFormStepsPage.reviewButton('event').click();
      await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Free Ticketing - Cancel Add Ticketing - No Changes Made',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, ticketingManagementHomePage, baseTicketedEventFormPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137
      await baseTicketedEventFormPage.page.goto('/ticketing/174137');
      await ticketingManagementHomePage.eventsMenuItem('non-ticketed').click();
      // This is 'Men's Varsity Soccer vs Hudl Automation - Reports'
      await ticketingManagementHomePage.addTicketingButton('U2NoZWR1bGVFbnRyeTIyMjUzNjM0').click();
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await baseFormStepsPage.cancelButton.click();
      await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
      await expect(ticketingManagementHomePage.eventsMenuItemSelected('non-ticketed')).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Free Ticketing - Cancel Add Ticketing - Changes Made',
    { tag: '@regression' },
    async ({
      baseFormStepsPage,
      ticketingManagementHomePage,
      baseTicketedEventFormPage,
      eventDetailsStepPage,
      reviewAndPublishStepPage,
    }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137
      await baseTicketedEventFormPage.page.goto('/ticketing/174137');
      await ticketingManagementHomePage.eventsMenuItem('non-ticketed').click();
      // This is 'Men's Varsity Soccer vs Hudl Automation - Reports'
      await ticketingManagementHomePage.addTicketingButton('U2NoZWR1bGVFbnRyeTIyMjUzNjM0').click();
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Mountain Time option
      await eventDetailsStepPage.timeZoneSelectOption('America/Boise').click();
      await baseFormStepsPage.cancelButton.click();
      await expect(reviewAndPublishStepPage.saveChangesModal).toBeVisible();
      await reviewAndPublishStepPage.discardChangesButton.click();
      await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
      await expect(ticketingManagementHomePage.eventsMenuItemSelected('non-ticketed')).toBeVisible();
      await ticketingManagementHomePage.addTicketingButton('U2NoZWR1bGVFbnRyeTIyMjUzNjM0').click();
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await expect(eventDetailsStepPage.timeZoneSelect).toHaveText('Select event time zone');
    }
  );

  // TODO: Edit Ticketed Event Afterwards scenario -- needs setup and cleanup steps

  test(
    'Ticketed Event Form - Invalid Event ID',
    { tag: '@regression' },
    async ({ sharedComponentsPage, ticketingManagementHomePage, baseTicketedEventFormPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0fwes
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0fwes');
      await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
      await expect(sharedComponentsPage.errorLoadingTicketedEventToast).toBeVisible();
    }
  );

  // TODO need to migrate 'Ticketed Event Form - Free Ticketed Event - Publish Button Disabled When Backdoored'
  // need hudl employee auth setup
});

test.describe('Add Ticketing Form Validation', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Form Validation - Date in the Past',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.dateInput.fill('2024-02-18');
      await expect(eventDetailsStepPage.dateInputErrorLabel).toBeVisible();
      await expect(eventDetailsStepPage.dateInputErrorLabel).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.dateInputError).toHaveCSS('border-color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.dateInputErrorHelpText).toHaveText('Date cannot be in the past.');
    }
  );

  test(
    'Ticketed Event Form - Form Validation - No Event Title Text',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Alaska option
      await eventDetailsStepPage.timeZoneSelectOption('America/Juneau').click();
      await expect(baseFormStepsPage.continueButton).toBeVisible();
      await eventDetailsStepPage.eventTitleInput.clear();
      // Need to click out of the event title ot trigger the error
      await eventDetailsStepPage.eventDescriptionInput.click();
      await expect(eventDetailsStepPage.eventTitleInputError).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.eventTitleInputError).toHaveCSS('border-color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.eventTitleInputErrorHelpText).toHaveText('Event title is required.');
      await expect(eventDetailsStepPage.eventTitleInputErrorHelpText).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Form Validation - Event Title More Than Max Character Limit',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();

      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      // This is the Pacific Time option
      await eventDetailsStepPage.timeZoneSelect.click();
      await eventDetailsStepPage.timeZoneSelectOption('America/Los_Angeles').click();
      await expect(baseFormStepsPage.continueButton).toBeVisible();
      // This event title is too long and should be met with an error
      const longTitle =
        "Men's Varsity Soccer vs Hudl Automation - Reports Men's Varsity Soccer vs Hudl Automation - Reports Men's Varsity Soccer vs Hudl Automation again - Reports";
      await eventDetailsStepPage.eventTitleInput.fill(longTitle);
      await expect(eventDetailsStepPage.eventTitleInputErrorLabel).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.eventTitleInputError).toHaveCSS('border-color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.eventTitleInputErrorHelpText).toHaveText('Character limit: 155/150');
      await expect(eventDetailsStepPage.eventTitleInputErrorHelpText).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Form Validation - Event Description More Than Max Character Limit',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();

      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelect.click();
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await expect(baseFormStepsPage.continueButton).toBeVisible();
      // This event description is too long and should be met with an error
      const longDescription = eventDetailsStepPage.createStringOfLength(2001);
      await eventDetailsStepPage.eventDescriptionInput.fill(await longDescription);
      await expect(eventDetailsStepPage.eventDescriptionErrorLabel).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.eventDescriptionErrorInput).toHaveCSS('border-color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.eventDescriptionErrorHelpText).toHaveText('Character limit: 2001/2000');
      await expect(eventDetailsStepPage.eventDescriptionErrorHelpText).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Form Validation - Add Custom Field Name Input More Than Max Character Limit',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();

      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.addCustomFieldButton.click();
      const longName = eventDetailsStepPage.createStringOfLength(151);
      await eventDetailsStepPage.addCustomFieldNameInput.fill(await longName);
      await expect(eventDetailsStepPage.addCustomFieldNameInputErrorLabel).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.addCustomFieldNameInputError).toHaveCSS('border-color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.addCustomFieldNameInputErrorHelpText).toHaveText('Character limit: 151/150');
      await expect(eventDetailsStepPage.addCustomFieldNameInputErrorHelpText).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Form Validation - Add Custom Field Help Text Input More Than Max Character Limit',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();

      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.addCustomFieldButton.click();
      const longDescription = eventDetailsStepPage.createStringOfLength(301);
      await eventDetailsStepPage.addCustomFieldHelpTextInput.fill(await longDescription);
      await expect(eventDetailsStepPage.addCustomFieldHelpTextInputErrorLabel).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.addCustomFieldHelpTextInputError).toHaveCSS('border-color', 'rgb(187, 23, 0)');
      await expect(eventDetailsStepPage.addCustomFieldHelpTextInputErrorHelpText).toHaveText(
        'Character limit: 301/300'
      );
      await expect(eventDetailsStepPage.addCustomFieldHelpTextInputErrorHelpText).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Form Validation - Quantity for Sale - Less Than 1',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();

      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelect.click();
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.reviewButton('event')).toBeVisible();
      // Inputting illegal quantity (0) for sale should cause error state
      await baseFormStepsPage.quantityForSaleInput.fill('0');
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.quantityForSaleInputErrorLabel).toHaveCSS('color', 'rgb(187, 23, 0)');
      await expect(baseFormStepsPage.quantityForSaleInputError).toHaveCSS('border-color', 'rgb(187, 23, 0)');
      await expect(baseFormStepsPage.quantityForSaleInputStateHelpText('error')).toHaveText(
        'Quantity must be greater than 0.'
      );
      await expect(baseFormStepsPage.quantityForSaleInputStateHelpText('error')).toHaveCSS('color', 'rgb(187, 23, 0)');
    }
  );
});

test.describe('Add Custom Form Fields', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Free Ticketing - Add New Custom Field Modal Structure',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await eventDetailsStepPage.addCustomFieldButton.click();
      await expect(eventDetailsStepPage.addCustomFieldModal).toBeVisible();
      await expect(eventDetailsStepPage.disabledAddCustomFieldModalAddButton).toBeVisible();
      await eventDetailsStepPage.addCustomFieldNameInput.fill('Optional Snack Preference');
      await expect(eventDetailsStepPage.addCustomFieldHelpTextInput).toBeVisible();
      await eventDetailsStepPage.addCustomFieldModalRadioOption('no').click();
      await expect(eventDetailsStepPage.addCustomFieldModalAddButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Free Ticketing - Add New Custom Field Modal - No Help Text - Optional',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await eventDetailsStepPage.addCustomFieldButton.click();
      await expect(eventDetailsStepPage.addCustomFieldModal).toBeVisible();
      await expect(eventDetailsStepPage.disabledAddCustomFieldModalAddButton).toBeVisible();
      await eventDetailsStepPage.addCustomFieldNameInput.fill('Optional Snack Preference');
      await eventDetailsStepPage.addCustomFieldModalRadioOption('no').click();
      await expect(eventDetailsStepPage.addCustomFieldModalAddButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Free Ticketing - Add New Custom Field Modal - Help Text - Required',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await eventDetailsStepPage.addCustomFieldButton.click();
      await expect(eventDetailsStepPage.addCustomFieldModal).toBeVisible();
      await expect(eventDetailsStepPage.disabledAddCustomFieldModalAddButton).toBeVisible();
      await eventDetailsStepPage.addCustomFieldNameInput.fill('Required Snack Preference with Help Text');
      await eventDetailsStepPage.addCustomFieldHelpTextInput.fill(
        'Let us know what snacks you would like us to provide.'
      );
      await expect(eventDetailsStepPage.addCustomFieldModalAddButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Free Ticketing - Form Field Item Selection - No Help Text - Optional',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await expect(eventDetailsStepPage.addCustomFieldButton).toBeVisible();
      await eventDetailsStepPage.formFieldSelectDropdown.click();
      await eventDetailsStepPage.formFieldSelectOption('Dietary Restrictions / Allergies').click();
      await expect(eventDetailsStepPage.selectedFormFieldItem('Dietary Restrictions / Allergies')).toBeVisible();
      await expect(eventDetailsStepPage.formFieldItemLabel('Dietary Restrictions / Allergies')).toBeVisible();
      await expect(eventDetailsStepPage.formFieldItemRequiredStatus).toHaveText('Required: No');
    }
  );

  test(
    'Ticketed Event Form - Free Ticketing - Form Field Item Selection - Help Text - Required',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await expect(eventDetailsStepPage.addCustomFieldButton).toBeVisible();
      await eventDetailsStepPage.formFieldSelectDropdown.click();
      // This is the T-Shirt Size option
      await eventDetailsStepPage.formFieldSelectOption('T-Shirt Size').click();
      await expect(eventDetailsStepPage.selectedFormFieldItem('T-Shirt Size')).toBeVisible();
      await expect(eventDetailsStepPage.formFieldItemLabel('T-Shirt Size')).toBeVisible();
      await expect(eventDetailsStepPage.formFieldItemHelpText).toHaveText(
        'Help Text: Enter your T-Shirt Size: S, M, L, XL, 2XL'
      );
      await expect(eventDetailsStepPage.formFieldItemRequiredStatus).toHaveText('Required: Yes');
    }
  );

  test(
    'Ticketed Event Form - Free Ticketing - Form Field Item Selection - Deselect Item',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await expect(eventDetailsStepPage.addCustomFieldButton).toBeVisible();
      await eventDetailsStepPage.formFieldSelectDropdown.click();
      // This is the T-Shirt Size option
      await eventDetailsStepPage.formFieldSelectOption('T-Shirt Size').click();
      await expect(eventDetailsStepPage.selectedFormFieldItem('T-Shirt Size')).toBeVisible();
      await eventDetailsStepPage.verifySelectedFormFieldCount(1);
      await eventDetailsStepPage.formFieldItemDeleteButton('T-Shirt Size').click();
      await eventDetailsStepPage.verifySelectedFormFieldCount(0);
    }
  );

  test(
    'Ticketed Event Form - Free Ticketing - Form Field Item Selection at the Selection Limit',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await baseTicketedEventFormPage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await expect(eventDetailsStepPage.addCustomFieldButton).toBeVisible();
      await eventDetailsStepPage.createNumFormFieldItems(10);
      await eventDetailsStepPage.verifySelectedFormFieldCount(10);
      await expect(eventDetailsStepPage.formFieldMaxLimitReachedError).toBeVisible();
      await expect(eventDetailsStepPage.disabledAddCustomFieldButton).toBeVisible();
    }
  );
});

test.describe('Paid Ticketing - Add Ticket Types', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Paid Ticketing - Add New Ticket Type Modal Structure',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, eventDetailsStepPage, addTicketsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
      await baseTicketedEventFormPage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      // Navigate to Event Details (first continue is implied by the URL, the form opens on "Ticketed Event Type")
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Arizona option
      await eventDetailsStepPage.timeZoneSelectOption('America/Phoenix').click();
      await baseFormStepsPage.continueButton.click();

      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(addTicketsStepPage.ticketTypeSelectDropdown(0)).toBeVisible();
      // Assert modal structure
      await expect(addTicketsStepPage.ticketTypeContainer(0)).toBeVisible();
      await addTicketsStepPage.ticketTypeNameInput(0).fill('Free');
      await addTicketsStepPage.ticketTypeCreateNewTypeOption('Free').click();
      // Clear name input to disable save button
      await addTicketsStepPage.addTicketTypeModalNameInput.fill('');
      await expect(addTicketsStepPage.addTicketTypeModal).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalNameInput).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalPriceInput).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalSaveButton).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalSaveButton).toBeDisabled();
      await expect(addTicketsStepPage.addTicketTypeModalCancelButton).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Paid Ticketing - Add New Ticket Type Save Button Behavior',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, addTicketsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
      await baseTicketedEventFormPage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(addTicketsStepPage.ticketTypeSelectDropdown(0)).toBeVisible();
      // Fill in new ticket type modal - save button should be enabled
      await expect(addTicketsStepPage.ticketTypeContainer(0)).toBeVisible();
      await addTicketsStepPage.ticketTypeNameInput(0).fill('Free');
      await addTicketsStepPage.ticketTypeCreateNewTypeOption('Free').click();
      await expect(addTicketsStepPage.addTicketTypeModalNameInput).toHaveValue('Free');
      await addTicketsStepPage.addTicketTypeModalPriceInput.fill('20');
      await expect(addTicketsStepPage.addTicketTypeModalSaveButton).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalSaveButton).toBeEnabled();
    }
  );

  test(
    'Ticketed Event Form - Paid Ticketing - Multiple Ticket Types Behavior',
    { tag: '@smoke' },
    async ({ baseFormStepsPage, baseTicketedEventFormPage, addTicketsStepPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
      await baseTicketedEventFormPage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await addTicketsStepPage.verifyNumberOfTicketTypeRows(1);
      await expect(addTicketsStepPage.ticketTypeDeleteButton(0)).not.toBeVisible();
      await expect(baseFormStepsPage.reviewButtonDisabled('event')).toBeVisible();
      await addTicketsStepPage.ticketTypeSelectDropdown(0).click();
      // The option being clicked here is the 'Test Automation' option
      await addTicketsStepPage.ticketTypeSelectOption('Test Automation').click();
      await expect(addTicketsStepPage.selectedTicketType(0)).toHaveText('Test Automation');
      await expect(addTicketsStepPage.ticketPriceInput(0)).toHaveValue('20.00');
      await expect(addTicketsStepPage.ticketTypeQuantityForSaleInput(0)).toHaveValue('');
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.reviewButton('event')).toBeVisible();
      // Add another ticket type and confirm it shows up
      await addTicketsStepPage.addTicketTypeButton.click();
      await addTicketsStepPage.verifyNumberOfTicketTypeRows(2);
      await expect(addTicketsStepPage.ticketTypeDeleteButton(0)).toBeVisible();
      await expect(addTicketsStepPage.ticketTypeDeleteButton(1)).toBeVisible();
      // Add another ticket type and confirm it shows up
      await addTicketsStepPage.addTicketTypeButton.click();
      await addTicketsStepPage.verifyNumberOfTicketTypeRows(3);
      await expect(addTicketsStepPage.ticketTypeDeleteButton(2)).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('event')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      // Delete two tickets and confirm they're gone
      await addTicketsStepPage.ticketTypeDeleteButton(2).click();
      await addTicketsStepPage.ticketTypeDeleteButton(1).click();
      await addTicketsStepPage.verifyNumberOfTicketTypeRows(1);
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.reviewButton('event')).toBeVisible();
    }
  );
});

test.describe('Paid Ticketing - Bundled Fees', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test('Ticketed Event Form - Paid Ticketing - Bundled Fees - Existing Event - Your Organization Selected', async ({
    baseFormStepsPage,
    baseTicketedEventFormPage,
    ticketingManagementHomePage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/175428
    await baseTicketedEventFormPage.page.goto('/ticketing/175428');
    await ticketingManagementHomePage
      .ticketedEventActionList('VGlja2V0ZWRFdmVudDY2YmZjNzE1ZjgwMzBhZTQzMzY1Yjk3NQ==')
      .click();
    await ticketingManagementHomePage
      .ticketedEventActionListOption('edit', 'VGlja2V0ZWRFdmVudDY2YmZjNzE1ZjgwMzBhZTQzMzY1Yjk3NQ==')
      .click();
    await expect(baseTicketedEventFormPage.updateTicketingForm).toBeVisible();
    await baseFormStepsPage.checkedStepIndicator(FormSteps.AddTickets).click();
    await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
    await expect(baseFormStepsPage.bundledFeesContainer).toBeVisible();
    await expect(baseFormStepsPage.bundledFeesTutorialLink).toBeVisible();
    await expect(baseFormStepsPage.selectedBundledFeesOption('your-organization')).toBeVisible();
  });

  test('Ticketed Event Form - Paid Ticketing - Bundled Fees - New Event - Your Fans Default', async ({
    baseFormStepsPage,
    baseTicketedEventFormPage,
    eventDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
    await baseTicketedEventFormPage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
    await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    await eventDetailsStepPage.timeZoneSelect.click();
    // This is the Mountain Time option
    await eventDetailsStepPage.timeZoneSelectOption('America/Boise');
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.bundledFeesContainer).toBeVisible();
    await expect(baseFormStepsPage.selectedBundledFeesOption('your-fans')).toBeVisible();
    await baseFormStepsPage.bundledFeesToolTipIcon('your-fans').hover();
    await expect(baseFormStepsPage.bundledFeesYourFansToolTipText).toBeVisible();
  });

  test("Ticketed Event Form - Paid Ticketing - Bundled Fees - New Event - Free Ticket - Selecting Your Organization Doesn't Error", async ({
    baseFormStepsPage,
    baseTicketedEventFormPage,
    addTicketsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
    await baseTicketedEventFormPage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
    await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    // Select 'Free Ticket Type' and check price validation
    await addTicketsStepPage.ticketTypeSelectDropdown(0).click();
    await addTicketsStepPage.ticketTypeSelectOption('Free Ticket Type').click();
    await expect(addTicketsStepPage.selectedTicketType(0)).toHaveText('Free Ticket Type');
    await expect(addTicketsStepPage.ticketPriceInput(0)).toHaveValue('0.00');
    await expect(baseFormStepsPage.priceInputError).not.toBeVisible();

    await expect(baseFormStepsPage.reviewButton('event')).toBeVisible();
    await baseFormStepsPage.bundledFeesOption('your-organization').click();
    await expect(baseFormStepsPage.priceInputError).not.toBeVisible();
    await expect(baseFormStepsPage.reviewButton('event')).toBeVisible();
  });

  test("Ticketed Event Form - Paid Ticketing - Bundled Fees - New Event - 10 Dollar Ticket - Selecting Your Organization Doesn't Error", async ({
    baseFormStepsPage,
    baseTicketedEventFormPage,
    addTicketsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
    await baseTicketedEventFormPage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
    await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    // Select 'Multiple Ticket Type 2' and check price validation
    await addTicketsStepPage.ticketTypeSelectDropdown(0).click();
    await addTicketsStepPage.ticketTypeSelectOption('Multiple Ticket Type 2').click();
    await expect(addTicketsStepPage.selectedTicketType(0)).toHaveText('Multiple Ticket Type 2');
    await expect(addTicketsStepPage.ticketPriceInput(0)).toHaveValue('10.00');
    await expect(baseFormStepsPage.priceInputError).not.toBeVisible();

    await expect(baseFormStepsPage.reviewButton('event')).toBeVisible();
    await baseFormStepsPage.bundledFeesOption('your-organization').click();
    await expect(baseFormStepsPage.priceInputError).not.toBeVisible();
    await expect(baseFormStepsPage.reviewButton('event')).toBeVisible();
  });

  test('Ticketed Event Form - Paid Ticketing - Bundled Fees - New Event - 1 Dollar Ticket - Selecting Your Organization Errors', async ({
    baseFormStepsPage,
    baseTicketedEventFormPage,
    addTicketsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
    await baseTicketedEventFormPage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
    await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    // Select '1$ Ticket Type' and check price validation
    await addTicketsStepPage.ticketTypeSelectDropdown(0).click();
    await addTicketsStepPage.ticketTypeSelectOption('1$ Ticket Type').click();
    await expect(addTicketsStepPage.selectedTicketType(0)).toHaveText('1$ Ticket Type');
    await expect(addTicketsStepPage.ticketPriceInput(0)).toHaveValue('1.00');
    await expect(baseFormStepsPage.priceInputError).not.toBeVisible();

    await expect(baseFormStepsPage.reviewButton('event')).toBeVisible();
    await baseFormStepsPage.bundledFeesOption('your-organization').click();
    await expect(baseFormStepsPage.priceInputError).toBeVisible();
    await expect(baseFormStepsPage.disabledReviewButton('event')).toBeVisible();
  });
});
