import { expect, test } from '../../fixtures/ticketingBackOfficeManagementFixture';
import { FormSteps } from '../../pages/forms/baseFormStepsPage';

/*
    # ==============
    # Notes
    # ==============

    Published Ticketed Event Form URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}/add-ticketing/{scheduleEntryID}/ticketedEventId={ticketedEventID}

    You can access this page by clicking Edit on a published ticketed event
*/

/* ==============
   Published Ticketed Event Form Interactions
   ============== */

test.describe('Published Ticketed Event Form Interactions', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Published Ticketed Event Form - Changes Made - Cancel Button Behavior', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    eventDetailsStepPage,
    baseFormStepsPage,
    reviewAndPublishStepPage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/174137 */
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Edit ticketed event
    const ticketedEventId = 'VGlja2V0ZWRFdmVudDY0ZWUyNjJjYjgyZjllNjQzNmRmY2Q0MA==';
    await ticketingManagementHomePage.ticketedEventActionList(ticketedEventId).click();
    await ticketingManagementHomePage.ticketedEventActionListOption('edit', ticketedEventId).click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('update')).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    // Navigate to the Event Details step
    await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    // Clear the Event Description textbox
    await eventDetailsStepPage.eventDescriptionInput.clear();
    await expect(eventDetailsStepPage.eventDescriptionInput).toHaveText('');
    // Click cancel button in form to go back to the ticketing management home page
    await baseFormStepsPage.cancelButton.click();
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await expect(ticketingManagementHomePage.eventsMenuItemSelected('published')).toBeVisible();
    // Edit ticketed event again and make sure description is still there
    await ticketingManagementHomePage.ticketedEventActionList(ticketedEventId).click();
    await ticketingManagementHomePage.ticketedEventActionListOption('edit', ticketedEventId).click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('update')).toBeVisible();
    await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
    await expect(eventDetailsStepPage.eventDescriptionInput).toHaveText(
      'This ticketed event is used for tests for the Update Ticketing page, but no changes will ever be made to it!!!'
    );
  });

  test('Published Ticketed Event Form - Changes Made - Changing Date Prompts Event Details Change Modal', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    eventDetailsStepPage,
    baseFormStepsPage,
    reviewAndPublishStepPage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/174137 */
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Edit ticketed event
    const ticketedEventId = 'VGlja2V0ZWRFdmVudDY0ZWUyNjJjYjgyZjllNjQzNmRmY2Q0MA==';
    await ticketingManagementHomePage.ticketedEventActionList(ticketedEventId).click();
    await ticketingManagementHomePage.ticketedEventActionListOption('edit', ticketedEventId).click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('update')).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    // Navigate to the Event Details step
    await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    // Change the Event Date
    await eventDetailsStepPage.dateInput.click();
    await eventDetailsStepPage.dateInput.press('ArrowUp');
    // Go back to Review & Publish step and click Update
    await baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish).click();
    await reviewAndPublishStepPage.updateButton('ticketed-event').click();
    // Check that the Event Details Change modal is visible
    await expect(reviewAndPublishStepPage.detailsChangeModal('event')).toBeVisible();
    await expect(reviewAndPublishStepPage.detailsChangeModalInstructionsText('event')).toBeVisible();
    await expect(reviewAndPublishStepPage.detailsChangeModalYesUpdateButton('event')).toBeVisible();
    await reviewAndPublishStepPage.detailsChangeModalCancelButton('event').click();
    // Check that the Event Details Change modal is not visible
    await expect(reviewAndPublishStepPage.detailsChangeModal('event')).not.toBeVisible();
  });

  test('Published Ticketed Event Form - Changes Made - Changing Start Time Prompts Event Details Change Modal', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    eventDetailsStepPage,
    baseFormStepsPage,
    reviewAndPublishStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Edit ticketed event
    const ticketedEventId = 'VGlja2V0ZWRFdmVudDY0ZWUyNjJjYjgyZjllNjQzNmRmY2Q0MA==';
    await ticketingManagementHomePage.ticketedEventActionList(ticketedEventId).click();
    await ticketingManagementHomePage.ticketedEventActionListOption('edit', ticketedEventId).click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('update')).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    // Navigate to the Event Details step
    await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    // Change the Start Time
    await eventDetailsStepPage.startTimeInput.click();
    await eventDetailsStepPage.startTimeInput.press('ArrowUp');
    // Go back to Review & Publish step and click Update
    await baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish).click();
    await reviewAndPublishStepPage.updateButton('ticketed-event').click();
    // Check that the Event Details Change modal is visible
    await expect(reviewAndPublishStepPage.detailsChangeModal('event')).toBeVisible();
    await reviewAndPublishStepPage.detailsChangeModalCancelButton('event').click();
    // Check that the Event Details Change modal is not visible
    await expect(reviewAndPublishStepPage.detailsChangeModal('event')).not.toBeVisible();
  });

  test('Published Ticketed Event Form - Changes Made - Changing Time Zone Prompts Event Details Change Modal', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    eventDetailsStepPage,
    baseFormStepsPage,
    reviewAndPublishStepPage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/174137 */
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Edit ticketed event
    const ticketedEventId = 'VGlja2V0ZWRFdmVudDY0ZWUyNjJjYjgyZjllNjQzNmRmY2Q0MA==';
    await ticketingManagementHomePage.ticketedEventActionList(ticketedEventId).click();
    await ticketingManagementHomePage.ticketedEventActionListOption('edit', ticketedEventId).click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('update')).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    // Navigate to the Event Details step
    await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    // Change the Time Zone selected
    await eventDetailsStepPage.timeZoneSelect.click();
    await eventDetailsStepPage.timeZoneSelectOption('America/Boise').click();
    // Go back to Review & Publish step and click Update
    await baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish).click();
    await reviewAndPublishStepPage.updateButton('ticketed-event').click();
    // Check that the Event Details Change modal is visible
    await expect(reviewAndPublishStepPage.detailsChangeModal('event')).toBeVisible();
    await reviewAndPublishStepPage.detailsChangeModalDismissButton('event').click();
    // Check that the Event Details Change modal is not visible
    await expect(reviewAndPublishStepPage.detailsChangeModal('event')).not.toBeVisible();
  });
});
