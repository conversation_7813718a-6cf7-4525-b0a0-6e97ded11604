import { expect, test } from '../../fixtures/ticketingBackOfficeManagementFixture';
import { FormSteps } from '../../pages/forms/baseFormStepsPage';

/*  # ==============
    # Notes
    # ==============

    To use the Reserved Seating workflow, you must select Reserved Seating ticketed event type in the
    first step of the ticketed event form.

    Ticketed Event Form URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}/add-ticketing/{scheduleEntryId}


    # ==============
    # Page Structure
    # ============== */

test.describe('Ticketed Event Form - Reserved Seating - Free and Paid Ticketing', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Reserved Seating - School with No Venues',
    { tag: '@smoke' },
    async ({ ticketingManagementHomePage, baseTicketedEventFormPage, baseFormStepsPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/181416/add-ticketing/U2NoZWR1bGVFbnRyeTI1OTI0NTky
      await ticketingManagementHomePage.page.goto('/ticketing/181416/add-ticketing/U2NoZWR1bGVFbnRyeTI1OTI0NTky');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'selected');
      await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'disabled');
    }
  );

  test(
    'Ticketed Event Form - Reserved Seating - School with No Seating Charts',
    { tag: '@smoke' },
    async ({ ticketingManagementHomePage, baseTicketedEventFormPage, baseFormStepsPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/181428/add-ticketing/U2NoZWR1bGVFbnRyeTI1OTI1NjEx
      await ticketingManagementHomePage.page.goto('/ticketing/181428/add-ticketing/U2NoZWR1bGVFbnRyeTI1OTI1NjEx');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'selected');
      await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'disabled');
    }
  );

  test(
    'Ticketed Event Form - Reserved Seating - Navigation',
    { tag: '@smoke' },
    async ({ ticketingManagementHomePage, baseTicketedEventFormPage, baseFormStepsPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await ticketingManagementHomePage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      // Verify form steps
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
      // Verify Type radios and form steps after clicking Reserved Seating type radio
      await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'selected');
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
      await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'selected');
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.stepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      // Continue to Event Details Page and verify form steps again
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddEventDetails)).toBeVisible();
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
      // Go back and verify type radio change was saved
      await baseFormStepsPage.backButton('ticketed-event-form').click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
      await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'enabled');
      await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'selected');
    }
  );
});

/*  # ==============
    # Ticket Information Interactions
    # ============== */

test.describe('Ticketed Event Form - Reserved Seating - Free Ticketing', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Reserved Seating Workflow - Free Ticketing',
    { tag: '@regression @non_prod' },
    async ({
      ticketingManagementHomePage,
      baseTicketedEventFormPage,
      addTicketsStepPage,
      eventDetailsStepPage,
      baseFormStepsPage,
    }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await ticketingManagementHomePage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();

      await baseFormStepsPage.continueButton.click();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Alaska option
      await eventDetailsStepPage.timeZoneSelectOption('America/Juneau').click();
      // Should not be able to go past Ticket Information page without filling out required information
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(addTicketsStepPage.venueSelectDropdown).toBeVisible();
      await expect(addTicketsStepPage.disabledSeatingLayoutSelectDropdown).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('event')).toBeVisible();
      // Selecting a venue and a seating layout should work and save properly
      await addTicketsStepPage.venueSelectDropdown.click();
      await addTicketsStepPage.venueSelectDropdownOption('UI Automation - Outdoor Venue 1').click();

      await addTicketsStepPage.seatingLayoutSelectDropdown.click();
      await addTicketsStepPage
        .seatingLayoutSelectDropdownOption('Automation - Outdoor Venue Config 2 - With Seating Chart + One Category')
        .click();
      await expect(addTicketsStepPage.seatingLayoutSelectDropdown).toHaveText(
        'Automation - Outdoor Venue Config 2 - With Seating Chart + One Category'
      );
      await expect(addTicketsStepPage.seatingChartPreview).toBeVisible();
      await expect(addTicketsStepPage.createTicketTypeButton).not.toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('event')).toBeVisible();

      await addTicketsStepPage.ticketTypeSelectionTypeDropdown(1).click();
      // This is the Free Admission - $0.00 option in the select (the only option)
      await addTicketsStepPage.ticketTypeSelectionTypeDropdownOption('Free Admission - $0.00').click();
      // Review Event button should be enabled after filling in all information and Ticket Type
      await baseFormStepsPage.reviewButton('event').click();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
      await expect(baseFormStepsPage.saveAsDraftButton('ticketed-event')).toBeVisible();
      await expect(baseFormStepsPage.publishButton('ticketed-event')).toBeVisible();
    }
  );
});

test.describe('Ticketed Event Form - Reserved Seating - Paid Ticketing', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Reserved Seating Workflow - Paid Ticketing',
    { tag: '@regression @non_prod' },
    async ({
      ticketingManagementHomePage,
      baseTicketedEventFormPage,
      addTicketsStepPage,
      eventDetailsStepPage,
      baseFormStepsPage,
    }) => {
      // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
      await ticketingManagementHomePage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
      await baseFormStepsPage.continueButton.click();

      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Alaska option
      await eventDetailsStepPage.timeZoneSelectOption('America/Juneau').click();
      // Should not be able to go past Ticket Information page without filling out required information
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(addTicketsStepPage.venueSelectDropdown).toBeVisible();
      await expect(addTicketsStepPage.disabledSeatingLayoutSelectDropdown).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('event')).toBeVisible();
      // Selecting a venue with one category should work and save properly
      await addTicketsStepPage.venueSelectDropdown.click();
      await addTicketsStepPage.venueSelectDropdownOption('UI Automation - Paid Ticketing - Outdoor Venue 1').click();

      await addTicketsStepPage.seatingLayoutSelectDropdown.click();
      await addTicketsStepPage
        .seatingLayoutSelectDropdownOption('Venue Config 2 - Seating Chart with One Category')
        .click();
      await expect(addTicketsStepPage.seatingLayoutSelectDropdown).toHaveText(
        'Venue Config 2 - Seating Chart with One Category'
      );
      await expect(addTicketsStepPage.seatingChartPreview).toBeVisible();
      await expect(addTicketsStepPage.createTicketTypeButton).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('event')).toBeVisible();

      await addTicketsStepPage.ticketTypeSelectionTypeDropdown(1).click();
      // This is the Multiple Ticket Type 1 - $20.00 option in the select (the only option)
      await addTicketsStepPage.ticketTypeSelectionTypeDropdownOption('Multiple Ticket Type 1 - $20.00').click();
      // This is the Multiple Ticket Type 3 - $5.00 option in the select (the only option)
      await addTicketsStepPage.ticketTypeSelectionTypeDropdownOption('Multiple Ticket Type 3 - $5.00').click();
      // Check that dropdown now contains the two selected ticket types
      await expect(addTicketsStepPage.ticketTypeSelectionTypeDropdown(1)).toContainText(
        'Multiple Ticket Type 1 - $20.00'
      );
      await expect(addTicketsStepPage.ticketTypeSelectionTypeDropdown(1)).toContainText(
        'Multiple Ticket Type 3 - $5.00'
      );
      // Review Event button should be enabled after filling in all information and Ticket Type
      await baseFormStepsPage.reviewButton('event').click();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
      await expect(baseFormStepsPage.saveAsDraftButton('ticketed-event')).toBeVisible();
      await expect(baseFormStepsPage.publishButton('ticketed-event')).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Reserved Seating Workflow - Paid Ticketing - Add New Ticket Type Modal',
    { tag: '@regression @non_prod' },
    async ({
      ticketingManagementHomePage,
      baseTicketedEventFormPage,
      addTicketsStepPage,
      eventDetailsStepPage,
      baseFormStepsPage,
    }) => {
      // https://main--latest.app.thorhudl.com/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz
      await ticketingManagementHomePage.page.goto('/ticketing/175428/add-ticketing/U2NoZWR1bGVFbnRyeTIyNjc4Mzgz');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
      await baseFormStepsPage.continueButton.click();

      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Alaska option
      await eventDetailsStepPage.timeZoneSelectOption('America/Juneau').click();
      // Should not be able to go past Ticket Information page without filling out required information
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTickets)).toBeVisible();
      await expect(addTicketsStepPage.venueSelectDropdown).toBeVisible();
      await expect(addTicketsStepPage.disabledSeatingLayoutSelectDropdown).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('event')).toBeVisible();
      // Selecting a venue with multiple categories should work and save properly
      await addTicketsStepPage.venueSelectDropdown.click();
      await addTicketsStepPage.venueSelectDropdownOption('UI Automation - Paid Ticketing - Outdoor Venue 1').click();

      await addTicketsStepPage.seatingLayoutSelectDropdown.click();
      await addTicketsStepPage
        .seatingLayoutSelectDropdownOption('Venue Config 1 - Seating Chart with Multiple Categories')
        .click();
      await expect(addTicketsStepPage.seatingLayoutSelectDropdown).toHaveText(
        'Venue Config 1 - Seating Chart with Multiple Categories'
      );
      await expect(addTicketsStepPage.seatingChartPreview).toBeVisible();
      // Create Ticket Type modal & flow should work as expected
      await expect(addTicketsStepPage.createTicketTypeButton).toBeVisible();
      await addTicketsStepPage.createTicketTypeButton.click();
      await expect(addTicketsStepPage.addTicketTypeModal).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalNameInput).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalPriceInput).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalAddToSectionDropdown).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalAddTicketTypeToSectionNote).toBeVisible();
      await expect(addTicketsStepPage.addTicketTypeModalSaveButton).toBeDisabled();
    }
  );
});

test.describe('Ticketed Event Form - Reserved Seating - Free and Paid Ticketing', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Ticketed Event Form - Reserved Seating Workflow - Venue with Only One Venue Configuration',
    { tag: '@smoke @non_prod' },
    async ({
      ticketingManagementHomePage,
      baseTicketedEventFormPage,
      addTicketsStepPage,
      eventDetailsStepPage,
      baseFormStepsPage,
    }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await ticketingManagementHomePage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();

      await baseFormStepsPage.continueButton.click();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Alaska option
      await eventDetailsStepPage.timeZoneSelectOption('America/Juneau').click();
      // Venue with one seating layout possibility should work as expected
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await addTicketsStepPage.venueSelectDropdown.click();
      await addTicketsStepPage.venueSelectDropdownOption('UI Automation - Indoor Venue 1').click();
      await expect(addTicketsStepPage.seatingLayoutSelectDropdown).toHaveText(
        'Automation - Indoor Venue Config 1 - With Valid Seating Chart'
      );
      await expect(addTicketsStepPage.seatingChartPreview).toBeVisible();
      await expect(addTicketsStepPage.ticketTypeSelectionContainer).toBeVisible();
    }
  );

  test(
    'Ticketed Event Form - Reserved Seating Workflow - Venue Configuration with No Categories',
    { tag: '@smoke' },
    async ({
      ticketingManagementHomePage,
      baseTicketedEventFormPage,
      addTicketsStepPage,
      eventDetailsStepPage,
      baseFormStepsPage,
    }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await ticketingManagementHomePage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();

      await baseFormStepsPage.continueButton.click();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Alaska option
      await eventDetailsStepPage.timeZoneSelectOption('America/Juneau').click();
      // Venue with a seating chart with no caegories should show the No Sections Available State
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await addTicketsStepPage.venueSelectDropdown.click();
      await addTicketsStepPage.venueSelectDropdownOption('UI Automation - Outdoor Venue 1').click();
      await addTicketsStepPage.seatingLayoutSelectDropdown.click();
      await addTicketsStepPage
        .seatingLayoutSelectDropdownOption('Automation - Outdoor Venue Config 3 - With Seating Chart + No Categories')
        .click();
      await expect(addTicketsStepPage.seatingLayoutSelectDropdown).toHaveText(
        'Automation - Outdoor Venue Config 3 - With Seating Chart + No Categories'
      );
      await expect(addTicketsStepPage.noSectionsAvailableState).toBeVisible();
      await expect(addTicketsStepPage.noSectionsAvailableContactSupportButton).toBeVisible();
      await expect(addTicketsStepPage.noSectionsAvailableContactSupportButton).toBeEnabled();
    }
  );

  test(
    'Ticketed Event Form - Reserved Seating Workflow - Venue with No Venue Configuration',
    { tag: '@smoke' },
    async ({
      ticketingManagementHomePage,
      baseTicketedEventFormPage,
      addTicketsStepPage,
      eventDetailsStepPage,
      baseFormStepsPage,
    }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0
      await ticketingManagementHomePage.page.goto('/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIyMjUzNjM0');
      await expect(baseTicketedEventFormPage.addTicketingForm).toBeVisible();
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticketed-event-type')).toBeVisible();
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();

      await baseFormStepsPage.continueButton.click();
      await eventDetailsStepPage.timeZoneSelect.click();
      // This is the Alaska option
      await eventDetailsStepPage.timeZoneSelectOption('America/Juneau').click();
      // Venue with no seating layout should show an error state
      await baseFormStepsPage.continueButton.click();
      await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
      await addTicketsStepPage.venueSelectDropdown.click();
      await addTicketsStepPage.venueSelectDropdownOption('UI Automation - Indoor Venue 2').click();
      await expect(addTicketsStepPage.seatingLayoutSelectErrorState).toBeVisible();
    }
  );
});
