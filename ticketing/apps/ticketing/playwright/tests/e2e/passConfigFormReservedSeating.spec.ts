import { expect, test } from '../../fixtures/ticketingBackOfficeManagementFixture';
import { FormSteps } from '../../pages/forms/baseFormStepsPage';

/* # ==============
    # Notes
    # ==============

    To use the Reserved Seating workflow, you must select Reserved Seating pass type in the
    first step of the pass config form.

    Pass Config Form URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}/create-pass */

/*  # ==============
    # Page Structure
    # ============== */

test.describe('Pass Config Form - Reserved Seating - Page Structure - Free and Paid Passes', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test('Pass Config Form - Reserved Seating - School with No Venues', async ({
    ticketingManagementHomePage,
    basePassConfigFormPage,
    baseFormStepsPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/181416/create-pass
    await ticketingManagementHomePage.page.goto('/ticketing/181416/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.verifyAllFormStepsState(FormSteps.SelectAType, 'completed', 'GeneralAdmission');
    await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'selected');
    await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'disabled');
  });

  test('Pass Config Form - Reserved Seating - School with No Seating Charts', async ({
    ticketingManagementHomePage,
    basePassConfigFormPage,
    baseFormStepsPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/181428/create-pass
    await ticketingManagementHomePage.page.goto('/ticketing/181428/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.verifyAllFormStepsState(FormSteps.SelectAType, 'completed', 'GeneralAdmission');
    await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'selected');
    await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'disabled');
  });

  test('Pass Config Form - Reserved Seating - Navigation', async ({
    ticketingManagementHomePage,
    basePassConfigFormPage,
    baseFormStepsPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await ticketingManagementHomePage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();

    // Check pass form steps visibilities and selection states
    await baseFormStepsPage.verifyAllFormStepsState(FormSteps.SelectAType, 'completed', 'GeneralAdmission');

    // Check General Admission and Reserved Seating radio buttons
    await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'selected');
    await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
    await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'selected');
    await baseFormStepsPage.verifyAllFormStepsState(FormSteps.SelectAType, 'completed', 'ReservedSeating');

    // Check form steps after clicking 'Continue'
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await baseFormStepsPage.verifyAllFormStepsState(FormSteps.AddPassDetails, 'selected', 'ReservedSeating');
    await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();

    // Check form steps/radio buttons after clicking 'Back'
    await baseFormStepsPage.backButton('pass-config-form').click();
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'enabled');
    await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'selected');
  });

  test('Pass Config Form - Paid Ticketing - Ensure Bundled Fees Form is Visible', async ({
    ticketingManagementHomePage,
    basePassConfigFormPage,
    baseFormStepsPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/175428/create-pass
    await ticketingManagementHomePage.page.goto('/ticketing/175428/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();

    // Check pass form steps visibilities and selection states
    await baseFormStepsPage.verifyAllFormStepsState(FormSteps.SelectAType, 'completed', 'GeneralAdmission');

    // Check General Admission and Reserved Seating radio buttons
    await baseFormStepsPage.verifyTypeRadioState('GeneralAdmission', 'selected');
    await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
    await baseFormStepsPage.verifyTypeRadioState('ReservedSeating', 'selected');

    // Check Bundled Fees Container
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await baseFormStepsPage.verifyAllFormStepsState(FormSteps.AddPassDetails, 'selected', 'ReservedSeating');
    await expect(baseFormStepsPage.bundledFeesContainer).toBeVisible();
    await expect(baseFormStepsPage.selectedBundledFeesOption('your-fans')).toBeVisible();

    await baseFormStepsPage.priceInput.fill('0');
    await baseFormStepsPage.bundledFeesOption('your-organization').click();
    await expect(baseFormStepsPage.priceInput).toBeVisible();
    await baseFormStepsPage.priceInput.fill('3');
    await expect(baseFormStepsPage.priceInputError).toBeVisible();
    await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
  });
});

/* # ==============
  # Pass Information Interactions
  # ============== */

test.describe('Pass Config Form Reserved Seating - Free Passes', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Pass Config Form - Reserved Seating Workflow - Free Passes',
    { tag: '@non_prod @regression' },
    async ({ basePassConfigFormPage, baseFormStepsPage, passDetailsStepPage, addTeamsStepPage, addSeatingPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
      await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
      await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();

      // Select Reserved Seating ticketed event type
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
      await baseFormStepsPage.continueButton.click();

      // Fill in pass details
      await passDetailsStepPage.fillPassTitleAndDateDetails();
      await expect(baseFormStepsPage.disabledPriceInput).toHaveValue('0.00');
      await expect(baseFormStepsPage.quantityForSaleInput).not.toBeVisible();
      await baseFormStepsPage.continueButton.click();

      // Team Information step
      await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTeamRS)).toBeVisible();
      await addTeamsStepPage.passTeamSingleSelectDropdown.click();
      await addTeamsStepPage.teamsSelectOption("Ticketing - Girl's Basketball").click();
      await expect(baseFormStepsPage.continueButton).toBeEnabled();
      await baseFormStepsPage.continueButton.click();

      // Seating Information step
      await expect(baseFormStepsPage.formStepHeadline('seating-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddSeating)).toBeVisible();
      await expect(addSeatingPage.venueSelectDropdown).toBeVisible();
      await expect(addSeatingPage.disabledSeatingLayoutSelectDropdown).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('pass')).toBeVisible();

      // Select Venue and Seating Layout
      await addSeatingPage.fillVenueAndSeatingDetails(
        'UI Automation - Outdoor Venue 1',
        'Automation - Outdoor Venue Config 2 - With Seating Chart + One Category'
      );
      await expect(addSeatingPage.seatingLayoutPreview).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('pass')).toBeVisible();
      await expect(addSeatingPage.sectionsSelectDropdown).toBeVisible();

      // This is the One Category To Rule Them All option in the select (the only option)
      await addSeatingPage.sectionsSelectDropdown.click();
      await addSeatingPage.sectionsSelectOption('One Category To Rule Them All').click();

      // Review Pass
      await baseFormStepsPage.reviewButton('pass').click();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
      await expect(baseFormStepsPage.saveAsDraftButton('pass-config')).toBeVisible();
      await expect(baseFormStepsPage.publishButton('pass-config')).toBeVisible();
    }
  );
});

test.describe('Pass Config Form Reserved Seating - Paid Passes', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Pass Config Form - Reserved Seating Workflow - Paid Passes',
    { tag: '@non_prod @regression' },
    async ({ basePassConfigFormPage, baseFormStepsPage, passDetailsStepPage, addTeamsStepPage, addSeatingPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/175428/create-pass
      await basePassConfigFormPage.page.goto('/ticketing/175428/create-pass');
      await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();

      // Select Reserved Seating ticketed event type
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
      await baseFormStepsPage.continueButton.click();

      // Fill in pass details
      await passDetailsStepPage.fillPassTitleAndDateDetails();
      await baseFormStepsPage.priceInput.fill('5');
      await expect(baseFormStepsPage.quantityForSaleInput).not.toBeVisible();
      await baseFormStepsPage.continueButton.click();

      // Team Information step
      await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTeamRS)).toBeVisible();
      await expect(addTeamsStepPage.passTeamSingleSelectDropdown).toBeVisible();
      await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
      await addTeamsStepPage.passTeamSingleSelectDropdown.click();
      await addTeamsStepPage.teamsSelectOption('Ticketing - Paid Ticketing w/ Events').click();
      await expect(baseFormStepsPage.continueButton).toBeEnabled();
      await baseFormStepsPage.continueButton.click();

      // Seating Information step
      await expect(baseFormStepsPage.formStepHeadline('seating-information')).toBeVisible();
      await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddSeating)).toBeVisible();
      await expect(addSeatingPage.venueSelectDropdown).toBeVisible();
      await expect(addSeatingPage.disabledSeatingLayoutSelectDropdown).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('pass')).toBeVisible();

      // Select Venue and Seating Layout
      await addSeatingPage.fillVenueAndSeatingDetails(
        'UI Automation - Paid Ticketing - Outdoor Venue 1',
        'Venue Config 2 - Seating Chart with One Category'
      );
      await expect(addSeatingPage.seatingLayoutPreview).toBeVisible();
      await expect(baseFormStepsPage.disabledReviewButton('pass')).toBeVisible();
      await expect(addSeatingPage.sectionsSelectDropdown).toBeVisible();

      // This is the One Category To Rule Them All option in the select (the only option)
      await addSeatingPage.sectionsSelectDropdown.click();
      await addSeatingPage.sectionsSelectOption('One Category To Rule Them All').click();
      await expect(addSeatingPage.selectedSectionOption('1')).toHaveText('One Category To Rule Them All');

      // Review Pass
      await baseFormStepsPage.reviewButton('pass').click();
      await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
      await expect(baseFormStepsPage.saveAsDraftButton('pass-config')).toBeVisible();
      await expect(baseFormStepsPage.publishButton('pass-config')).toBeVisible();
    }
  );
});

test.describe('Pass Config Form Reserved Seating - Free and Paid Ticketing', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });
  test(
    'Pass Config Form - Reserved Seating Workflow - Venue with Only One Venue Configuration',
    { tag: '@non_prod' },
    async ({ basePassConfigFormPage, baseFormStepsPage, passDetailsStepPage, addTeamsStepPage, addSeatingPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
      await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
      await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();

      // Select Reserved Seating ticketed event type
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
      await baseFormStepsPage.continueButton.click();

      // Fill in pass details
      await passDetailsStepPage.fillPassTitleAndDateDetails();
      await baseFormStepsPage.continueButton.click();

      // Team Information step
      await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
      await expect(addTeamsStepPage.passTeamSingleSelectDropdown).toBeVisible();
      await addTeamsStepPage.passTeamSingleSelectDropdown.click();
      // The option being clicked here is the 'Ticketing - Girl's Basketball' option
      await addTeamsStepPage.teamsSelectOption("Ticketing - Girl's Basketball").click();
      await baseFormStepsPage.continueButton.click();

      // Select Venue and Seating Layout
      await addSeatingPage.fillVenueAndSeatingDetails(
        'UI Automation - Indoor Venue 1',
        'Automation - Indoor Venue Config 1 - With Valid Seating Chart'
      );
      await expect(addSeatingPage.seatingLayoutPreview).toBeVisible();
    }
  );

  test(
    'Pass Config Form - Reserved Seating Workflow - Venue Configuration with No Sections',
    { tag: '@smoke' },
    async ({ basePassConfigFormPage, baseFormStepsPage, passDetailsStepPage, addTeamsStepPage, addSeatingPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
      await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
      await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();

      // Select Reserved Seating ticketed event type
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
      await baseFormStepsPage.continueButton.click();

      // Fill in pass details
      await passDetailsStepPage.fillPassTitleAndDateDetails();
      await expect(baseFormStepsPage.disabledPriceInput).toHaveValue('0.00');
      await expect(baseFormStepsPage.quantityForSaleInput).not.toBeVisible();
      await baseFormStepsPage.continueButton.click();

      // Team Information step
      await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
      await expect(addTeamsStepPage.passTeamSingleSelectDropdown).toBeVisible();
      await addTeamsStepPage.passTeamSingleSelectDropdown.click();
      await addTeamsStepPage.teamsSelectOption("Ticketing - Girl's Basketball").click();
      await baseFormStepsPage.continueButton.click();

      // Select Venue and Seating Layout
      await addSeatingPage.fillVenueAndSeatingDetails(
        'UI Automation - Outdoor Venue 1',
        'Automation - Outdoor Venue Config 3 - With Seating Chart + No Categories'
      );
      await expect(addSeatingPage.noSectionsAvailableComponent).toBeVisible();
      await expect(addSeatingPage.noSectionsAvailableContactSupportButton).toBeVisible();
      await expect(addSeatingPage.noSectionsAvailableContactSupportButton).toBeEnabled();
    }
  );

  test(
    'Pass Config Form - Reserved Seating Workflow - Venue with No Venue Configuration',
    { tag: '@smoke' },
    async ({ basePassConfigFormPage, baseFormStepsPage, passDetailsStepPage, addTeamsStepPage, addSeatingPage }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
      await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
      await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();

      // Select Reserved Seating ticketed event type
      await baseFormStepsPage.typeSelectionRadio('ReservedSeating').click();
      await baseFormStepsPage.continueButton.click();

      // Fill in pass details
      await passDetailsStepPage.fillPassTitleAndDateDetails();
      await baseFormStepsPage.continueButton.click();

      // Team Information step
      await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
      await expect(addTeamsStepPage.passTeamSingleSelectDropdown).toBeVisible();
      await addTeamsStepPage.passTeamSingleSelectDropdown.click();
      await addTeamsStepPage.teamsSelectOption("Ticketing - Girl's Basketball").click();
      await baseFormStepsPage.continueButton.click();

      // Select Venue and Seating Layout
      await addSeatingPage.venueSelectDropdown.click();
      await addSeatingPage.venueSelectOption('UI Automation - Indoor Venue 2').click();
      await expect(addSeatingPage.venueSelectError).toBeVisible();
    }
  );
});
