import { expect, test } from '../../fixtures/ticketingBackOfficeManagementFixture';
import { FormSteps } from '../../pages/forms/baseFormStepsPage';

/*  # ==============
    # Notes
    # ==============

    Draft Ticketed Event Form URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}/add-ticketing/{scheduleEntryID}/ticketedEventId={ticketedEventID}
*/

/* ==============
    Navigation
   ============== */

test.describe('Draft Ticketed Event Form - Navigation', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Navigate to the Draft Ticketed Event Form', async ({ ticketingManagementHomePage, baseFormStepsPage }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    // Navigate to Draft section
    await ticketingManagementHomePage.eventsMenuItem('draft').click();
    await expect(ticketingManagementHomePage.eventsHeadline('draft')).toBeVisible();
    // Click Review button for 'Don't Tough - UI Automation - Draft State - Do Not Make Changes!!!!'
    await ticketingManagementHomePage.reviewDraftButton('VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==').click();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
  });
});

/* ==============
    Page Structure
   ============== */

test.describe('Draft Ticketed Event Form - Page Structure', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  const orgId = '174137';
  const scheduledEventId = 'U2NoZWR1bGVFbnRyeTIzMDM3ODM1';
  const ticketedEventId = 'VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==';

  test('Draft Ticketed Event Form - Page Structure - Free Ticketing', async ({
    ticketingManagementHomePage,
    baseFormStepsPage,
    reviewAndPublishStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIzMDM3ODM1?ticketedEventId=VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==
    await ticketingManagementHomePage.page.goto(
      `/ticketing/${orgId}/add-ticketing/${scheduledEventId}?ticketedEventId=${ticketedEventId}`
    );
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    await expect(reviewAndPublishStepPage.reviewItemSection('event-details')).toBeVisible();
    await expect(reviewAndPublishStepPage.reviewItemSection('ticket-information')).toBeVisible();
    await expect(baseFormStepsPage.publishButton('ticketed-event')).toBeVisible();
    await expect(baseFormStepsPage.publishButton('ticketed-event')).toBeEnabled();
    await expect(baseFormStepsPage.disabledSaveAsDraftButton('ticketed-event')).toBeVisible();
    await expect(baseFormStepsPage.cancelButton).toBeVisible();
  });

  test('Draft Ticketed Event Form - Page Structure - Free Ticketing - Ticket Pricing', async ({
    ticketingManagementHomePage,
    baseFormStepsPage,
    baseTicketedEventFormPage,
  }) => {
    /* https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIzMDM3ODM1?ticketedEventId=VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ== */
    await ticketingManagementHomePage.page.goto(
      `/ticketing/${orgId}/add-ticketing/${scheduledEventId}?ticketedEventId=${ticketedEventId}`
    );
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    await baseFormStepsPage.backButton('ticketed-event-form').click();
    await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();
    await expect(baseTicketedEventFormPage.ticketPricingDescriptionText).not.toBeVisible();
    await expect(baseTicketedEventFormPage.disabledTicketType).toHaveText('Free Admission');
    await expect(baseFormStepsPage.readOnlyPriceInput).toHaveValue('0.00');
    await expect(baseFormStepsPage.priceInputHelpText).not.toBeVisible();
    await expect(baseFormStepsPage.quantityForSaleInput).toBeEmpty();
    await expect(baseFormStepsPage.quantityForSaleInput).toHaveAttribute('placeholder', 'Enter quantity');
    await expect(baseTicketedEventFormPage.addTicketTypeSelectionButton).not.toBeVisible();
  });
});

/* ==============
    Draft Ticketed Event Form Interactions
  ============== */

test.describe('Draft Ticketed Event Form - Form Interactions', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Draft Ticketed Event Form - No Changes Made - Cancel Button Behavior', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    baseFormStepsPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await ticketingManagementHomePage.eventsMenuItem('draft').click();
    await ticketingManagementHomePage.reviewDraftButton('VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==').click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('review')).toBeVisible();
    // Expect 'cancel' button behavior to take user out of event without changing anything
    await baseFormStepsPage.cancelButton.click();
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await expect(ticketingManagementHomePage.eventsMenuItemSelected('draft')).toBeVisible();
  });

  test('Draft Ticketed Event Form - Changes Made - Missing Required Field - Cancel Button Behavior', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    baseFormStepsPage,
    reviewAndPublishStepPage,
    eventDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await ticketingManagementHomePage.eventsMenuItem('draft').click();
    await ticketingManagementHomePage.reviewDraftButton('VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==').click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('review')).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
    // Clear the text in event title input
    await eventDetailsStepPage.eventTitleInput.fill('');
    // Need to click out of the event title to trigger the error
    await eventDetailsStepPage.eventDescriptionInput.click();
    // Assert that the event title input has a red border (e.g., using the CSS variable or hex value)
    await expect(eventDetailsStepPage.eventTitleInputError).toBeVisible();
    await expect(eventDetailsStepPage.eventTitleInputError).toHaveCSS('border-color', 'rgb(187, 23, 0)');
    await baseFormStepsPage.cancelButton.click();
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await expect(ticketingManagementHomePage.eventsMenuItemSelected('draft')).toBeVisible();
  });

  test('Draft Ticketed Event Form - Changes Made - Cancel Button Behavior - Save Changes Modal - Discard Changes', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    baseFormStepsPage,
    reviewAndPublishStepPage,
    eventDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await ticketingManagementHomePage.eventsMenuItem('draft').click();
    await ticketingManagementHomePage.reviewDraftButton('VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==').click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('review')).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
    await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();

    // Clear the text in event title input then discard changes
    await eventDetailsStepPage.eventTitleInput.fill('This is an automated test');
    await baseFormStepsPage.cancelButton.click();
    await expect(baseFormStepsPage.saveChangesModal).toBeVisible();
    await baseFormStepsPage.discardChangesButton.click();
    await expect(baseFormStepsPage.saveChangesModal).not.toBeVisible();
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    await expect(ticketingManagementHomePage.eventsMenuItemSelected('draft')).toBeVisible();
    await ticketingManagementHomePage.reviewDraftButton('VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==').click();
    await expect(baseTicketedEventFormPage.ticketedEventForm('review')).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();

    // Expect changes to have discarded
    await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
    await expect(eventDetailsStepPage.eventDescriptionInput).toBeEmpty();
  });

  test(
    'Draft Ticketed Event Form - Changes Made - Cancel Button Behavior - Save Changes Modal - Save Changes',
    { tag: '@non_prod' },
    async ({
      ticketingManagementHomePage,
      baseTicketedEventFormPage,
      baseFormStepsPage,
      reviewAndPublishStepPage,
      eventDetailsStepPage,
    }) => {
      // https://main--latest.app.thorhudl.com/ticketing/174137
      await ticketingManagementHomePage.page.goto('/ticketing/174137');
      await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
      await ticketingManagementHomePage.eventsMenuItem('draft').click();
      await ticketingManagementHomePage
        .reviewDraftButton('VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==')
        .click();
      await expect(baseTicketedEventFormPage.ticketedEventForm('review')).toBeVisible();
      await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
      await expect(baseFormStepsPage.disabledSaveAsDraftButton('ticketed-event')).toBeVisible();
      await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();

      // Change the event's title text and description and save changes
      await eventDetailsStepPage.eventTitleInput.fill('This is an automated test');
      await eventDetailsStepPage.eventDescriptionInput.fill('');
      await baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish).click();
      await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
      await expect(baseFormStepsPage.saveAsDraftButton('ticketed-event')).toBeVisible();
      await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
      await expect(baseFormStepsPage.formStepHeadline('event-details')).toBeVisible();
      await eventDetailsStepPage.eventDescriptionInput.fill('This will be edited and cleared and then added back');
      await baseFormStepsPage.cancelButton.click();
      await expect(baseFormStepsPage.saveChangesModal).toBeVisible();
      await baseFormStepsPage.saveChangesButton.click();
      await expect(baseFormStepsPage.saveChangesModal).not.toBeVisible();
      await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
      await expect(ticketingManagementHomePage.eventsMenuItemSelected('draft')).toBeVisible();

      // Expect newly added text to have saved
      await ticketingManagementHomePage
        .reviewDraftButton('VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==')
        .click();
      await expect(baseTicketedEventFormPage.ticketedEventForm('review')).toBeVisible();
      await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
      await expect(eventDetailsStepPage.eventDescriptionInput).toHaveText(
        'This will be edited and cleared and then added back'
      );

      // Update event title and description again
      await eventDetailsStepPage.eventTitleInput.fill(
        "Don't Touch - UI Automation - Draft State - Do Not Make Changes!!!!"
      );
      await eventDetailsStepPage.eventDescriptionInput.fill('');
      await baseFormStepsPage.cancelButton.click();
      await baseFormStepsPage.saveChangesButton.click();
      await ticketingManagementHomePage
        .reviewDraftButton('VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==')
        .click();
      await reviewAndPublishStepPage.editDetailsButton('event', 'details').click();
      await expect(eventDetailsStepPage.eventDescriptionInput).toHaveText('');
    }
  );

  /* ==== Review Ticketing Form Validation ==== # */

  test('Draft Ticketed Event Form - Form Validation - Quantity for Sale - Less Than 1', async ({
    ticketingManagementHomePage,
    baseTicketedEventFormPage,
    baseFormStepsPage,
    reviewAndPublishStepPage,
  }) => {
    const orgId = '174137';
    const scheduledEventId = 'U2NoZWR1bGVFbnRyeTIzMDM3ODM1';
    const ticketedEventId = 'VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==';

    // https://main--latest.app.thorhudl.com/ticketing/174137/add-ticketing/U2NoZWR1bGVFbnRyeTIzMDM3ODM1?ticketedEventId=VGlja2V0ZWRFdmVudDY0ZGZlOGYzYmI1NzgyYzliN2JjN2ExMQ==
    await ticketingManagementHomePage.page.goto(
      `/ticketing/${orgId}/add-ticketing/${scheduledEventId}?ticketedEventId=${ticketedEventId}`
    );
    await expect(baseTicketedEventFormPage.ticketedEventForm('review')).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('review-&-publish')).toBeVisible();
    await expect(baseFormStepsPage.publishButton('ticketed-event')).toBeVisible();
    await expect(baseFormStepsPage.publishButton('ticketed-event')).toBeEnabled();
    await expect(baseFormStepsPage.disabledSaveAsDraftButton('ticketed-event')).toBeVisible();
    await reviewAndPublishStepPage.editDetailsButton('ticket', 'information').click();
    await expect(baseFormStepsPage.formStepHeadline('ticket-information')).toBeVisible();

    // Enter "0" into the Quantity for Sale input and check for error state
    await baseFormStepsPage.quantityForSaleInput.fill('0');
    await expect(baseFormStepsPage.quantityForSaleInputError).toHaveCSS('color', 'rgb(187, 23, 0)');
    await expect(baseFormStepsPage.quantityForSaleInputError).toHaveCSS('border-color', 'rgb(187, 23, 0)');
    await expect(baseFormStepsPage.quantityForSaleInputStateHelpText('error')).toHaveText(
      'Quantity must be greater than 0.'
    );
    await expect(baseFormStepsPage.quantityForSaleInputStateHelpText('error')).toHaveCSS('color', 'rgb(187, 23, 0)');

    // Assert the disabled Review Event button is visible
    await expect(baseFormStepsPage.reviewButtonDisabled('event')).toBeVisible();
  });
});
