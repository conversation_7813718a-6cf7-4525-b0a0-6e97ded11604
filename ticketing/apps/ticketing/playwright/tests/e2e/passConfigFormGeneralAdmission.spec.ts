import { expect, test } from '../../fixtures/ticketingBackOfficeManagementFixture';
import { FormSteps } from '../../pages/forms/baseFormStepsPage';

/* # ==============
    # Notes
    # ==============

    Pass Config Form URL:
        https://main--latest.app.thorhudl.com/ticketing/{organizationID}/create-pass


    # ==============
    # Navigation
    # ============== */

test.describe('Pass Config Form General Admission - Navigation', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Navigate to the Pass Config Form - Admin', async ({ ticketingManagementHomePage, basePassConfigFormPage }) => {
    // Go to the Ticketing Management Home Page for org 174137
    // https://main--latest.app.thorhudl.com/ticketing/174137
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();

    await ticketingManagementHomePage.passesMenuItem('current').click();
    await expect(ticketingManagementHomePage.passesHeadline('current')).toBeVisible();

    await ticketingManagementHomePage.createPassConfigButton.click();
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
  });
});

/*  # ==============
    # Page Structure
    # ==============

    # ==== Free Ticketing ==== # */

test.describe('Pass Config Form General Admission - Page Structure', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Pass Config Form - Page Structure - Free Ticketing - Pass Details Step', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();

    // Step indicators
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

    // Pass details
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await expect(passDetailsStepPage.passNameInput).toBeVisible();
    await expect(passDetailsStepPage.passDateInput('start')).toBeVisible();
    await expect(passDetailsStepPage.passDateInput('end')).toBeVisible();
    await expect(passDetailsStepPage.passDescriptionTextbox).toBeVisible();
    await expect(passDetailsStepPage.disabledPriceInput).toBeDisabled();
    await expect(passDetailsStepPage.passVisibilitySelection).toBeVisible();
    await expect(passDetailsStepPage.formFieldSelection).toBeVisible();
    await expect(passDetailsStepPage.addCustomFieldButton).toBeVisible();
    await expect(passDetailsStepPage.addCustomFieldButton).toBeEnabled();
    await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
    await expect(baseFormStepsPage.cancelButton).toBeVisible();
  });

  test('Pass Config Form - Page Structure - Free Ticketing - Add Teams Step', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();

    // Enter pass detail inputs
    await passDetailsStepPage.passNameInput.fill('UI Automation - Pass Config Automation');
    await passDetailsStepPage.passDateInput('start').fill('2028-01-01');
    await passDetailsStepPage.passDateInput('end').fill('2028-01-02');
    await baseFormStepsPage.continueButton.click();

    await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
    await expect(passDetailsStepPage.teamMultiSelectDropdown).toBeVisible();
    await expect(baseFormStepsPage.disabledReviewButton('pass')).toBeVisible();
    await expect(baseFormStepsPage.cancelButton).toBeVisible();
  });

  test('Pass Config Form - Page Structure - Free Ticketing - Review and Publish Step', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();

    // Enter pass detail inputs
    await passDetailsStepPage.passNameInput.fill('UI Automation - Pass Config Automation');
    await passDetailsStepPage.passDateInput('start').fill('2028-01-01');
    await passDetailsStepPage.passDateInput('end').fill('2028-01-02');
    await baseFormStepsPage.continueButton.click();
    await passDetailsStepPage.teamMultiSelectDropdown.click();
    // Select 'Ticketing - Girl's Basketball' option
    await passDetailsStepPage.teamMultiSelectOption("Ticketing - Girl's Basketball").click();
    await baseFormStepsPage.reviewButton('pass').click();
    await expect(baseFormStepsPage.formStepHeadline(FormSteps.ReviewAndPublish)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
    await expect(baseFormStepsPage.publishButton('pass-config')).toBeVisible();
    await expect(baseFormStepsPage.saveAsDraftButton('pass-config')).toBeVisible();
  });

  test('Pass Config Form - Free Ticketing - Ensure Bundled Fees Form is Not Visible', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();

    // Step indicators
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

    // Pass details
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();
    await expect(baseFormStepsPage.typeSelectionRadio('GeneralAdmission')).toBeVisible();
    await expect(baseFormStepsPage.typeSelectionRadio('GeneralAdmission')).toHaveClass(
      /.*_radioButtonContainerSelected.*/
    );
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    // Verify that the Bundled Fees section is not visible since it is a Free Ticketing school
    await expect(baseFormStepsPage.bundledFeesContainer).not.toBeVisible();
  });
});

/* # ==== Paid Ticketing ==== # */

test.describe('Pass Config Form General Admission - Paid Ticketing - Page Structure', () => {
  test.use({ storageState: 'playwright/.auth/paidTicketingSchoolAdmin.json' });

  test('Pass Config Form - Paid Ticketing - Ensure Bundled Fees Form is Visible', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/175428/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/175428/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();

    // Step indicators
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

    // Pass details
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();
    await expect(baseFormStepsPage.typeSelectionRadio('GeneralAdmission')).toBeVisible();
    await expect(baseFormStepsPage.typeSelectionRadio('GeneralAdmission')).toHaveClass(
      /.*_radioButtonContainerSelected.*/
    );
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(passDetailsStepPage.passPriceInput).toBeEnabled();

    // Bundled Fees
    // Verify that the Bundled Fees section is visible since it is a Paid Ticketing school
    await expect(baseFormStepsPage.bundledFeesContainer).toBeVisible();
    await expect(baseFormStepsPage.selectedBundledFeesOption('your-fans')).toBeVisible();
    await baseFormStepsPage.priceInput.fill('0');
    await baseFormStepsPage.bundledFeesOption('your-organization').click();
    await expect(baseFormStepsPage.priceInput).toBeVisible();
    await baseFormStepsPage.priceInput.fill('3');
    await expect(baseFormStepsPage.priceInputError).toBeVisible();
    await expect(baseFormStepsPage.disabledContinueButton).toBeVisible();
  });
});

/*  # ==============
    # Form Interactions
    # ============== */

test.describe('Pass Config Form General Admission - Form Interactions', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  test('Pass Config Form - Free Ticketing - Move Between Steps Interactions', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // Load Pass Config Form
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();

    // Verify the correct Step indicators are present and in the correct initial state
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();
    await expect(baseFormStepsPage.typeSelectionRadio('GeneralAdmission')).toBeVisible();
    // Verify that the General Admission type is selected
    await expect(baseFormStepsPage.typeSelectionRadio('GeneralAdmission')).toHaveClass(
      /.*_radioButtonContainerSelected.*/
    );

    // Continue to Add Pass Details step
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();

    // Fill in pass details
    await passDetailsStepPage.passNameInput.fill('UI Automation - Pass Config Automation');
    await passDetailsStepPage.passDateInput('start').fill('2028-01-01');
    await passDetailsStepPage.passDateInput('end').fill('2028-02-01');

    // Continue to Add Teams step
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

    // Open Team multi select and select all
    await passDetailsStepPage.teamMultiSelectDropdown.click();
    await passDetailsStepPage.teamMultiSelectOption('Select All').click();

    // Review Pass button should be enabled
    await expect(baseFormStepsPage.reviewButton('pass')).toBeEnabled();
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.AddTeams)).toBeVisible();

    // Go to Review & Publish
    await baseFormStepsPage.reviewButton('pass').click();
    await expect(baseFormStepsPage.formStepHeadline(FormSteps.ReviewAndPublish)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
    await expect(baseFormStepsPage.publishButton('pass-config')).toBeEnabled();
    await expect(baseFormStepsPage.saveAsDraftButton('pass-config')).toBeEnabled();

    // Go back to Add Teams
    await baseFormStepsPage.backButton('pass-config-form').click();
    await expect(baseFormStepsPage.formStepHeadline('team-information')).toBeVisible();
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

    // Check step indicators
    await baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType).click();
    await expect(baseFormStepsPage.formStepHeadline('pass-type')).toBeVisible();
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();

    await baseFormStepsPage.stepIndicator(FormSteps.ReviewAndPublish).click();
    await expect(baseFormStepsPage.formStepHeadline(FormSteps.ReviewAndPublish)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.AddTeams)).toBeVisible();
    await expect(baseFormStepsPage.checkedSelectedStepIndicator(FormSteps.ReviewAndPublish)).toBeVisible();
    await expect(baseFormStepsPage.publishButton('pass-config')).toBeEnabled();
  });

  // Need the date for several sets below so I am creating the data for it here
  const today = new Date();
  const todaysDateFormatted = today.toISOString().split('T')[0]; // YYYY-MM-DD format
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  const yesterdaysDateFormatted = yesterday.toISOString().split('T')[0]; // YYYY-MM-DD format

  test('Pass Config Form - Date Can Be Set in Past', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    // Enter pass detail inputs
    await passDetailsStepPage.passNameInput.fill('UI Automation - Pass Config - Date Can Be Set in Past');
    // Set start date in the past
    await passDetailsStepPage.passDateInput('start').fill(yesterdaysDateFormatted);
    await passDetailsStepPage.passDateInput('end').fill('2028-01-02');
    await expect(baseFormStepsPage.continueButton).toBeEnabled();
  });

  test('Pass Config Form - Start Date and End Date Can be the Same', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();

    // Enter pass detail inputs
    await passDetailsStepPage.passNameInput.fill('UI Automation - Pass Config - Same Start and End Date');
    // Set start date in the past
    await passDetailsStepPage.passDateInput('start').fill(todaysDateFormatted);
    await passDetailsStepPage.passDateInput('end').fill(todaysDateFormatted);
    await expect(baseFormStepsPage.continueButton).toBeEnabled();
  });

  test('Pass Config Form - Cancel Creating Pass Config - No Changes Made - No Save Changes Made Modal Prompted', async ({
    ticketingManagementHomePage,
    basePassConfigFormPage,
    baseFormStepsPage,
  }) => {
    // Go to the Ticketing Management Home Page for org 174137
    // https://main--latest.app.thorhudl.com/ticketing/174137
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Navigate to Current Passes section
    await ticketingManagementHomePage.passesMenuItem('current').click();
    await expect(ticketingManagementHomePage.passesHeadline('current')).toBeVisible();
    // Click on Create Pass Config button
    await ticketingManagementHomePage.createPassConfigButton.click();
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await expect(baseFormStepsPage.checkedStepIndicator(FormSteps.SelectAType)).toBeVisible();
    await expect(baseFormStepsPage.selectedStepIndicator(FormSteps.AddPassDetails)).toBeVisible();
    await expect(baseFormStepsPage.bundledFeesContainer).not.toBeVisible();
    // Click Cancel and verify it takes you back to the Current Passes page
    await baseFormStepsPage.cancelButton.click();
    await expect(ticketingManagementHomePage.passesHeadline('current')).toBeVisible();
  });

  test('Pass Config Form - Cancel Creating Pass Config - Changes Made - Discard Changes Takes you back to Current Passes', async ({
    ticketingManagementHomePage,
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
    saveChangesModal,
  }) => {
    // Go to the Ticketing Management Home Page for org 174137
    // https://main--latest.app.thorhudl.com/ticketing/174137
    await ticketingManagementHomePage.page.goto('/ticketing/174137');
    await expect(ticketingManagementHomePage.ticketingManagementHome).toBeVisible();
    // Navigate to Current Passes section
    await ticketingManagementHomePage.passesMenuItem('current').click();
    await expect(ticketingManagementHomePage.passesHeadline('current')).toBeVisible();
    // Click on Create Pass Config button
    await ticketingManagementHomePage.createPassConfigButton.click();
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    await expect(baseFormStepsPage.formStepHeadline('pass-details')).toBeVisible();
    await passDetailsStepPage.passNameInput.fill('UI Automation - Pass Config - Cancel Changes Made');
    // Set start date in the past
    await passDetailsStepPage.passDateInput('start').fill(todaysDateFormatted);
    await passDetailsStepPage.passDateInput('end').fill('2028-01-02');
    await baseFormStepsPage.continueButton.click();
    await passDetailsStepPage.teamMultiSelectDropdown.click();
    // Select 'Ticketing - Boy's Varsity Soccer' option
    await passDetailsStepPage.teamMultiSelectOption("Ticketing - Boy's Varsity Soccer").click();
    // Click Cancel button and make sure the Save Changes modal is visible
    await baseFormStepsPage.cancelButton.click();
    await expect(saveChangesModal.saveChangesModal).toBeVisible();
    await expect(saveChangesModal.yesSaveButton).toBeVisible();
    // Click Discard Changes button and verify it takes you back to the Current Passes page
    await saveChangesModal.discardChangesButton.click();
    await expect(ticketingManagementHomePage.passesHeadline('current')).toBeVisible();
  });
});

/*  # ==============
    # Form Validation
    # ============== */

test.describe('Pass Config Form General Admission - Form Validation', () => {
  test.use({ storageState: 'playwright/.auth/allSportsSchoolAdmin.json' });

  const today = new Date();
  const todaysDateFormatted = today.toISOString().split('T')[0]; // YYYY-MM-DD format
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);
  const tomorrowsDateFormatted = tomorrow.toISOString().split('T')[0]; // YYYY-MM-DD format

  test('Pass Config Form - Start Date Cannot be Set After End Date', async ({
    basePassConfigFormPage,
    baseFormStepsPage,
    passDetailsStepPage,
  }) => {
    // https://main--latest.app.thorhudl.com/ticketing/174137/create-pass
    await basePassConfigFormPage.page.goto('/ticketing/174137/create-pass');
    await expect(basePassConfigFormPage.createPassConfigForm).toBeVisible();
    await baseFormStepsPage.continueButton.click();
    // Enter pass detail inputs
    await passDetailsStepPage.passNameInput.fill('UI Automation - Pass Config - Date Can Be Set in Past');
    // Set start date for after the end date
    await passDetailsStepPage.passDateInput('start').fill(tomorrowsDateFormatted);
    await passDetailsStepPage.passDateInput('end').fill(todaysDateFormatted);

    // The End Date input should be red and have an error message
    await expect(passDetailsStepPage.passDateInput('end')).toHaveCSS('color', 'rgb(187, 23, 0)');
    await expect(passDetailsStepPage.passDateInput('end')).toHaveCSS('border-color', 'rgb(187, 23, 0)');
    await expect(passDetailsStepPage.passDateInputHelpText('end')).toHaveText('End date must be after start date.');
    await expect(passDetailsStepPage.passDateInputHelpText('end')).toHaveCSS('color', 'rgb(187, 23, 0)');
    await expect(baseFormStepsPage.disabledContinueButton).toBeDisabled();
  });
});
