import { expect, type Locator, type Page } from '@playwright/test';

/*
  All methods and locators for the Hudl Ticketing Management Home page
    - https://main--latest.app.thorhudl/ticketing/{organizationID} 
*/

export class TicketingManagementHomePage {
  readonly page: Page;
  readonly ticketingManagementHome: Locator;

  /* Onboarding */
  readonly onboardingHeader: Locator;
  readonly onboardingStep: (stepName: string, status: string, requirement: string) => Locator;
  readonly adminRequestBanner: Locator;
  readonly completeOnboardingButton: (status: string) => Locator;
  readonly onboardingCTAButton: (buttonName: string) => Locator;

  /* Ticketing Side Menu */
  readonly ticketingSideMenu: Locator;
  readonly onboardingMenuItem: Locator;
  readonly eventsMenuItem: (menuItem: string) => Locator;
  readonly eventsMenuItemSelected: (menuItem: string) => Locator;
  readonly passesMenuItem: (menuItem: string) => Locator;
  readonly passesMenuItemSelected: (menuItem: string) => Locator;

  readonly manageActionList: Locator;
  readonly manageActionListOption: (option: string) => Locator;

  /* Ticketed Events Tab Empty States */
  readonly eventsHeadline: (eventType: string) => Locator;
  readonly noEventsHeader: Locator;
  readonly publishTicketedEventsText: Locator;
  readonly noEventsScheduledHeader: Locator;
  readonly scheduleUpcomingEventsText: Locator;
  readonly saveEventsAsDraftText: Locator;
  readonly noPastEventsText: Locator;

  /* Ticketed Events */
  readonly ticketedEventItem: (ticketedEventId: string) => Locator;
  readonly ticketedEventDetailsLink: (ticketedEventId: string) => Locator;
  readonly ticketedEventDateAndTime: (ticketedEventId: string) => Locator;
  readonly ticketedEventNumberOfTicketsSold: (ticketedEventId: string) => Locator;
  readonly ticketedEventActionList: (ticketedEventId: string) => Locator;
  readonly ticketedEventActionListOption: (option: string, ticketedEventId: string) => Locator;
  readonly ticketingCreateAdHocEventButton: Locator;

  /* Non-Ticketed Events */
  readonly addTicketingButton: (ticktedEventId: string) => Locator;

  /* Draft Events */
  readonly reviewDraftButton: (ticketedEventId: string) => Locator;

  /* Passes Tab Empty States */
  readonly passesHeadline: (passType: string) => Locator;
  readonly emptyStateHeader: Locator;
  readonly publishPassesText: Locator;
  readonly savePassAsDraftText: Locator;
  readonly noPastPassesText: Locator;

  readonly createPassConfigButton: Locator;

  /* Pass Configs */
  readonly passConfig: (passConfigId: string) => Locator;
  readonly passConfigDetailsLink: (passConfigId: string) => Locator;
  readonly passConfigActiveTimePeriod: (passConfigId: string) => Locator;
  readonly passConfigTeams: (passConfigId: string) => Locator;
  readonly passConfigPassesSold: (passConfigId: string) => Locator;
  readonly passConfigActionList: (passConfigId: string) => Locator;
  readonly passConfigActionListOption: (option: string, passConfigId: string) => Locator;

  constructor(page: Page) {
    this.page = page;
    this.ticketingManagementHome = page.getByTestId('ticketing-management-home');

    /* Onboarding */
    this.onboardingHeader = page.getByTestId('onboarding-header-container');
    this.onboardingStep = (stepName: string, status: string, requirement: string) =>
      page.getByTestId(`onboarding-step-${stepName}-${status}-${requirement}`);
    this.adminRequestBanner = page.getByTestId('action-banner');
    this.completeOnboardingButton = (status: string) => page.getByTestId(`complete-onboarding-button-${status}`);
    this.onboardingCTAButton = (buttonName: string) => page.getByTestId(`cta-${buttonName}`);

    /* Ticketing Side Menu */
    this.ticketingSideMenu = page.getByTestId('ticketing-management-home-vertical-nav-side-menu');
    this.onboardingMenuItem = page.getByTestId('ticketing-side-menu-tab-onboarding');
    this.eventsMenuItem = (menuItem: string) => page.getByTestId(`events-${menuItem}-menu-item`);
    this.eventsMenuItemSelected = (menuItem: string) => page.getByTestId(`events-${menuItem}-menu-item-selected`);
    this.passesMenuItem = (menuItem: string) => page.getByTestId(`passes-${menuItem}-menu-item`);
    this.passesMenuItemSelected = (menuItem: string) => page.getByTestId(`passes-${menuItem}-menu-item-selected`);

    this.manageActionList = page.getByTestId('manage-dropdown');
    this.manageActionListOption = (option: string) => page.getByTestId(`manage-dropdown-${option}-action`);

    /* Ticketed Events Tab Empty States */
    this.eventsHeadline = (eventType: string) => page.getByTestId(`${eventType}-events-headline`);
    this.noEventsHeader = page.getByTestId('no-events-headline');
    this.publishTicketedEventsText = page.getByTestId('publish-ticketed-events-text');
    this.noEventsScheduledHeader = page.getByTestId('no-events-scheduled-headline');
    this.scheduleUpcomingEventsText = page.getByTestId('schedule-upcoming-events-text');
    this.saveEventsAsDraftText = page.getByTestId('draft-ticketed-events-text');
    this.noPastEventsText = page.getByTestId('past-ticketed-events-text');

    /* Ticketed Events */
    this.ticketedEventItem = (ticketedEventId: string) => page.getByTestId(`ticketed-event-item-${ticketedEventId}`);
    this.ticketedEventDetailsLink = (ticketedEventId: string) =>
      page.getByTestId(`ticketed-event-details-${ticketedEventId}-details-link`);
    this.ticketedEventDateAndTime = (ticketedEventId: string) =>
      page.getByTestId(`ticketed-event-date-${ticketedEventId}`);
    this.ticketedEventNumberOfTicketsSold = (ticketedEventId: string) =>
      page.getByTestId(`ticket-count-${ticketedEventId}`);
    this.ticketedEventActionList = (ticketedEventId: string) =>
      page.getByTestId(`ticketed-event-${ticketedEventId}-action-list-button`);
    this.ticketedEventActionListOption = (option: string, ticketedEventId: string) =>
      page.getByTestId(`ticketed-event-item-${option}-action-${ticketedEventId}`);
    this.ticketingCreateAdHocEventButton = page.getByTestId('ticketing-create-ad-hoc-event-button');

    /* Non-Ticketed Events */
    this.addTicketingButton = (ticketedEventId: string) => page.getByTestId(`add-ticketing-button-${ticketedEventId}`);

    /* Draft Events */
    this.reviewDraftButton = (ticketedEventId: string) => page.getByTestId(`review-button-${ticketedEventId}`);

    /* Passes Tab Empty States */
    this.passesHeadline = (passType: string) => page.getByTestId(`${passType}-passes-headline`);
    this.emptyStateHeader = page.getByTestId('no-passes-headline');
    this.publishPassesText = page.getByTestId('published-passes-empty-text');
    this.savePassAsDraftText = page.getByTestId('draft-passes-empty-text');
    this.noPastPassesText = page.getByTestId('past-passes-empty-text');
    this.createPassConfigButton = page.getByTestId('ticketing-create-pass-button');

    /* Pass Configs */
    this.passConfig = (passConfigId: string) => page.getByTestId(`pass-config-row-${passConfigId}`);
    this.passConfigDetailsLink = (passConfigId: string) => page.getByTestId(`pass-config-details-link-${passConfigId}`);
    this.passConfigActiveTimePeriod = (passConfigId: string) =>
      page.getByTestId(`pass-config-active-time-period-${passConfigId}`);
    this.passConfigTeams = (passConfigId: string) => page.getByTestId(`pass-config-teams-${passConfigId}`);
    this.passConfigPassesSold = (passConfigId: string) => page.getByTestId(`pass-config-passes-sold-${passConfigId}`);
    this.passConfigActionList = (passConfigId: string) =>
      page.getByTestId(`pass-config-${passConfigId}-action-list-button`);
    this.passConfigActionListOption = (option: string, passConfigId: string) =>
      page.getByTestId(`pass-config-item-${option}-action-${passConfigId}`);
  }

  async isIncompleteOnboardingFlowVisible(): Promise<void> {
    await expect(this.onboardingHeader).toBeVisible();
    await expect(this.onboardingStep('Configure Ticketing Preferences', 'Incomplete', 'Required')).toBeVisible();
    await expect(
      this.onboardingStep('Payout Selection: Check or Direct Deposit', 'Incomplete', 'Required')
    ).toBeVisible();
    await expect(this.onboardingStep('Schedule Reference File Uploader', 'Incomplete', 'Optional')).toBeVisible();
    await expect(this.onboardingStep('Default Ticket Type Selection', 'Incomplete', 'Required')).toBeVisible();
  }

  async isUpcomingEventsEmptyStateVisible(): Promise<void> {
    await expect(this.eventsHeadline('upcoming')).toBeVisible();
    await expect(this.noEventsHeader).toBeVisible();
    await expect(this.publishTicketedEventsText).toBeVisible();
  }

  async isNonTicketedEventsEmptyStateVisible(): Promise<void> {
    await expect(this.eventsHeadline('non-ticketed')).toBeVisible();
    await expect(this.noEventsScheduledHeader).toBeVisible();
    await expect(this.scheduleUpcomingEventsText).toBeVisible();
  }

  async isDraftEventsEmptyStateVisible(): Promise<void> {
    await expect(this.eventsHeadline('draft')).toBeVisible();
    await expect(this.noEventsHeader).toBeVisible();
    await expect(this.saveEventsAsDraftText).toBeVisible();
  }

  async isPastEventsEmptyStateVisible(): Promise<void> {
    await expect(this.eventsHeadline('past')).toBeVisible();
    await expect(this.noEventsHeader).toBeVisible();
    await expect(this.noPastEventsText).toBeVisible();
  }

  async isCurrentPassesEmptyStateVisible(): Promise<void> {
    await expect(this.passesHeadline('current')).toBeVisible();
    await expect(this.emptyStateHeader).toHaveText('No published passes.');
    await expect(this.publishPassesText).toBeVisible();
  }

  async isDraftPassesEmptyStateVisible(): Promise<void> {
    await expect(this.passesHeadline('draft')).toBeVisible();
    await expect(this.emptyStateHeader).toHaveText('No draft passes.');
    await expect(this.savePassAsDraftText).toBeVisible();
  }

  async isPastPassesEmptyStateVisible(): Promise<void> {
    await expect(this.passesHeadline('past')).toBeVisible();
    await expect(this.emptyStateHeader).toHaveText('No past passes.');
    await expect(this.noPastPassesText).toBeVisible();
  }
}
