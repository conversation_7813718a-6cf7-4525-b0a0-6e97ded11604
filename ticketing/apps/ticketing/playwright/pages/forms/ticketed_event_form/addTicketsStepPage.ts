import { expect, type Locator, type Page } from '@playwright/test';

export class AddTicketsStepPage {
  readonly page: Page;
  readonly ticketTypeContainer: (rowNumber: number) => Locator;
  readonly ticketTypeSelectOption: (selectableTicketType: string) => Locator;
  readonly ticketTypeCreateNewTypeOption: (ticketName: string) => Locator;
  readonly ticketTypeSelectDropdown: (rowNumber: number) => Locator;
  readonly disabledTicketTypeSelectDropdown: Locator;
  readonly selectedTicketTypeDisabled: (rowNumber: number) => Locator;
  readonly selectedTicketType: (rowNumber: number) => Locator;
  readonly noTicketTypesText: Locator;

  // Add Ticket Type Modal
  readonly addTicketTypeModal: Locator;
  readonly addTicketTypeModalNameInput: Locator;
  readonly addTicketTypeModalPriceInput: Locator;
  readonly addTicketTypeModalAddToSectionDropdown: Locator;
  readonly addTicketTypeModalAddTicketTypeToSectionNote: Locator;
  readonly addTicketTypeModalSaveButton: Locator;
  readonly addTicketTypeModalCancelButton: Locator;

  // Reserved Seating Event Locators
  readonly venueSelectDropdown: Locator;
  readonly venueSelectDropdownOption: (option: string) => Locator;
  readonly selectedVenueOption: Locator;

  readonly disabledSeatingLayoutSelectDropdown: Locator;
  readonly seatingLayoutSelectDropdown: Locator;
  readonly seatingLayoutSelectDropdownOption: (option: string) => Locator;
  readonly seatingLayoutSelectErrorState: Locator;
  readonly createTicketTypeButton: Locator;

  readonly seatingChartPreview: Locator;
  readonly noSectionsAvailableState: Locator;
  readonly noSectionsAvailableContactSupportButton: Locator;

  readonly ticketTypeSelectionContainer: Locator;
  readonly ticketTypeSelectionRow: (index: number) => Locator;
  readonly ticketTypeSectionName: (name: string) => Locator;
  readonly ticketTypeSelectionTypeDropdown: (containerIndex: number) => Locator;
  readonly ticketTypeSelectionTypeDropdownOption: (type: string) => Locator;
  readonly ticketTypesList: Locator;
  readonly addTicketTypeButton: Locator;

  readonly ticketTypeNameInput: (rowNumber: number) => Locator;
  readonly ticketPriceInputReadOnly: (rowNumber: number) => Locator;
  readonly ticketPriceInput: (rowNumber: number) => Locator;
  readonly ticketTypeDeleteButton: (rowNumber: number) => Locator;
  readonly ticketTypeQuantityForSaleInput: (rowNumber: number) => Locator;

  constructor(page: Page) {
    this.page = page;
    this.ticketTypeContainer = (rowNumber: number) =>
      this.ticketTypesList.locator(`[class*='_ticketTypeSelection_']`).nth(rowNumber);
    this.ticketTypeSelectOption = (selectableTicketType: string) =>
      page.getByRole('option').filter({ hasText: selectableTicketType });
    this.ticketTypeCreateNewTypeOption = (ticketName: string) =>
      page.getByRole('option').filter({ hasText: `Add ${ticketName} as a new ticket type ` });
    this.ticketTypeSelectDropdown = (rowNumber: number) =>
      this.ticketTypeContainer(rowNumber).getByTestId('type-input-select');
    this.disabledTicketTypeSelectDropdown = page.getByTestId('type-input-disabled-select');
    this.selectedTicketTypeDisabled = (rowNumber: number) =>
      this.ticketTypeContainer(rowNumber).getByTestId('type-input-disabled-single-value');
    this.selectedTicketType = (rowNumber: number) =>
      this.ticketTypeContainer(rowNumber).getByTestId('type-input-single-value');
    this.noTicketTypesText = page.getByTestId('no-ticket-type-options-message');

    // Add Ticket Type Modal
    this.addTicketTypeModal = page.getByTestId('add-ticket-type-modal');
    this.addTicketTypeModalNameInput = page.getByTestId('ticket-type-name-input');
    this.addTicketTypeModalPriceInput = this.addTicketTypeModal.getByTestId('price-input');
    this.addTicketTypeModalAddToSectionDropdown = this.addTicketTypeModal.getByTestId(
      'add-ticket-types-to-section-select-select'
    );
    this.addTicketTypeModalAddTicketTypeToSectionNote = this.addTicketTypeModal.getByText(
      'Once created, you can add this ticket type to any section in any seating layout'
    );
    this.addTicketTypeModalSaveButton = this.addTicketTypeModal.getByRole('button', { name: 'Save' });
    this.addTicketTypeModalCancelButton = this.addTicketTypeModal.getByRole('button', { name: 'Cancel' });

    // Reserved Seating Event Locators
    this.venueSelectDropdown = page.getByTestId('venue-select-select');
    this.venueSelectDropdownOption = (option: string) => page.getByRole('option', { name: option });
    this.selectedVenueOption = page.getByTestId('venue-select-single-value');

    this.disabledSeatingLayoutSelectDropdown = page.getByTestId('venue-configuration-select-disabled-select');
    this.seatingLayoutSelectDropdown = page.getByTestId('venue-configuration-select-select');
    this.seatingLayoutSelectDropdownOption = (option: string) => page.getByRole('option', { name: option });
    this.seatingLayoutSelectErrorState = page.getByTestId('venue-configuration-select-error');
    this.createTicketTypeButton = page.getByRole('button', { name: 'Create Ticket Type' });

    this.seatingChartPreview = page.getByTestId('seating-chart-preview');
    this.noSectionsAvailableState = page.getByTestId('no-sections-empty-state');
    this.noSectionsAvailableContactSupportButton = this.noSectionsAvailableState.getByRole('button', {
      name: 'Contact Support',
    });

    this.ticketTypeSelectionContainer = page.getByTestId('category-selections-container');
    this.ticketTypeSelectionRow = (index: number) => page.getByTestId(`category-select-${index}`);
    this.ticketTypeSectionName = (name: string) => page.getByText(name);
    this.ticketTypeSelectionTypeDropdown = (containerIndex: number) =>
      this.ticketTypeSelectionRow(containerIndex).getByTestId('ticket-types-for-category-select-select');
    this.ticketTypeSelectionTypeDropdownOption = (type: string) => page.getByRole('option', { name: type });

    this.ticketTypesList = page.getByTestId('ticket-types-list');
    this.addTicketTypeButton = this.ticketTypesList.getByRole('button', { name: 'Add' });

    this.ticketTypeNameInput = (rowNumber: number) =>
      this.ticketTypeContainer(rowNumber).getByTestId('type-input-input');
    this.ticketPriceInputReadOnly = (rowNumber: number) =>
      this.ticketTypeContainer(rowNumber).getByTestId('price-input-read-only');
    this.ticketPriceInput = (rowNumber: number) => this.ticketTypeContainer(rowNumber).getByTestId('price-input');
    this.ticketTypeDeleteButton = (rowNumber: number) => this.ticketTypeContainer(rowNumber).getByLabel('Delete');
    this.ticketTypeQuantityForSaleInput = (rowNumber: number) =>
      this.ticketTypeContainer(rowNumber).getByTestId('quantity-for-sale-input');
  }

  async verifyNumberOfTicketTypeRows(expected: number) {
    const numRows = this.ticketTypesList.locator(`[class*='_ticketTypeSelection_']`).count();
    await expect(await numRows).toBe(expected);
  }
}
