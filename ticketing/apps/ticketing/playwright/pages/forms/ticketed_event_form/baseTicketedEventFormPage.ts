import { type Locator, type Page } from '@playwright/test';

/*
  All base methods and locators for ticketed event form steps (Select Event, Select a Type, Add Event Details, Add Tickets, and Review & Publish steps)
    - Add Ticketing: https://main--latest.app.thorhudl/ticketing/{organizationID}/add-ticketing/{scheduleEntryId}
    - Review and Edit Ticketing: https://main--latest.app.thorhudl/ticketing/{organizationID}/add-ticketing/{scheduleEntryID}/ticketedEventId={ticketedEventID}
*/

export class BaseTicketedEventFormPage {
  readonly page: Page;
  readonly addTicketingForm: Locator;
  readonly updateTicketingForm: Locator;
  readonly ticketedEventForm: (formType: string) => Locator;
  readonly selectEventButton: (selectableItem: string) => Locator;
  readonly disabledTicketType: Locator;
  readonly ticketPricingDescriptionText: Locator;
  readonly addTicketTypeSelectionButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.addTicketingForm = page.getByTestId('add-ticketing-to-your-event');
    this.updateTicketingForm = page.getByTestId('update-ticketing-for-your-event');
    this.ticketedEventForm = (formType: string) => page.getByTestId(`${formType}-ticketing-for-your-event`);
    this.selectEventButton = (selectableItem: string) => page.getByTestId(`selectable-item-${selectableItem}`);
    this.disabledTicketType = page.getByTestId('type-input-disabled-single-value');
    this.ticketPricingDescriptionText = page.getByTestId('ticket-pricing-description');
    this.addTicketTypeSelectionButton = page.getByTestId('add-ticket-type-selection-button');
  }
}
