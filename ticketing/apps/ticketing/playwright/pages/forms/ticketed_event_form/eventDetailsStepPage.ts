import { expect, type Locator, type Page } from '@playwright/test';
import { formatInTimeZone, zonedTimeToUtc } from 'date-fns-tz';
import { createRandomNumericId } from 'playwright-shared';

/*
  All methods and locators for the Event Information step on the ticketed event form
    - Add Ticketing: https://main--latest.app.thorhudl/ticketing/{organizationID}/add-ticketing/{scheduleEntryId}
    - Review and Edit Ticketing: https://main--latest.app.thorhudl/ticketing/{organizationID}/add-ticketing/{scheduleEntryID}/ticketedEventId={ticketedEventID}
*/

export class EventDetailsStepPage {
  readonly page: Page;
  readonly eventDetailsInstructions: Locator;
  readonly reviewEventInfoText: Locator;
  readonly scheduledEventDropdown: Locator;
  readonly selectedScheduleEvent: Locator;

  // Event Description
  readonly eventDescriptionLabel: Locator;
  readonly eventDescriptionInput: Locator;
  readonly eventDescriptionErrorLabel: Locator;
  readonly eventDescriptionErrorInput: Locator;
  readonly eventDescriptionErrorHelpText: Locator;

  // Date
  readonly dateInput: Locator;
  readonly dateInputError: Locator;
  readonly dateInputLabel: Locator;
  readonly dateInputErrorLabel: Locator;
  readonly dateInputErrorHelpText: Locator;

  // Time
  readonly startTimeInput: Locator;
  readonly timeZoneSelect: Locator;
  readonly timeZoneSelectOption: (option: string) => Locator;

  // Event Title
  readonly eventTitleInput: Locator;
  readonly eventTitleInputLabel: Locator;
  readonly eventTitleHelpText: Locator;
  readonly eventTitleInputError: Locator;
  readonly eventTitleInputErrorLabel: Locator;
  readonly eventTitleInputErrorHelpText: Locator;
  readonly reviewEventInformationText: Locator;

  // Event Visibility
  readonly eventVisibilityContainer: Locator;
  readonly selectedVisibilityOption: (option: string) => Locator;

  // Custom Field
  readonly formFieldSelectionContainer: Locator;
  readonly addCustomFieldButton: Locator;
  readonly disabledAddCustomFieldButton: Locator;
  readonly addCustomFieldModal: Locator;
  readonly addCustomFieldNameInputLabel: Locator;
  readonly addCustomFieldNameInput: Locator;
  readonly addCustomFieldNameInputHelpText: Locator;
  readonly addCustomFieldHelpTextInputLabel: Locator;
  readonly addCustomFieldHelpTextInput: Locator;
  readonly addCustomFieldHelpTextInputHelpText: Locator;

  readonly addCustomFieldNameInputErrorLabel: Locator;
  readonly addCustomFieldNameInputError: Locator;
  readonly addCustomFieldNameInputErrorHelpText: Locator;
  readonly addCustomFieldHelpTextInputErrorLabel: Locator;
  readonly addCustomFieldHelpTextInputError: Locator;
  readonly addCustomFieldHelpTextInputErrorHelpText: Locator;

  readonly formFieldSelectDropdown: Locator;
  readonly formFieldSelectOption: (option: string) => Locator;
  readonly formFieldSelectedItemsContainer: Locator;
  readonly selectedFormFieldItem: (option: string) => Locator;
  readonly formFieldItemLabel: (option: string) => Locator;
  readonly formFieldItemHelpText: Locator;
  readonly formFieldItemRequiredStatus: Locator;
  readonly formFieldItemDeleteButton: (option: string) => Locator;
  readonly formFieldMaxLimitReachedError: Locator;

  readonly addCustomFieldModalAddButton: Locator;
  readonly disabledAddCustomFieldModalAddButton: Locator;
  readonly addCustomFieldModalRadioOption: (optionName: string) => Locator;
  readonly addCustomFieldModalSelectedRadioOption: (optionName: string) => Locator;

  constructor(page: Page) {
    this.page = page;
    this.eventDetailsInstructions = page.getByTestId('event-details-instructions');
    this.reviewEventInfoText = page.getByTestId('review-event-information').filter({
      hasText:
        "Review the scheduled event information. To make changes, update the event from the team's schedule page before adding digital ticketing.",
    });
    this.scheduledEventDropdown = page.getByTestId('schedule-entry-select-select');
    this.selectedScheduleEvent = page.getByTestId('schedule-entry-select-single-value');

    // Event Description
    this.eventDescriptionLabel = page.getByTestId('event-description-input-label');
    this.eventDescriptionInput = page.getByTestId(`event-description-input`);
    this.eventDescriptionErrorLabel = page.getByTestId('event-description-input-error-label');
    this.eventDescriptionErrorInput = page.getByTestId('event-description-input-error');
    this.eventDescriptionErrorHelpText = page.getByTestId('event-description-input-error-help-text');

    // Date
    this.dateInput = page.getByTestId('date-input');
    this.dateInputError = page.getByTestId('date-input-error');
    this.dateInputLabel = page.getByTestId('date-input-label');
    this.dateInputErrorLabel = page.getByTestId('date-input-error-label');
    this.dateInputErrorHelpText = page.getByTestId('date-input-error-help-text');

    // Time
    this.startTimeInput = page.getByTestId('start-time-input');
    this.timeZoneSelect = page.getByTestId('time-zone-identifier-input-select');
    this.timeZoneSelectOption = (option: string) => page.getByTestId(`time-zone-identifier-input-${option}-option`);

    // Event Title
    this.eventTitleInput = page.getByTestId('event-title-input');
    this.eventTitleInputLabel = page.getByTestId('event-title-input-label');
    this.eventTitleHelpText = page.getByTestId('event-title-input-help-text');
    this.eventTitleInputError = page.getByTestId('event-title-input-error');
    this.eventTitleInputErrorLabel = page.getByTestId('event-title-input-error-label');
    this.eventTitleInputErrorHelpText = page.getByTestId('event-title-input-error-help-text');
    this.reviewEventInformationText = page.getByTestId('review-event-information');

    // Event Visibility
    this.eventVisibilityContainer = page.getByTestId('visibility-selection-container');
    this.selectedVisibilityOption = (option: string) => page.getByTestId(`visibility-option-${option}-selected`);

    // Custom Field
    this.formFieldSelectionContainer = page.getByTestId('form-field-selection-container');
    this.addCustomFieldButton = page.getByRole('button', { name: 'Add Custom Field' });
    this.disabledAddCustomFieldButton = page.getByRole('button', { name: 'Add Custom Field', disabled: true });
    this.addCustomFieldModal = page.getByTestId('add-form-field-modal');
    this.addCustomFieldNameInputLabel = page.getByTestId('add-form-field-modal-name-input-label');
    this.addCustomFieldNameInput = page.getByTestId('add-form-field-modal-name-input');
    this.addCustomFieldNameInputHelpText = page.getByTestId('add-form-field-modal-name-input-help-text');
    this.addCustomFieldHelpTextInputLabel = page.getByTestId('add-form-field-modal-help-text-input-label');
    this.addCustomFieldHelpTextInput = page.getByTestId('add-form-field-modal-help-text-input');
    this.addCustomFieldHelpTextInputHelpText = page.getByTestId('add-form-field-modal-help-text-input-help-text');

    this.addCustomFieldNameInputErrorLabel = page.getByTestId('add-form-field-modal-name-input-error-label');
    this.addCustomFieldNameInputError = page.getByTestId('add-form-field-modal-name-input-error');
    this.addCustomFieldNameInputErrorHelpText = page.getByTestId('add-form-field-modal-name-input-error-help-text');
    this.addCustomFieldHelpTextInputErrorLabel = page.getByTestId('add-form-field-modal-help-text-input-error-label');
    this.addCustomFieldHelpTextInputError = page.getByTestId('add-form-field-modal-help-text-input-error');
    this.addCustomFieldHelpTextInputErrorHelpText = page.getByTestId(
      'add-form-field-modal-help-text-input-error-help-text'
    );

    this.formFieldSelectDropdown = page.getByTestId('form-field-selection-select-select');
    this.formFieldSelectedItemsContainer = page.locator(`[class^='_selectedFormFieldsContainer_']`);
    this.formFieldSelectOption = (option: string) => page.getByRole('option', { name: `${option}` });
    this.selectedFormFieldItem = (option: string) =>
      page.locator(`[class^='_formFieldItemContainer_']`).filter({ hasText: `${option}` });
    this.formFieldItemLabel = (option: string) =>
      page.locator(`[class*='_formFieldItemLabel_']`).filter({ hasText: `${option}` });
    this.formFieldItemHelpText = page.locator(`[class*='_formFieldItemHelpText_']`);
    this.formFieldItemRequiredStatus = page.locator(`[class*='_formFieldItemRequiredText_']`);
    this.formFieldItemDeleteButton = (option: string) => this.selectedFormFieldItem(`${option}`).getByLabel('Delete');
    this.formFieldMaxLimitReachedError = page.getByText('Max limit of 10 custom fields used.');

    this.addCustomFieldModalAddButton = this.addCustomFieldModal
      .getByRole('button', { name: 'Add Custom Field', disabled: false })
      .filter();
    this.disabledAddCustomFieldModalAddButton = this.addCustomFieldModal.getByRole('button', {
      name: 'Add Custom Field',
      disabled: true,
    });
    this.addCustomFieldModalRadioOption = (optionName: string) =>
      page.getByTestId(`add-form-field-modal-required-${optionName}-label`);
    this.addCustomFieldModalSelectedRadioOption = (optionName: string) =>
      page.getByTestId(`add-form-field-modal-required-${optionName}-checked-label`);
  }

  /**
   * Creates a string of specified length by appending 'b' the specified number of times.
   * @param length Desired length of string.
   */
  async createStringOfLength(length: number): Promise<string> {
    let output = '';
    for (let i = 0; i < length; i++) {
      output = output + 'b';
    }
    return output;
  }

  async countNumSelectedFormFields(): Promise<number> {
    return this.formFieldSelectedItemsContainer.locator(`[class^='_formFieldItemContainer_']`).count();
  }

  async verifySelectedFormFieldCount(expected: number) {
    await expect(await this.countNumSelectedFormFields()).toBe(expected);
  }

  async createCustomFormFieldItem(): Promise<void> {
    const id = await createRandomNumericId(7);
    await this.addCustomFieldButton.click();
    await this.addCustomFieldNameInput.fill(id);
    await this.addCustomFieldModalAddButton.click();
    await expect(this.addCustomFieldModal).not.toBeVisible();
  }

  async createNumFormFieldItems(numIterations: number): Promise<void> {
    for (let i = 0; i < numIterations; i++) {
      await this.createCustomFormFieldItem();
    }
  }
}
