import { expect, type Locator, type Page } from '@playwright/test';

export class PassDetailsStepPage {
  readonly page: Page;
  readonly passNameInput: Locator;
  readonly passDateInput: (period: string) => Locator;
  readonly passDateInputHelpText: (period: string) => Locator;
  readonly passDescriptionTextbox: Locator;
  readonly disabledPriceInput: Locator;
  readonly passPriceInput: Locator;
  readonly passVisibilitySelection: Locator;
  readonly formFieldSelection: Locator;
  readonly addCustomFieldButton: Locator;
  readonly teamMultiSelectDropdown: Locator;
  readonly teamMultiSelectDropdownRemoveOption: (optionName: string) => Locator;
  readonly teamMultiSelectOption: (optionName: string) => Locator;

  constructor(page: Page) {
    this.page = page;
    this.passNameInput = page.getByTestId('pass-name-input');
    this.passDateInput = (period: string) => page.getByTestId(`pass-${period}-date-input`);
    this.passDateInputHelpText = (period: string) => page.getByTestId(`pass-${period}-date-input-help-text`);
    this.passDescriptionTextbox = page.getByTestId('description-input');
    this.disabledPriceInput = page.getByTestId('price-input-disabled');
    this.passPriceInput = page.getByTestId('price-input');
    this.passVisibilitySelection = page.getByTestId('visibility-selection-container');
    this.formFieldSelection = page.getByTestId('form-field-selection-container');
    this.addCustomFieldButton = page.getByTestId('form-field-selection-add-field-button');
    this.teamMultiSelectDropdown = page.getByTestId('pass-team-select-multi-select');
    this.teamMultiSelectDropdownRemoveOption = (optionName: string) => page.getByLabel(`Remove ${optionName}`);
    this.teamMultiSelectOption = (optionName: string) => page.getByRole('option', { name: optionName });
  }

  /**
   * Fills a pass with a test title and test start/end dates.
   */
  async fillPassTitleAndDateDetails(): Promise<void> {
    await this.passNameInput.fill('UI Automation - Pass Config Automation');
    await this.passDateInput('start').fill('2028-01-01');
    await this.passDateInput('end').fill('2028-01-02');
  }
}
