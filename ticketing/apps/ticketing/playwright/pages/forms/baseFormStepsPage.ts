import { expect, type Locator, type Page } from '@playwright/test';

/*
  All base methods and locators for the form steps seen on the ticketed event form (Select a Type, Add Event Details,
  Add Tickets, and Review & Publish) and the pass config form (Select a Type, Add Pass Details, Add Team, Review & Publish)
    - Add Ticketing: https://main--latest.app.thorhudl/ticketing/{organizationID}/add-ticketing/{scheduleEntryId}
    - Review and Edit Ticketing: https://main--latest.app.thorhudl/ticketing/{organizationID}/add-ticketing/{scheduleEntryID}/ticketedEventId={ticketedEventID}
    - Create Pass: https://main--latest.app.thorhudl/ticketing/{organizationID}/create-pass
    - Review and Edit Pass: https://main--latest.app.thorhudl/ticketing/{organizationID}/create-pass?passConfigId={passConfigID}
*/

export enum FormSteps {
  SelectEvent = 'select-event',
  SelectAType = 'select-a-type',
  AddPassDetails = 'add-pass-details',
  AddEventDetails = 'add-event-details',
  AddTeams = 'add-teams',
  AddTeamRS = 'add-team',
  AddSeating = 'add-seating',
  AddTickets = 'add-tickets',
  ReviewAndPublish = 'review-&-publish',
}

export class BaseFormStepsPage {
  readonly page: Page;

  readonly stepIndicator: (stepName: FormSteps) => Locator;
  readonly selectedStepIndicator: (stepName: FormSteps) => Locator;
  readonly checkedStepIndicator: (stepName: FormSteps) => Locator;
  readonly checkedSelectedStepIndicator: (stepName: FormSteps) => Locator;
  readonly formStepHeadline: (stepHeadlineName: string) => Locator;

  readonly cancelButton: Locator;
  readonly continueButton: Locator;
  readonly backButton: (pageName: string) => Locator;
  readonly reviewButton: (entity: string) => Locator;
  readonly reviewButtonDisabled: (entity: string) => Locator;
  readonly saveAsDraftButton: (eventName: string) => Locator;
  readonly disabledSaveAsDraftButton: (eventName: string) => Locator;
  readonly disabledPublishButton: (entity: string) => Locator;
  readonly disabledContinueButton: Locator;
  readonly disabledReviewButton: (entity: string) => Locator;
  readonly onboardingBlockPublishTooltip: Locator;
  readonly publishButton: (entity: string) => Locator;
  readonly priceInput: Locator;
  readonly priceInputHelpText: Locator;
  readonly priceInputError: Locator;
  readonly readOnlyPriceInput: Locator;
  readonly disabledPriceInput: Locator;
  readonly quantityForSaleInputLabel: Locator;
  readonly quantityForSaleInput: Locator;
  readonly disabledQuantityForSale: Locator;
  readonly quantityForSaleInputErrorLabel: Locator;
  readonly quantityForSaleInputError: Locator;
  readonly quantityForSaleInputStateHelpText: (inputState: string) => Locator;
  readonly quantityForSaleInputHelpText: Locator;

  readonly bundledFeesContainer: Locator;
  readonly selectedBundledFeesOption: (option: string) => Locator;
  readonly bundledFeesOption: (option: string) => Locator;
  readonly bundledFeesToolTipIcon: (option: string) => Locator;
  readonly bundledFeesYourFansToolTipText: Locator;
  readonly bundledFeesYourOrgToolTipText: Locator;
  readonly bundledFeesTutorialLink: Locator;

  readonly saveChangesModal: Locator;
  readonly discardChangesButton: Locator;
  readonly saveChangesButton: Locator;
  readonly typeSelectionRadio: (type: string) => Locator;

  constructor(page: Page) {
    this.page = page;

    this.stepIndicator = (stepName: FormSteps) => page.getByTestId(`form-step-indicator-${stepName}`);
    this.selectedStepIndicator = (stepName: FormSteps) => page.getByTestId(`form-step-indicator-${stepName}-selected`);
    this.checkedStepIndicator = (stepName: FormSteps) => page.getByTestId(`form-step-indicator-${stepName}-checked`);
    this.checkedSelectedStepIndicator = (stepName: FormSteps) =>
      page.getByTestId(`form-step-indicator-${stepName}-checked-selected`);
    this.formStepHeadline = (stepHeadlineName: string) => page.getByTestId(`form-step-headline-${stepHeadlineName}`);
    this.reviewButton = (entity: string) => page.getByTestId(`review-${entity}-button`);
    this.reviewButtonDisabled = (entity: string) => page.getByTestId(`review-${entity}-button-disabled`);
    this.saveAsDraftButton = (pageName: string) => page.getByTestId(`save-as-draft-${pageName}-button`);
    this.disabledSaveAsDraftButton = (pageName: string) =>
      page.getByTestId(`save-as-draft-${pageName}-button-disabled`);

    this.cancelButton = page.getByTestId('cancel-button');
    this.continueButton = page.getByTestId('continue-button');
    this.backButton = (pageName: string) => page.getByTestId(`${pageName}-back-button`);
    this.disabledPublishButton = (entity: string) => page.getByTestId(`publish-${entity}-button-disabled`);
    this.disabledContinueButton = page.getByTestId('continue-button-disabled');
    this.disabledReviewButton = (entity: string) => page.getByTestId(`review-${entity}-button-disabled`);
    this.onboardingBlockPublishTooltip = page.getByTestId('publish-disabled-by-payment-status-trigger');
    this.publishButton = (entity: string) => page.getByTestId(`publish-${entity}-button`);
    this.priceInput = page.getByTestId('price-input');
    this.priceInputHelpText = page.getByTestId('price-input-read-only-help-text');
    this.priceInputError = page.getByTestId('price-input-error');
    this.readOnlyPriceInput = page.getByTestId('price-input-read-only');
    this.disabledPriceInput = page.getByTestId('price-input-disabled');
    this.quantityForSaleInputLabel = page.getByTestId('quantity-for-sale-input-label');
    this.quantityForSaleInput = page.getByTestId('quantity-for-sale-input');
    this.disabledQuantityForSale = page.getByTestId('quantity-for-sale-input-disabled');
    this.quantityForSaleInputErrorLabel = page.getByTestId('quantity-for-sale-input-error-label');
    this.quantityForSaleInputError = page.getByTestId('quantity-for-sale-input-error');
    this.quantityForSaleInputStateHelpText = (inputState: string) =>
      page.getByTestId(`quantity-for-sale-input-${inputState}-help-text`);
    this.quantityForSaleInputHelpText = page.getByTestId('quantity-for-sale-input-help-text');

    this.bundledFeesContainer = page.getByTestId('bundled-fees-container');
    this.selectedBundledFeesOption = (option: string) => page.getByTestId(`bundled-fees-radio-${option}-checked-label`);
    this.bundledFeesOption = (option: string) => page.getByTestId(`bundled-fees-radio-${option}-label`);
    this.bundledFeesToolTipIcon = (option: string) => page.getByTestId(`tooltip-icon-${option}`);
    this.bundledFeesYourOrgToolTipText = page
      .getByRole('tooltip', { name: "You don't pay any extra" })
      .getByTestId('bundled-fees-tooltip-your-organization-tooltip');
    this.bundledFeesYourFansToolTipText = page
      .getByRole('tooltip', { name: 'Fees will automatically be ' })
      .getByTestId('bundled-fees-tooltip-your-fans-tooltip');
    this.bundledFeesTutorialLink = page.getByRole('link', { name: 'fees for this event' });

    this.saveChangesModal = page.getByTestId('save-changes-modal');
    this.discardChangesButton = page.getByRole('button', { name: 'Discard Changes' });
    this.saveChangesButton = page.getByRole('button', { name: 'Yes, Save' });
    this.typeSelectionRadio = (type: string) => page.getByTestId(`selection-type-${type}`);
  }

  /**
   * Verifies the state of the given type selection radio for the type of pass being sold.
   *
   * @param type 'GeneralAdmission' or 'ReservedSeating' for what type of pass is being sold.
   * @param supposedState 'selected', 'enabled', or 'disabled' for what state the type is supposed to be in.
   */
  async verifyTypeRadioState(
    type: 'GeneralAdmission' | 'ReservedSeating',
    supposedState: 'selected' | 'enabled' | 'disabled'
  ): Promise<void> {
    await expect(this.typeSelectionRadio(type)).toBeVisible();
    if (supposedState === 'selected') {
      await expect(this.typeSelectionRadio(type)).toHaveClass(/.*_radioButtonContainerSelected.*/);
    } else if (supposedState === 'disabled') {
      await expect(this.typeSelectionRadio(type)).toHaveClass(/.*_radioButtonContainerDisabled.*/);
    }
  }

  /**
   * Verifies the state of all relevant form steps based on the current active step
   * and the selected admission type.
   *
   * @param currentActiveStep The FormSteps enum value for the step currently being worked on.
   * @param currentActiveStepState 'selected' or 'completed' for the state of the current active step.
   * @param selectedAdmissionType 'GeneralAdmission' or 'ReservedSeating' to handle conditional steps.
   */
  async verifyAllFormStepsState(
    currentActiveStep: FormSteps,
    currentActiveStepState: 'selected' | 'completed',
    selectedAdmissionType: 'GeneralAdmission' | 'ReservedSeating'
  ): Promise<void> {
    // 1. Define the expected sequence of steps based on the admission type
    const expectedStepsSequence: FormSteps[] = [FormSteps.SelectAType, FormSteps.AddPassDetails];
    if (selectedAdmissionType === 'GeneralAdmission') {
      expectedStepsSequence.push(FormSteps.AddTeams);
    } else {
      expectedStepsSequence.push(FormSteps.AddSeating);
      expectedStepsSequence.push(FormSteps.AddTeamRS);
    }
    expectedStepsSequence.push(FormSteps.ReviewAndPublish);

    // 2. Iterate through the sequence and assert the state of each step
    let foundCurrent = false;

    for (const step of expectedStepsSequence) {
      // Determine the expected state based on its position relative to `currentActiveStep`
      if (step === currentActiveStep && currentActiveStepState === 'selected') {
        // This is the current active step
        await expect(this.selectedStepIndicator(step)).toBeVisible();
        foundCurrent = true; // All subsequent steps will be 'incomplete'
      } else if (step === currentActiveStep && currentActiveStepState === 'completed') {
        await expect(this.checkedSelectedStepIndicator(step)).toBeVisible();
        foundCurrent = true;
      } else if (!foundCurrent) {
        // These are steps that come before the current active step (completed)
        await expect(this.checkedStepIndicator(step)).toBeVisible();
      } else {
        // These are steps that come after the current active step (incomplete/future)
        await expect(this.stepIndicator(step)).toBeVisible();
      }
    }
  }
}
