import { type Locator, type Page } from '@playwright/test';

/*
  All methods and locators for components that exists on any page throughout the backoffice for ticketing
    - Ticketing Management Home page: https://main--latest.app.thorhudl/ticketing/{organizationID}
    - Ticketed Event Details page: https://main--latest.app.thorhudl/ticketing/{organizationID}/event/{graphQLTicketedEventId}
    - Pass Config Details page: https://main--latest.app.thorhudl/ticketing/{organizationID}/pass/{graphQLPassConfigId}
*/

export class SharedComponentsPage {
  readonly page: Page;

  // Entity Badges
  readonly entityBadge: (badgeName: string, passConfigId: string) => Locator;

  // Toasts
  readonly successCopyingShareLinkToast: Locator;
  readonly errorLoadingTicketedEventToast: Locator;

  constructor(page: Page) {
    this.page = page;

    // Entity Badges
    this.entityBadge = (badgeName: string, itemId: string) => page.getByTestId(`entity-badge-${badgeName}-${itemId}`);

    // Toasts
    this.successCopyingShareLinkToast = page.getByTestId('success-copying-share-link-toast');
    this.errorLoadingTicketedEventToast = page.getByTestId('error-loading-ticketed-event-toast');
  }
}
