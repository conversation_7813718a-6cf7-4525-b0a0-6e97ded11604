import { PropsWithChildren, useCallback, useState } from 'react';

import { useSelector } from 'react-redux';
import { PopoverContainer } from 'shared/components/Popover/PopoverContainer';

import { PopoverContent, PopoverTrigger } from '@hudl/performance-core-shared';

import { isMomentEditingEnabledSelector } from '../../../../store/analyze/app/listModule/listModuleSelectors';
import { MomentDetailsPopoverContent } from './MomentDetailsPopoverContent';
import { isListModuleOpenSelector } from 'store/analyze/ui/modules/moduleSelectors';

import styles from './styles.module.scss';

interface MomentDetailsPopoverProps extends PropsWithChildren {
  momentId: string;
}

export function MomentDetailsPopover({ children, momentId }: MomentDetailsPopoverProps) {
  const [isOpen, setIsOpen] = useState(false);

  const isMomentEditingEnabled = useSelector(isMomentEditingEnabledSelector);
  const isMomentEditing = useSelector(isListModuleOpenSelector);

  const canOpenPopover = isMomentEditingEnabled && !isMomentEditing;

  // only open the popover if moment editing is enabled
  const openPopover = useCallback(() => setIsOpen(true), []);
  const closePopover = useCallback(() => setIsOpen(false), []);

  const handleOpenChange = useCallback(
    (newIsOpen: boolean) => {
      if (newIsOpen) {
        openPopover();
      } else {
        closePopover();
      }
    },
    [openPopover, closePopover]
  );

  return (
    <PopoverContainer level={1} initialFocus={-1} isOpen={isOpen} onIsOpenChange={handleOpenChange}>
      {canOpenPopover ? (
        <PopoverTrigger asChild onPress={openPopover}>
          {children}
        </PopoverTrigger>
      ) : (
        children
      )}
      <PopoverContent className={styles.popoverContent}>
        <MomentDetailsPopoverContent momentId={momentId} onExpand={closePopover} />
      </PopoverContent>
    </PopoverContainer>
  );
}
