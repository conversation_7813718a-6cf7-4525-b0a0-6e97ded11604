import { useSelector } from 'react-redux';

import { isPlaylistSelector } from '../../../../store/analyze/app/selectedVideo/selectedPlaylistSelector';
import {
  isInsightsModuleConfigLoadingSelector,
  isInsightsModuleSetupSelector,
} from '../../../../store/analyze/domain/configurations/configurationSelectors';
import { isAIGeneratedSessionsSelector } from '../../../../store/analyze/domain/taggingSessions/taggingSessionSelectors';
import InsightsModule from './InsightsModule';
import {
  isEmptyVideoStateSelector,
  isNoClipsStateSelector,
  isNoFilteredClipsStateSelector,
} from 'store/analyze/app/appContext/errorStateSelectors';
import { activeClipsSelector } from 'store/analyze/app/clips/clipsSelectors';
import { clipFiltersSelector } from 'store/analyze/app/filters/filtersSelectors';
import { clearAllFilters } from 'store/analyze/app/filters/filtersThunks';
import { expandedCardKeySelector } from 'store/analyze/app/insightsModule/insightsModuleSelectors';
import { isLoadingOrErrorSelector } from 'store/analyze/app/stats/statsSelectors';
import { useHasAnyStats } from 'store/analyze/app/stats/useHasAnyStats';
import { insightsModuleDisplaySelector, insightsModuleModeSelector } from 'store/analyze/ui/modules/moduleSelectors';
import { useAppDispatch } from 'store/hooks';

interface Props {
  isLoading?: boolean;
}

function InsightsModuleContainer({ isLoading }: Props) {
  const dispatch = useAppDispatch();
  const isInsightsModuleConfigLoading = useSelector(isInsightsModuleConfigLoadingSelector);
  const activeClips = useSelector(activeClipsSelector);
  const isEmptyState = useSelector(isEmptyVideoStateSelector);
  const isNoClipsState = useSelector(isNoClipsStateSelector);
  const isNoFilteredClipsState = useSelector(isNoFilteredClipsStateSelector);
  const isLoadingOrError = useSelector(isLoadingOrErrorSelector);
  const isPlaylist = useSelector(isPlaylistSelector);
  const hasAnyStats = useHasAnyStats();
  const isAITaggingSession = useSelector(isAIGeneratedSessionsSelector);
  const isNoDataState = (!hasAnyStats && !isLoadingOrError) || isPlaylist || isAITaggingSession;
  const expandedCard = useSelector(expandedCardKeySelector);
  const display = useSelector(insightsModuleDisplaySelector);
  const mode = useSelector(insightsModuleModeSelector);
  const isInsightsModuleSetup = useSelector(isInsightsModuleSetupSelector);
  const isNoCardGroupDefinitionProvided = !isInsightsModuleSetup && !isLoading && !isInsightsModuleConfigLoading;
  const shouldShowPlaceholder =
    isEmptyState || isNoClipsState || isNoDataState || isLoading || isInsightsModuleConfigLoading || isAITaggingSession;
  const hasExpandedCard = !!expandedCard;
  const clipFilters = useSelector(clipFiltersSelector);

  // Verify whether filters have been applied and if the clip(s) have been modified in a way that renders the filters ineffective.
  const editedClipsDoNotMatchFilters = activeClips.length > 0 && clipFilters.length > 0 && !hasAnyStats;
  // Reset the insights module filters when multiple filters are present but are inapplicable
  if (!shouldShowPlaceholder && !hasAnyStats && clipFilters.length > 0 && !editedClipsDoNotMatchFilters) {
    dispatch(clearAllFilters());
  }

  return (
    <InsightsModule
      isLoading={Boolean(isLoading)}
      isInsightsModuleConfigLoading={isInsightsModuleConfigLoading}
      shouldShowPlaceholder={shouldShowPlaceholder}
      hasExpandedCard={hasExpandedCard}
      isNoClipsState={isNoClipsState}
      isNoClipsMatchFilters={isNoFilteredClipsState || editedClipsDoNotMatchFilters}
      isNoDataState={isNoDataState}
      isNoCardGroupDefinitionProvided={isNoCardGroupDefinitionProvided}
      display={display}
      mode={mode}
    />
  );
}

export default InsightsModuleContainer;
