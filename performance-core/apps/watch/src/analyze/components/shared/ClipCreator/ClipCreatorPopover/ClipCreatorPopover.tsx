import { memo, useCallback, useState } from 'react';

import { useSelector } from 'react-redux';
import { PopoverContainer } from 'shared/components/Popover/PopoverContainer';

import { ModuleName } from '@hudl/performance-core-domain';
import { PopoverContent, PopoverOptions, PopoverTrigger } from '@hudl/performance-core-shared';
import { ButtonProps, IconAdd } from '@hudl/uniform-web';

import { isAIGeneratedSessionsSelector } from '../../../../../store/analyze/domain/taggingSessions/taggingSessionSelectors';
import ClipCreatorPopoverContent from './ClipCreatorPopoverContent';
import { resetEditClipMode } from 'store/analyze/app/editClips/editClipSlice';
import { editClipModeIsOpenSelector } from 'store/analyze/app/editClips/editClipsSelectors';
import { useAppDispatch } from 'store/hooks';

import styles from './styles.module.scss';

interface TriggerOptions extends Pick<ButtonProps, 'buttonStyle' | 'buttonType' | 'size' | 'icon' | 'isDisabled'> {
  label?: string;
  forceOpen?: boolean;
  hideTrigger?: boolean;
  className?: string;
}

interface ClipCreatorPopoverProps {
  /** Expected to be virtual clip ids or universal clip ids - if undefined, then we take the currently playing clip */
  clipIds?: Array<string>;
  placement?: PopoverOptions['placement'];
  triggerOptions?: TriggerOptions;
  sourceModuleName?: ModuleName;
}

function ClipCreatorPopover({ clipIds, placement, triggerOptions, sourceModuleName }: ClipCreatorPopoverProps) {
  const dispatch = useAppDispatch();
  const isEditClipModeOpen = useSelector(editClipModeIsOpenSelector);
  const isAITaggingSession = useSelector(isAIGeneratedSessionsSelector);

  const [isOpen, setIsOpen] = useState(false);

  const openPopover = useCallback(() => setIsOpen(true), []);
  const closePopover = useCallback(() => setIsOpen(false), []);

  const resetPopoverData = useCallback(() => {
    setIsOpen(false);

    setTimeout(() => {
      dispatch(resetEditClipMode());
    }, 200);
  }, [dispatch]);

  const handleOpenChange = useCallback(
    (newIsOpen: boolean) => {
      if (newIsOpen) {
        openPopover();
      } else {
        resetPopoverData();
      }
    },
    [openPopover, resetPopoverData]
  );

  return (
    <PopoverContainer
      maxPopoverHeight={600}
      isOpen={triggerOptions?.forceOpen ? true : isOpen}
      onIsOpenChange={handleOpenChange}
      placement={placement ?? 'bottom-end'}
      initialFocus={-1}
      qaIdPrefix="clip-creator"
    >
      {!triggerOptions?.hideTrigger && (
        <PopoverTrigger
          buttonStyle="minimal"
          buttonType="subtle"
          size="xsmall"
          icon={<IconAdd />}
          {...triggerOptions}
          isDisabled={isEditClipModeOpen || triggerOptions?.isDisabled || isAITaggingSession}
          onClick={openPopover}
        >
          {triggerOptions?.label}
        </PopoverTrigger>
      )}
      {triggerOptions?.hideTrigger && (
        <PopoverTrigger asChild>
          <div></div>
        </PopoverTrigger>
      )}

      <PopoverContent className={styles.popoverContent}>
        <ClipCreatorPopoverContent
          clipIds={clipIds}
          closePopover={closePopover}
          resetPopoverData={resetPopoverData}
          sourceModuleName={sourceModuleName}
        />
      </PopoverContent>
    </PopoverContainer>
  );
}

export default memo(ClipCreatorPopover);
