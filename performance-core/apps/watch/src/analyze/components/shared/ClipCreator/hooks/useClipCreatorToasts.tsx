import { useCallback } from 'react';

import { FormattedMessage } from 'react-intl';
import { SnowplowContentActionSubmissionLocation } from 'snowplow/SnowplowTypes';

import { LibraryType } from '@hudl/performance-core-domain';
import { format } from 'frontends-i18n';

import { playLibraryItem } from 'store/analyze/domain/libraryItems/libraryItemPlaybackThunks';
import { showToast } from 'store/analyze/ui/toast/toastSlice';
import { useAppDispatch } from 'store/hooks';

export const useClipCreatorToasts = () => {
  const dispatch = useAppDispatch();

  const displayPlaylistToast = useCallback(
    (
      result: string,
      clipCount: number,
      title: string,
      numberAdditionalSuccessfulRequests: number,
      navigationItemId?: string,
      retryAction?: () => void
    ) => {
      const abbreviatedTitle = title.length > 85 ? `${title.slice(0, 80)}...` : title;

      if (result === 'success') {
        dispatch(
          showToast({
            message: (
              <FormattedMessage
                id={
                  numberAdditionalSuccessfulRequests === 0
                    ? 'actions.clips.add_clips_to_playlists_and_highlights.success_toast_message'
                    : 'actions.clips.add_clips_to_playlists_and_highlights.multiple_success_toast_message'
                }
                values={{
                  clipCount,
                  title: abbreviatedTitle,
                  bold: (chunks) => <span style={{ fontWeight: 'bold' }}>{chunks}</span>,
                  numberAdditionalSuccessfulRequests,
                }}
              />
            ),
            actionText: navigationItemId ? format('performance-core.clip-creator.open-playlist') : undefined,
            onActionClick: navigationItemId
              ? () => {
                  dispatch(
                    playLibraryItem(
                      { id: navigationItemId, title, dateCreated: new Date(), libraryType: LibraryType.Playlist },
                      {
                        originDestinationContext: SnowplowContentActionSubmissionLocation.ClipCreator,
                      }
                    )
                  );
                }
              : undefined,
            type: 'success',
            identifier:
              numberAdditionalSuccessfulRequests === 0
                ? 'add-clips-to-playlists-and-highlights-succeeded'
                : 'add-clips-to-playlists-and-highlights-multiple-succeeded',
          })
        );
      } else if (result === 'error') {
        dispatch(
          showToast({
            message: format('actions.clips.add_clips_to_playlists_and_highlights.error_toast_message'),
            type: 'error',
            identifier: 'add-clips-to-playlists-and-highlights-failure',
            actionText: format('performance-core.shared.retry'),
            onActionClick: () => {
              retryAction && retryAction();
              return;
            },
          })
        );
      }
    },
    [dispatch]
  );

  return { displayPlaylistToast };
};
