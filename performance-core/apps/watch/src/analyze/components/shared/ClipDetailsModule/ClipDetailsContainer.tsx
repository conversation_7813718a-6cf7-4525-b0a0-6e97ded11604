import { ChangeEvent, KeyboardEvent, useCallback, useEffect, useState } from 'react';

import _ from 'lodash';
import { useSelector } from 'react-redux';
import { trackObjectOpenClose } from 'snowplow/SnowplowService';
import { ObjectOpenCloseContext } from 'snowplow/SnowplowTypes';

import { Sport } from '@hudl/hudl-domain-types';
import { LibraryContent } from '@hudl/performance-core-domain';
import { Button, Input, Note, Text, Textarea } from '@hudl/uniform-web';
import { utils } from '@hudl/video-interface';
import { format } from 'frontends-i18n';

import { isAIGeneratedSessionsSelector } from '../../../../store/analyze/domain/taggingSessions/taggingSessionSelectors';
import { useClipCreatorToasts } from '../ClipCreator/hooks/useClipCreatorToasts';
import { useGetPlaylistsQuery } from '../ClipCreator/hooks/useGetPlaylistsQuery';
import { useSaveModifiedClip } from '../ClipCreator/hooks/useSaveModifiedClip';
import MultiSelectClipCreatorContainer from '../ClipCreator/MultiSelectClipCreatorContainer';
import HighlightsConfirmationAlert from '../Highlights/HighlightsConfirmationAlert';
import { setClipComment, setClipTitle } from 'store/analyze/app/editClips/editClipSlice';
import {
  editClipModeClipCommentSelector,
  editClipModeClipInContextSelector,
  editClipModeClipTitleSelector,
  selectedHighlightsSelector,
  selectedPlaylistsSelector,
} from 'store/analyze/app/editClips/editClipsSelectors';
import { closeEditClipMode } from 'store/analyze/app/editClips/editClipThunks';
import { canAddToHighlightsSelector } from 'store/analyze/app/highlights/highlightsSelectors';
import { currentlyPlayingClipSelector } from 'store/analyze/app/playback/playbackSelectors';
import {
  timelineSelectionEndTimeMsSelector,
  timelineSelectionStartTimeMsSelector,
} from 'store/analyze/app/timeline/timelineSelectors';
import { sportSelector } from 'store/analyze/domain/teams/teamSelectors';
import { useAppDispatch } from 'store/hooks';
import { RootState } from 'store/rootReducer';

import styles from './styles.module.scss';

export enum ClipModifiableProperties {
  Name = 'name',
  Comment = 'comment',
  Duration = 'duration',
}

export function ClipDetailsContainer() {
  const dispatch = useAppDispatch();

  const title = useSelector(editClipModeClipTitleSelector);
  const comment = useSelector(editClipModeClipCommentSelector);
  const currentlyPlayingClip = useSelector(currentlyPlayingClipSelector) ?? null;
  const selectedPlaylists = useSelector(selectedPlaylistsSelector);
  const selectedHighlights = useSelector(selectedHighlightsSelector);
  const startTime = useSelector(timelineSelectionStartTimeMsSelector);
  const endTime = useSelector(timelineSelectionEndTimeMsSelector);
  const isAITaggingSession = useSelector(isAIGeneratedSessionsSelector);
  const { playlists } = useGetPlaylistsQuery({ searchQuery: '' });
  const { displayPlaylistToast } = useClipCreatorToasts();

  const [isValidatingHighlights, setIsValidatingHighlights] = useState(false);
  const [initialStartTime] = useState(startTime);
  const [initialEndTime] = useState(endTime);
  const [modifiedProperties, setModifiedProperties] = useState<ClipModifiableProperties[]>([]);
  const [pendingToastData, setPendingToastData] = useState<{
    result: string;
    navigationItemId?: string;
    modifiedProperties?: ClipModifiableProperties[];
    clipCount?: number;
    title?: string;
    numberAdditionalSuccessfulRequests?: number;
  } | null>(null);

  const sport = useSelector(sportSelector);
  const clipIdInContext = useSelector(editClipModeClipInContextSelector);

  const clipToSave = clipIdInContext ? [clipIdInContext] : [];

  const canAddToHighlights = useSelector((state: RootState) => canAddToHighlightsSelector(state, clipToSave));
  const { onSaveModifiedClip } = useSaveModifiedClip();
  const [result, setResult] = useState<'spinning' | undefined>(undefined);

  const defaultClipTitle = startTime !== undefined ? `Clip at ${utils.msToReadableString(startTime)}` : '';
  // Supports comments if the sport is not Baseball or Wrestling
  const supportsComments = sport !== Sport.Baseball && sport !== Sport.Wrestling && sport !== Sport.Softball;
  const handleModifiedProperties = (propertyWasChanged: boolean, property: ClipModifiableProperties) => {
    if (propertyWasChanged) {
      !modifiedProperties.includes(property) && setModifiedProperties([...modifiedProperties, property]);
    } else {
      setModifiedProperties(_.without(modifiedProperties, property));
    }
  };

  useEffect(() => {
    dispatch(setClipTitle(defaultClipTitle));
  }, []);

  useEffect(() => {
    const durationWasChanged = startTime !== initialStartTime || endTime !== initialEndTime;
    handleModifiedProperties(durationWasChanged, ClipModifiableProperties.Duration);
  }, [startTime, endTime, initialStartTime, initialEndTime]);

  const handleSave = useCallback(
    (options?: { shouldTrimLongClips: boolean }) => {
      async function saveClip() {
        setResult('spinning');
        const response = await onSaveModifiedClip(clipToSave[0], modifiedProperties, options);

        if (response.result === 'validating') {
          setIsValidatingHighlights(true);
          return;
        }

        setPendingToastData(response);
        setResult(undefined);
      }
      void saveClip();
    },
    [clipToSave, modifiedProperties, onSaveModifiedClip]
  );

  useEffect(() => {
    if (pendingToastData) {
      const playlist: LibraryContent | undefined = playlists.find((p) => p.id === pendingToastData.navigationItemId);

      if ((pendingToastData.navigationItemId && playlist) || !pendingToastData.navigationItemId) {
        displayPlaylistToast(
          pendingToastData.result,
          pendingToastData.clipCount ?? 0,
          pendingToastData.title ?? '',
          pendingToastData.numberAdditionalSuccessfulRequests ?? 0,
          pendingToastData.navigationItemId,
          () => {
            handleSave();
          }
        );
        if (pendingToastData.result !== 'error') {
          dispatch(closeEditClipMode());
        }
        setPendingToastData(null);
      }
    }
  }, [pendingToastData, playlists, displayPlaylistToast, dispatch, handleSave]);

  const onCancelClick = useCallback((): void => {
    // @ts-expect-error 'Edit Clip Details Panel' is not part of 'object-open-close' type
    dispatch(trackObjectOpenClose('Edit Clip Details Panel', 'Close', [], ObjectOpenCloseContext.Video));
    dispatch(closeEditClipMode());
  }, [dispatch]);

  const onSave = useCallback(() => handleSave(), [handleSave]);

  if (currentlyPlayingClip === undefined) {
    return null;
  }

  const handleOnBlur = () => {
    if (title === '') {
      dispatch(setClipTitle(defaultClipTitle));
    }
  };

  const onKeyDown = (e: KeyboardEvent<HTMLElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      e.currentTarget.blur();
    }
  };

  const onTitleChanged = (value: string) => {
    const currentTitle = value;
    const titleWasChanged = Boolean(currentTitle && currentTitle !== defaultClipTitle);
    handleModifiedProperties(titleWasChanged, ClipModifiableProperties.Name);
    dispatch(setClipTitle(value));
  };

  const onCommentChanged = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const commentWasChanged = Boolean(e.target.value);
    handleModifiedProperties(commentWasChanged, ClipModifiableProperties.Comment);
    dispatch(setClipComment(e.target.value));
  };

  const handleCancel = () => {
    setIsValidatingHighlights(false);
    setResult(undefined);
  };

  const handleConfirm = () => {
    setIsValidatingHighlights(false);
    handleSave({ shouldTrimLongClips: true });
  };

  return (
    <>
      <div className={styles.clipDetailsModule}>
        <div className={styles.form}>
          <Note size="xsmall" type="information" className={styles.note}>
            Edits will only appear in playlists.
          </Note>
          <div className={styles.formItem}>
            <Text className={styles.formItemLabel}>Clip Title</Text>
            <Input
              formSize="xsmall"
              onChange={onTitleChanged}
              value={title || ''}
              aria-label="Clip Title"
              onBlur={handleOnBlur}
              onKeyDown={onKeyDown}
            />
          </div>
          {supportsComments ? (
            <div className={styles.formItem}>
              <Text className={styles.formItemLabel}>Comment</Text>
              <Textarea
                minHeight={100}
                placeholder="Add a comment..."
                value={comment || ''}
                onChange={onCommentChanged}
                formSize="xsmall"
                autoHeight
                aria-label="Comment"
                onKeyDown={onKeyDown}
              />
            </div>
          ) : null}
          <div className={`${styles.formItem} ${styles.selectPlaylist}`}>
            <MultiSelectClipCreatorContainer
              options={{
                canAddToHighlights,
                canPlayPlaylist: false,
              }}
            />
          </div>
          <div className={styles.buttonsFooter}>
            <Button
              className={styles.button}
              buttonType="cancel"
              onPress={onCancelClick}
              qaId={'clip-details-cancel-button'}
              size="medium"
            >
              {format('performance-core.shared.cancel')}
            </Button>
            <Button
              className={styles.button}
              buttonType={'primary'}
              onPress={onSave}
              qaId={'clip-details-save-button'}
              isDisabled={(selectedHighlights.length === 0 && selectedPlaylists.length === 0) || isAITaggingSession}
              size="medium"
              status={result}
            >
              {format('performance-core.shared.save')}
            </Button>
          </div>
        </div>
      </div>

      <HighlightsConfirmationAlert isOpen={isValidatingHighlights} onConfirm={handleConfirm} onCancel={handleCancel} />
    </>
  );
}
