import { useSelector } from 'react-redux';

import { Sport, SportClass, VirtualClip } from '@hudl/hudl-domain-types';
import { ModuleName } from '@hudl/performance-core-domain';
import { Subhead, Text } from '@hudl/uniform-web';
import { Row, TimeSubscriber } from '@hudl/video-interface';
import { format } from 'frontends-i18n';

import { isAIGeneratedSessionsSelector } from '../../../../store/analyze/domain/taggingSessions/taggingSessionSelectors';
import ClipCreatorPopover from '../ClipCreator/ClipCreatorPopover/ClipCreatorPopover';
import { ClipContextDiamondSportsMomentsContainer } from './ClipContextDiamondSportsMomentsContainer/ClipContextDiamondSportsMomentsContainer';
import { ClipContextGameState } from './ClipContextGameState/ClipContextGameState';
import { ClipContextMomentsContainer } from './ClipContextMomentsContainer/ClipContextMomentsContainer';
import { shouldShowClipTitle } from './helpers/clipContextTitleHelper';
import {
  activeClipsLengthSelector,
  latestActiveClipIndexSelector,
  latestActiveClipSelector,
} from 'store/analyze/app/clips/clipsSelectors';
import { sportSelector } from 'store/analyze/domain/teams/teamSelectors';
import { hasSelectedClipsSelector } from 'store/analyze/ui/grid/gridSelectors';
import { activePlayableSelector } from 'store/shared/selectors/playbackSelectors';

import styles from './styles.module.scss';

export function ClipContextBar() {
  const activeClip = useSelector(latestActiveClipSelector);
  const activeClipIndex = useSelector(latestActiveClipIndexSelector);
  const clipTotal = useSelector(activeClipsLengthSelector);
  const playable = useSelector(activePlayableSelector);
  const sport = useSelector(sportSelector);
  const hasSelectedClips = useSelector(hasSelectedClipsSelector);
  const isAITaggingSession = useSelector(isAIGeneratedSessionsSelector);

  const clipNumber = activeClipIndex !== undefined ? activeClipIndex + 1 : undefined;

  if (!activeClip) {
    return null;
  }

  return (
    <Row className={styles.clipContextRow}>
      <div className={styles.fullContainer} data-qa-id={`clip-context-bar`}>
        <div className={styles.leftContainer}>
          {Boolean(clipTotal) && (
            <Text className={styles.clipCount} qaId="clip-context-clip-count">
              {format('module.video.clip_context_bar.play_count', { clipNumber, clipTotal })}
            </Text>
          )}
          {getMomentsContainer(sport, activeClip)}
        </div>
        <div className={styles.rightContainer}>
          <TimeSubscriber>
            <ClipContextGameState sport={sport} clip={activeClip} playable={playable} />
          </TimeSubscriber>
          <div className={styles.actionButton}>
            <ClipCreatorPopover
              triggerOptions={{
                buttonStyle: 'standard',
                buttonType: 'primary',
                label: format('performance-core.clip-creator.save-clip'),
                isDisabled: hasSelectedClips || isAITaggingSession,
              }}
              sourceModuleName={ModuleName.Video}
            />
          </div>
        </div>
      </div>
    </Row>
  );
}

const getMomentsContainer = (sport: SportClass, clip: VirtualClip) => {
  switch (sport) {
    case Sport.Baseball:
    case Sport.Softball:
      return <ClipContextDiamondSportsMomentsContainer clip={clip} />;
    case Sport.Soccer:
      // Show custom clip titles for playlist clips
      if (shouldShowClipTitle(clip)) {
        return (
          <Subhead level="small" className={styles.title} qaId="clip-context-clipTitle">
            {clip.title!}
          </Subhead>
        );
      }
      return null;
    case Sport.Basketball:
    case Sport.Wrestling:
    default:
      return <ClipContextMomentsContainer sport={sport} clip={clip} />;
  }
};
