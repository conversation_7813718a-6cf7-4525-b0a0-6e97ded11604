import { useEffect, useMemo } from 'react';

import { useSelector } from 'react-redux';

import { SportClass } from '@hudl/hudl-domain-types';
import {
  getTimelineConfig,
  TimelineModes,
  TimelineTrackData,
  TimelineTrackDataItem,
} from '@hudl/performance-core-domain';

import { isEditingMomentBoundariesSelector } from '../../../../store/analyze/app/momentEditing/momentEditingSelectors';
import { gridDefinitionKeyToTimelineTrackDisplay } from '../Timeline/helpers';
import {
  activeClipIndexSelector,
  activeClipsSelector,
  focusedClipIndexSelector,
  unfilteredClipsSelector,
} from 'store/analyze/app/clips/clipsSelectors';
import { opponentsInUIContextSelector, teamInUIContextSelector } from 'store/analyze/app/team/teamSelectors';
import {
  timelineExpandedIdsSelector,
  timelineModeSelector,
  timelineSelectedIdsSelector,
} from 'store/analyze/app/timeline/timelineSelectors';
import { setTimelineTimespans } from 'store/analyze/app/timeline/timelineSlice';
import { participantIdToAthleteMapSelector } from 'store/analyze/domain/roster/rosterSelectors';
import {
  activeTaggingSessionsSelector,
  isAIGeneratedSessionsSelector,
  momentsSelector,
} from 'store/analyze/domain/taggingSessions/taggingSessionSelectors';
import { ownTeamInfoSelector } from 'store/analyze/domain/teams/teamSelectors';
import { gridDefinitionSelector, selectedClipIdsSelector } from 'store/analyze/ui/grid/gridSelectors';
import { isListModuleOpenSelector } from 'store/analyze/ui/modules/moduleSelectors';
import TimespanRecord from 'store/filter/records/TimespanRecord';
import { useAppDispatch } from 'store/hooks';
import { playbackModeSelector } from 'store/shared/selectors/playerControlsSelectors';
import PlaybackMode from 'store/shared/utils/PlaybackMode';

export function useTimelineData(sport: SportClass) {
  const dispatch = useAppDispatch();
  const config = useMemo(() => getTimelineConfig(sport), [sport]);
  const timelineMode = useSelector(timelineModeSelector);
  const isEditingMomentBoundaries = useSelector(isEditingMomentBoundariesSelector);

  // Data loading area, refactor this to a hook probably.
  const moments = useSelector(momentsSelector);
  const filteredClips = useSelector(activeClipsSelector);
  const unfilteredClips = useSelector(unfilteredClipsSelector);
  const activeClipIndex = useSelector(activeClipIndexSelector);
  const playbackMode = useSelector(playbackModeSelector);
  const focusedClipIndex = useSelector(focusedClipIndexSelector);
  const isListModuleOpen = useSelector(isListModuleOpenSelector);
  const parsedFocusedClipIndex = playbackMode === PlaybackMode.LOOP && isListModuleOpen ? focusedClipIndex : undefined;
  const isAISession = useSelector(isAIGeneratedSessionsSelector);

  // Scout and Opponent Team in Context
  const opponentTeamsInContext = useSelector(opponentsInUIContextSelector);
  const teamInContext = useSelector(teamInUIContextSelector);
  const ownTeamInfo = useSelector(ownTeamInfoSelector);
  // Generates sport specific track data for the timeline given state and configuration.

  // Current grid definition
  const gridDefinitionKey = useSelector(gridDefinitionSelector);

  // Athlete Roster
  const roster = useSelector(participantIdToAthleteMapSelector);

  const expandedTrackIds = useSelector(timelineExpandedIdsSelector);
  const gridSelectedClips = useSelector(selectedClipIdsSelector);
  const timelineSelectedClips = useSelector(timelineSelectedIdsSelector);
  const selectedClips = useMemo(
    () => [...gridSelectedClips, ...timelineSelectedClips],
    [gridSelectedClips, timelineSelectedClips]
  );

  const taggingSessions = useSelector(activeTaggingSessionsSelector);
  const taggingSession = taggingSessions.length > 0 ? taggingSessions[0] : undefined;

  const defaultOne = 'TeamOneDefaultColor';
  const defaultTwo = 'TeamTwoDefaultColor';

  const taggingColors = useMemo(
    () => ({
      teamOne: taggingSession?.taggingSettings?.teamOne?.color ?? defaultOne,
      teamTwo: taggingSession?.taggingSettings?.teamTwo?.color ?? defaultTwo,
    }),
    [taggingSession]
  );

  const tracks: TimelineTrackData[] = useMemo(() => {
    // hide tracks when editing moment boundaries
    if (isEditingMomentBoundaries) {
      return [];
    }

    return (
      config.createTrackData({
        moments,
        filteredClips,
        unfilteredClips,
        sport,
        gender: ownTeamInfo?.gender,
        teamInContext,
        selectedClips,
        expandedTrackIds,
        opponentTeamsInContext,
        displayMode: gridDefinitionKeyToTimelineTrackDisplay(gridDefinitionKey),
        roster,
        isAISession,
        focusedClipIndex: parsedFocusedClipIndex,
        activeClipIndex,
        colors: taggingColors,
      }) || []
    );
  }, [
    isEditingMomentBoundaries,
    config,
    moments,
    filteredClips,
    unfilteredClips,
    sport,
    ownTeamInfo?.gender,
    teamInContext,
    selectedClips,
    expandedTrackIds,
    opponentTeamsInContext,
    isAISession,
    gridDefinitionKey,
    roster,
    parsedFocusedClipIndex,
    activeClipIndex,
    taggingColors,
  ]);

  useEffect(() => {
    if (timelineMode === TimelineModes.Edit) {
      dispatch(setTimelineTimespans([]));
    }
    const allItems = tracks.reduce<TimelineTrackDataItem[]>((acc: TimelineTrackDataItem[], track) => {
      return acc.concat(track.data);
    }, []);

    const timeSpans = allItems.map((item) =>
      TimespanRecord.fromJS({ startTimeMs: item.startTimeMs, endTimeMs: item.endTimeMs })
    );
    dispatch(setTimelineTimespans(timeSpans));
  }, [tracks, timelineMode, dispatch]);

  return { tracks, config };
}
