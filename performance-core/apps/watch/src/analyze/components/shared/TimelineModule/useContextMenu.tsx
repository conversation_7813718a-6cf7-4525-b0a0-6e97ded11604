import { useCallback, useRef } from 'react';

import { useSelector } from 'react-redux';

import { ModuleName, TimelineModes } from '@hudl/performance-core-domain';
import { IconAdd } from '@hudl/uniform-web';
import { viActions } from '@hudl/video-interface';
import { format } from 'frontends-i18n';

import { isAIGeneratedSessionsSelector } from '../../../../store/analyze/domain/taggingSessions/taggingSessionSelectors';
import ClipCreatorPopover from '../ClipCreator/ClipCreatorPopover/ClipCreatorPopover';
import { normalizeUniversalId } from '../Timeline/helpers';
import { ContextMenuConfig, ContextMenuDataItem } from '../Timeline/timelineTypes';
import {
  timelineContextMenuSelector,
  timelineModeSelector,
  timelineSelectedIdsSelector,
} from 'store/analyze/app/timeline/timelineSelectors';
import {
  closeTimelineContextMenu,
  setTimelineContextMenu,
  setTimelineSelectedIds,
} from 'store/analyze/app/timeline/timelineSlice';
import { selectedClipIdsSelector } from 'store/analyze/ui/grid/gridSelectors';
import { useAppDispatch } from 'store/hooks';
import { isPlayerPausedSelector, videoPlayerIdSelector } from 'store/shared/selectors/videoPlayerSelectors';

export function useContextMenu(): ContextMenuConfig | undefined {
  const dispatch = useAppDispatch();
  const isPlayerPaused = useSelector(isPlayerPausedSelector);
  const previouslyPaused = useRef(false);
  const videoPlayerId = useSelector(videoPlayerIdSelector);
  const timelineMode = useSelector(timelineModeSelector);
  const contextMenu = useSelector(timelineContextMenuSelector);
  const isAITaggingSession = useSelector(isAIGeneratedSessionsSelector);

  const gridSelectedClipIds = useSelector(selectedClipIdsSelector);
  const timelineSelectedClipIds = useSelector(timelineSelectedIdsSelector);
  const selectedClipIds = [...gridSelectedClipIds, ...timelineSelectedClipIds];

  const handleOpenContextMenu = useCallback(
    (data: ContextMenuDataItem, position: { x: number; y: number }) => {
      dispatch(setTimelineSelectedIds([normalizeUniversalId(data.id)]));
      previouslyPaused.current = isPlayerPaused;
      dispatch(viActions.pause(videoPlayerId));
      dispatch(setTimelineContextMenu({ path: 'root', position, data }));
    },
    [dispatch, videoPlayerId, isPlayerPaused]
  );

  const handleCloseContextMenu = useCallback(() => {
    dispatch(setTimelineSelectedIds([]));
    if (!previouslyPaused.current) {
      dispatch(viActions.play(videoPlayerId));
    }
    dispatch(closeTimelineContextMenu());
  }, [dispatch, videoPlayerId]);

  // TODO: Make this more configurable
  return timelineMode === TimelineModes.Playback && !isAITaggingSession
    ? {
        ...contextMenu,
        onClose: handleCloseContextMenu,
        onOpen: handleOpenContextMenu,

        menu: {
          root: {
            id: 'root',
            actions: [
              {
                text: format('performance-core.clip-creator.save-clip'),
                icon: <IconAdd size="small" />,
                onClick: (data?: ContextMenuDataItem) => {
                  dispatch(setTimelineContextMenu({ ...contextMenu, path: 'playlist', data }));
                },
              },
            ],
          },
          playlist: {
            id: 'playlist',
            actions: [],
            render: (currentContextMenu: ContextMenuConfig, data?: ContextMenuDataItem) => {
              if (!data || !currentContextMenu) return null;
              return (
                <ClipCreatorPopover
                  clipIds={selectedClipIds}
                  triggerOptions={{
                    forceOpen: true,
                    hideTrigger: true,
                  }}
                  placement="top"
                  sourceModuleName={ModuleName.Timeline}
                />
              );
            },
          },
        },
      }
    : undefined;
}
