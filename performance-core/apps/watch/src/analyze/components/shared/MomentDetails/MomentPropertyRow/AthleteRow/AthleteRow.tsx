import { useCallback } from 'react';

import { AthletePropertyMetadata, HudlTaggingMoment } from '@hudl/hudl-domain-types';
import { Select } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import { useMomentEditing } from '../../hooks/useMomentEditing';
import { EditablePropertyRow } from '../EditablePropertyRow/EditablePropertyRow';
import { MomentAthleteOption, useMomentAthleteOptions } from './useMomentAthleteOptions';
import { updateMomentAthleteTagsWithCallback } from 'store/analyze/domain/taggingSessions/taggingSessionSlice';
import { useAppDispatch } from 'store/hooks';

import styles from './styles.module.scss';

interface AthleteRowProps {
  moment: HudlTaggingMoment;
  property: AthletePropertyMetadata;
  isEditable: boolean;
}

export function AthleteRow({ moment, property, isEditable }: AthleteRowProps) {
  const dispatch = useAppDispatch();

  const { isEditing, startEditing, stopEditing } = useMomentEditing(moment.id);
  const { optionGroups, selectedOption } = useMomentAthleteOptions(moment, property);

  const onChange = useCallback(
    (option: MomentAthleteOption | null) => {
      void dispatch(
        updateMomentAthleteTagsWithCallback(moment, [option?.athlete], (updatedMoment) =>
          property.set(updatedMoment, option?.athlete ?? null)
        )
      );
      stopEditing();
    },
    [dispatch, moment, property, stopEditing]
  );

  return (
    <EditablePropertyRow
      label={format(property.labelI18nKey)}
      valuePlaceholder={format('performance-core.moment-details.placeholder.select-an-athlete')}
      valueLabel={selectedOption?.label}
      isEditable={isEditable}
      isEditing={isEditing}
      onStartEditing={startEditing}
    >
      <Select
        className={styles.select}
        autoFocus={true}
        defaultMenuIsOpen={true}
        isClearable={true}
        formSize="xsmall"
        options={optionGroups}
        value={selectedOption}
        menuPortalTarget={document.getElementById('react-select-portal')}
        onBlur={stopEditing}
        onChange={onChange}
        components={{ DropdownIndicator: null }}
      />
    </EditablePropertyRow>
  );
}
