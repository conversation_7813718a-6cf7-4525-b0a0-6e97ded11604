import { use<PERSON><PERSON>back, useMemo } from 'react';

import { useSelector } from 'react-redux';
import type { GroupBase } from 'react-select';

import {
  AthletePropertyMetadata,
  getMomentAthlete<PERSON>ey,
  HudlTaggingMoment,
  MomentAthlete,
  MomentTeamIndex,
  MultiAthletePropertyMetadata,
} from '@hudl/hudl-domain-types';
import { defaultSortMomentAthletes } from '@hudl/hudl-domain-types';
import { TaggingSettingsPlayerSettings } from '@hudl/performance-core-domain';
import { format } from 'frontends-i18n';

import { GameInfo, videoIdToGameInfoMapSelector } from '../../../../../../store/analyze/domain/video/videoSelectors';
import { athletesSelector } from 'store/analyze/domain/roster/rosterSelectors';
import { Athlete } from 'store/analyze/domain/roster/rosterSlice';
import { videoIdToTaggingSessionSelector } from 'store/analyze/domain/taggingSessions/taggingSessionSelectors';
import notEmpty from 'utils/notEmpty';

export interface MomentAthleteOption {
  label: string;
  value: string;
  athlete: MomentAthlete;
}

interface UseMomentAthleteOptionsResult {
  optionGroups: GroupBase<MomentAthleteOption>[];
  selectedOption?: MomentAthleteOption;
  selectedOptions?: MomentAthleteOption[];
}

const defaultJerseyNumbers = ['00'].concat(
  Array.from(Array(100).keys()).map((jerseyNumber) => jerseyNumber.toString())
);

export function useMomentAthleteOptions(
  moment: HudlTaggingMoment,
  property: AthletePropertyMetadata | MultiAthletePropertyMetadata
): UseMomentAthleteOptionsResult {
  const videoIdToGameInfoMap = useSelector(videoIdToGameInfoMapSelector);
  const gameInfo = moment.videoId ? videoIdToGameInfoMap.get(moment.videoId) : undefined;

  const teamIndex = useMemo(() => {
    if (!property.getTeam) {
      return null;
    }

    return property.getTeam(moment);
  }, [moment, property]);

  const teamLabel = useMemo(() => {
    if (!gameInfo || !teamIndex) {
      return format('performance-core.moment-details.athlete-groups.roster');
    }

    const { teamOneLabel, teamTwoLabel } = getTeamLabels(gameInfo);

    if (teamIndex === '1') {
      return teamOneLabel;
    } else {
      return teamTwoLabel;
    }
  }, [gameInfo, teamIndex]);

  const teamAthletes = useTeamAthletes(gameInfo, teamIndex);
  const taggingSettingsAthletes = useTaggingSettingsAthletes(moment.videoId, teamIndex);

  const roster = useMemo(() => {
    const rosterMap = new Map<string, TaggingSettingsPlayerSettings>();
    teamAthletes.map((athlete) => {
      if (athlete.userId) {
        rosterMap.set(athlete.userId, {
          userId: athlete.userId,
          firstName: athlete.firstName,
          lastName: athlete.lastName,
          jersey: athlete.jersey,
        });
      }
    });

    // Prioritize tagging settings athletes over team athletes (i.e. override team athletes if there is a matching tagging settings athlete)
    taggingSettingsAthletes.map((athlete) => {
      if (athlete.userId) {
        rosterMap.set(athlete.userId, athlete);
      }
    });

    return rosterMap;
  }, [teamAthletes, taggingSettingsAthletes]);

  const toAthleteOption = useCallback(
    (athlete: MomentAthlete): MomentAthleteOption => ({
      label: property.format(athlete, { roster }) ?? '',
      value: getMomentAthleteKey(athlete),
      athlete,
    }),
    [property, roster]
  );

  const unknownJerseyOptions = useMemo(() => {
    const jerseyNumbers = new Set(
      Array.from(roster.values())
        .map((a) => a.jersey)
        .filter(notEmpty)
    );

    return defaultJerseyNumbers
      .filter((jerseyNumber) => !jerseyNumbers.has(jerseyNumber))
      .map((jerseyNumber) => toAthleteOption({ jersey: jerseyNumber }));
  }, [roster, toAthleteOption]);

  const rosterOptions = useMemo(() => {
    const rosterAthletes = Array.from(roster.values());
    const optionsBuilder = rosterAthletes
      .map(({ userId, jersey }) => {
        if (userId) {
          return toAthleteOption({ userId });
        }

        if (jersey) {
          return toAthleteOption({ jersey });
        }

        return undefined;
      })
      .filter(notEmpty)
      .sort((a, b) => defaultSortMomentAthletes(a.athlete, b.athlete, { roster }));
    return optionsBuilder;
  }, [roster, toAthleteOption]);

  const unknownOptions = useMemo(() => {
    const options: MomentAthleteOption[] = [];
    if (property.supports.unknown) {
      options.push(toAthleteOption({ unknown: true }));
    }

    if (property.supports.none) {
      options.push(toAthleteOption({ none: true }));
    }

    return options.concat(unknownJerseyOptions);
  }, [unknownJerseyOptions, property.supports.none, property.supports.unknown, toAthleteOption]);

  const optionGroups = useMemo(() => {
    const groups: GroupBase<MomentAthleteOption>[] = [];

    if (rosterOptions.length > 0) {
      groups.push({
        label: teamLabel,
        options: rosterOptions,
      });
    }

    if (unknownOptions.length > 0) {
      groups.push({
        label: format('performance-core.moment-details.athlete-groups.other'),
        options: unknownOptions,
      });
    }

    return groups;
  }, [rosterOptions, teamLabel, unknownOptions]);

  // Single-Select Option
  const selectedOption = useMemo(() => {
    const value = property.get(moment);
    if (!value || Array.isArray(value)) {
      return undefined;
    }

    const valueOption = toAthleteOption(value);
    return rosterOptions.find((o) => o.value === valueOption.value) ?? valueOption;
  }, [moment, rosterOptions, property, toAthleteOption]);

  // Multi-Select Option
  const selectedOptions = useMemo(() => {
    const value = property.get(moment);
    if (!value || !Array.isArray(value) || value.length === 0) {
      return undefined;
    }

    return value.map(toAthleteOption);
  }, [moment, property, toAthleteOption]);

  return { optionGroups, selectedOption, selectedOptions };
}

function useTaggingSettingsAthletes(
  videoId: string | undefined,
  teamIndex: MomentTeamIndex | null
): TaggingSettingsPlayerSettings[] {
  const videoIdToTaggingSession = useSelector(videoIdToTaggingSessionSelector);

  const taggingSettings = useMemo(() => {
    if (!videoId) {
      return undefined;
    }

    return videoIdToTaggingSession.get(videoId)?.taggingSettings;
  }, [videoId, videoIdToTaggingSession]);

  return useMemo(() => {
    if (!taggingSettings || !teamIndex) {
      return [];
    }

    const teamOne = taggingSettings.teamOne?.roster ?? [];
    const teamTwo = taggingSettings.teamTwo?.roster ?? [];

    if (teamIndex === '1') {
      return teamOne;
    } else {
      return teamTwo;
    }
  }, [taggingSettings, teamIndex]);
}

/**
 * Athletes that are on the team's current roster. Not necessarilly the same athletes on the tagging settings.
 * Included here to allow users to select athletes that are not on the tagging settings roster.
 */
function useTeamAthletes(gameInfo: GameInfo | undefined, teamIndex: MomentTeamIndex | null): Athlete[] {
  const athletes = useSelector(athletesSelector);

  const seasonId = gameInfo?.seasonId;
  const teamId = teamIndex === '1' ? gameInfo?.teamOneId : gameInfo?.teamTwoId;

  // Find the athletes that are on the specified team and match the season
  return useMemo(() => {
    return (
      athletes?.filter((athlete) => {
        const isOnTeam = athlete.teamId === teamId;
        if (!isOnTeam) {
          return false;
        }

        // If there's no season, just include the athlete to be safe
        if (!seasonId) {
          return true;
        }

        const hasMatchingSeason = seasonId && athlete.seasonIds.indexOf(seasonId) !== -1;
        return hasMatchingSeason;
      }) ?? []
    );
  }, [athletes, seasonId, teamId]);
}

export function getTeamLabels(gameInfo: GameInfo) {
  const hasSameAbbreviation =
    gameInfo.teamOneAbbreviation.length > 0 && gameInfo.teamOneAbbreviation === gameInfo.teamTwoAbbreviation;
  const teamOneLabel = hasSameAbbreviation ? gameInfo?.teamOneName : gameInfo.teamOneAbbreviation;
  const teamTwoLabel = hasSameAbbreviation ? gameInfo?.teamTwoName : gameInfo.teamTwoAbbreviation;
  return { teamOneLabel, teamTwoLabel };
}
