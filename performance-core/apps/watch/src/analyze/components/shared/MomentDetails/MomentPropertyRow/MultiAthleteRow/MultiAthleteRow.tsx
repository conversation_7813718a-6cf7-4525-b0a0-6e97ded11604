import { useCallback } from 'react';

import { MultiValue } from 'react-select';

import { HudlTaggingMoment, MultiAthletePropertyMetadata } from '@hudl/hudl-domain-types';
import { Select } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import { useMomentEditing } from '../../hooks/useMomentEditing';
import { MomentAthleteOption, useMomentAthleteOptions } from '../AthleteRow/useMomentAthleteOptions';
import { EditablePropertyRow } from '../EditablePropertyRow/EditablePropertyRow';
import { updateMomentAthleteTagsWithCallback } from 'store/analyze/domain/taggingSessions/taggingSessionSlice';
import { useAppDispatch } from 'store/hooks';

import styles from './styles.module.scss';

interface MultiAthleteRowProps {
  moment: HudlTaggingMoment;
  property: MultiAthletePropertyMetadata;
  isEditable: boolean;
}

export function MultiAthleteRow({ moment, property, isEditable }: MultiAthleteRowProps) {
  const dispatch = useAppDispatch();

  const { isEditing, startEditing, stopEditing } = useMomentEditing(moment.id);
  const { optionGroups, selectedOptions } = useMomentAthleteOptions(moment, property);

  const onChange = useCallback(
    (newValue: MultiValue<MomentAthleteOption>) => {
      if (property.athleteLimit !== undefined && newValue && newValue.length > property.athleteLimit) {
        // If the new value exceeds the max allowed, we do not update the moment.
        return;
      }

      const athletes = newValue ? newValue.map((option) => option.athlete) : [];
      void dispatch(
        updateMomentAthleteTagsWithCallback(moment, athletes, (updatedMoment) => property.set(updatedMoment, athletes))
      );
      stopEditing();
    },
    [dispatch, moment, property, stopEditing]
  );

  const valueLabel = selectedOptions?.map((option) => option.label).join(', ') ?? '';

  return (
    <EditablePropertyRow
      label={format(property.labelI18nKey)}
      valuePlaceholder={format('performance-core.moment-details.placeholder.select-athletes')}
      valueLabel={valueLabel}
      isEditable={isEditable}
      isEditing={isEditing}
      onStartEditing={startEditing}
    >
      <Select
        className={styles.select}
        autoFocus={true}
        defaultMenuIsOpen={true}
        isClearable={true}
        formSize="xsmall"
        options={optionGroups}
        value={selectedOptions}
        isMulti={true}
        menuPortalTarget={document.getElementById('react-select-portal')}
        onBlur={stopEditing}
        onChange={onChange}
        components={{ DropdownIndicator: null }}
      />
    </EditablePropertyRow>
  );
}
