import * as React from 'react';

import { ReorderClipsInPlaylistOnboarding } from 'shared/components/Onboarding/PlaylistOnboarding/ReorderClipsInPlaylistOnboarding/ReorderClipsInPlaylistOnboarding';
import { ManuallySegmentClipsOnboarding } from 'shared/components/Onboarding/VideoSegmentationOnboarding/ManuallySegmentClipsOnboarding';

import { ModuleDisplay } from '@hudl/performance-core-domain';
import { Button, IconUiLayoutBottom, IconUiLayoutRight, Tooltip } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import FieldSetButton from '../../../football/FieldSets/FieldSetButton/FieldSetButton';
import AddSlideButton from '../../Slides/AddSlideButton';
import { ManageClipsOptions, ReorderOptions } from './NavigationHeader.types';

import styles from './styles.module.scss';

type NavigationModuleHeaderContentActionsProps = {
  clipCount?: number;
  addDataOptions: {
    supportsAddingData: boolean;
    onAddData?: () => void;
  };
  addSlideOptions: {
    supportsAddSlide: boolean;
    isDisabled?: boolean;
  };
  manageClipsOptions: ManageClipsOptions;
  reorderOptions: ReorderOptions;
  hasFilterOrSort: boolean;
  fieldSetOptions: {
    supportsFieldSetChanges: boolean;
  };
  moduleOptions: {
    supportsDifferentDisplays: boolean;
    displayOption?: ModuleDisplay;
    onClick?: () => void;
  };
};

function NavigationModuleHeaderContentMode({
  clipCount,
  hasFilterOrSort,
  fieldSetOptions,
  addDataOptions,
  addSlideOptions,
  manageClipsOptions,
  reorderOptions,
  moduleOptions,
}: NavigationModuleHeaderContentActionsProps) {
  if (!clipCount) {
    return null;
  }

  return (
    <div className={styles.navigationModuleHeaderContent}>
      {addDataOptions.supportsAddingData && (
        <Button onPress={addDataOptions.onAddData} buttonType="subtle" size="xsmall">
          {format('module.grid.header.add-data')}
        </Button>
      )}

      {addSlideOptions.supportsAddSlide && <AddSlideButton disableButton={addSlideOptions.isDisabled ?? false} />}
      {manageClipsOptions.supportsManageClips && (
        <ManuallySegmentClipsOnboarding>
          <Button
            size="xsmall"
            buttonStyle="minimal"
            buttonType="subtle"
            data-qa-id="manage-clips-button"
            onPress={manageClipsOptions.onManageClipsStart}
          >
            {format('module.grid.header.manage_clips')}
          </Button>
        </ManuallySegmentClipsOnboarding>
      )}
      {reorderOptions.supportsReorder && (
        <>
          {!(hasFilterOrSort || reorderOptions.hasMultipleVideosSelected) && (
            <ReorderClipsInPlaylistOnboarding>
              <Button
                size="xsmall"
                buttonStyle="minimal"
                buttonType="subtle"
                onPress={reorderOptions.onReorderStart}
                qaId="reorder-button"
              >
                {format('module.grid.header.reorder')}
              </Button>
            </ReorderClipsInPlaylistOnboarding>
          )}
          {(hasFilterOrSort || reorderOptions.hasMultipleVideosSelected) && (
            <Tooltip
              content={`Clips can't be reordered when ${
                hasFilterOrSort ? 'sorting or filtering data' : 'multiple videos or playlists are selected'
              }.`}
            >
              <Button size="xsmall" buttonStyle="minimal" buttonType="subtle" className={styles.disabledButton}>
                {format('module.grid.header.reorder')}
              </Button>
            </Tooltip>
          )}
        </>
      )}
      {fieldSetOptions.supportsFieldSetChanges && <FieldSetButton />}
      {moduleOptions.supportsDifferentDisplays && (
        <Button
          size="xsmall"
          buttonStyle="minimal"
          buttonType="subtle"
          style={{ boxShadow: 'none' }}
          icon={moduleOptions.displayOption === ModuleDisplay.Right ? <IconUiLayoutRight /> : <IconUiLayoutBottom />}
          onPress={moduleOptions.onClick}
          qaId={`grid-module-display-${moduleOptions.displayOption}`}
        />
      )}
    </div>
  );
}

export { NavigationModuleHeaderContentMode };
