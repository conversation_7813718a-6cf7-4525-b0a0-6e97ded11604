import { useCallback, useEffect, useMemo } from 'react';
import * as React from 'react';

import { useSelector } from 'react-redux';

import { Sport } from '@hudl/hudl-domain-types';
import { ModuleDisplay, ModuleMode, ModuleName } from '@hudl/performance-core-domain';
import { format } from 'frontends-i18n';

import { GridMode, SortOrder } from '../../Grid/Grid.types';
import SelectedClipsHeader from '../../TimelineModule/TimelineModuleHeader/SelectedClipsHeader';
import NavigationModuleHeader from './NavigationModuleHeader';
import { NavigationModuleHeaderContentClipsManaging } from './NavigationModuleHeaderContentClipsManaging';
import { NavigationModuleHeaderContentClipsReordering } from './NavigationModuleHeaderContentClipsReordering';
import { NavigationModuleHeaderContentClipsSelection } from './NavigationModuleHeaderContentClipsSelection';
import { NavigationModuleHeaderContentMode } from './NavigationModuleHeaderContentMode';
import { ColumnSetProviderKey } from 'analyze/domain/columnDefinitions/ColumnDefinition';
import { GridDefinitionKey, SharedGridDefinitionKey } from 'analyze/domain/columnDefinitions/GridDefinition';
import { getGridDefinitions } from 'analyze/domain/columnDefinitions/gridDefinitions';
import { isContinuousSubset } from 'analyze/utils/isContinuousSubset';
import { useRemoveClipsFromPlaylistMutation } from 'graphqlSrc/hooks/useRemoveClipsFromPlaylist/useRemoveClipsFromPlaylistMutation';
import { useUndoRemoveClipsFromPlaylistMutation } from 'graphqlSrc/hooks/useUndoRemoveClipsFromPlaylist/useUndoRemoveClipsFromPlaylistMutation';
import {
  activeClipsSelector,
  unfilteredClipsSelector,
  unsortedFilteredClipsSelector,
} from 'store/analyze/app/clips/clipsSelectors';
import { filtersSelectedCountSelector } from 'store/analyze/app/filters/filtersSelectors';
import { isMomentEditingEnabledSelector } from 'store/analyze/app/listModule/listModuleSelectors';
import {
  doneManagingClips,
  mergeV3Clips,
  removeClassicClips,
  startManagingClips,
} from 'store/analyze/app/manageClips/manageClipsThunks';
import { isEditingMomentBoundariesSelector } from 'store/analyze/app/momentEditing/momentEditingSelectors';
import { startCreatingNewMoment } from 'store/analyze/app/momentEditing/momentEditingThunks';
import { isPlaylistSelector } from 'store/analyze/app/selectedVideo/selectedPlaylistSelector';
import {
  selectedCutupsSelector,
  selectedPlaylistObjectSelector,
  selectedVideoObjectSelector,
} from 'store/analyze/app/selectedVideo/selectedVideoSelectors';
import { PageName } from 'store/analyze/domain/configurations/layout/LayoutUIConfiguration.types';
import { updateLayoutUIConfiguration } from 'store/analyze/domain/configurations/layout/layoutUIConfigurationThunks';
import { buildUniversalClipId } from 'store/analyze/domain/playlist/playlistUtils';
import { isAIGeneratedSessionsSelector } from 'store/analyze/domain/taggingSessions/taggingSessionSelectors';
import { isClassicSelector, sportSelector } from 'store/analyze/domain/teams/teamSelectors';
import { isMobileSelector } from 'store/analyze/ui/environment/environmentSelectors';
import {
  columnSortOrderSelector,
  gridDefinitionSelector,
  gridModeSelector,
  selectedClipIdsSelector,
} from 'store/analyze/ui/grid/gridSelectors';
import {
  deselectClips,
  setColumnSet,
  setGridDefinition,
  setSelectedClipIds,
  setSelectedFieldIndex,
} from 'store/analyze/ui/grid/gridSlice';
import { changeGridMode, clearClipSelections } from 'store/analyze/ui/grid/gridThunks';
import {
  navigationModuleDisplaySelector,
  navigationModuleModeSelector,
} from 'store/analyze/ui/modules/moduleSelectors';
import { updateModuleDisplay } from 'store/analyze/ui/modules/moduleThunks';
import { showToast } from 'store/analyze/ui/toast/toastSlice';
import { useAppDispatch } from 'store/hooks';
import isCoachOrAdmin from 'utils/isCoachOrAdmin';
import { filterOnlyClipIds } from 'utils/MediaStreamClips';

interface NavigationModuleHeaderContainerProps {
  isLoading?: boolean;
}

function NavigationModuleHeaderContainer({ isLoading }: NavigationModuleHeaderContainerProps): React.JSX.Element {
  const dispatch = useAppDispatch();

  const navigationModuleMode = useSelector(navigationModuleModeSelector);
  const navigationModuleDisplay = useSelector(navigationModuleDisplaySelector);
  const gridMode = useSelector(gridModeSelector);
  const unfilteredClips = useSelector(unfilteredClipsSelector);
  const unsortedFilteredClips = useSelector(unsortedFilteredClipsSelector);
  const activeClips = useSelector(activeClipsSelector);
  const filterCount = useSelector(filtersSelectedCountSelector);
  const columnSortOrder = useSelector(columnSortOrderSelector);
  const isClassic = useSelector(isClassicSelector);
  const sport = useSelector(sportSelector);
  const selectedClipIds = useSelector(selectedClipIdsSelector);
  const selectedCutups = useSelector(selectedCutupsSelector);
  const selectedVideos = useSelector(selectedVideoObjectSelector);
  const selectedPlaylistObjects = useSelector(selectedPlaylistObjectSelector);
  const selectedGridDefinition = useSelector(gridDefinitionSelector);
  const isPlaylist = useSelector(isPlaylistSelector);
  const isMobile = useSelector(isMobileSelector);
  const isAI = useSelector(isAIGeneratedSessionsSelector);
  const isMomentEditingEnabled = useSelector(isMomentEditingEnabledSelector);
  const isEditingMomentBoundaries = useSelector(isEditingMomentBoundariesSelector);

  const supportsDifferentDisplays = Boolean(!isMobile && sport === Sport.Football);

  const [removeClipsFromPlaylist] = useRemoveClipsFromPlaylistMutation();
  const [undoRemoveClipsFromPlaylist] = useUndoRemoveClipsFromPlaylistMutation();

  const hasFilter = filterCount > 0;
  const hasSort = columnSortOrder !== undefined && columnSortOrder.sortOrder !== SortOrder.None;
  const hasFilterOrSort = hasFilter || hasSort;
  const multipleCutupsSelected = selectedCutups.length > 1;
  const multiplePlaylistsSelected = selectedPlaylistObjects.length > 1;
  const hasMultipleVideosSelected = isClassic ? multipleCutupsSelected : multiplePlaylistsSelected;

  const clipIds = isClassic ? activeClips.map((c) => c.id) : activeClips.map(buildUniversalClipId);

  const isGridView =
    navigationModuleMode === ModuleMode.Grid &&
    (navigationModuleDisplay === ModuleDisplay.Bottom || navigationModuleDisplay === ModuleDisplay.Fullscreen);

  useEffect(() => {
    // Should this be here? Where should this logic live?
    dispatch(changeGridMode(GridMode.Edit));
  }, [dispatch, navigationModuleDisplay]);

  useEffect(() => {
    if (gridMode === GridMode.Reorder && hasMultipleVideosSelected) {
      dispatch(changeGridMode(GridMode.Edit));
    }
  }, [dispatch, gridMode, hasMultipleVideosSelected]);

  const universalActiveClipIds = useMemo(() => activeClips.map((c) => buildUniversalClipId(c)), [activeClips]);
  const activeClipIds = useMemo(() => activeClips.map((c) => c.id), [activeClips]);

  useEffect(() => {
    const selectedClipsNotInActiveClips = selectedClipIds.filter(
      (clipId) => !universalActiveClipIds.includes(clipId) && !activeClipIds.includes(clipId)
    );

    if (selectedClipsNotInActiveClips.length > 0) {
      dispatch(deselectClips(selectedClipsNotInActiveClips));
    }
  }, [selectedClipIds, universalActiveClipIds, dispatch, activeClipIds]);

  const removeV3Clips = useCallback(async (): Promise<void> => {
    // Only allow removal of clips when a single playlist is selected
    if (multiplePlaylistsSelected) {
      return;
    }

    const selectedPlaylist = selectedPlaylistObjects.at(0);

    if (selectedPlaylist) {
      await removeClipsFromPlaylist(selectedPlaylist.id, selectedClipIds);
      dispatch(deselectClips(filterOnlyClipIds(selectedClipIds)));
      dispatch(
        showToast({
          message: format('analyze.playlist.remove_clips_message', { clipCount: selectedClipIds.length }),
          type: 'success',
          actionText: 'Undo',
          onActionClick: async () => {
            await undoRemoveClipsFromPlaylist(selectedPlaylist.id, selectedClipIds);
            dispatch(setSelectedClipIds(selectedClipIds));
          },
        })
      );
    }
  }, [
    dispatch,
    multiplePlaylistsSelected,
    removeClipsFromPlaylist,
    selectedClipIds,
    selectedPlaylistObjects,
    undoRemoveClipsFromPlaylist,
  ]);

  const reorderOptions = useMemo(() => {
    const shouldSupportReorder = isClassic || (!isClassic && isPlaylist);
    const supportsReorder = isGridView && isCoachOrAdmin() && shouldSupportReorder;
    if (!supportsReorder) {
      return {
        supportsReorder,
      };
    }

    return {
      supportsReorder,
      isReordering: gridMode === GridMode.Reorder,
      hasMultipleVideosSelected: isClassic ? multipleCutupsSelected : multiplePlaylistsSelected,
      onReorderStart: () => dispatch(changeGridMode(GridMode.Reorder)),
      onReorderDone: () => dispatch(changeGridMode(GridMode.Edit)),
    };
  }, [isClassic, isPlaylist, isGridView, gridMode, multipleCutupsSelected, multiplePlaylistsSelected, dispatch]);

  const fieldSetOptions = useMemo(() => {
    return {
      supportsFieldSetChanges: sport === Sport.Football,
    };
  }, [sport]);

  const moduleOptions = useMemo(() => {
    if (!supportsDifferentDisplays) {
      return {
        supportsDifferentDisplays,
      };
    }

    const newDisplay = isGridView ? ModuleDisplay.Right : ModuleDisplay.Bottom;
    return {
      supportsDifferentDisplays,
      displayOption: isGridView ? ModuleDisplay.Right : ModuleDisplay.Bottom,
      onClick: () => {
        dispatch(updateModuleDisplay(ModuleName.Navigation, newDisplay));
        dispatch(updateLayoutUIConfiguration(PageName.Video));
      },
    };
  }, [dispatch, supportsDifferentDisplays, isGridView]);

  const addDataOptions = useMemo(() => {
    return {
      supportsAddingData: isMomentEditingEnabled && !isEditingMomentBoundaries,
      onAddData: () => {
        dispatch(startCreatingNewMoment());
      },
    };
  }, [dispatch, isMomentEditingEnabled, isEditingMomentBoundaries]);

  const hasAddSlideButton = navigationModuleMode === ModuleMode.List || isGridView;
  const supportsAddSlide = isClassic && isCoachOrAdmin() && hasAddSlideButton && isGridView;
  const addSlideOptions = useMemo(() => {
    if (!supportsAddSlide) {
      return {
        supportsAddSlide: false,
      };
    }

    return {
      supportsAddSlide: true,
      isDisabled: multipleCutupsSelected,
    };
  }, [supportsAddSlide, multipleCutupsSelected]);

  const selectionOptions = useMemo(() => {
    const allClipsAreSelected = selectedClipIds.length === clipIds.length;
    const isRemoveDisabled =
      allClipsAreSelected || multipleCutupsSelected || hasFilterOrSort || (isPlaylist && multiplePlaylistsSelected);

    let toolTipReason = '';
    if (hasFilterOrSort) {
      toolTipReason = 'sorting or filtering data';
    } else if (multipleCutupsSelected) {
      toolTipReason = 'multiple videos are selected';
    } else if (multiplePlaylistsSelected) {
      toolTipReason = 'multiple playlists are selected';
    } else {
      toolTipReason = 'all clips are selected';
    }

    return {
      selectedClipIds,
      isRemoveSupported: isGridView && isCoachOrAdmin() && (isClassic || isPlaylist),
      isRemoveDisabled,
      removeDisabledReason: toolTipReason,
      onRemoveClick: () => (isPlaylist ? removeV3Clips() : dispatch(removeClassicClips())),
      onClearSelection: () => dispatch(clearClipSelections()),
    };
  }, [
    dispatch,
    selectedClipIds,
    clipIds.length,
    hasFilterOrSort,
    multipleCutupsSelected,
    multiplePlaylistsSelected,
    isPlaylist,
    isGridView,
    isClassic,
    removeV3Clips,
  ]);

  const gridProviderOptions = useMemo(() => {
    const gridDefinitions = Array.from(getGridDefinitions({ sport, isAI })?.values() || []) ?? [];
    return {
      onSetGridDefinition: (rowKey: GridDefinitionKey, columnSetKey: ColumnSetProviderKey) => {
        dispatch(setColumnSet(columnSetKey));
        dispatch(setGridDefinition(rowKey));
        dispatch(setSelectedFieldIndex(undefined));
      },
      isPlaylist,
      selected: selectedGridDefinition || SharedGridDefinitionKey.Default,
      gridDefinitions: gridDefinitions.filter((gridDefinition) => !gridDefinition.hidden),
    };
  }, [dispatch, selectedGridDefinition, sport, isPlaylist, isAI]);

  const manageClipsOptions = useMemo(() => {
    if (!isCoachOrAdmin() || isMobile || isAI) {
      return {
        supportsManageClips: false,
      };
    }

    const isOneVideoSelected = selectedVideos.length === 1;
    const isFromAssist =
      unfilteredClips?.some((clip) =>
        clip?.moments?.some((moment) => (moment?.source?.indexOf('assist') ?? -1) !== -1)
      ) ?? false;
    const isContinuous = isContinuousSubset(clipIds, selectedClipIds);

    return {
      supportsManageClips: isOneVideoSelected && !isFromAssist,
      isManagingClips: gridMode === GridMode.ManageClips,
      isMergeClipsAllowed: selectedClipIds.length > 1 && isContinuous,
      onManageClipsStart: () => {
        dispatch(startManagingClips(selectedVideos[0].id));

        // Make sure button doesn't retain focus
        if (document.activeElement instanceof HTMLElement) {
          document?.activeElement?.blur();
        }
      },
      onManageClipsDone: () => dispatch(doneManagingClips()),
      onMergeClips: () => dispatch(mergeV3Clips(selectedClipIds)),
    };
  }, [isMobile, gridMode, selectedVideos, unfilteredClips, clipIds, selectedClipIds, isAI, dispatch]);

  // While reordering clips we swap out the normal header for this reorder-specific one instead
  const isReordering = reorderOptions.supportsReorder && reorderOptions.isReordering;
  // While managing clips we swap out the normal header for this managing-clips-specific one instead
  const isManaging = manageClipsOptions.supportsManageClips && manageClipsOptions.isManagingClips;

  const selectedCount = selectedClipIds.length;

  return (
    <NavigationModuleHeader isLoading={isLoading}>
      {isReordering && <NavigationModuleHeaderContentClipsReordering reorderOptions={reorderOptions} />}
      {isManaging && (
        <NavigationModuleHeaderContentClipsManaging
          clipCount={activeClips?.length}
          manageClipsOptions={manageClipsOptions}
          navigationModuleMode={navigationModuleMode}
        />
      )}
      {!isReordering && !isManaging && selectedClipIds.length > 0 && (
        <SelectedClipsHeader
          clipCount={activeClips?.length}
          selectionOptions={selectionOptions}
          sourceModuleName={ModuleName.Navigation}
        />
      )}
      {!isReordering && !isManaging && !selectedCount && (
        <>
          <NavigationModuleHeaderContentClipsSelection
            clipCount={activeClips?.length}
            filteredClips={unsortedFilteredClips}
            selectionOptions={selectionOptions}
            gridProviderOptions={gridProviderOptions}
            sourceModuleName={ModuleName.Navigation}
          />
          <NavigationModuleHeaderContentMode
            clipCount={activeClips?.length}
            hasFilterOrSort={hasFilterOrSort}
            manageClipsOptions={manageClipsOptions}
            reorderOptions={reorderOptions}
            fieldSetOptions={fieldSetOptions}
            addDataOptions={addDataOptions}
            addSlideOptions={addSlideOptions}
            moduleOptions={moduleOptions}
          />
        </>
      )}
    </NavigationModuleHeader>
  );
}

export default React.memo(NavigationModuleHeaderContainer);
