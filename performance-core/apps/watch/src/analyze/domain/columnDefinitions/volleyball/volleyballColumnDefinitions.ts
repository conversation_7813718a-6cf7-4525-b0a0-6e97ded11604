import {
  formatVolleyballMomentLabelWithSubtype,
  formatVolleyballResultLabel,
  TagConstants,
  VolleyballTaggingMoment,
} from '@hudl/hudl-domain-types';
import { getDataChipsForSequence } from '@hudl/performance-core-domain';
import { getIntl } from 'frontends-i18n';
import { format } from 'frontends-i18n';

import { GridCellType, GridHeaderCellType } from '../../../components/shared/Grid/Grid.types';
import { ColumnDefinition, ColumnDefinitionType } from '../ColumnDefinition';
import { generateAthleteColumn } from '../helpers/generateAthleteColumn';
import generateStringColumn from '../helpers/generateStringColumn';
import { numberPairToSortKey } from '../helpers/toSortKeyHelpers';
import { VolleyballColumnDefinitionKey } from './volleyballColumnDefinitionKey';

export const getVolleyballColumnDefinitions = (): ColumnDefinition[] => [
  generateStringColumn({
    key: VolleyballColumnDefinitionKey.Period,
    label: 'Set',
    name: 'setOfGame',
    qaId: `${VolleyballColumnDefinitionKey.Period}-column`,
    toSortKey: (clip) => {
      if (clip.moments.length) {
        const moments = clip.moments as VolleyballTaggingMoment[];
        return parseInt(moments[0]?.period ?? '0', 10);
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        return moment.period || '';
      }
      return '';
    },
    description: 'The period number.',
  }),

  generateStringColumn({
    key: VolleyballColumnDefinitionKey.Rally,
    label: 'Rally',
    name: 'rally',
    qaId: `${VolleyballColumnDefinitionKey.Rally}-column`,
    toSortKey: (clip) => {
      if (clip.moments.length) {
        const moments = clip.moments as VolleyballTaggingMoment[];
        return parseInt(moments[0]?.rally ?? '0', 10);
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        return moment.rally || '';
      }
      return '';
    },
    description: 'The rally number',
  }),

  generateStringColumn({
    key: VolleyballColumnDefinitionKey.Contact,
    label: 'Contact',
    name: 'contact',
    qaId: `${VolleyballColumnDefinitionKey.Contact}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        return formatVolleyballMomentLabelWithSubtype(moment);
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let value = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        value = formatVolleyballMomentLabelWithSubtype(moment);
      }
      return value || '';
    },
    description: 'The contact',
  }),
  {
    key: VolleyballColumnDefinitionKey.RallyDetails,
    type: ColumnDefinitionType.DataChip,
    toSortKey: (clip) => clip.moments?.length ?? 0,
    generateHeader: () => ({
      type: GridHeaderCellType.String,
      label: format('analyze.domain.column_definitions.volleyball.rallyDetails.header_label'),
      isSortable: false,
    }),
    generateCell: ({ clip, sport, teamInContext, opponentInContext }) => ({
      type: GridCellType.DataChip,
      chips: getDataChipsForSequence(clip.moments, { sport, teamInContext, opponentInContext }),
    }),
  },

  generateStringColumn({
    key: VolleyballColumnDefinitionKey.TeamInPossession,
    label: 'Team',
    name: 'teamInPossession',
    qaId: `${VolleyballColumnDefinitionKey.TeamInPossession}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        return moment.team;
      }
      return undefined;
    },
    getValue: ({ clip, teamInContext, opponentInContext }) => {
      let team = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        if (moment) {
          if (moment.team === '1') {
            team = teamInContext?.schoolAbbreviation;
          } else {
            team = opponentInContext?.schoolAbbreviation ?? teamInContext?.schoolAbbreviation.concat(' Opponent');
          }
        }
      }
      return team || '';
    },
    description: 'The team who made the contact',
  }),

  generateAthleteColumn({
    key: VolleyballColumnDefinitionKey.Athlete,
    label: 'Athlete',
    name: 'athlete',
    userIdTagKey: TagConstants.PlayerUserId,
    jerseyTagKey: TagConstants.PlayerJersey,
    teamIndexTagKey: TagConstants.Team,
    unknownTagKey: TagConstants.PlayerUnknown,
    filterAthletesByTeam: true,
    alwaysShowJerseys: false,
    qaId: `${VolleyballColumnDefinitionKey.Athlete}-column`,
  }),

  generateStringColumn({
    key: VolleyballColumnDefinitionKey.Result,
    label: 'Result',
    name: 'result',
    qaId: `${VolleyballColumnDefinitionKey.Result}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        return formatVolleyballResultLabel(moment);
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let value = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        if (moment.type === 'error') {
          value = '';
        } else {
          value = formatVolleyballResultLabel(moment);
        }
      }
      return value || '';
    },
    description: 'The result of the contact',
  }),

  generateStringColumn({
    key: VolleyballColumnDefinitionKey.Quality,
    label: 'Quality',
    name: 'quality',
    qaId: `${VolleyballColumnDefinitionKey.Quality}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        return moment.quality;
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let value = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as VolleyballTaggingMoment;
        value = moment.quality;
      }
      return value || '';
    },
    description: 'The quality of the result from the contact',
  }),

  generateStringColumn({
    key: VolleyballColumnDefinitionKey.Score,
    label: 'Score',
    name: 'score',
    description: 'The current score of the game',
    qaId: `${VolleyballColumnDefinitionKey.Score}-column`,
    toSortKey: (clip, sortOrder) => {
      const moment = clip.moments[0] as VolleyballTaggingMoment;
      const hasScoreTeamOne = moment?.scoreTeamOne !== null && moment?.scoreTeamOne !== undefined;
      const hasScoreTeamTwo = moment?.scoreTeamTwo !== null && moment?.scoreTeamTwo !== undefined;
      if (!hasScoreTeamOne || !hasScoreTeamTwo) {
        return undefined;
      }

      return numberPairToSortKey(parseInt(moment.scoreTeamOne), parseInt(moment.scoreTeamTwo), sortOrder);
    },
    getValue: ({ clip }) => {
      const moment = clip.moments[0] as VolleyballTaggingMoment;
      const hasScoreTeamOne = moment?.scoreTeamOne !== null && moment?.scoreTeamOne !== undefined;
      const hasScoreTeamTwo = moment?.scoreTeamTwo !== null && moment?.scoreTeamTwo !== undefined;
      if (!hasScoreTeamOne || !hasScoreTeamTwo) {
        return '';
      }

      const scoreTeamOneLabel = getIntl().formatNumber(parseInt(moment.scoreTeamOne ?? 0));
      const scoreTeamTwoLabel = getIntl().formatNumber(parseInt(moment.scoreTeamTwo ?? 0));

      return `${scoreTeamOneLabel} - ${scoreTeamTwoLabel}`;
    },
  }),
];
