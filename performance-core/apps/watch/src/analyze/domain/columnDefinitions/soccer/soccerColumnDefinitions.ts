import { SoccerConstants, SoccerTaggingMoment, TagConstants } from '@hudl/hudl-domain-types';

import { ColumnDefinition, ColumnDefinitionType, GridAthlete } from '../ColumnDefinition';
import { generateAthleteColumn, toAthleteValue } from '../helpers/generateAthleteColumn';
import generateSegmentedRangeColumn from '../helpers/generateSegmentedRangeColumn';
import generateStringColumn from '../helpers/generateStringColumn';
import { SharedColumnDefinitionKey } from '../shared/SharedColumnDefinitionKey';
import { SoccerColumnDefinitionKey } from './soccerColumnDefinitionKey';
import { generateSoccerMomentObjects } from './soccerMomentsCellGenerator';
import {
  GridCellType,
  GridHeaderCellType,
  GridIconType,
  GridSelectOption,
  IconCell,
  MultiValueCell,
  SegmentObject,
  SortOrder,
} from 'analyze/components/shared/Grid/Grid.types';
import isCoachOrAdmin from 'utils/isCoachOrAdmin';

const {
  TagConstants: SoccerTagConstants,
  HiddenMomentTypes,
  SetPieceResult,
  ShotResult,
  MomentTypes,
} = SoccerConstants;

const momentTypesWithoutAthleteColumn = [MomentTypes.EndPeriod, ...HiddenMomentTypes];

const additionalAthleteOptions: GridSelectOption[] = [
  {
    label: 'Goalkeeper',
    value: toAthleteValue({ jerseyNumber: 'Goalkeeper' }),
  },
];

const isSecondaryPlayerEditable = (result: string): boolean => {
  const uneditableResults = [SetPieceResult.OutOfPlay, SetPieceResult.OffTarget, ShotResult.OffTarget];
  return !uneditableResults.includes(result);
};

export function getInvolvedAthleteJerseyNumbers(players: string[], roster: Map<string, GridAthlete>): string[] {
  const jerseyNumbers: string[] = [];

  players.forEach((involvedAthlete) => {
    // Player ID  format is 'u' + userId + '-' + teamId
    // Jersey format is 'j' + jerseyNumber + '-' + teamId
    const prefix = involvedAthlete[0];
    // Substring the value to remove the prefix and suffix
    const value = involvedAthlete.substring(1, involvedAthlete.length - 2);
    if (!roster || !roster.size) {
      jerseyNumbers.push(involvedAthlete);
    }

    switch (prefix) {
      case 'x':
        jerseyNumbers.push('UN');
        break;
      case 'u': {
        const athlete = roster.get(value);
        if (!athlete) {
          jerseyNumbers.push('UN');
        }
        if (athlete?.jersey) {
          jerseyNumbers.push(athlete.jersey);
        }
        break;
      }
      case 'j':
        jerseyNumbers.push(value);
        break;
      case 'g':
        jerseyNumbers.push('GK');
        break;
      default:
        jerseyNumbers.push('UN');
    }
  });
  return [...new Set(jerseyNumbers)].sort((a, b) => {
    const aIsNumeric = /^\d+$/.test(a);
    const bIsNumeric = /^\d+$/.test(b);
    if (aIsNumeric && bIsNumeric) {
      return parseInt(a, 10) - parseInt(b, 10);
    }

    if (aIsNumeric) return -1;
    if (bIsNumeric) return 1;

    return a.localeCompare(b);
  });
}

export const getSoccerColumnDefinitions = (): ColumnDefinition[] => [
  generateStringColumn({
    key: SoccerColumnDefinitionKey.Debug,
    label: 'Debug',
    name: 'debug',
    qaId: `${SoccerColumnDefinitionKey.Debug}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        return moment.secondaryPlayer;
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let debug = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        if (moment) {
          debug = `Secondary Player: ${moment.secondaryPlayer} Result: ${moment.result}`;
        }
      }
      return debug || '';
    },
    description: 'The team in possession of the ball when the sequence started.',
  }),
  generateStringColumn({
    key: SoccerColumnDefinitionKey.TeamInPossession,
    label: 'In Poss',
    name: 'teamInPossession',
    qaId: `${SoccerColumnDefinitionKey.TeamInPossession}-column`,
    teamAvatarTagName: SoccerTagConstants.SequenceTeamInPossession,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        return moment.sequenceTeamInPossession;
      }
      return undefined;
    },
    getValue: ({ clip, teamInContext, opponentInContext }) => {
      let team = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        if (moment) {
          if (moment.sequenceTeamInPossession === '1') {
            team = teamInContext?.schoolAbbreviation;
          } else {
            team = opponentInContext?.schoolAbbreviation ?? teamInContext?.schoolAbbreviation.concat(' Opponent');
          }
        }
      }
      return team || '';
    },
    description: 'The team in possession of the ball when the sequence started.',
  }),
  generateStringColumn({
    key: SoccerColumnDefinitionKey.Sequence,
    label: 'Seq',
    name: 'sequence',
    qaId: `${SoccerColumnDefinitionKey.Sequence}-column`,
    toSortKey: (clip) => {
      if (clip.moments.length) {
        const moments = clip.moments as SoccerTaggingMoment[];
        return parseInt(moments[0]?.sequence ?? '0', 10);
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        return moment.sequence || '';
      }
      return '';
    },
    description: 'The sequence number.',
  }),
  generateStringColumn({
    key: SoccerColumnDefinitionKey.SequenceStart,
    label: 'Starts With',
    name: 'start',
    qaId: `${SoccerColumnDefinitionKey.SequenceStart}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;

        return moment.sequenceStartEvent;
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let value = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;

        value = moment.sequenceStartEvent;
      }
      return value || '';
    },
    description: 'How the seqence started.',
  }),
  generateStringColumn({
    key: SoccerColumnDefinitionKey.SequenceEnd,
    label: 'Outcome',
    name: 'end',
    qaId: `${SoccerColumnDefinitionKey.SequenceEnd}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        return moment.sequenceEndEvent;
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let value = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        value = moment.sequenceEndEvent;
      }
      return value || '';
    },
    description: 'How the sequence ended.',
  }),
  generateStringColumn({
    key: SoccerColumnDefinitionKey.StartingThird,
    label: 'Start Third',
    name: 'startThird',
    qaId: `${SoccerColumnDefinitionKey.StartingThird}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;

        return moment.sequenceStartingThird;
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let value = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;

        value = moment.sequenceStartingThird;
      }
      return value || '';
    },
    description: 'The third where the sequence started.',
  }),
  generateStringColumn({
    key: SoccerColumnDefinitionKey.EndingThird,
    label: 'End Third',
    name: 'endThird',
    qaId: `${SoccerColumnDefinitionKey.EndingThird}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;

        return moment.sequenceEndingThird;
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let value = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;

        value = moment.sequenceEndingThird;
      }
      return value || '';
    },
    description: 'The third where the sequence ended.',
  }),
  generateSegmentedRangeColumn({
    key: SoccerColumnDefinitionKey.AttackProgression,
    label: 'Progression',
    name: 'progression',
    qaId: `${SoccerColumnDefinitionKey.AttackProgression}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        return moment.sequenceEndingThird;
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        if (moment) {
          // Check if sequence play occured in each thirds
          const segments = ['DEF', 'MID', 'ATK'].map(
            (label): SegmentObject => ({
              label: label,
              value: Boolean(moment.sequenceThirds?.includes(label)),
            })
          );

          return {
            startLabel: '',
            endLabel: '',
            segments,
          };
        }
      }
      return null;
    },
    description: 'The thirds on the pitch where play occurred during this sequence of play.',
  }),
  {
    key: SharedColumnDefinitionKey.AIMoments,
    type: ColumnDefinitionType.MultiValue,
    sortOrderOptions: [SortOrder.Ascending, SortOrder.Descending],
    toSortKey: ({ clip, roster, teamInContext, opponentInContext }) => {
      const momentObjects = generateSoccerMomentObjects(
        clip,
        roster,
        teamInContext,
        opponentInContext,
        undefined,
        undefined,
        true
      );
      return momentObjects?.length > 0 ? momentObjects.length : undefined;
    },
    generateHeader: (sortOrder) => ({
      type: GridHeaderCellType.String,
      label: 'AI Moments',
      qaId: `${SharedColumnDefinitionKey.AIMoments}-column`,
      isSortable: true,
      sortOrder,
    }),
    generateCell: ({
      clip,
      roster,
      teamInContext,
      opponentInContext,
      clipNumber,
      subColumns,
      subCellGenerators,
    }): MultiValueCell => {
      return {
        qaId: `${SharedColumnDefinitionKey.AIMoments}-column-${clipNumber}`,
        type: GridCellType.MultiValue,
        value: generateSoccerMomentObjects(
          clip,
          roster,
          teamInContext,
          opponentInContext,
          subColumns,
          subCellGenerators,
          true
        ),
      };
    },
  },
  generateStringColumn({
    key: SoccerColumnDefinitionKey.ThirdLocation,
    label: 'Third',
    name: 'thirdLocation',
    qaId: `${SoccerColumnDefinitionKey.ThirdLocation}-column`,
    toSortKey: (clip) => {
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        return moment.thirdLocation;
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      let value = undefined;
      if (clip.moments && clip.moments.length) {
        const moment = clip.moments[0] as SoccerTaggingMoment;
        value = moment.thirdLocation;
      }
      return value || '';
    },
    description: 'Which third of the pitch the start of the event occured on',
  }),
  {
    key: SoccerColumnDefinitionKey.ScoutTeamPlayerInvolvement,
    type: ColumnDefinitionType.InvolvedAthletes,
    sortOrderOptions: [SortOrder.Ascending, SortOrder.Descending],
    toSortKey: ({ clip, roster }) => {
      const moments = clip.moments as SoccerTaggingMoment[];
      const players = getInvolvedAthleteJerseyNumbers(moments[0]?.sequenceTeamOnePlayers ?? [], roster);
      return players?.length > 0 ? players.length : undefined;
    },
    generateHeader: ({ sortOrder, teamInContext }) => ({
      type: GridHeaderCellType.String,
      label: teamInContext ? `${teamInContext.schoolAbbreviation} Involvement` : 'Player Involvement',
      qaId: `${SoccerColumnDefinitionKey.ScoutTeamPlayerInvolvement}-column`,
      isSortable: true,
      sortOrder,
    }),
    generateCell: ({ clip, teamInContext, roster, clipNumber, teamTaggingSettings }): IconCell => {
      const moments = clip.moments as SoccerTaggingMoment[];
      const players = getInvolvedAthleteJerseyNumbers(moments[0]?.sequenceTeamOnePlayers ?? [], roster);
      const color = teamTaggingSettings?.find((tts) => tts.teamId === teamInContext?.teamId)?.color;
      return {
        qaId: `${SoccerColumnDefinitionKey.ScoutTeamPlayerInvolvement}-column-${clipNumber}`,
        type: GridCellType.Icon,
        iconType: GridIconType.PlayerInvovlement,
        isDisabled: false,
        iconOptions: {
          playerInvolvementOptions: {
            players: players,
            primaryColor: color ?? teamInContext?.primaryColor,
          },
        },
      };
    },
  },
  {
    key: SoccerColumnDefinitionKey.OpponentTeamPlayerInvolvement,
    type: ColumnDefinitionType.InvolvedAthletes,
    sortOrderOptions: [SortOrder.Ascending, SortOrder.Descending],
    toSortKey: ({ clip, roster }) => {
      const moments = clip.moments as SoccerTaggingMoment[];
      const players = getInvolvedAthleteJerseyNumbers(moments[0]?.sequenceTeamTwoPlayers ?? [], roster);
      return players?.length > 0 ? players.length : undefined;
    },
    generateHeader: ({ sortOrder }) => ({
      type: GridHeaderCellType.String,
      label: 'Opp Involvement',
      qaId: `${SoccerColumnDefinitionKey.OpponentTeamPlayerInvolvement}-column`,
      isSortable: true,
      sortOrder,
    }),
    generateCell: ({ clip, opponentInContext, roster, clipNumber, teamTaggingSettings }): IconCell => {
      const moments = clip.moments as SoccerTaggingMoment[];
      const players = getInvolvedAthleteJerseyNumbers(moments[0]?.sequenceTeamTwoPlayers ?? [], roster);
      const color = teamTaggingSettings?.[1]?.color;
      return {
        qaId: `${SoccerColumnDefinitionKey.OpponentTeamPlayerInvolvement}-column-${clipNumber}`,
        type: GridCellType.Icon,
        iconType: GridIconType.PlayerInvovlement,
        isDisabled: false,
        iconOptions: {
          playerInvolvementOptions: {
            players: players,
            primaryColor: color ?? opponentInContext?.primaryColor,
          },
        },
      };
    },
  },
  generateStringColumn({
    key: SoccerColumnDefinitionKey.SequencePassCount,
    label: 'Passes',
    name: 'passes',
    qaId: `${SoccerColumnDefinitionKey.SequencePassCount}-column`,
    toSortKey: (clip) => {
      if (clip.moments.length) {
        const moments = clip.moments as SoccerTaggingMoment[];
        return parseInt(moments[0]?.sequencePassCount ?? '0', 10);
      }
      return undefined;
    },
    getValue: ({ clip }) => {
      if (clip.moments.length) {
        const moments = clip.moments as SoccerTaggingMoment[];
        return moments[0]?.sequencePassCount?.toString() ?? '';
      }
      return '';
    },
    description: 'The number of passes that occured in the sequence.',
  }),
  generateAthleteColumn({
    key: SoccerColumnDefinitionKey.Athlete,
    isVisible: ({ moment, index }) => {
      const { type } = moment;
      return Boolean(type && index === 1 && !momentTypesWithoutAthleteColumn.includes(type) && isCoachOrAdmin());
    },
    label: 'Athlete',
    name: 'athlete',
    userIdTagKey: TagConstants.PlayerUserId,
    jerseyTagKey: TagConstants.PlayerJersey,
    teamIndexTagKey: TagConstants.Team,
    unknownTagKey: TagConstants.PlayerUnknown,
    filterAthletesByTeam: true,
    alwaysShowJerseys: false,
    additionalJerseyNumberOptions: additionalAthleteOptions,
    qaId: `${SoccerColumnDefinitionKey.Athlete}-column`,
  }),
  generateAthleteColumn({
    key: SoccerColumnDefinitionKey.SecondaryAthlete,
    isVisible: ({ moment, index }) => {
      const { type, result } = moment;
      return Boolean(
        type &&
          result &&
          index === 2 &&
          !momentTypesWithoutAthleteColumn.includes(type) &&
          isCoachOrAdmin() &&
          isSecondaryPlayerEditable(result)
      );
    },
    label: 'Athlete',
    name: 'secondaryAthlete',
    userIdTagKey: new Map<string, string>([
      ['default', SoccerTagConstants.ReceivingPlayerUserId],
      [SoccerConstants.MomentTypes.Foul, SoccerConstants.TagConstants.FouledPlayerUserId],
    ]),
    jerseyTagKey: new Map<string, string>([
      ['default', SoccerTagConstants.ReceivingPlayerJersey],
      [SoccerConstants.MomentTypes.Foul, SoccerConstants.TagConstants.FouledPlayerJersey],
    ]),
    teamIndexTagKey: new Map<string, string>([
      ['default', SoccerTagConstants.ReceivingTeam],
      [SoccerConstants.MomentTypes.Foul, SoccerConstants.TagConstants.FouledTeam],
    ]),
    unknownTagKey: new Map<string, string>([
      ['default', SoccerTagConstants.ReceivingPlayerUnknown],
      [SoccerConstants.MomentTypes.Foul, SoccerConstants.TagConstants.FouledPlayerUnknown],
    ]),
    filterAthletesByTeam: true,
    alwaysShowJerseys: false,
    additionalJerseyNumberOptions: additionalAthleteOptions,
    qaId: `${SoccerColumnDefinitionKey.SecondaryAthlete}-column`,
  }),
];
