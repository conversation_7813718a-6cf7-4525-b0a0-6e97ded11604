import {
  createSoccerMomentLabels,
  formatSoccerAthlete,
  SoccerTaggingMoment,
  VirtualClip,
} from '@hudl/hudl-domain-types';

import { ColumnDefinition, GridAthlete, SubCellGenerators } from '../ColumnDefinition';
import { getSubCellsForMoment } from '../helpers/subCellHelpers';
import { validateSoccerMoment } from './soccerMomentValidator';
import { DisplayableObject } from 'analyze/components/shared/Grid/Grid.types';
import TeamDisplay from 'store/shared/types/TeamDisplay';

export const getAthleteDisplay = (
  moment: SoccerTaggingMoment | undefined | null,
  roster: Map<string, GridAthlete>,
  playerOverride: string | undefined = undefined
): string => {
  if (!moment) return '';

  return formatSoccerAthlete(playerOverride === undefined ? moment.primaryPlayer : playerOverride, roster);
};

const createDisplayableObject = (
  clip: VirtualClip,
  moments: SoccerTaggingMoment[],
  playMoment: SoccerTaggingMoment,
  text: string,
  roster: Map<string, GridAthlete>,
  teamInContext?: TeamDisplay,
  opponentInContext?: TeamDisplay,
  playerOverride?: string | undefined,
  idx?: number,
  subColumns?: ColumnDefinition[],
  subCellGenerators?: SubCellGenerators,
  isAISession: boolean = false
): DisplayableObject => {
  const athleteDisplay = getAthleteDisplay(playMoment, roster, playerOverride);
  const team = playMoment.team === '1' ? teamInContext : opponentInContext;
  const subCells = getSubCellsForMoment(clip, playMoment, subColumns, subCellGenerators, idx);
  return {
    key: [moments.indexOf(playMoment), idx, playMoment.type, playMoment.result, playerOverride, playMoment.id].join(
      '-'
    ),
    id: playMoment.id,
    text: text + athleteDisplay,
    avatar: {
      imageUrl: isAISession ? undefined : team?.imageUrl,
      primaryColor: isAISession ? undefined : team?.primaryColor,
      sport: 'soccer',
    },
    subCells,
    errorMessages: validateSoccerMoment(playMoment),
  };
};

export const generateSoccerMomentObjects = (
  clip: VirtualClip,
  roster: Map<string, GridAthlete>,
  teamInContext?: TeamDisplay,
  opponentInContext?: TeamDisplay,
  subColumns?: ColumnDefinition[],
  subCellGenerators?: SubCellGenerators,
  isAISession?: boolean
): DisplayableObject[] => {
  const moments = clip.moments as SoccerTaggingMoment[];
  const displayableObjects = new Array<DisplayableObject>();

  if (!moments || moments.length === 0) return displayableObjects;
  moments.forEach((moment) => {
    const momentLabels = createSoccerMomentLabels(moment);

    if (moment.type && momentLabels[0]) {
      displayableObjects.push(
        createDisplayableObject(
          clip,
          moments,
          moment,
          momentLabels[0],
          roster,
          teamInContext,
          opponentInContext,
          undefined,
          1,
          subColumns,
          subCellGenerators,
          isAISession
        )
      );
      if (momentLabels.length > 1 && momentLabels[1]) {
        displayableObjects.push(
          createDisplayableObject(
            clip,
            moments,
            moment,
            momentLabels[1],
            roster,
            moment.isSuccessful ? teamInContext : opponentInContext,
            moment.isSuccessful ? opponentInContext : teamInContext,
            moment.secondaryPlayer,
            2,
            subColumns,
            subCellGenerators,
            isAISession
          )
        );
      }
    }
  });
  return displayableObjects;
};
