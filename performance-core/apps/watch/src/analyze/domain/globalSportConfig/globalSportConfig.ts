import { useMemo } from 'react';

import { useSelector } from 'react-redux';

import { Sport, SportClass } from '@hudl/hudl-domain-types';
import {
  ModuleName,
  ModulesObject,
  videoAndStatsAndGridModuleState,
  videoAndStatsAndTimelineModuleState,
  videoAndTimelineModuleState,
} from '@hudl/performance-core-domain';
import { getFeaturePrivilege } from 'frontends-features';
import { FeaturePrivilege } from 'frontends-preload';

import { sportSelector } from 'store/analyze/domain/teams/teamSelectors';
import PlaybackMode from 'store/shared/utils/PlaybackMode';
import getUserTeamId from 'utils/getUserTeamId';

/** DISCLAIMER - We want to minimize sport-specific differences in the product. Please do not introduce sport-specific differences without careful consideration. **/
export interface GlobalSportConfig {
  defaultVideoExperience: VideoExperience;
  availableVideoExperiences: Set<VideoExperience>;
  eliteVideoExperience: VideoExperience;
  availableModules: Set<ModuleName>;
  defaultModuleLayout: ModulesObject;
  segmentName: SegmentName;
  defaultPlaybackMode: PlaybackMode;
  supportsMomentEditing: boolean;
}

/** How we name these segments. This will appear on insights module tooltips. */
export enum SegmentName {
  Plays = 'Plays',
  Pitches = 'Pitches',
  Sequences = 'Sequences',
}

export enum VideoExperience {
  PerformanceCore = 'PerformanceCore',
  V3 = 'V3',
  Classic = 'Classic',
  Balltime = 'Balltime',
}

/** This config will apply if the sport does not define its own. This config will also be used for any "unsupported" sports. */
const DEFAULT_GLOBAL_SPORT_CONFIG: GlobalSportConfig = {
  defaultVideoExperience: VideoExperience.PerformanceCore,
  availableVideoExperiences: new Set([VideoExperience.PerformanceCore]),
  eliteVideoExperience: VideoExperience.V3,
  availableModules: new Set([
    ModuleName.Collaboration,
    ModuleName.Navigation,
    ModuleName.Timeline,
    ModuleName.ClipDetails,
  ]),
  defaultModuleLayout: videoAndTimelineModuleState,
  segmentName: SegmentName.Sequences,
  defaultPlaybackMode: PlaybackMode.NORMAL,
  supportsMomentEditing: true,
};

/** Any sport-specific configs, providing a value here will override the default above. It's not necessary to provide a value if it makes the default. */
const AMFB_SPORT_CONFIG = {
  defaultVideoExperience: VideoExperience.PerformanceCore,
  eliteVideoExperience: VideoExperience.PerformanceCore,
  availableVideoExperiences: new Set([VideoExperience.PerformanceCore, VideoExperience.Classic]),
  availableModules: new Set([ModuleName.Insights, ModuleName.Collaboration, ModuleName.Navigation, ModuleName.Library]),
  defaultModuleLayout: videoAndStatsAndGridModuleState,
  segmentName: SegmentName.Plays,
  defaultPlaybackMode: PlaybackMode.LOOP,
  supportsMomentEditing: false,
};
const BASKETBALL_SPORT_CONFIG = {
  defaultVideoExperience: VideoExperience.V3,
  eliteVideoExperience: VideoExperience.V3,
  availableVideoExperiences: new Set([VideoExperience.PerformanceCore, VideoExperience.V3]),
  defaultModuleLayout: videoAndStatsAndTimelineModuleState,
  availableModules: new Set([
    ModuleName.Insights,
    ModuleName.Collaboration,
    ModuleName.Navigation,
    ModuleName.Filters,
    ModuleName.Timeline,
    ModuleName.ClipDetails,
  ]),
  supportsMomentEditing: false,
};
const BASEBALL_SOFTBALL_SPORT_CONFIG = {
  defaultVideoExperience: VideoExperience.PerformanceCore,
  eliteVideoExperience: VideoExperience.PerformanceCore,
  availableVideoExperiences: new Set([VideoExperience.PerformanceCore]),
  segmentName: SegmentName.Pitches,
  availableModules: new Set([
    ModuleName.Insights,
    ModuleName.Collaboration,
    ModuleName.Navigation,
    ModuleName.Filters,
    ModuleName.Timeline,
    ModuleName.ClipDetails,
  ]),
  defaultModuleLayout: videoAndStatsAndGridModuleState,
  supportsMomentEditing: false,
};
const WRESTLING_SPORT_CONFIG = {
  defaultVideoExperience: VideoExperience.PerformanceCore,
  eliteVideoExperience: VideoExperience.PerformanceCore,
  availableVideoExperiences: new Set([VideoExperience.PerformanceCore]),
  defaultModuleLayout: videoAndStatsAndGridModuleState,
  availableModules: new Set([
    ModuleName.Insights,
    ModuleName.Collaboration,
    ModuleName.Navigation,
    ModuleName.Timeline,
    ModuleName.ClipDetails,
  ]),
  supportsMomentEditing: false,
};
const SOCCER_SPORT_CONFIG = {
  defaultVideoExperience: VideoExperience.V3,
  eliteVideoExperience: VideoExperience.V3,
  availableVideoExperiences: new Set([VideoExperience.PerformanceCore, VideoExperience.V3]),
  defaultModuleLayout: videoAndStatsAndTimelineModuleState,
  availableModules: new Set([
    ModuleName.Insights,
    ModuleName.Collaboration,
    ModuleName.Navigation,
    ModuleName.Filters,
    ModuleName.Timeline,
    ModuleName.ClipDetails,
  ]),
  supportsMomentEditing: true,
};
const VOLLEYBALL_SPORT_CONFIG = {
  defaultVideoExperience: VideoExperience.V3,
  eliteVideoExperience: VideoExperience.V3,
  availableVideoExperiences: new Set([VideoExperience.V3]),
  defaultModuleLayout: videoAndStatsAndTimelineModuleState,
  availableModules: new Set([
    ModuleName.Insights,
    ModuleName.Collaboration,
    ModuleName.Navigation,
    ModuleName.Filters,
    ModuleName.Timeline,
    ModuleName.ClipDetails,
  ]),
  supportsMomentEditing: true,
};
const ICE_HOCKEY_SPORT_CONFIG: GlobalSportConfig = {
  defaultVideoExperience: VideoExperience.V3,
  eliteVideoExperience: VideoExperience.V3,
  availableVideoExperiences: new Set([VideoExperience.V3]),
  availableModules: new Set([
    ModuleName.Collaboration,
    ModuleName.Navigation,
    ModuleName.Filters,
    ModuleName.Timeline,
    ModuleName.ClipDetails,
  ]),
  defaultModuleLayout: videoAndTimelineModuleState,
  segmentName: SegmentName.Sequences,
  defaultPlaybackMode: PlaybackMode.NORMAL,
  supportsMomentEditing: true,
};
const DEFAULT_V3_ONLY_SPORT_CONFIG: GlobalSportConfig = {
  defaultVideoExperience: VideoExperience.V3,
  eliteVideoExperience: VideoExperience.V3,
  availableVideoExperiences: new Set([VideoExperience.V3]),
  availableModules: new Set([
    ModuleName.Collaboration,
    ModuleName.Navigation,
    ModuleName.Timeline,
    ModuleName.ClipDetails,
  ]),
  defaultModuleLayout: videoAndTimelineModuleState,
  segmentName: SegmentName.Sequences,
  defaultPlaybackMode: PlaybackMode.NORMAL,
  supportsMomentEditing: false,
};

export const GLOBAL_SPORT_CONFIG_MAP = new Map([
  [Sport.Football, AMFB_SPORT_CONFIG],
  [Sport.Basketball, BASKETBALL_SPORT_CONFIG],
  [Sport.Baseball, BASEBALL_SOFTBALL_SPORT_CONFIG],
  [Sport.Softball, BASEBALL_SOFTBALL_SPORT_CONFIG],
  [Sport.Wrestling, WRESTLING_SPORT_CONFIG],
  [Sport.Soccer, SOCCER_SPORT_CONFIG],
  [Sport.Volleyball, VOLLEYBALL_SPORT_CONFIG],
  [Sport.IceHockey, ICE_HOCKEY_SPORT_CONFIG],
  [Sport.Lacrosse, DEFAULT_V3_ONLY_SPORT_CONFIG],
  [Sport.Rugby, DEFAULT_V3_ONLY_SPORT_CONFIG],
  [Sport.RugbyLeague, DEFAULT_V3_ONLY_SPORT_CONFIG],
  [Sport.RugbyUnion, DEFAULT_V3_ONLY_SPORT_CONFIG],
  [Sport.AustralianRulesFootball, DEFAULT_V3_ONLY_SPORT_CONFIG],
]);

export const getGlobalSportConfig = (sport: SportClass) => {
  const featurePrivilegeMap = getConfigFeaturePrivilegeMap();
  const sportSpecificConfig = GLOBAL_SPORT_CONFIG_MAP.get(sport);

  if (!sportSpecificConfig) {
    return DEFAULT_GLOBAL_SPORT_CONFIG;
  }

  if (sport === Sport.Volleyball && featurePrivilegeMap[FeaturePrivilege.VolleyballPerformanceCore]) {
    sportSpecificConfig.defaultVideoExperience = VideoExperience.PerformanceCore;
    sportSpecificConfig.availableVideoExperiences = new Set([VideoExperience.PerformanceCore]);
  }
  return {
    ...DEFAULT_GLOBAL_SPORT_CONFIG,
    ...sportSpecificConfig,
  };
};

export function useGlobalSportConfig() {
  const sport = useSelector(sportSelector);
  return useMemo(() => getGlobalSportConfig(sport), [sport]);
}

type ConfigFeaturePrivilegeMap = {
  [key in FeaturePrivilege]?: boolean;
};

export function getConfigFeaturePrivilegeMap(): ConfigFeaturePrivilegeMap {
  const teamId = getUserTeamId();
  return {
    [FeaturePrivilege.VolleyballPerformanceCore]: getFeaturePrivilege(
      teamId,
      FeaturePrivilege.VolleyballPerformanceCore
    ),
  };
}
