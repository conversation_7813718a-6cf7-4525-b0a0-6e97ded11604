import { createSelector } from '@reduxjs/toolkit';

import { RootState } from '../../../rootReducer';
import { Video } from './videoSlice';
import { UnknownVideoTeamId } from 'store/shared/types/TeamDisplay';

export const videoMapSelector = (state: RootState) => state.analyze.domain.video.videoMap;
export const v3VideoIdentifierSummariesSelector = (state: RootState) =>
  state.analyze.domain.video.v3VideoIdentifierSummaries;

export const getVideo = (state: RootState, videoId: string | null | undefined): Video | undefined => {
  if (!videoId) return undefined;

  return videoMapSelector(state)[videoId];
};

export interface GameInfo {
  teamOneId: string;
  teamTwoId: string;
  teamOneColor: string;
  teamTwoColor: string;
  teamOneAbbreviation: string;
  teamTwoAbbreviation: string;
  teamOneName: string;
  teamTwoName: string;
  seasonId: string | null;
}

export const videoIdToGameInfoMapSelector = createSelector([videoMapSelector], (videoMap): Map<string, GameInfo> => {
  const videos = Object.values(videoMap);

  return new Map(videos.map((video) => [video.id, getGameInfoForVideo(video)]));
});

export const getGameInfoForVideo = (video: Video): GameInfo => {
  const gameInfo = video?.gameInfo;
  const teamOne = gameInfo?.teams?.find((t) => t?.teamNumber === '1');
  const teamTwo = gameInfo?.teams?.find((t) => t?.teamNumber === '2');

  return {
    teamOneId: teamOne?.teamId ?? UnknownVideoTeamId,
    teamTwoId: teamTwo?.teamId ?? UnknownVideoTeamId,
    teamOneColor: teamOne?.color ?? '',
    teamTwoColor: teamTwo?.color ?? '',
    teamOneAbbreviation: teamOne?.abbreviation ?? '',
    teamTwoAbbreviation: teamTwo?.abbreviation ?? '',
    teamOneName: teamOne?.teamName ?? '',
    teamTwoName: teamTwo?.teamName ?? '',
    seasonId: gameInfo?.seasonId ?? null,
  };
};

export const v3EventIdToPartitionIdsMapSelector = createSelector(
  [v3VideoIdentifierSummariesSelector],
  (v3VideoIdentifierSummaries): Map<string, string[]> => {
    return v3VideoIdentifierSummaries.reduce((map, videoIdentifierSummary) => {
      const { legacyEventId: partitionId, eventId } = videoIdentifierSummary;
      if (eventId) {
        const encodedEventId = btoa('Event' + eventId);
        map.has(encodedEventId) ? map.get(encodedEventId).push(partitionId) : map.set(encodedEventId, [partitionId]);
      }
      return map;
    }, new Map());
  }
);

export const v3PartitionIdsForVideoNotAttachedToEventsArray = createSelector(
  [v3VideoIdentifierSummariesSelector],
  (v3VideoIdentifierSummaries): string[] => {
    const v3VideosWithoutEvents = v3VideoIdentifierSummaries.filter((vis) => !vis.eventId);
    return v3VideosWithoutEvents.map((v) => v.legacyEventId);
  }
);
