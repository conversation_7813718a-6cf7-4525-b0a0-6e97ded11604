import { HudlTaggingMoment } from '@hudl/hudl-domain-types';

import { transformTaggingSettings } from '../taggingSessionTransformations';
import { isValidMoment } from '../taggingSessionTransformations';
import { mergeOverlappingMoments } from '../taggingSessionTransformations';
import { TaggingSettings } from 'graphqlSrc/api.generated';

describe('transformTaggingSettings()', () => {
  it.each([
    ['true', true],
    ['false', false],
    [undefined, undefined],
    ['unexpected', false],
  ])('handles useHalves metadata value (%s) appropriately', (useHalvesInput, expectedUseHalvesOutput) => {
    const taggingSettings: TaggingSettings = { id: '123', metadata: [], dateUpdated: Date.now() };
    if (useHalvesInput !== undefined) {
      taggingSettings.metadata?.push({ key: 'useHalves', values: [useHalvesInput] });
    }
    const result = transformTaggingSettings(taggingSettings);
    expect(result.useHalves).toBe(expectedUseHalvesOutput);
  });
});

describe('isValidMoment', () => {
  it('should return false for null or undefined moments', () => {
    expect(isValidMoment(null)).toBe(false);
    expect(isValidMoment(undefined)).toBe(false);
  });

  it('should return false for moments with null or negative startTimeMs', () => {
    expect(isValidMoment({ startTimeMs: null })).toBe(false);
    expect(isValidMoment({ startTimeMs: -1 })).toBe(false);
  });

  it('should return true for moments with valid startTimeMs and no endTimeMs', () => {
    expect(isValidMoment({ startTimeMs: 0, endTimeMs: null })).toBe(true);
    expect(isValidMoment({ startTimeMs: 100, endTimeMs: null })).toBe(true);
  });

  it('should return true for moments where startTimeMs is before endTimeMs', () => {
    expect(isValidMoment({ startTimeMs: 100, endTimeMs: 101 })).toBe(true);
  });

  it('should return false for moments where startTimeMs is after to endTimeMs', () => {
    expect(isValidMoment({ startTimeMs: 100, endTimeMs: 99 })).toBe(false);
    expect(isValidMoment({ startTimeMs: 100, endTimeMs: 34 })).toBe(false);
  });

  it('should handle undefined properties correctly', () => {
    expect(isValidMoment({ startTimeMs: 100 })).toBe(true);
    expect(isValidMoment({})).toBe(false);
  });
});

describe('mergeOverlappingMoments', () => {
  it('should merge overlapping moments of the same type', () => {
    const moments = [
      new HudlTaggingMoment('1', 0, 100, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('2', 90, 200, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('3', 210, 300, [{ key: 'type', values: ['Chance'] }], ''),
    ];
    const merged = mergeOverlappingMoments(moments);

    expect(merged).toHaveLength(2);
    expect(merged[0].startTimeMs).toBe(0);
    expect(merged[0].endTimeMs).toBe(200);
    expect(merged[1].startTimeMs).toBe(210);
    expect(merged[1].endTimeMs).toBe(300);
  });

  it('should not get rid of goals or other important moments', () => {
    // this is a specific example we found where several goals got lost to merging
    const moments = [
      new HudlTaggingMoment('1', 388767, 396767, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('2', 389733, 397733, [{ key: 'type', values: ['Goal'] }], ''),
      new HudlTaggingMoment('3', 506500, 514500, [{ key: 'type', values: ['GK_Restart'] }], ''),
      new HudlTaggingMoment('4', 833233, 841233, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('5', 834067, 842067, [{ key: 'type', values: ['Goal'] }], ''),
      new HudlTaggingMoment('6', 870200, 878200, [{ key: 'type', values: ['Kickoff'] }], ''),
      new HudlTaggingMoment('7', 2859100, 2867100, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('8', 2941233, 2949233, [{ key: 'type', values: ['Goal'] }], ''),
      new HudlTaggingMoment('9', 2972800, 2980800, [{ key: 'type', values: ['Kickoff'] }], ''),
      new HudlTaggingMoment('10', 3209967, 3217967, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('12', 3212367, 3220367, [{ key: 'type', values: ['Goal'] }], ''),
      new HudlTaggingMoment('12', 3253567, 3261567, [{ key: 'type', values: ['Kickoff'] }], ''),
      new HudlTaggingMoment('13', 3676500, 3684500, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('14', 3677100, 3685100, [{ key: 'type', values: ['Goal'] }], ''),
      new HudlTaggingMoment('15', 3716133, 3724133, [{ key: 'type', values: ['Kickoff'] }], ''),
    ];
    const merged = mergeOverlappingMoments(moments);
    expect(merged).toHaveLength(15);
  });

  it('should not merge overlapping moments of different types', () => {
    const moments = [
      new HudlTaggingMoment('1', 0, 100, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('2', 90, 200, [{ key: 'type', values: ['Goal'] }], ''),
      new HudlTaggingMoment('3', 190, 300, [{ key: 'type', values: ['Chance'] }], ''),
    ];
    const merged = mergeOverlappingMoments(moments);
    expect(merged).toHaveLength(3);
    expect(merged[0].startTimeMs).toBe(0);
    expect(merged[0].endTimeMs).toBe(100);
    expect(merged[1].startTimeMs).toBe(90);
    expect(merged[1].endTimeMs).toBe(200);
    expect(merged[2].startTimeMs).toBe(190);
    expect(merged[2].endTimeMs).toBe(300);
  });

  it('should return empty array if input is empty', () => {
    expect(mergeOverlappingMoments([])).toEqual([]);
  });

  it('should handle moments with no overlap', () => {
    const moments = [
      new HudlTaggingMoment('1', 0, 50, [{ key: 'type', values: ['Chance'] }], ''),
      new HudlTaggingMoment('2', 60, 100, [{ key: 'type', values: ['Chance'] }], ''),
    ];
    const merged = mergeOverlappingMoments(moments);
    expect(merged).toHaveLength(2);
    expect(merged[0].startTimeMs).toBe(0);
    expect(merged[0].endTimeMs).toBe(50);
    expect(merged[1].startTimeMs).toBe(60);
    expect(merged[1].endTimeMs).toBe(100);
  });
});
