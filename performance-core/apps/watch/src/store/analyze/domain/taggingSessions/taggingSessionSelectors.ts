import { createSelector } from '@reduxjs/toolkit';

import { HudlTaggingMoment } from '@hudl/hudl-domain-types';

import { draftMomentSelector } from '../../app/momentEditing/momentEditingSelectors';
import { videoMapSelector } from '../video/videoSelectors';
import { TaggingSession, TaggingSettings } from './taggingSessionSlice';
import { FEED_TYPES } from 'analyze/components/shared/DetailsModule/ManageData/utils/constants';
import { selectedLibraryContentIdsSelector } from 'store/analyze/app/selectedVideo/selectedVideoBaseSelectors';
import { RootState } from 'store/rootReducer';
import getUserTeamId from 'utils/getUserTeamId';

export const taggingSessionsSelector = (state: RootState): TaggingSession[] =>
  state.analyze.domain.taggingSessions.taggingSessions;

export const activeTaggingSessionsSelector = createSelector(
  taggingSessionsSelector,
  selectedLibraryContentIdsSelector,
  (taggingSessions, selectedItems): TaggingSession[] =>
    taggingSessions.filter((taggingSession) => selectedItems.includes(taggingSession?.videoId))
);

export const containsExchangeGameSelector = createSelector(
  [activeTaggingSessionsSelector, videoMapSelector],
  (taggingSessions, videoMap): boolean => {
    return taggingSessions.some((taggingSession) => {
      const video = videoMap[taggingSession.videoId];
      if (!video) {
        return false;
      }
      const taggingTeamId = taggingSession.taggingSettings?.teamOne?.teamId;
      const gameTeamId = (video?.gameInfo?.teams || []).find((team) => team?.teamNumber === '1')?.teamId;
      if (!taggingTeamId || !gameTeamId) {
        return false;
      }
      return taggingTeamId !== gameTeamId;
    });
  }
);

export const isAIGeneratedSessionsSelector = createSelector(
  activeTaggingSessionsSelector,
  (taggingSessions): boolean => {
    return taggingSessions.some((taggingSession) => taggingSession?.dataSource?.feedType === FEED_TYPES.AI_GENERATED);
  }
);

export const taggingSessionMapSelector = createSelector(
  taggingSessionsSelector,
  (taggingSessions): Map<string, TaggingSession> => {
    return new Map(taggingSessions.map((taggingSession) => [taggingSession.id, taggingSession]));
  }
);
export const videoIdToTaggingSessionSelector = createSelector(
  taggingSessionsSelector,
  (taggingSessions): Map<string, TaggingSession> => {
    return new Map(taggingSessions.map((taggingSession) => [taggingSession.videoId, taggingSession]));
  }
);
export const momentIdToTaggingSessionSelector = createSelector(
  taggingSessionsSelector,
  (taggingSessions): Map<string, TaggingSession> => {
    return new Map(
      taggingSessions.flatMap((taggingSession) => taggingSession.moments.map((moment) => [moment.id, taggingSession]))
    );
  }
);
export const taggingSettingsSelector = createSelector(
  taggingSessionsSelector,
  (taggingSessions): (TaggingSettings | undefined)[] => {
    return taggingSessions.flatMap((taggingSession) => taggingSession.taggingSettings);
  }
);
export const momentsSelector = createSelector(taggingSessionsSelector, (taggingSessions): HudlTaggingMoment[] => {
  return taggingSessions.flatMap((taggingSession) => taggingSession.moments);
});
export const momentsMapSelector = createSelector(momentsSelector, (moments): Map<string, HudlTaggingMoment> => {
  return new Map(moments.map((moment) => [moment.id, moment]));
});

export const removedMomentsSelector = createSelector(
  taggingSessionsSelector,
  (taggingSessions): HudlTaggingMoment[] => {
    return taggingSessions.flatMap((taggingSession) => taggingSession.removedMoments);
  }
);

export const removedMomentsMapSelector = createSelector(
  removedMomentsSelector,
  (moments): Map<string, HudlTaggingMoment> => {
    return new Map(moments.map((moment) => [moment.id, moment]));
  }
);

export const getMoment = (
  state: RootState,
  momentId: string | undefined,
  includeRemovedMoments = false
): HudlTaggingMoment | undefined => {
  if (!momentId) {
    return undefined;
  }

  const moment = momentsMapSelector(state).get(momentId);
  if (moment) {
    return moment;
  }

  const draftMoment = draftMomentSelector(state);
  if (draftMoment && draftMoment.id === momentId) {
    return draftMoment;
  }

  return includeRemovedMoments ? removedMomentsMapSelector(state).get(momentId) : undefined;
};
export const getMoments = (
  state: RootState,
  momentIds: string[],
  includeRemovedMoments = false
): Map<string, HudlTaggingMoment> => {
  const momentIdToMoment = new Map<string, HudlTaggingMoment>();
  momentIds.forEach((momentId) => {
    const moment = getMoment(state, momentId, includeRemovedMoments);
    if (moment) {
      momentIdToMoment.set(momentId, moment);
    }
  });
  return momentIdToMoment;
};
export const getMomentContext = (
  state: RootState,
  momentId: string
): { teamId: string | null; taggingSessionId: string | null; videoId: string | null } => {
  const teamId = getUserTeamId();
  if (!teamId) {
    return { teamId: null, taggingSessionId: null, videoId: null };
  }

  const taggingSession = momentIdToTaggingSessionSelector(state).get(momentId);
  if (!taggingSession) {
    return { teamId: null, taggingSessionId: null, videoId: null };
  }

  return { teamId, taggingSessionId: taggingSession.id, videoId: taggingSession.videoId };
};
