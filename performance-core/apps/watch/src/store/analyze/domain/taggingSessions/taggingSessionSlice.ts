import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import notEmpty from 'shared/utils/notEmpty';
import { trackObjectChangeForMoment } from 'snowplow/SnowplowService';

import {
  BaseballTaggingMoment,
  HudlTaggingMoment,
  isUserIdMomentAthlete,
  MomentAthlete,
  VirtualClip,
} from '@hudl/hudl-domain-types';
import { getCurrentTeamId } from '@hudl/performance-core-domain';
import { format } from 'frontends-i18n';
import { getUserId } from 'frontends-shared';

import { momentEditingModeSelector } from '../../app/momentEditing/momentEditingSelectors';
import { addUserIdsToDraftMoment, MomentEditingMode, setDraftMoment } from '../../app/momentEditing/momentEditingSlice';
import { DRAFT_MOMENT_ID } from '../../app/momentEditing/momentEditingThunks';
import { athleteMapSelector } from '../roster/rosterSelectors';
import { Athlete } from '../roster/rosterSlice';
import { sportSelector } from '../teams/teamSelectors';
import { videoIdToGameInfoMapSelector } from '../video/videoSelectors';
import { copyMoment, getDefaultTags, parseTags, regenerateMoments, setMomentTags } from './taggingSessionHelpers';
import {
  addMomentsMutation,
  createTaggingSessionMutation,
  createTaggingSettingsMutation,
  deleteMomentsMutation,
  MomentInput,
  updateMomentTagsMutation,
  updateTaggingSettingsMutation,
} from './taggingSessionMutations';
import {
  getMomentContext,
  momentsSelector,
  taggingSessionMapSelector,
  videoIdToTaggingSessionSelector,
} from './taggingSessionSelectors';
import { AutofillActions, AutofillValues } from 'analyze/domain/columnDefinitions/autofill';
import { AthleteParseNewValueResult, KeyValue, TagKeyValue } from 'analyze/domain/columnDefinitions/ColumnDefinition';
import { getFirstMoment } from 'analyze/domain/columnDefinitions/helpers/virtualClipHelpers';
import { unfilteredClipsSelector } from 'store/analyze/app/clips/clipsSelectors';
import { addAutofilledValues } from 'store/analyze/ui/grid/gridSlice';
import { showToast } from 'store/analyze/ui/toast/toastSlice';
import { AppThunk, AsyncAppThunk } from 'store/store';
import getUserTeamId from 'utils/getUserTeamId';

export interface TaggingSessionState {
  taggingSessions: TaggingSession[];
}

export const initialState: TaggingSessionState = {
  taggingSessions: [],
};

export interface TaggingSession {
  id: string;
  internalId?: string;
  videoId: string;
  moments: HudlTaggingMoment[];
  removedMoments: HudlTaggingMoment[];
  taggingSessionMetadata?: TaggingSessionMetadata;
  taggingSettings?: TaggingSettings;
  dataSource?: TaggingSessionDataSource;
  presentationViews?: TaggingSessionPresentationView[] | null;
}

export interface TaggingSessionUser {
  id?: string;
  firstName?: string;
  lastName?: string;
  isCoachOrAdmin?: boolean;
  useCoachPrefix?: boolean;
}

export interface TaggingSessionMetadata {
  /**
   * A mapping of team index (the id stored with a moment, e.g., '1' or '2') to team ID.
   */
  teams?: Map<string, string>;
  user?: TaggingSessionUser;
  createdAt?: Date;
  updatedAt?: Date;
}
export interface TaggingSettings {
  id?: string;
  useHalves?: boolean;
  teamOne?: TeamTaggingSettings;
  teamTwo?: TeamTaggingSettings;
  metadata?: TaggingSettingsMetadata[];
}
export interface TaggingSessionDataSource {
  feedType: string;
  importParameters?: TaggingSessionImportParams;
  isDeleted: boolean;
  isValid: boolean;
}
export interface TaggingSessionImportParams {
  fileReferences?: FileReference[];
  user?: TaggingSessionUser;
  bypassBreakdancer?: boolean;
  createdAt?: Date;
}
export interface FileReference {
  key?: string;
  originalFileName?: string;
  serverId?: string;
  fileSize?: number;
  downloadUrl?: string | null;
  updatedAt?: Date;
}
export interface TaggingSessionPresentationView {
  id?: string | null;
  title?: string | null;
}
export interface TaggingSettingsMetadata {
  key: string | null;
  values: Array<string | null>;
}
export interface TeamTaggingSettings {
  teamId?: string;
  roster?: TaggingSettingsPlayerSettings[];
  metadata?: TaggingSettingsPlayerSettingsMetadata[];
  color?: string;
}
export interface TaggingSettingsPlayerSettings {
  userId?: string;
  firstName?: string;
  lastName?: string;
  jersey?: string;
}
export interface TaggingSettingsPlayerSettingsMetadata {
  key?: string;
  values?: string[];
}

export interface UpdateTaggingSessionPayload {
  taggingSessionId: string;
  videoId: string;
  taggingSettings?: TaggingSettings;
  taggingSessionMetadata?: TaggingSessionMetadata;
}
export interface AddTaggingSessionPayload {
  taggingSession: TaggingSession;
  seasonId: string | null;
}
export interface AddMomentPayload {
  taggingSessionId: string;
  moments: HudlTaggingMoment[];
}
export interface RemoveMomentPayload {
  taggingSessionId: string;
  momentIds: string[];
}
export interface InsertMomentPayload {
  taggingSessionId: string;
  index: number;
  moment: HudlTaggingMoment;
}

const updateMomentsCommon = (state: TaggingSessionState, payload: HudlTaggingMoment[]): void => {
  const momentIdsToUpdate = new Set(payload.map((m) => m.id));
  state.taggingSessions = state.taggingSessions.map((taggingSession) => {
    const hasUpdatedMoment = taggingSession.moments.some((m) => momentIdsToUpdate.has(m.id));
    if (hasUpdatedMoment) {
      return {
        ...taggingSession,
        moments: taggingSession.moments.map((moment) => {
          if (momentIdsToUpdate.has(moment.id)) {
            return payload.find((m) => m.id === moment.id) ?? moment;
          }
          return moment;
        }),
      };
    }

    return taggingSession;
  });
};

const taggingSessionSlice = createSlice({
  name: 'taggingSession',
  initialState,
  reducers: {
    addTaggingSession: (state: TaggingSessionState, action: PayloadAction<AddTaggingSessionPayload>) => {
      const existingTaggingSessionIds = new Set(state.taggingSessions.map((taggingSession) => taggingSession.id));
      if (existingTaggingSessionIds.has(action.payload.taggingSession.id)) {
        return;
      }

      state.taggingSessions.push(action.payload.taggingSession);
    },
    addTaggingSessions: (state: TaggingSessionState, action: PayloadAction<Array<AddTaggingSessionPayload>>) => {
      const existingTaggingSessionIds = new Set(state.taggingSessions.map((taggingSession) => taggingSession.id));

      action.payload.forEach(({ taggingSession }) => {
        if (existingTaggingSessionIds.has(taggingSession.id)) {
          return;
        }

        state.taggingSessions.push(taggingSession);
      });
    },
    updateTaggingSession: (state: TaggingSessionState, action: PayloadAction<UpdateTaggingSessionPayload>) => {
      state.taggingSessions = state.taggingSessions.map((taggingSession) => {
        if (taggingSession.id === action.payload.taggingSessionId) {
          return {
            ...taggingSession,
            videoId: action.payload.videoId,
            taggingSessionMetadata: action.payload.taggingSessionMetadata,
            taggingSettings: action.payload.taggingSettings,
          };
        }

        return taggingSession;
      });
    },
    addMoments: (state: TaggingSessionState, action: PayloadAction<AddMomentPayload>) => {
      state.taggingSessions = state.taggingSessions.map((taggingSession) => {
        if (taggingSession.id === action.payload.taggingSessionId) {
          const existingMomentIds = new Set(taggingSession.moments.map((m) => m.id));
          const newMoments = action.payload.moments.filter((moment) => !existingMomentIds.has(moment.id));
          const newMomentIds = new Set(newMoments.map((m) => m.id));

          return {
            ...taggingSession,
            moments: [...taggingSession.moments, ...newMoments],
            removedMoments: taggingSession.removedMoments?.filter((moment) => !newMomentIds.has(moment.id)),
          };
        }

        return taggingSession;
      });
    },
    removeMoments: (state: TaggingSessionState, action: PayloadAction<RemoveMomentPayload>) => {
      const momentIdsToRemove = new Set(action.payload.momentIds);

      const momentsBeingRemoved = state.taggingSessions
        ?.find((taggingSession) => taggingSession.id === action.payload.taggingSessionId)
        ?.moments.filter((moment) => momentIdsToRemove.has(moment.id));

      state.taggingSessions = state.taggingSessions.map((taggingSession) => {
        if (taggingSession.id === action.payload.taggingSessionId) {
          return {
            ...taggingSession,
            moments: taggingSession.moments?.filter((moment) => !momentIdsToRemove.has(moment.id)) ?? [],
            removedMoments: [...(taggingSession.removedMoments ?? []), ...(momentsBeingRemoved ?? [])],
          };
        }

        return taggingSession;
      });
    },
    updateMoment: (state: TaggingSessionState, action: PayloadAction<HudlTaggingMoment>) => {
      updateMomentsCommon(state, [action.payload]);
    },
    updateMoments: (state: TaggingSessionState, action: PayloadAction<HudlTaggingMoment[]>) => {
      updateMomentsCommon(state, action.payload);
    },
    reorderMoments: (state: TaggingSessionState, action: PayloadAction<Map<string, string[]>>) => {
      state.taggingSessions = state.taggingSessions.map((taggingSession) => {
        const newMomentsOrder = action.payload.get(taggingSession.id);
        if (newMomentsOrder) {
          const newMoments = taggingSession.moments.map((moment, index) => {
            const newMoment = taggingSession.moments.find((m) => m.id === newMomentsOrder[index]) ?? moment;
            return newMoment;
          });

          return {
            ...taggingSession,
            moments: newMoments,
          };
        }

        return taggingSession;
      });
    },
    insertMoment: (state: TaggingSessionState, action: PayloadAction<InsertMomentPayload>) => {
      state.taggingSessions = state.taggingSessions.map((taggingSession) => {
        if (taggingSession.id === action.payload.taggingSessionId) {
          const newMoments = taggingSession.moments;
          newMoments.splice(action.payload.index, 0, action.payload.moment);

          return {
            ...taggingSession,
            moments: newMoments,
          };
        }

        return taggingSession;
      });
    },
  },
});

export const {
  addTaggingSession,
  addTaggingSessions,
  updateTaggingSession,
  addMoments,
  updateMoment,
  updateMoments,
  removeMoments,
  reorderMoments,
  insertMoment,
} = taggingSessionSlice.actions;

/** This will refresh any derived tags */
export const recalculateMomentsForVideo =
  (videoId: string): AppThunk =>
  (dispatch, getState) => {
    const sport = sportSelector(getState());
    const allMoments = momentsSelector(getState());
    if (!allMoments?.length) {
      return;
    }

    const momentsToRecalculate = allMoments.filter((moment) => moment.videoId === videoId);
    const recalculatedMoments = regenerateMoments(momentsToRecalculate, sport);
    dispatch(updateMoments(recalculatedMoments));
  };

/** Determines appropriate moments for a video and saves them to a new tagging session */
export const createTaggingSessionFromVirtualClips =
  (videoId: string): AsyncAppThunk<Array<HudlTaggingMoment> | undefined> =>
  async (dispatch, getState) => {
    const teamId = getUserTeamId();
    const sport = sportSelector(getState());
    const unfilteredClips = unfilteredClipsSelector(getState());

    const momentInputs = unfilteredClips
      .filter((clip) => clip.videoId === videoId && !clip.moments?.length)
      .map(
        (clip): MomentInput => ({
          startTimeMs: clip.startTimeMs,
          endTimeMs: clip.endTimeMs,
          tags: getDefaultTags(sport),
        })
      );

    const existingTaggingSession = videoIdToTaggingSessionSelector(getState()).get(videoId);

    // Attempt to add moments to the existing tagging session, instead of creating another one
    if (existingTaggingSession) {
      try {
        const newMoments = await dispatch(
          addMomentsMutation(videoId, teamId, existingTaggingSession.id, momentInputs, sport)
        );
        dispatch(addMoments({ taggingSessionId: existingTaggingSession.id, moments: newMoments }));
      } catch (error) {
        console.error(error);

        dispatch(
          showToast({
            message: format('analyze.module.content.create_tagging_session_error_toast'),
            type: 'error',
          })
        );
      }
      return;
    }

    return await dispatch(createTaggingSessionForMoments(videoId, momentInputs));
  };

/** Creates a new tagging session (and tagging settings) if one does not already exist */
export const createTaggingSessionForMoments =
  (videoId: string, momentInputs: Array<MomentInput>): AsyncAppThunk<Array<HudlTaggingMoment> | undefined> =>
  async (dispatch, getState) => {
    if (!momentInputs?.length) {
      return;
    }

    const teamId = getUserTeamId();
    const userId = getUserId();
    const sport = sportSelector(getState());

    const existingTaggingSession = videoIdToTaggingSessionSelector(getState()).get(videoId);
    if (existingTaggingSession) {
      console.warn('There is an existing tagging session for this video. Aborting request to make another one.');
      return;
    }

    const gameInfo = videoIdToGameInfoMapSelector(getState()).get(videoId);
    if (!gameInfo) {
      console.warn(`Cannot create tagging session without game info. No game info found for video=${videoId}:`);
      return;
    }

    try {
      const teamOneRoster: Athlete[] = [];
      const teamTwoRoster: Athlete[] = [];
      const teamOneColor = gameInfo.teamOneColor;
      const teamTwoColor = gameInfo.teamTwoColor;

      const taggingSettings = await dispatch(
        createTaggingSettingsMutation(
          gameInfo.teamOneId,
          teamOneColor,
          teamOneRoster,
          gameInfo.teamTwoId,
          teamTwoColor,
          teamTwoRoster
        )
      );
      if (!taggingSettings?.id) {
        throw new Error('There was an error creating the tagging settings.');
      }

      const taggingSession = await dispatch(
        createTaggingSessionMutation(videoId, teamId, userId, sport, taggingSettings.id)
      );
      if (!taggingSession?.id) {
        throw new Error('There was an error creating the tagging session.');
      }

      const newMoments = await dispatch(addMomentsMutation(videoId, teamId, taggingSession.id, momentInputs, sport));

      dispatch(
        addTaggingSession({
          taggingSession: {
            ...taggingSession,
            taggingSettings,
            moments: newMoments,
          },
          seasonId: gameInfo?.seasonId ?? null,
        })
      );

      return newMoments;
    } catch (error) {
      console.error(error);

      dispatch(
        showToast({
          message: format('analyze.module.content.create_tagging_session_error_toast'),
          type: 'error',
        })
      );
    }
  };

/** Updates moments with new tags, if a tag is not provided then that tag will not be modified. This will create a tagging session if one does not exist */
export const updateTagsForClip =
  (clip: VirtualClip, tags: Array<TagKeyValue>, momentId?: string): AppThunk =>
  async (dispatch, getState) => {
    let momentToUpdate = getFirstMoment(clip);
    if (momentId) {
      momentToUpdate = clip.moments.find((m) => m.id === momentId);
    }

    // Create Tagging Session and Moments if they don't exist
    if (!momentToUpdate?.id) {
      await dispatch(createTaggingSessionFromVirtualClips(clip.videoId));

      const taggingSession = videoIdToTaggingSessionSelector(getState()).get(clip.videoId);
      momentToUpdate = taggingSession?.moments.find(
        (moment) =>
          moment.videoId === clip.videoId &&
          moment.startTimeMs === clip.startTimeMs &&
          moment.endTimeMs === clip.endTimeMs
      );

      if (!momentToUpdate?.id) {
        console.error('Moment not found');

        dispatch(
          showToast({
            message: format('analyze.shared.navigation.edit_clip_error_toast'),
            type: 'error',
          })
        );
        return;
      }
    }

    const { teamId, taggingSessionId, videoId } = getMomentContext(getState(), momentToUpdate.id);
    if (!teamId || !taggingSessionId || !videoId) {
      dispatch(
        showToast({
          message: format('analyze.shared.navigation.edit_clip_error_toast'),
          type: 'error',
        })
      );
      return;
    }

    const sport = sportSelector(getState());
    const parsedTags = Array.isArray(tags) ? parseTags(tags) : parseTags([tags]);
    const updatedMoment = setMomentTags(momentToUpdate, parsedTags, sport);

    dispatch(updateMoment(updatedMoment));
    dispatch(recalculateMomentsForVideo(videoId));

    try {
      await dispatch(updateMomentTagsMutation(teamId, videoId, taggingSessionId, momentToUpdate.id, parsedTags));
    } catch (e) {
      console.error(e);

      // revert local moments on server failure
      dispatch(updateMoment(momentToUpdate));
      dispatch(recalculateMomentsForVideo(videoId));
      dispatch(
        showToast({
          message: format('analyze.shared.navigation.edit_clip_error_toast'),
          type: 'error',
        })
      );
    }
  };

/** Adds multiple newly tagged athletes to the tagging settings */
export const addAthletesToTaggingSettings =
  (taggingSessionId: string, userIds: Array<string>): AsyncAppThunk =>
  async (dispatch, getState) => {
    const taggingSession = taggingSessionMapSelector(getState()).get(taggingSessionId);
    const taggingSettings = taggingSession?.taggingSettings;
    if (!taggingSettings || !taggingSettings.id || !userIds.length) {
      return;
    }

    const athleteMap = athleteMapSelector(getState());
    const athletes = userIds
      .map((userId) => {
        const athlete = athleteMap.get(userId);
        if (!athlete) return null;

        if (athlete.teamId === taggingSettings?.teamOne?.teamId) {
          return { athlete, teamIndex: '1' as const };
        } else if (athlete.teamId === taggingSettings?.teamTwo?.teamId) {
          return { athlete, teamIndex: '2' as const };
        }

        return null;
      })
      .filter(notEmpty);

    if (!athletes.length) {
      return;
    }

    const teamOneRoster = [...(taggingSettings.teamOne?.roster ?? [])];
    const teamTwoRoster = [...(taggingSettings.teamTwo?.roster ?? [])];
    const taggingSettingUserIds = new Set([...teamOneRoster, ...teamTwoRoster].map((a) => a.userId));

    // Filter out athletes that are already in tagging settings and create new athlete objects
    const newAthletes = athletes
      .filter(({ athlete, teamIndex }) => teamIndex && !taggingSettingUserIds.has(athlete.userId))
      .map(({ athlete, teamIndex }) => ({
        newAthlete: {
          userId: athlete.userId,
          firstName: athlete.firstName,
          lastName: athlete.lastName,
          jersey: athlete.jersey,
        },
        teamIndex,
      }));

    // Only proceed if there were actual changes
    if (!newAthletes?.length) {
      return;
    }

    // Separate athletes by team and add to rosters
    const team1Athletes = newAthletes.filter(({ teamIndex }) => teamIndex === '1');
    const team2Athletes = newAthletes.filter(({ teamIndex }) => teamIndex === '2');

    teamOneRoster.push(...team1Athletes.map(({ newAthlete }) => newAthlete));
    teamTwoRoster.push(...team2Athletes.map(({ newAthlete }) => newAthlete));

    const newTaggingSettings = {
      ...taggingSettings,
      teamOne: {
        ...taggingSettings.teamOne,
        roster: teamOneRoster,
      },
      teamTwo: {
        ...taggingSettings.teamTwo,
        roster: teamTwoRoster,
      },
    };

    dispatch(
      updateTaggingSession({
        taggingSessionId: taggingSession.id,
        videoId: taggingSession.videoId,
        taggingSettings: newTaggingSettings,
        taggingSessionMetadata: taggingSession.taggingSessionMetadata,
      })
    );

    try {
      await dispatch(
        updateTaggingSettingsMutation(
          taggingSettings.id,
          newTaggingSettings.teamOne?.teamId ?? '0',
          newTaggingSettings.teamOne?.color ?? '',
          newTaggingSettings.teamOne?.roster ?? [],
          newTaggingSettings.teamTwo?.teamId ?? '0',
          newTaggingSettings.teamTwo?.color ?? '',
          newTaggingSettings.teamTwo?.roster ?? [],
          newTaggingSettings.metadata ?? []
        )
      );
    } catch (e) {
      console.error(e);

      dispatch(
        updateTaggingSession({
          taggingSessionId: taggingSession.id,
          videoId: taggingSession.videoId,
          taggingSettings: taggingSettings,
          taggingSessionMetadata: taggingSession.taggingSessionMetadata,
        })
      );
    }
  };

/**  Updates tags for next clip in the video that autofill logic was generated for */
export const updateAutofilledTagsForClip =
  (clip: VirtualClip, autofillValues: AutofillValues[]): AppThunk =>
  (dispatch, getState) => {
    const taggingSession = videoIdToTaggingSessionSelector(getState()).get(clip.videoId);
    const moment = getFirstMoment(clip);
    const currentMomentIndex = taggingSession?.moments.findIndex((m) => m.id === moment?.id);

    let nextMomentIndex: number;
    if (
      (currentMomentIndex || currentMomentIndex === 0) &&
      taggingSession?.moments.length &&
      currentMomentIndex !== -1
    ) {
      if (currentMomentIndex !== taggingSession?.moments.length - 1) {
        nextMomentIndex = currentMomentIndex + 1;
      } else {
        return;
      }
    } else {
      return;
    }

    const currentMoment = taggingSession.moments.at(currentMomentIndex) as BaseballTaggingMoment;
    const nextMoment = taggingSession.moments.at(nextMomentIndex) as BaseballTaggingMoment;

    const clipUpdates: Array<KeyValue<string | null>> = [];
    autofillValues.forEach((autofillValue) => {
      let currentMomentValue = currentMoment[autofillValue.momentKey as keyof BaseballTaggingMoment] as number;

      if (currentMomentValue === null) {
        currentMomentValue = 0;
      }

      let updatedValue;
      switch (autofillValue.value) {
        case AutofillActions.Increment:
          updatedValue = currentMomentValue + 1;
          break;
        case AutofillActions.Decrement:
          updatedValue = currentMomentValue - 1;
          break;
        case AutofillActions.Equal:
          updatedValue = currentMomentValue;
          break;
        case AutofillActions.Zero:
        default:
          updatedValue = 0;
          break;
      }

      clipUpdates.push({
        key: autofillValue.momentKey,
        value: updatedValue.toString(),
      });
    });

    if (!clipUpdates.length) {
      return;
    }

    const unfilteredClips = unfilteredClipsSelector(getState());
    const nextClip = unfilteredClips.find((cl) => cl.id === nextMoment.id);
    if (nextClip) {
      dispatch(updateTagsForClip(nextClip, clipUpdates));
      dispatch(
        addAutofilledValues({
          clipId: nextClip.id,
          autofilledColumnKeys: autofillValues.map((val) => val.autofilledColumnKey),
        })
      );
    }
  };

/** Draft moments don't exist on the server, so we can just update it locally until they are saved */
export const updateDraftMomentWithCallback =
  (moment: HudlTaggingMoment, updateMomentCallback: (moment: HudlTaggingMoment) => void): AppThunk =>
  (dispatch, getState) => {
    if (moment.id !== DRAFT_MOMENT_ID) {
      return;
    }

    updateMomentCallback(moment);

    const sport = sportSelector(getState());
    const updatedMoment = copyMoment(moment, sport);
    dispatch(setDraftMoment(updatedMoment));
  };

/** Similar to `updateMomentTagsWithCallback`, however it adds athletes to the tagging settings as well */
export const updateMomentAthleteTagsWithCallback =
  (
    moment: HudlTaggingMoment,
    athletes: Array<MomentAthlete | undefined>,
    updateMomentCallback: (moment: HudlTaggingMoment) => void
  ): AsyncAppThunk =>
  async (dispatch, getState) => {
    const userIds = athletes
      .map((athlete) => (athlete && isUserIdMomentAthlete(athlete) ? athlete.userId : null))
      .filter(notEmpty);

    if (moment.id === DRAFT_MOMENT_ID) {
      if (userIds.length) {
        dispatch(addUserIdsToDraftMoment(userIds));
      }

      dispatch(updateDraftMomentWithCallback(moment, updateMomentCallback));
      return;
    }

    const { taggingSessionId } = getMomentContext(getState(), moment.id);
    if (taggingSessionId && userIds.length) {
      await dispatch(addAthletesToTaggingSettings(taggingSessionId, userIds));
    }

    return await dispatch(updateMomentTagsWithCallback(moment, updateMomentCallback));
  };

export const updateMomentTagsWithCallback =
  (moment: HudlTaggingMoment, updateMomentCallback: (moment: HudlTaggingMoment) => void): AsyncAppThunk =>
  async (dispatch, getState) => {
    if (moment.id === DRAFT_MOMENT_ID) {
      dispatch(updateDraftMomentWithCallback(moment, updateMomentCallback));
      return;
    }

    const { teamId, taggingSessionId, videoId } = getMomentContext(getState(), moment.id);
    if (!teamId || !taggingSessionId || !videoId) {
      throw new Error(`Moment context does not exist ${moment.id}`);
    }

    const sport = sportSelector(getState());
    const updatedMoment = copyMoment(moment, sport);
    updateMomentCallback(updatedMoment);
    const updatedTags = updatedMoment.getUpdatedTags();

    if (!updatedTags.length) {
      return;
    }

    dispatch(updateMoment(updatedMoment));
    dispatch(recalculateMomentsForVideo(videoId));

    const momentEditingMode = momentEditingModeSelector(getState());
    if (momentEditingMode !== MomentEditingMode.Properties) {
      // Other modes only save when they press the "Save" button
      return;
    }

    try {
      await dispatch(updateMomentTagsMutation(teamId, videoId, taggingSessionId, moment.id, updatedTags));
      dispatch(trackObjectChangeForMoment(moment, updatedTags, 'Edit'));
    } catch (e) {
      console.error(e);

      // revert local moments on server failure
      dispatch(updateMoment(moment));
      dispatch(recalculateMomentsForVideo(videoId));
      dispatch(
        showToast({
          // TODO: i18n and copy
          message: 'Unable to update moment',
          type: 'error',
        })
      );
    }
  };

/** Updates specifically athlete tags, this includes logic for updating tagging settings if a new user is added */
export const updateAthleteTagsForClip =
  (clip: VirtualClip, result: AthleteParseNewValueResult, momentId?: string): AsyncAppThunk =>
  async (dispatch, getState) => {
    let taggingSession = videoIdToTaggingSessionSelector(getState()).get(clip.videoId);
    if (!taggingSession) {
      await dispatch(createTaggingSessionFromVirtualClips(clip.videoId));
      taggingSession = videoIdToTaggingSessionSelector(getState()).get(clip.videoId);

      if (!taggingSession) {
        console.error('Unable to create tagging session.');

        dispatch(
          showToast({
            message: format('analyze.shared.navigation.edit_clip_error_toast'),
            type: 'error',
          })
        );
        return;
      }
    }

    if (result.userIdTag?.value) {
      const { value } = result.userIdTag;
      const userIds = Array.isArray(value) ? value : [value];

      if (userIds.length > 0) {
        void dispatch(addAthletesToTaggingSettings(taggingSession.id, userIds));
      }
    }

    const newTags = [
      result.userIdTag,
      result.jerseyTag,
      result.teamIndexTag,
      result.unknownTag,
      result.fullNameTag,
    ].filter(notEmpty);
    dispatch(updateTagsForClip(clip, newTags, momentId));
  };

export const buildTaggingSessionIdForCutup = (cutupId: string): string => `classic-cutup-${cutupId}`;

export const restoreMoments =
  (moments: HudlTaggingMoment[], taggingSessionId: string, videoId: string): AsyncAppThunk =>
  async (dispatch, getState) => {
    const sport = sportSelector(getState());
    const teamId = getCurrentTeamId();
    if (!teamId) {
      return;
    }

    try {
      const addedMoments: HudlTaggingMoment[] = await dispatch(
        addMomentsMutation(videoId, teamId, taggingSessionId, moments, sport)
      );
      dispatch(addMoments({ taggingSessionId, moments: addedMoments }));
      dispatch(recalculateMomentsForVideo(videoId));
    } catch (error) {
      const errorMessage = format('performance-core.moment-details-popover.error_toast.restore', {
        count: moments.length,
      });

      dispatch(
        showToast({
          message: errorMessage,
          actionText: format('performance-core.shared.retry'),
          type: 'error',
          onActionClick: () => {
            void dispatch(restoreMoments(moments, taggingSessionId, videoId));
          },
        })
      );
    }
  };

export const deleteMomentsThunk =
  (moments: HudlTaggingMoment[]): AsyncAppThunk =>
  async (dispatch, getState) => {
    const momentIds = moments.map((moment) => moment.id);

    // Button should not be enabled for derived moments so this is a double-check
    if (moments.some((moment) => moment.isDerived)) {
      return;
    }

    const { teamId, taggingSessionId, videoId } = getMomentContext(getState(), momentIds[0]);
    if (!teamId || !taggingSessionId || !videoId) {
      return;
    }

    try {
      await dispatch(deleteMomentsMutation(videoId, teamId, taggingSessionId, momentIds));
      dispatch(
        removeMoments({
          taggingSessionId: taggingSessionId,
          momentIds: momentIds,
        })
      );

      const countMessage = format('performance-core.moment-details-popover.number-of-moments-deleted', {
        count: momentIds.length,
      });

      dispatch(
        showToast({
          message: countMessage,
          actionText: format('analyze.undo'),
          type: 'success',
          onActionClick: () => {
            void dispatch(restoreMoments(moments, taggingSessionId, videoId));
          },
        })
      );
    } catch (error) {
      const errorMessage = format('performance-core.moment-details-popover.error_toast.delete', {
        count: momentIds.length,
      });

      dispatch(
        showToast({
          message: errorMessage,
          actionText: format('performance-core.shared.retry'),
          type: 'error',
          onActionClick: () => {
            void dispatch(deleteMomentsThunk(moments));
          },
        })
      );
    }
  };

export default taggingSessionSlice.reducer;
