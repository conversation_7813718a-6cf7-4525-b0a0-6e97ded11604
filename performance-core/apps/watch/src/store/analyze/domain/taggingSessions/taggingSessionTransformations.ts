import {
  HudlTaggingDerivedMomentsCalculator,
  HudlTaggingMoment,
  HudlTaggingMomentFactory,
  HudlTaggingTagCalculator,
  SportClass,
} from '@hudl/hudl-domain-types';

import { FEED_TYPES } from '../../../../analyze/components/shared/DetailsModule/ManageData/utils/constants';
import { Video } from '../video/videoSlice';
import {
  FileReference,
  TaggingSession,
  TaggingSessionDataSource,
  TaggingSessionImportParams,
  TaggingSessionMetadata,
  TaggingSessionPresentationView,
  TaggingSessionUser,
  TaggingSettings,
  TaggingSettingsMetadata,
  TaggingSettingsPlayerSettingsMetadata,
  TeamTaggingSettings,
} from './taggingSessionSlice';
import { isTruthy } from 'analyze/utils/isTruthy';
import {
  AddMomentsToTaggingSessionMutation,
  CreateTaggingSessionMutation,
  CreateTaggingSettingsMutation,
  PresentationView,
  TaggingSessionDataSource as TaggingSessionDataSourceResult,
  TaggingSessionImportParams as TaggingSessionImportParamsResult,
  TaggingSettings as TaggingSettingsResult,
  TeamTaggingSettings as TeamTaggingSettingsResult,
  UpdateTaggingSettingsMutation,
} from 'graphqlSrc/api.generated';

type CreateTaggingSettingsMutationResult = CreateTaggingSettingsMutation['createTaggingSettings'];
type UpdateTaggingSettingsMutationResult = UpdateTaggingSettingsMutation['updateTaggingSettings'];
type CreateTaggingSessionMutationResult = CreateTaggingSessionMutation['createTaggingSession'];
type CreateTaggingSessionMetadataResult = NonNullable<CreateTaggingSessionMutationResult>['taggingSessionMetadata'];
type CreateTaggingSessionMomentsResult = NonNullable<CreateTaggingSessionMutationResult>['moments'];
type CreateTaggingSessionMomentItemResult = NonNullable<CreateTaggingSessionMomentsResult>['items'];
type AddMomentsToTaggingSessionResult = AddMomentsToTaggingSessionMutation['addMomentsToTaggingSession'];

export const transformTaggingSession = (
  video: Omit<Video, 'segmentTimings'> | string,
  taggingSession: CreateTaggingSessionMutationResult,
  sport: SportClass
): TaggingSession | undefined => {
  if (!taggingSession) {
    return undefined;
  }

  const taggingSettings: TaggingSettings = transformTaggingSettings(taggingSession.taggingSettings);
  const videoId = typeof video === 'string' ? video : video.id;
  const videoData = typeof video === 'string' ? undefined : video;
  let transformedSession: TaggingSession = {
    id: taggingSession.id,
    internalId: taggingSession.internalId,
    videoId: videoId,
    taggingSessionMetadata: transformTaggingSessionMetadata(taggingSession.taggingSessionMetadata),
    taggingSettings: taggingSettings,
    moments: transformMoments(taggingSession.moments?.items, videoId, sport, taggingSettings, videoData),
    removedMoments: [],
    dataSource: transformTaggingSessionDataSource(taggingSession?.dataSource) ?? undefined,
    presentationViews: transformTaggingSessionPresentationViews(taggingSession?.presentationViews),
  };

  if (transformedSession.dataSource?.feedType === FEED_TYPES.AI_GENERATED) {
    transformedSession = mutateForAiGeneratedSession(transformedSession);
  }

  return transformedSession;
};

enum AIGeneratedMomentTypes {
  CHANCE = 'Chance',
  RESTART = 'Restart',
  GK_RESTART = 'GK Restart',
  KICKOFF = 'Kickoff',
  GOAL = 'Goal',
}

const alphaMoments = Object.values(AIGeneratedMomentTypes) as string[];
const getMomentType = (moment: HudlTaggingMoment): string | undefined => {
  const tagType = moment.tags.find((tag) => tag.key === 'type');
  return tagType?.values[0];
};

/**
 * Temporarily mutates the tagging session for AI-generated sessions by setting the teams to the same team.
 * This is done to remove coach bias around team-related tags, as we are not interested in that question
 * to coaches during our initial alpha phase. This method will be improved as we continue to enhance the
 * quality of the teams on AI-generated tagging sessions.
 *
 * @param taggingSession - The tagging session to be mutated.
 * @returns The mutated tagging session with teams set to the same team.
 */
export const mutateForAiGeneratedSession = (taggingSession: TaggingSession): TaggingSession => {
  for (let incomingMoment of taggingSession.moments) {
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'sequenceEndingThird');
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'sequenceStartingThird');
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'sequenceThirds');
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'sequenceStartEvent');
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'sequenceEndEvent');
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'previousMomentType');
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'nextMomentType');
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'sequencePassCount');
    incomingMoment.tags = incomingMoment.tags.filter((t) => t.key !== 'sequence');
    incomingMoment = changeMomentTypeForAiSession(incomingMoment);
  }
  taggingSession.moments = mergeOverlappingMoments(taggingSession.moments);
  taggingSession.moments = taggingSession.moments.filter((moment) =>
    alphaMoments.includes(getMomentType(moment) ?? '')
  );
  return taggingSession;
};

export const mergeOverlappingMoments = (moments: HudlTaggingMoment[]): HudlTaggingMoment[] => {
  if (moments.length === 0) return [];

  const sortedMoments = moments.sort((a, b) => a.startTimeMs - b.startTimeMs);
  const mergedMoments: HudlTaggingMoment[] = [];
  let currentMoment = sortedMoments[0];
  for (let i = 1; i < sortedMoments.length; i++) {
    const nextMoment = sortedMoments[i];
    const sameType = getMomentType(currentMoment) === getMomentType(nextMoment);
    const overlaps = currentMoment.endTimeMs >= nextMoment.startTimeMs;

    if (overlaps && sameType) {
      // Merge overlapping moments of the same type
      currentMoment.endTimeMs = nextMoment.endTimeMs;
    } else {
      mergedMoments.push(currentMoment);
      currentMoment = nextMoment;
    }
  }
  mergedMoments.push(currentMoment);
  return mergedMoments;
};

const changeMomentTypeForAiSession = (moment: HudlTaggingMoment): HudlTaggingMoment => {
  const tags = moment.tags;
  const tagType = tags.find((tag) => tag.key === 'type');
  const tagSubType = tags.find((tag) => tag.key === 'setPieceType');

  if (tagType && tagType.key === 'type' && tagType.values.length > 0) {
    if (tagType.values[0] === 'setPiece') {
      // this means we should change the type
      if (tagSubType && tagSubType.key === 'setPieceType' && tagSubType.values.length > 0) {
        tagType.values = [getAIGeneratedMomentType(tagSubType.values[0])];
      }
    } else {
      tagType.values = [getAIGeneratedMomentType(tagType.values[0])];
    }
  }

  moment.tags = moment.tags.filter((t) => t.key !== 'setPieceType');
  return moment;
};

const getAIGeneratedMomentType = (momentType: string): string => {
  switch (momentType) {
    case 'corner':
    case 'cross':
    case 'shot':
      return AIGeneratedMomentTypes.CHANCE;
    case 'goal':
      return AIGeneratedMomentTypes.GOAL;
    case 'freeKick':
      return AIGeneratedMomentTypes.RESTART;
    case 'goalKick':
      return AIGeneratedMomentTypes.GK_RESTART;
    case 'kickoff':
    case 'kickOff':
      return AIGeneratedMomentTypes.KICKOFF;
    default:
      return momentType;
  }
};

export const transformMoments = (
  moments: CreateTaggingSessionMomentItemResult | AddMomentsToTaggingSessionResult | undefined,
  videoId: string,
  sport: SportClass,
  taggingSettings: TaggingSettings | undefined = undefined,
  video: Omit<Video, 'segmentTimings'> | undefined = undefined
): HudlTaggingMoment[] => {
  if (!moments?.length || !videoId || !sport) {
    return [];
  }

  const validMoments = moments.filter(isValidMoment);

  if (validMoments.length !== moments.length) {
    const invalidMoments = moments.filter((m) => !isValidMoment(m));
    console.warn('Some moments are invalid and will be filtered out', { invalidMoments });
  }

  const momentFactory = new HudlTaggingMomentFactory();
  const newMoments = validMoments.map((m) => {
    return momentFactory.createMoment(sport, {
      id: m?.id,
      startTimeMs: m?.startTimeMs,
      endTimeMs: m?.endTimeMs,
      videoId: videoId,
      tags: m?.tags,
    });
  });

  const derivedMoments = HudlTaggingDerivedMomentsCalculator.calculate(sport, newMoments, undefined);
  const combinedMoments = newMoments.concat(derivedMoments);
  return HudlTaggingTagCalculator.calculate(sport, combinedMoments, taggingSettings, video);
};

export function isValidMoment(moment?: { startTimeMs?: number | null; endTimeMs?: number | null } | null): boolean {
  if (moment === null || moment === undefined) {
    return false;
  }

  const startTimeMs = moment.startTimeMs ?? null;
  const endTimeMs = moment.endTimeMs ?? null;

  // Moments must have a non-negative startTimeMs
  if (startTimeMs === null || startTimeMs < 0) {
    return false;
  }

  // Allow moments without endTimeMs
  if (endTimeMs === null) {
    return true;
  }

  // Start time must be before end time
  return startTimeMs <= endTimeMs;
}

export const transformTaggingSessionMetadata = (
  metadata?: CreateTaggingSessionMetadataResult | null
): TaggingSessionMetadata => {
  const teams = new Map<string, string>();
  for (const team of metadata?.teams?.filter(isTruthy) ?? []) {
    teams.set(team.index, team.teamName);
  }

  const user: TaggingSessionUser = {
    id: metadata?.user?.id,
    firstName: metadata?.user?.firstName ?? undefined,
    lastName: metadata?.user?.lastName ?? undefined,
    isCoachOrAdmin: metadata?.user?.isCoachOrAdmin,
    useCoachPrefix: metadata?.user?.useCoachPrefix,
  };

  return {
    teams,
    user,
    createdAt: metadata?.createdAt,
    updatedAt: metadata?.updatedAt,
  };
};

export const transformTaggingSessionDataSource = (
  dataSource?: TaggingSessionDataSourceResult
): TaggingSessionDataSource | undefined => {
  if (!dataSource) return undefined;

  return {
    feedType: dataSource.feedType,
    importParameters: transformTaggingSessionImportParams(dataSource.importParameters),
    isDeleted: dataSource.isDeleted,
    isValid: dataSource.isValid,
  };
};

export const transformTaggingSessionImportParams = (
  importParams?: TaggingSessionImportParamsResult | null
): TaggingSessionImportParams | undefined => {
  if (!importParams) return undefined;

  const fileReferences: FileReference[] = (importParams.fileReferences?.filter(isTruthy) ?? []).map((fileRef) => ({
    key: fileRef?.key,
    originalFileName: fileRef?.originalFileName,
    serverId: fileRef?.serverId,
    fileSize: fileRef?.fileSize,
    downloadUrl: fileRef?.downloadUrl,
    updatedAt: fileRef?.updatedAt,
  }));

  const user: TaggingSessionUser = {
    id: importParams?.user?.id,
    firstName: importParams?.user?.firstName ?? undefined,
    lastName: importParams?.user?.lastName ?? undefined,
    isCoachOrAdmin: importParams?.user?.isCoachOrAdmin,
    useCoachPrefix: importParams?.user?.useCoachPrefix,
  };

  return {
    fileReferences: fileReferences,
    user: user,
    bypassBreakdancer: importParams.bypassBreakdancer,
    createdAt: importParams.createdAt,
  };
};

export const transformTaggingSessionPresentationViews = (
  presentationViews?: (PresentationView | null)[] | null
): TaggingSessionPresentationView[] | undefined => {
  if (!presentationViews) return undefined;

  const taggingSessionPresentationViews: TaggingSessionPresentationView[] | undefined = (
    presentationViews.filter(isTruthy) ?? []
  ).map((view) => ({
    id: view?.id,
    title: view?.title,
  }));

  return taggingSessionPresentationViews;
};

export const transformTaggingSettings = (
  taggingSettings: TaggingSettingsResult | CreateTaggingSettingsMutationResult | UpdateTaggingSettingsMutationResult
): TaggingSettings => {
  const metadata: TaggingSettingsMetadata[] | undefined = taggingSettings?.metadata?.map((keyValues) => ({
    key: keyValues?.key ?? null,
    values: keyValues?.values ?? [],
  }));

  const useHalvesSetting = metadata?.find((keyValues) => keyValues?.key === taggingSettingsMetadataKnownKeys.useHalves)
    ?.values?.[0];

  return {
    id: taggingSettings?.id ?? undefined,
    useHalves: useHalvesSetting ? useHalvesSetting.toLowerCase() === 'true' : undefined,
    teamOne: taggingSettings?.teamOne ? transformTeamTaggingSettings(taggingSettings?.teamOne) : undefined,
    teamTwo: taggingSettings?.teamTwo ? transformTeamTaggingSettings(taggingSettings?.teamTwo) : undefined,
    metadata,
  };
};

export const transformTeamTaggingSettings = (
  teamTaggingSettings?: TeamTaggingSettingsResult
): TeamTaggingSettings | undefined => {
  if (!teamTaggingSettings) return undefined;

  const teamTaggingSettingsMetadata: TaggingSettingsPlayerSettingsMetadata[] | undefined =
    teamTaggingSettings?.metadata?.map((keyValues) => ({
      key: keyValues?.key ?? undefined,
      values: keyValues?.values?.filter(isTruthy) ?? [],
    }));

  return {
    teamId: teamTaggingSettings?.teamId ? teamTaggingSettings.teamId : undefined,
    roster: teamTaggingSettings?.roster?.map((athlete) => ({
      firstName: athlete?.firstName ? athlete.firstName : undefined,
      lastName: athlete?.lastName ? athlete.lastName : undefined,
      jersey: athlete?.jersey ? athlete.jersey : undefined,
      userId: athlete?.userId ? athlete.userId : undefined,
    })),
    metadata: teamTaggingSettingsMetadata ? teamTaggingSettingsMetadata : undefined,
    color: teamTaggingSettings?.color ? teamTaggingSettings.color : undefined,
  };
};

/**
 * This doesn't appear to be exposed in any other packages as a constant metadata key, but it could go somewhere more centralized.
 */
const taggingSettingsMetadataKnownKeys = {
  useHalves: 'useHalves',
};
