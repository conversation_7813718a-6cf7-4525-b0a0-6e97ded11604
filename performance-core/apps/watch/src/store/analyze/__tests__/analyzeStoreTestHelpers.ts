import {
  Classic<PERSON>lip,
  Classic<PERSON>lip<PERSON>ngle,
  ClassicColumn,
  Cutup,
  FootballTaggingMoment,
  HudlTaggingMoment,
  Sport,
  TreeCategory,
  TreeCategoryType,
} from '@hudl/hudl-domain-types';
import {
  blankModuleState,
  CardGroupKey,
  CardKey,
  ContextTypes,
  Filter,
  ModuleName,
  TimelineModes,
} from '@hudl/performance-core-domain';
import { LibraryFilter, LibraryFilterType, LibraryVideoType } from '@hudl/performance-core-domain';
import { AddNewEventModalSaveState } from '@hudl/schedules-edit';

import { RootState } from '../../rootReducer';
import {
  LibraryItemListToggleMode,
  LibraryValidSortDirections,
  LibraryValidSortKeys,
} from '../app/libraryModule/libraryEnums';
import { MomentEditingMode, MomentEditingSaveState } from '../app/momentEditing/momentEditingSlice';
import {
  PermissionLevel,
  ShareModalFeatures,
  ShareModalLoadState,
  ShareModalPageType,
} from '../app/shareModal/shareModalTypes';
import { GeneratedContentItem } from '../domain/aiGenerations/requests/apiRequests';
import { buildEffectClipKey } from '../domain/classicData/classicDataSelectors';
import { MobilePageType } from '../ui/modules/moduleSlice';
import { GridMode } from 'analyze/components/shared/Grid/Grid.types';
import { SprayChartMode } from 'analyze/components/shared/InsightsModule/visualizations/SprayChart/SprayChart.types';
import ClassicColumnValue from 'analyze/domain/football/ClassicColumnValue';
import { ApiVideoItem } from 'store/shared/types/ApiVideoItem';
import GameType from 'store/shared/types/GameType';
import { LibraryVideo } from 'store/shared/types/libraryModule/LibraryVideo';
import ScheduleEntryDisplay from 'store/shared/types/ScheduleEntryDisplay';
import TeamDisplay from 'store/shared/types/TeamDisplay';

/*
This is defined manually instead of referencing the exported initial state due to the slices importing a large
amount of dependencies, which causes problems in other tests with mocking.  Two options are defining manually or
separating initial state into a new file.  For now we are going with defining manually to keep the slices
truly one file.

Note that not all state needs to be defined
*/
export function getAnalyzeInitialState(): RootState['analyze'] {
  return {
    app: {
      labelTree: {
        labelInput: undefined,
        deleteLabelModal: {
          isOpen: false,
          labelData: undefined,
        },
      },
      moveSessionModal: {
        isOpen: false,
      },
      manageDataModal: {
        isOpen: false,
        libraryItem: null,
        uploadButtonStatus: '',
      },
      scheduleEntry: {
        opponentSearchResults: [],
        areOpponentSearchResultsLoading: false,
        saveState: AddNewEventModalSaveState.None,
      },
      shareModal: {
        isOpen: false,
        pageType: ShareModalPageType.Share,
        loadState: ShareModalLoadState.None,
        enabledFeatures: [ShareModalFeatures.ShareContent],
        shareForm: {
          content: undefined,
          permissionLevel: PermissionLevel.View,
          selectedValues: [],
          hasUpdatedPermissions: false,
          shouldNotify: true,
        },
        shareableCreationForm: {
          title: '',
          selectedValues: [],
          trigger: undefined,
        },
      },
      filter: { clipFilters: new Array<Filter>() },
      selectedVideo: {
        selectedLibraryContentIds: [],
        isSwitchingAngle: false,
        selectedAngleNames: [],
        selectedShareableId: null,
      },
      appContext: {
        isOwnTeamContext: false,
        areExperimentalFeaturesEnabled: null,
        specificTaggingSessionId: null,
        isAutomatedTest: null,
      },
      dataLoader: {
        hasInitialLoadForVideoCompleted: false,
      },
      libraryModule: {
        searchResults: [],
        selectedItems: [],
        hasNextPage: false,
        endCursor: null,
        totalCount: 0,
        firstPageIsLoading: false,
        pageIsLoading: false,
        filters: new Map<LibraryFilterType, LibraryFilter[]>(),
        fetchRecentlyDeletedContent: false,
        fetchItemsNotAttachedToEvents: false,
        searchText: '',
        allowedSorts: [],
        sortKey: LibraryValidSortKeys.CreatedAt,
        sortDirection: LibraryValidSortDirections.Descending,
        itemsDisplayMode: LibraryItemListToggleMode.Grid,
        downloadModal: {
          isOpen: false,
          videoIds: [],
          playlistIds: [],
          mediaStreams: [],
        },
        anchoredSelectedLibraryContentId: undefined,
        bulkItemActionsLoading: false,
        isSuggestedContentSectionExpanded: true,
        isReviewAndDeleteState: false,
      },
      detailsModule: {},
      videoExperience: {
        isVideoExperienceModalOpen: false,
        isMultipleVideosModalOpen: false,
      },
      clipForm: {
        clipId: null,
        values: new Map<string, ClassicColumnValue>(),
        clipToAutofilledColumns: new Map<string, string[]>(),
      },
      clips: {
        clipMomentsMap: new Map<string, HudlTaggingMoment[]>(),
        activeClipIndex: undefined,
        latestActiveClipIndex: 0,
        focusedClipIndex: undefined,
      },
      libraryContent: {
        libraryContentId: null,
        isRenamingLibraryContent: false,
        restorableLibraryContent: new Map<string, boolean>(),
      },
      insightsModule: {
        hiddenCardGroups: new Map<CardGroupKey, boolean>(),
        selectedReportTab: null,
        expandedCard: null,
        isExpandedCardFull: null,
        isPrinting: false,
        customFilteringCards: [],
        sprayChartMode: SprayChartMode.Lines,
        filterTeamId: null,
      },
      userPreferences: {
        userPreferences: {},
        userPreferencesAreLoading: true,
      },
      onboarding: {
        openOnboardingKey: null,
      },
      groupBy: {
        groupByConfigurations: new Map<CardKey, CardKey[][]>(),
      },
      playback: {
        temporaryPlaybackMode: undefined,
        isClipPreviewCollapsed: false,
      },
      commentUI: {
        isViewingConversation: false,
      },
      exportModal: {
        isOpen: false,
      },
      editClip: {
        isOpen: false,
        canSave: true,
        title: null,
        comment: null,
        fullSource: null,
        selectedPlaylists: [],
        selectedHighlights: [],
        unsavedPlaylists: [],
        submissionInfo: null,
        clipInContext: null,
      },
      timeline: {
        timelineVisibleCenterMs: undefined,
        timelineVisibleLengthMs: undefined,
        selectionStartTimeMs: undefined,
        selectionEndTimeMs: undefined,
        selectionEditState: undefined,
        timeSpans: [],
        timelineMode: TimelineModes.Playback,
        selectedIds: [],
        expandedIds: [],
        contextMenu: {
          path: null,
          position: { x: 0, y: 0 },
          data: undefined,
        },
      },
      deleteSessionModal: {
        isOpen: false,
      },
      momentEditing: {
        momentEditingMode: MomentEditingMode.Properties,
        saveState: MomentEditingSaveState.None,
        draftMoment: null,
        draftUserIds: [],
        previousMomentId: null,
      },
    },
    domain: {
      ai: {
        generatedResponsesVideoMap: new Map<string, GeneratedContentItem[]>(),
      },
      reportTemplates: {
        storedTemplates: [],
        reportsDefinitions: [],
      },
      taggingSessions: {
        taggingSessions: [],
      },
      classicData: {
        cutups: new Map<string, Cutup>(),
        clips: new Map<string, ClassicClip>(),
        isClassicCutupFromAssist: new Map<string, boolean>(),
        effectClipMap: new Map<string, ClassicClip>(),
        categories: new Map<string, TreeCategory>(),
        columns: new Map<string, ClassicColumn>(),
        seasonCategoryIds: [],
        categoriesAreLoading: true,
        clipsAreLoading: true,
        columnsAreLoading: true,
        libraryTreeIsLoading: true,
        oldPracticeCutups: [],
      },
      storage: {
        storageInfo: null,
        isLoadingStorageInfo: false,
        isStorageInfoExpanded: false,
        isFreeUpSpaceModalVisible: false,
        isLoadingStorageSuggestionsInfo: false,
        videoIdentifierSummariesForStorageSuggestions: null,
      },
      teams: {
        allSeasonTeams: null,
        sport: Sport.Football,
        ownTeamInfo: null,
        assistAccessLevel: null,
      },
      rosters: {
        seasonToRoster: {},
        athletes: [],
      },
      configurations: {
        isInsightsModuleConfigLoading: true,
        insightsModuleConfiguration: null,
        isInsightsModuleConfigLoadError: false,
        dataFieldConfiguration: null,
        isDataFieldConfigLoading: true,
        isDataFieldConfigLoadError: false,
        isEditingDataFieldConfiguration: false,
        onboardingConfiguration: null,
        isOnboardingConfigLoading: true,
        isOnboardingConfigLoadError: false,
        savedFilterConfigurations: new Map(),
        libraryFiltersConfiguration: null,
        isLibraryFiltersConfigLoading: true,
        isLibraryFiltersConfigLoadError: false,
        dismissedSuggestedContentConfiguration: null,
        isDismissedSuggestedContentLoading: true,
        isDismissedSuggestedContentError: false,
        layoutUIConfiguration: null,
        isLayoutUIConfigurationLoadError: false,
      },
      video: {
        videoMap: {},
        v3VideoIdentifierSummaries: [],
      },
      libraryItems: {
        videos: new Map<string, ApiVideoItem>(),
        isLoadingVideos: false,
        teamsWithContentLoaded: new Set<string>(),
        isBulkDeletionModalVisible: false,
      },
      shareables: {
        isShareablesLoading: false,
        shareables: new Map(),
        isCreatingShareableForConversation: false,
      },
      comments: {
        isLoading: false,
        videoToCommentThread: new Map(),
        commentThreads: new Map(),
        comments: new Map(),
        selectedCommentFromUrl: null,
        deletedComments: new Map(),
        pollingHaltedByServer: false,
        polledCommentThreads: new Map(),
        threadNewMessageCount: new Map(),
        isViewingReplies: false,
      },
    },
    // FIXME remove this ts-ignore. This is creating incomplete state. We should consider initializing the store with
    // its default state instead and editing as necessary instead of trying to manually mock it out.
    // @ts-expect-error -- Legacy code, needs review
    ui: {
      // Copied from contextSlice
      context: {
        type: ContextTypes.Personal,
        id: null,
      },
      modules: {
        modules: blankModuleState,
        moduleSnapshot: blankModuleState,
        moduleHistorySnapshot: [],
        mobilePageType: MobilePageType.LibraryItemView,
        mobileModule: ModuleName.Library,
        moduleHistory: [],
      },
      grid: {
        selectedClipIds: [],
        anchorSelectedClipId: undefined,
        mode: GridMode.Edit,
        selectedFieldIndex: undefined,
        carriageReturn: undefined,
        columnSortOrder: undefined,
        autofilledValues: [],
        temporaryNewClipIndex: undefined,
      },
    },
  };
}

export function createClip(clipId: string): ClassicClip {
  const clipAngle: ClassicClipAngle = {
    angleId: 1,
    files: [
      {
        quality: 1,
        fileName: 'asdf',
        status: 0,
      },
    ],
    angleName: 'wide',
    duration: 5000,
    snapTime: 2000,
    snapConfidence: 1,
    largeThumbnailFileName: 'asdf',
  };
  return new ClassicClip(clipId, [], [clipAngle], []);
}

export function createCutup(cutupId: string, clipIds: string[]): Cutup {
  const cutup = new Cutup(cutupId, '', '', 0);
  cutup.clipIds = clipIds;
  return cutup;
}

export function createTreeCategory(
  categoryId: string,
  parentCategoryId: string | undefined,
  subcategoryIds: string[],
  type: TreeCategoryType = TreeCategoryType.Game,
  cutupIds: string[] = []
): TreeCategory {
  const treeCategory = new TreeCategory(categoryId, categoryId, type);
  treeCategory.parentCategoryId = parentCategoryId;
  treeCategory.subcategoryIds = subcategoryIds;
  treeCategory.cutupIds = cutupIds;
  return treeCategory;
}

export function createTeamDisplay(
  gameIds: string[],
  cutupIds: string[][],
  playlistIds: string[][],
  gameDates: Date[],
  categoryIds: string[]
): TeamDisplay {
  const teamDisplay: TeamDisplay = {
    teamId: '',
    imageUrl: '',
    primaryColor: '',
    schoolAbbreviation: '',
    schoolName: '',
    secondaryColor: '',
    teamName: '',
    games: [],
    dateOfNearestEvent: new Date('01-01-2022 12:00:00'),
  };

  gameIds.forEach((gameId, index) => {
    const newGame: ScheduleEntryDisplay = {
      gameId: gameId,
      gameType: GameType.PostSeason,
      hasScore: true,
      isHome: true,
      isTie: false,
      isWin: true,
      score1: 5,
      score2: 2,
      seasonId: '1234',
      categoryId: categoryIds[index],
      location: '',
      localDateTime: '',
      localDate: gameDates[index],
      videos: null,
      playlists: null,
    };
    const cutupIdsForGame = cutupIds[index];
    cutupIdsForGame?.forEach((cutupId) => {
      if (!newGame.videos) {
        newGame.videos = [];
      }
      newGame.videos.push({
        id: cutupId,
        title: cutupId,
        videoType: LibraryVideoType.Game,
        updatedAt: new Date(),
      } as LibraryVideo);
    });

    const playlistIdsForGame = playlistIds[index];
    playlistIdsForGame?.forEach((cutupId) => {
      if (!newGame.playlists) {
        newGame.playlists = [];
      }
      newGame.playlists.push({
        id: cutupId,
        title: cutupId,
        updatedAt: new Date(),
        clipCount: 5,
      });
    });

    teamDisplay.games.push(newGame);
  });

  return teamDisplay;
}

export function setupMomentWithinSelectedCutup(state: RootState, moment: HudlTaggingMoment): void {
  const cutupId = '1234';
  state.analyze.domain.taggingSessions.taggingSessions = [
    {
      videoId: cutupId,
      id: cutupId,
      moments: [moment],
      removedMoments: [],
    },
  ];

  const footballMoment = moment as FootballTaggingMoment;
  footballMoment.classicClipId = moment.id;
  const clip = createClip(moment.id);
  state.analyze.domain.classicData.clips.set(clip.id, clip);
  state.analyze.domain.classicData.effectClipMap.set(buildEffectClipKey(cutupId, clip.id), clip);

  const cutup = createCutup(cutupId, [moment.id]);
  state.analyze.domain.classicData.cutups.set(cutupId, cutup);

  state.analyze.app.selectedVideo.selectedLibraryContentIds.push(cutupId);
}
