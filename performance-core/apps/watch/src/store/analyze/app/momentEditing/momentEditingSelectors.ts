import { createSelector } from '@reduxjs/toolkit';

import { HudlTaggingMoment } from '@hudl/hudl-domain-types';

import { RootState } from '../../../rootReducer';
import { MomentEditingMode, MomentEditingSaveState } from './momentEditingSlice';

export const momentEditingModeSelector = (state: RootState): MomentEditingMode =>
  state.analyze.app.momentEditing.momentEditingMode;
export const isEditingMomentBoundariesSelector = createSelector(momentEditingModeSelector, (momentEditingMode) => {
  return momentEditingMode === MomentEditingMode.Boundaries || momentEditingMode === MomentEditingMode.MomentCreation;
});

export const momentEditingSaveStateSelector = (state: RootState): MomentEditingSaveState =>
  state.analyze.app.momentEditing.saveState;
export const draftMomentSelector = (state: RootState): HudlTaggingMoment | null =>
  state.analyze.app.momentEditing.draftMoment;
export const draftUserIdsSelector = (state: RootState): string[] => state.analyze.app.momentEditing.draftUserIds;
export const previousMomentIdSelector = (state: RootState): string | null =>
  state.analyze.app.momentEditing.previousMomentId;
