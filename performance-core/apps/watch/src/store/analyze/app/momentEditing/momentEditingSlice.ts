import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { HudlTaggingMoment } from '@hudl/hudl-domain-types';

export interface MomentEditingState {
  momentEditingMode: MomentEditingMode;
  saveState: MomentEditingSaveState;
  /** A "Draft" moment represents a moment that is currently being created. */
  draftMoment: HudlTaggingMoment | null;
  draftUserIds: string[];
  previousMomentId: string | null;
}

export enum MomentEditingMode {
  Properties = 'properties',
  MomentCreation = 'moment-creation',
  Boundaries = 'boundaries',
}
export enum MomentEditingSaveState {
  None = 'none',
  Loading = 'loading',
  Success = 'success',
  Error = 'error',
}

const initialState: MomentEditingState = {
  momentEditingMode: MomentEditingMode.Properties,
  saveState: MomentEditingSaveState.None,
  draftMoment: null,
  draftUserIds: [],
  previousMomentId: null,
};

const momentEditing = createSlice({
  name: 'momentEditing',
  initialState,
  reducers: {
    setMomentEditingMode: (state: MomentEditingState, action: PayloadAction<MomentEditingMode>) => {
      state.momentEditingMode = action.payload;
    },
    setMomentEditingSaveState: (state: MomentEditingState, action: PayloadAction<MomentEditingSaveState>) => {
      state.saveState = action.payload;
    },
    setDraftMoment: (state: MomentEditingState, action: PayloadAction<HudlTaggingMoment>) => {
      state.draftMoment = action.payload;
    },
    addUserIdsToDraftMoment: (state: MomentEditingState, action: PayloadAction<string[]>) => {
      const uniqueUserIds = Array.from(new Set([...state.draftUserIds, ...action.payload]));
      state.draftUserIds = uniqueUserIds;
    },
    clearDraftMoment: (state: MomentEditingState) => {
      state.draftMoment = null;
      state.draftUserIds = [];
    },
    rememberPreviousMomentId: (state: MomentEditingState, action: PayloadAction<string | null>) => {
      state.previousMomentId = action.payload;
    },
  },
});

export const {
  setMomentEditingMode,
  setMomentEditingSaveState,
  rememberPreviousMomentId,
  setDraftMoment,
  addUserIdsToDraftMoment,
  clearDraftMoment,
} = momentEditing.actions;

export default momentEditing.reducer;
