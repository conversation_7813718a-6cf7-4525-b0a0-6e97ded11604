import {
  HudlTaggingMoment,
  HudlTaggingMomentClassMetadata,
  HudlTaggingMomentFactory,
  isV3VideoVirtualClip,
  TagConstants,
} from '@hudl/hudl-domain-types';
import { getCurrentTeamId, getTimelineConfig, TimelineModes } from '@hudl/performance-core-domain';
import { format } from 'frontends-i18n';

import { notEmpty } from '../../../../utils/notEmpty';
import { sleep } from '../../../../utils/sleepUtils';
import { AppThunk, AsyncAppThunk } from '../../../store';
import { copyMoment, DEFAULT_TIMELINE_NEW_MOMENT_SOURCE } from '../../domain/taggingSessions/taggingSessionHelpers';
import {
  addMomentsMutation,
  MomentInput,
  updateMomentMutation,
} from '../../domain/taggingSessions/taggingSessionMutations';
import {
  getMomentContext,
  videoIdToTaggingSessionSelector,
} from '../../domain/taggingSessions/taggingSessionSelectors';
import {
  addAthletesToTaggingSettings,
  addMoments,
  createTaggingSessionForMoments,
  recalculateMomentsForVideo,
  updateMoment,
} from '../../domain/taggingSessions/taggingSessionSlice';
import { sportSelector } from '../../domain/teams/teamSelectors';
import { videoMapSelector } from '../../domain/video/videoSelectors';
import { applyMomentBoundaryEditingLayout } from '../../ui/layout/layoutThunks';
import { restoreModuleSnapshot, takeModuleSnapshot } from '../../ui/modules/moduleSlice';
import { showToast } from '../../ui/toast/toastSlice';
import { activeClipsSelector, focusedClipSelector, latestActiveClipSelector } from '../clips/clipsSelectors';
import { videoStateSelector } from '../dataLoader/dataLoaderSelectors';
import { validateClipBounds } from '../editClips/editClipHelpers';
import { clearAllFilters } from '../filters/filtersThunks';
import { setFocusedClipIndexFromMomentId } from '../listModule/listModuleThunks';
import { getTrueVideoTime } from '../playback/playbackStyleThunks';
import {
  timelineSelectionEndTimeMsSelector,
  timelineSelectionStartTimeMsSelector,
} from '../timeline/timelineSelectors';
import {
  clearTimelineSelection,
  setTimelineMode,
  setTimelineSelectedIds,
  setTimelineSelectionEndTimeMs,
  setTimelineSelectionStartTimeMs,
  setTimelineVisibleStartAndEndTimeMs,
} from '../timeline/timelineSlice';
import { draftUserIdsSelector, momentEditingModeSelector } from './momentEditingSelectors';
import {
  clearDraftMoment,
  MomentEditingMode,
  MomentEditingSaveState,
  rememberPreviousMomentId,
  setDraftMoment,
  setMomentEditingMode,
  setMomentEditingSaveState,
} from './momentEditingSlice';

export const DRAFT_MOMENT_ID = 'TEMPORARY-MOMENT-ID';
const NEW_MOMENT_DURATION_MS = 5000;
const SUCCESS_DELAY_MS = 800;
const ERROR_DELAY_MS = 3000;

export const changeMomentEditingMode =
  (moment: HudlTaggingMoment | null, newMode: MomentEditingMode): AppThunk =>
  (dispatch, getState) => {
    const existingMode = momentEditingModeSelector(getState());
    if (existingMode === newMode) {
      return;
    }

    // Reset any moment boundary editing state
    if (newMode === MomentEditingMode.Properties) {
      dispatch(clearDraftMoment());
      dispatch(setTimelineMode(TimelineModes.Playback));
      dispatch(setMomentEditingMode(MomentEditingMode.Properties));
      dispatch(setMomentEditingSaveState(MomentEditingSaveState.None));
      dispatch(clearTimelineSelection());
      dispatch(restoreModuleSnapshot());
      dispatch(setTimelineSelectedIds([]));
      dispatch(setFocusedClipIndexFromMomentId(moment?.id ?? null));
      return;
    }

    if (existingMode === MomentEditingMode.MomentCreation && newMode === MomentEditingMode.Boundaries) {
      // Don't allow switching to boundaries mode from moment creation mode
      return;
    }

    const timeBoundaries = moment && HudlTaggingMomentClassMetadata.getTimeBoundaries(moment);
    if (!timeBoundaries) {
      console.warn('Could not get time boundaries for moment');
      return;
    }

    const clipStartTime = timeBoundaries.startTimeMs;
    const clipEndTime = timeBoundaries.endTimeMs;
    const selectionLength = clipEndTime - clipStartTime;

    const sport = sportSelector(getState());
    const { initialSelectionZoomPercentage } = getTimelineConfig(sport);
    const duration = videoStateSelector(getState()).durationMs;

    dispatch(setTimelineMode(TimelineModes.Edit));
    dispatch(setTimelineSelectionStartTimeMs(clipStartTime));
    dispatch(setTimelineSelectionEndTimeMs(clipEndTime));
    dispatch(
      setTimelineVisibleStartAndEndTimeMs({
        start: Math.max(clipStartTime - selectionLength * initialSelectionZoomPercentage, 0),
        end: Math.min(clipEndTime + selectionLength * initialSelectionZoomPercentage, duration),
      })
    );
    dispatch(setMomentEditingMode(newMode));
    dispatch(takeModuleSnapshot());
    dispatch(applyMomentBoundaryEditingLayout({ preserveFloatingModules: true }));
    dispatch(setFocusedClipIndexFromMomentId(moment.id));
  };

export const updateMomentBoundaries =
  (moment: HudlTaggingMoment): AsyncAppThunk =>
  async (dispatch, getState) => {
    const startTimeMs = timelineSelectionStartTimeMsSelector(getState());
    const endTimeMs = timelineSelectionEndTimeMsSelector(getState());

    if (startTimeMs === undefined || endTimeMs === undefined || startTimeMs > endTimeMs) {
      return;
    }

    const { teamId, taggingSessionId, videoId } = getMomentContext(getState(), moment.id);
    if (!teamId || !taggingSessionId || !videoId) {
      throw new Error(`Moment context does not exist ${moment.id}`);
    }

    const sport = sportSelector(getState());
    const updatedMoment = copyMoment(moment, sport);

    HudlTaggingMomentClassMetadata.setTimeBoundaries(updatedMoment, {
      startTimeMs: parseFloat(startTimeMs.toFixed(0)),
      endTimeMs: parseFloat(endTimeMs.toFixed(0)),
    });

    dispatch(setMomentEditingSaveState(MomentEditingSaveState.Loading));
    try {
      await dispatch(updateMomentMutation(videoId, teamId, taggingSessionId, [updatedMoment]));
      dispatch(setMomentEditingSaveState(MomentEditingSaveState.Success));
      dispatch(updateMoment(updatedMoment));
      dispatch(recalculateMomentsForVideo(videoId));

      // Wait so that the user sees that it was successful
      await sleep(SUCCESS_DELAY_MS);

      dispatch(changeMomentEditingMode(updatedMoment, MomentEditingMode.Properties));
    } catch (e) {
      console.error(e);
      dispatch(setMomentEditingSaveState(MomentEditingSaveState.Error));

      setTimeout(() => {
        dispatch(setMomentEditingSaveState(MomentEditingSaveState.None));
      }, ERROR_DELAY_MS);
    }
  };

export const startCreatingNewMoment = (): AppThunk => (dispatch, getState) => {
  const focusedClip = focusedClipSelector(getState());
  const previousMomentId = focusedClip?.moments[0]?.id ?? null;

  // Remember the last moment they had selected, in-case they cancel out of this flow
  dispatch(rememberPreviousMomentId(previousMomentId));

  const draftMoment = dispatch(generateDraftMoment());
  if (!draftMoment) {
    return;
  }

  dispatch(setDraftMoment(draftMoment));
  dispatch(changeMomentEditingMode(draftMoment, MomentEditingMode.MomentCreation));
};

export const saveDraftMoment =
  (moment: HudlTaggingMoment): AsyncAppThunk<void> =>
  async (dispatch, getState) => {
    if (moment.id !== DRAFT_MOMENT_ID) {
      return;
    }

    const teamId = getCurrentTeamId();
    if (!teamId) {
      throw new Error('No teamId found');
    }

    const sport = sportSelector(getState());
    const startTimeMs = timelineSelectionStartTimeMsSelector(getState());
    const endTimeMs = timelineSelectionEndTimeMsSelector(getState());
    if (startTimeMs === undefined || endTimeMs === undefined || startTimeMs >= endTimeMs) {
      return;
    }

    // Make sure `source` is set for new moments
    if (!moment.source?.filter(notEmpty)?.length) {
      moment.source = DEFAULT_TIMELINE_NEW_MOMENT_SOURCE;
    }

    const hasMomentType = moment.tags.some((tag) => tag.key === TagConstants.Type && tag.values?.length);
    if (!hasMomentType) {
      console.warn('Cannot save moment without a type');
      return;
    }

    const momentToAdd: MomentInput = {
      startTimeMs: parseFloat(startTimeMs.toFixed(0)),
      endTimeMs: parseFloat(endTimeMs.toFixed(0)),
      tags: moment.tags.filter((tag) => tag.values?.length > 0),
    };

    const latestActiveClip = latestActiveClipSelector(getState());
    const videoId = latestActiveClip.videoId;
    const taggingSession = videoIdToTaggingSessionSelector(getState()).get(videoId);

    dispatch(setMomentEditingSaveState(MomentEditingSaveState.Loading));
    try {
      const taggingSessionId = taggingSession?.id;

      let createdMoments: HudlTaggingMoment[] | undefined;
      if (!taggingSessionId) {
        createdMoments = await dispatch(createTaggingSessionForMoments(videoId, [momentToAdd]));
      } else {
        createdMoments = await dispatch(addMomentsMutation(videoId, teamId, taggingSessionId, [momentToAdd], sport));
        dispatch(addMoments({ taggingSessionId, moments: createdMoments ?? [] }));
      }

      const newMoment = createdMoments?.[0];
      if (!newMoment) {
        throw new Error('No moment was created.');
      }

      const userIds = draftUserIdsSelector(getState());
      const updatedTaggingSession = videoIdToTaggingSessionSelector(getState()).get(videoId);
      if (userIds.length > 0 && updatedTaggingSession?.id) {
        await dispatch(addAthletesToTaggingSettings(updatedTaggingSession.id, userIds));
      }

      dispatch(setMomentEditingSaveState(MomentEditingSaveState.Success));
      dispatch(recalculateMomentsForVideo(videoId));

      // Wait so that the user sees that it was successful
      await sleep(SUCCESS_DELAY_MS);

      const activeClips = activeClipsSelector(getState());
      const isMomentInActiveClips = activeClips.some((clip) => clip.moments.some((m) => m.id === newMoment.id));
      if (!isMomentInActiveClips) {
        // Let the user know that their new moment is being filtered out
        dispatch(
          showToast({
            message: format('performance-core.moment-details.saved-but-hidden-by-filters'),
            type: 'warning',
            actionText: format('performance-core.moment-details.clear-filters'),
            onActionClick: () => {
              dispatch(clearAllFilters());
            },
          })
        );
      }

      dispatch(changeMomentEditingMode(newMoment, MomentEditingMode.Properties));
    } catch (e) {
      console.error(e);
      dispatch(setMomentEditingSaveState(MomentEditingSaveState.Error));

      setTimeout(() => {
        dispatch(setMomentEditingSaveState(MomentEditingSaveState.None));
      }, ERROR_DELAY_MS);
    }
  };

const generateDraftMoment = (): AppThunk<HudlTaggingMoment | undefined> => (dispatch, getState) => {
  const latestActiveClip = latestActiveClipSelector(getState());
  if (!isV3VideoVirtualClip(latestActiveClip)) {
    console.warn('Cannot create new moment for non-V3 video');
    return;
  }

  const video = videoMapSelector(getState())[latestActiveClip.videoId];
  if (!video) {
    console.warn('No video found for latest active clip', latestActiveClip.videoId);
    return;
  }

  const sport = sportSelector(getState());
  const videoTime = dispatch(getTrueVideoTime());
  const videoDurationMs = video.durationMs ?? 0;
  const [startTimeMs, endTimeMs] = validateClipBounds(NEW_MOMENT_DURATION_MS, videoTime, [0, videoDurationMs]);

  const momentFactory = new HudlTaggingMomentFactory();
  const newMoment = momentFactory.createMoment(sport, {
    id: DRAFT_MOMENT_ID,
    startTimeMs: startTimeMs,
    endTimeMs: endTimeMs,
    videoId: latestActiveClip.videoId,
    tags: [{ key: 'team', values: ['1'] }],
  });

  return newMoment;
};
