import { createSelector } from '@reduxjs/toolkit';

import { Moment, Sport } from '@hudl/hudl-domain-types';
import { isVideo } from '@hudl/performance-core-domain';

import { getGlobalSportConfig } from '../../../../analyze/domain/globalSportConfig/globalSportConfig';
import isCoachOrAdmin from '../../../../utils/isCoachOrAdmin';
import { videoIdToTaggingSessionSelector } from '../../domain/taggingSessions/taggingSessionSelectors';
import { sportSelector } from '../../domain/teams/teamSelectors';
import { selectedLibraryItemsSelector } from '../libraryModule/libraryModuleSelectors';

export const isMomentEditingEnabledSelector = createSelector(
  sportSelector,
  selectedLibraryItemsSelector,
  videoIdToTaggingSessionSelector,
  (sport, selectedLibraryItems, videoIdToTaggingSession): boolean => {
    const { supportsMomentEditing } = getGlobalSportConfig(sport);
    if (!supportsMomentEditing) {
      return false;
    }

    const areOnlyV3VideosSelected = selectedLibraryItems.every((item) => isVideo(item));
    if (!selectedLibraryItems?.length || !areOnlyV3VideosSelected) {
      return false;
    }

    /*
    If the sport is soccer, we need to check if at least one of the selected videos tagging sessions contain
    a moment with the calculated tag isV3Moment set to true.
    */
    if (sport === Sport.Soccer) {
      const hasV3Moments = selectedLibraryItems.some((video) => {
        const taggingSession = videoIdToTaggingSession.get(video.id);
        return taggingSession?.moments?.some((moment) => (moment as Moment).getFirstTagValueAsBoolean('isV3Moment'));
      });
      if (hasV3Moments) {
        return false;
      }
    }

    const allVideosHaveTaggingSessions = selectedLibraryItems.every((video) => videoIdToTaggingSession.has(video.id));
    return allVideosHaveTaggingSessions && isCoachOrAdmin();
  }
);
