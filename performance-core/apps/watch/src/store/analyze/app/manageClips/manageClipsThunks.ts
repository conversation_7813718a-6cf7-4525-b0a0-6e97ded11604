import { HudlTaggingMoment, HudlTaggingMomentFactory, SportClass, VirtualClip } from '@hudl/hudl-domain-types';
import { viActions } from '@hudl/video-interface';
import { utils } from '@hudl/video-interface';
import { format } from 'frontends-i18n';

import { trackObjectCreated } from '../../../../snowplow/SnowplowService';
import { AppThunk, AsyncAppThunk } from '../../../store';
import { GridMode } from 'analyze/components/shared/Grid/Grid.types';
import { getFirstMoment } from 'analyze/domain/columnDefinitions/helpers/virtualClipHelpers';
import { logInfo } from 'analyze/services/logService';
import { reorder } from 'analyze/utils/reorder';
import {
  classicPlayableDataSelector,
  playableDataSelector,
  VspaPlayable,
} from 'store/analyze/app/appContext/playablesSelectors';
import { activeClipsSelector, getClipForClipId, getClipsFromClipIds } from 'store/analyze/app/clips/clipsSelectors';
import {
  currentlyPlayingClipIndexSelector,
  currentlyPlayingClipSelector,
} from 'store/analyze/app/playback/playbackSelectors';
import { addClipsToClassicPlaylist, removeClipsFromClassicPlaylist } from 'store/analyze/app/playlists/playlistsSlice';
import {
  activeTaggingSessionsSelector,
  selectedCutupsSelector,
} from 'store/analyze/app/selectedVideo/selectedVideoSelectors';
import { reorderCutupClips } from 'store/analyze/domain/classicData/classicDataRequests';
import { classicClipsSelector } from 'store/analyze/domain/classicData/classicDataSelectors';
import { buildUniversalClipId } from 'store/analyze/domain/playlist/playlistUtils';
import { getDefaultTags } from 'store/analyze/domain/taggingSessions/taggingSessionHelpers';
import {
  addMomentsMutation,
  deleteMomentsMutation,
  updateMomentMutation,
} from 'store/analyze/domain/taggingSessions/taggingSessionMutations';
import { MomentInput } from 'store/analyze/domain/taggingSessions/taggingSessionMutations';
import { getMomentContext } from 'store/analyze/domain/taggingSessions/taggingSessionSelectors';
import {
  addMoments,
  createTaggingSessionFromVirtualClips,
  recalculateMomentsForVideo,
  removeMoments,
  reorderMoments,
  TaggingSession,
  updateMoment,
} from 'store/analyze/domain/taggingSessions/taggingSessionSlice';
import { sportSelector } from 'store/analyze/domain/teams/teamSelectors';
import {
  gridModeSelector,
  isModifyingClipsSelector,
  selectedClipIdsSetSelector,
} from 'store/analyze/ui/grid/gridSelectors';
import { clearSelectedClips, setIsModifyingClips, setSelectedClipIds } from 'store/analyze/ui/grid/gridSlice';
import { changeGridMode } from 'store/analyze/ui/grid/gridThunks';
import { showToast } from 'store/analyze/ui/toast/toastSlice';
import { RootState } from 'store/rootReducer';
import IEffect from 'store/shared/records/IEffect';
import { currentPlayableEffectsSelector, unsavedEffectsSelector } from 'store/shared/selectors/effectsSelectors';
import {
  playingIndexSelector,
  videoPlayerIdSelector,
  videoPlayerSelector,
} from 'store/shared/selectors/videoPlayerSelectors';
import getUserTeamId from 'utils/getUserTeamId';
import isCoachOrAdmin from 'utils/isCoachOrAdmin';

/**
 * We don't want effects to exist on the boundary of clips.
 * We push the clip boundaries to avoid landing on a clip by this number of milliseconds.
 */
export const EFFECT_BOUNDARY_OFFSET_MS = 100;
export const MINIMUM_CLIP_DURATION_MS = 2000;
export const MANUAL_MINIMUM_CLIP_DURATION_MS = 1000;

export const startManagingClips =
  (videoId: string): AppThunk =>
  (dispatch) => {
    void dispatch(changeGridMode(GridMode.ManageClips));
    void dispatch(createTaggingSessionFromVirtualClips(videoId));
  };
export const doneManagingClips = (): AppThunk => (dispatch) => {
  dispatch(changeGridMode(GridMode.Edit));
  dispatch(clearSelectedClips());
};

/** Used for the removal of clips from a playlist within the classic American Football player */
export const removeClassicClips = (): AppThunk => (dispatch, getState) => {
  const selectedClipIdsSet = selectedClipIdsSetSelector(getState());
  const allClips = activeClipsSelector(getState());
  const selectedCutups = selectedCutupsSelector(getState());
  const resumePlayingIndex = currentlyPlayingClipIndexSelector(getState()) ?? 0;
  const clipsToRemoveByCutup = selectedCutups.map((cutup) => ({
    cutupId: cutup.id,
    clips:
      cutup.clipIds
        ?.filter((clip) => selectedClipIdsSet.has(clip))
        .map((clip) => ({ clipId: clip, order: allClips.map((ac) => ac.id).indexOf(clip) })) ?? [],
  }));
  dispatch(clearSelectedClips());
  for (const removableContent of clipsToRemoveByCutup) {
    dispatch(
      removeClipsFromClassicPlaylist(
        removableContent.cutupId,
        '',
        removableContent.clips.map((c) => c.clipId)
      )
    );
  }

  const clipsRemoved = clipsToRemoveByCutup
    .flatMap((cc) => cc.clips)
    .sort((a, b) => {
      return a.order - b.order;
    });

  dispatch(
    showToast({
      message: `You removed ${clipsRemoved.length} clips.`,
      type: 'success',
      actionText: 'Undo',
      onActionClick: () => {
        void (async () => {
          const addClipsToPlaylistPromises = clipsToRemoveByCutup.map((removedClipsSet) => {
            return dispatch(
              addClipsToClassicPlaylist({
                playlistId: removedClipsSet.cutupId,
                title: '',
                clipIds: removedClipsSet.clips.map((c) => c.clipId),
              })
            );
          });

          await Promise.all(addClipsToPlaylistPromises);

          let clips = activeClipsSelector(getState()).map((vc) => vc.id);

          for (const removedClip of clipsRemoved) {
            const currentClipIndex = clips.indexOf(removedClip.clipId);
            clips = reorder(clips, currentClipIndex, removedClip.order);
          }
          await dispatch(reorderClassicClipsInternal(clips, resumePlayingIndex));
          dispatch(setSelectedClipIds(Array.from(selectedClipIdsSet)));
        })();
      },
    })
  );
};

export const reorderClassicClips =
  (momentIds: Array<string>, sourceIndex: number, destinationIndex: number): AppThunk =>
  (dispatch) => {
    const reorderedMomentIds = reorder(momentIds, sourceIndex, destinationIndex);
    void dispatch(reorderClassicClipsInternal(reorderedMomentIds, destinationIndex));
  };

const reorderClassicClipsInternal =
  (reorderedClipIds: string[], resumePlayingIndex: number): AsyncAppThunk =>
  async (dispatch, getState) => {
    const state = getState();
    const classicClipsMap = classicClipsSelector(state);
    const activeTaggingSessions = activeTaggingSessionsSelector(state);
    const playingIndex = playingIndexSelector(state);
    const { playables, clipAngleIdToPlayableIdMap } = classicPlayableDataSelector(state);
    const playableIdToPlayableMap = new Map(playables.map((playable) => [playable.id, playable]));
    // Classic data has one playable per clip angle, so when reordering clips we need to translate it into clip angle reorderings
    let newPlayingIndex = 0;
    const reorderedPlayables = [] as Array<VspaPlayable>;
    reorderedClipIds.forEach((clipId, clipIndex) => {
      const classicClip = classicClipsMap.get(clipId)!;
      if (!classicClip.angles.length) {
        return;
      }

      classicClip.angles.forEach((clipAngle, angleIndex) => {
        const playableId = clipAngleIdToPlayableIdMap.get(clipAngle.angleId);
        const playable = playableId ? playableIdToPlayableMap.get(playableId) : undefined;

        if (!playable) {
          return;
        }

        if (angleIndex === 0 && resumePlayingIndex === clipIndex) {
          newPlayingIndex = reorderedPlayables.length;
        }

        reorderedPlayables.push(playable);
      });
    });

    const playerId = videoPlayerIdSelector(state);
    dispatch(viActions.setPlayables(playerId, reorderedPlayables, true, newPlayingIndex));

    const reorderedTaggingSessionsToMomentIds = determineNewTaggingSessionToMomentIds(
      activeTaggingSessions,
      reorderedClipIds
    );
    dispatch(reorderMoments(reorderedTaggingSessionsToMomentIds));

    const teamId = getUserTeamId();
    const selectedCutups = selectedCutupsSelector(state);
    const selectedCutupIds = selectedCutups.map((c) => c.id);

    try {
      await reorderCutupClips(teamId, reorderedClipIds, selectedCutupIds);

      logInfo('Reorder', 'Clips', {
        cutupIds: selectedCutupIds,
        teamId: teamId,
      });
    } catch (error) {
      console.error(error);
      void dispatch(
        showToast({
          message: 'Unable to reorder clips. Please try again.',
          type: 'error',
        })
      );

      // Attempt to revert the reorder
      dispatch(viActions.setPlayables(playerId, playables, playingIndex));
      const originalOrder = new Map(
        activeTaggingSessions.map((taggingSession) => [taggingSession.id, taggingSession.moments.map((m) => m.id)])
      );
      dispatch(reorderMoments(originalOrder));
    }
  };

const determineNewTaggingSessionToMomentIds = (
  activeTaggingSessions: TaggingSession[],
  reorderedMomentIds: string[]
) => {
  const reorderedTaggingSessionsToMomentIds = new Map<string, string[]>();
  Array.from(activeTaggingSessions).forEach((taggingSession) => {
    const taggingSessionMomentIds = new Set(taggingSession.moments.map((m) => m.id));
    const taggingSessionReorderedMomentIds = new Set<string>();

    reorderedMomentIds.forEach((reorderedMomentId) => {
      if (taggingSessionMomentIds.has(reorderedMomentId)) {
        taggingSessionReorderedMomentIds.add(reorderedMomentId);
      }
    });

    reorderedTaggingSessionsToMomentIds.set(taggingSession.id, Array.from(taggingSessionReorderedMomentIds));
  });

  return reorderedTaggingSessionsToMomentIds;
};

/** Chunks a clip up into even segments of length "chunkLengthMs". */
export const chunkV3Clips =
  (clipId: string, chunkLengthMs: number): AsyncAppThunk =>
  async (dispatch, getState) => {
    const canModify = canModifyClips(getState());
    if (!canModify) {
      return;
    }

    let clipToChunk = getClipForClipId(getState(), clipId);
    if (!clipToChunk) {
      return;
    }

    const isValidChunk = isValidChunkLength(chunkLengthMs, clipToChunk);
    if (!isValidChunk) {
      return;
    }

    const doesClipHaveMoment = clipToChunk?.moments?.length >= 1;
    if (!doesClipHaveMoment) {
      await dispatch(createTaggingSessionFromVirtualClips(clipToChunk.videoId));
      clipToChunk = getClipForClipId(getState(), buildUniversalClipId(clipToChunk));
    }

    const originalMoment = getFirstMoment(clipToChunk);
    if (!originalMoment) {
      return;
    }

    const { teamId, taggingSessionId, videoId } = getMomentContext(getState(), originalMoment.id);
    if (!teamId || !taggingSessionId || !videoId) {
      return;
    }

    const sport = sportSelector(getState());
    const [firstMoment, ...momentsToAdd] = chunkMoments(
      originalMoment.startTimeMs,
      originalMoment.endTimeMs,
      chunkLengthMs,
      sport
    );
    if (!firstMoment || !momentsToAdd?.length) {
      console.error('Chunking resulted in zero new moments');
      return;
    }

    // now shrink our original moment
    const momentFactory = new HudlTaggingMomentFactory();
    const updatedMoment = momentFactory.createMoment(sport, {
      id: originalMoment.id,
      videoId: originalMoment.videoId,
      startTimeMs: firstMoment.startTimeMs,
      endTimeMs: firstMoment.endTimeMs,
      tags: originalMoment.tags, // Make sure we preserve the original moment's tags
    });

    let createdMoments: HudlTaggingMoment[] = [];
    dispatch(setIsModifyingClips(true));
    try {
      await dispatch(updateMomentMutation(videoId, teamId, taggingSessionId, [updatedMoment]));
      createdMoments = await dispatch(addMomentsMutation(videoId, teamId, taggingSessionId, momentsToAdd, sport));
    } catch (ex) {
      console.error(ex);

      dispatch(updateMoment(originalMoment));
      // if addMomentsMutation failed, we have no created moment IDs to undo
      dispatch(recalculateMomentsForVideo(videoId));
      void dispatch(
        showToast({
          message: format('analyze.shared.navigation.edit_clip_error_toast'),
          type: 'error',
        })
      );
      dispatch(setIsModifyingClips(false));
      return;
    }

    dispatch(updateMoment(updatedMoment));
    dispatch(addMoments({ taggingSessionId, moments: createdMoments }));
    dispatch(recalculateMomentsForVideo(videoId));
    dispatch(setIsModifyingClips(false));

    // Undo Action, reverts the previous local and server changes
    void dispatch(
      showToast({
        message: format('module.grid.clip_split.successful_message', { clipCount: (createdMoments?.length ?? 0) + 1 }),
        actionText: format('analyze.undo'),
        type: 'success',
        onActionClick: async () => {
          await dispatch(updateMomentMutation(videoId, teamId, taggingSessionId, [originalMoment]));

          const idsToRemove = createdMoments?.map((m) => m.id);
          if (idsToRemove?.length) {
            await dispatch(deleteMomentsMutation(videoId, teamId, taggingSessionId, idsToRemove));
          }

          dispatch(updateMoment(originalMoment));
          if (idsToRemove?.length) {
            dispatch(
              removeMoments({
                taggingSessionId: taggingSessionId,
                momentIds: idsToRemove,
              })
            );
          }
          dispatch(recalculateMomentsForVideo(videoId));
        },
      })
    );
  };

const isValidChunkLength = (chunkLengthMs: number, clipToChunk: VirtualClip): boolean => {
  if (isNaN(chunkLengthMs) || !chunkLengthMs || chunkLengthMs < MINIMUM_CLIP_DURATION_MS) {
    console.error(`Invalid chunkLengthMs input: chunkLengthMs=${chunkLengthMs}`);
    return false;
  }

  const clipLength = clipToChunk.endTimeMs - clipToChunk.startTimeMs;
  if (clipLength < MINIMUM_CLIP_DURATION_MS) {
    console.error('Clip to chunk is shorter than the minimum');
    return false;
  }

  return true;
};

export const chunkMoments = (
  originalStartTimeMs: number,
  originalEndTimeMs: number,
  chunkLengthMs: number,
  sport: SportClass
) => {
  if (originalStartTimeMs >= originalEndTimeMs || chunkLengthMs <= 0) {
    return [];
  }

  const newMoments: MomentInput[] = [];

  // Loop over the original moment, chunking it into new moments
  for (let startTimeMs = originalStartTimeMs; startTimeMs <= originalEndTimeMs; startTimeMs += chunkLengthMs) {
    const endTimeMs = Math.min(startTimeMs + chunkLengthMs, originalEndTimeMs); // Make sure the end time isn't over the original end time
    newMoments.push({
      startTimeMs,
      endTimeMs,
      tags: getDefaultTags(sport),
    });
  }

  // We need to make sure the last moment isn't too short
  const lastMoment = newMoments[newMoments.length - 1];
  const lastMomentDuration = lastMoment.endTimeMs - lastMoment.startTimeMs;
  if (lastMomentDuration < MINIMUM_CLIP_DURATION_MS) {
    newMoments.pop();

    // Updates the new last element (was previously the 2nd to last) to have the new end time
    newMoments[newMoments.length - 1].endTimeMs = lastMoment.endTimeMs;
  }

  return newMoments;
};

/** Splits the active clip into two. "splitTimeInputMs" represents how far into the clip to split, if it is not provided it will split at the video player's "currentTimeMs". */
export const splitActiveV3Clip =
  (splitTimeInputMs?: number): AppThunk =>
  async (dispatch, getState) => {
    const canModify = canModifyClips(getState());
    if (!canModify) {
      return;
    }

    let activeClip = currentlyPlayingClipSelector(getState());
    if (!activeClip) {
      return;
    }

    const doesClipHaveMoment = activeClip?.moments?.length >= 1;
    if (!doesClipHaveMoment) {
      await dispatch(createTaggingSessionFromVirtualClips(activeClip.videoId));
      activeClip = getClipForClipId(getState(), buildUniversalClipId(activeClip));
    }

    const activeMoment = getFirstMoment(activeClip);
    if (!activeMoment) {
      return;
    }

    const splitTimeMs = validateSplitTime(
      splitTimeInputMs,
      activeMoment.startTimeMs,
      activeMoment.endTimeMs,
      getState()
    );
    if (!splitTimeMs) {
      return;
    }

    const { teamId, taggingSessionId, videoId } = getMomentContext(getState(), activeMoment.id);
    if (!teamId || !taggingSessionId || !videoId) {
      return;
    }

    const sport = sportSelector(getState());

    // shrink our original moment
    const momentFactory = new HudlTaggingMomentFactory();
    const updatedMoment = momentFactory.createMoment(sport, {
      id: activeMoment.id,
      videoId: activeMoment.videoId,
      startTimeMs: activeMoment.startTimeMs,
      endTimeMs: splitTimeMs, // First moment ends at the split point
      tags: activeMoment.tags, // Make sure we preserve the original moment's tags
    });

    // prepare the new moment for creation
    const momentToAdd: MomentInput = {
      startTimeMs: splitTimeMs, // Second moment begins at the split point
      endTimeMs: activeMoment.endTimeMs,
      tags: getDefaultTags(sport),
    };

    let createdMoments: HudlTaggingMoment[] = [];
    dispatch(setIsModifyingClips(true));
    try {
      await dispatch(updateMomentMutation(videoId, teamId, taggingSessionId, [updatedMoment]));
      createdMoments = await dispatch(addMomentsMutation(videoId, teamId, taggingSessionId, [momentToAdd], sport));
    } catch (ex) {
      console.error(ex);

      dispatch(updateMoment(activeMoment));
      // if addMomentsMutation failed, we have no created moment IDs to undo
      dispatch(recalculateMomentsForVideo(videoId));
      void dispatch(
        showToast({
          message: format('analyze.shared.navigation.edit_clip_error_toast'),
          type: 'error',
        })
      );
      dispatch(setIsModifyingClips(false));
    }

    const playerId = videoPlayerIdSelector(getState());
    const playingIndex = playingIndexSelector(getState());
    const { playables } = playableDataSelector(getState());
    dispatch(splitPlayables(activeClip, splitTimeMs));
    dispatch(updateMoment(updatedMoment));
    dispatch(addMoments({ taggingSessionId, moments: createdMoments }));
    dispatch(recalculateMomentsForVideo(videoId));
    dispatch(setIsModifyingClips(false));
    dispatch(trackObjectCreated('Clip', { isAutomatic: false }, []));

    // Undo Action, reverts the previous local and server changes
    dispatch(
      showToast({
        message: format('module.grid.clip_manual_split.successful_message', {
          splitTime: utils.msToReadableString(
            splitTimeInputMs ? Math.floor(splitTimeInputMs) : Math.floor(splitTimeMs - activeMoment.startTimeMs)
          ),
        }),
        actionText: format('analyze.undo'),
        type: 'success',
        onActionClick: async () => {
          await dispatch(updateMomentMutation(videoId, teamId, taggingSessionId, [activeMoment]));

          const idsToRemove = createdMoments?.map((m) => m.id);
          if (idsToRemove?.length) {
            deleteMomentsMutation(videoId, teamId, taggingSessionId, idsToRemove);
          }

          dispatch(viActions.setPlayables(playerId, playables, true, playingIndex));
          // They technically could leave the manage clips mode, so make sure they're still there
          dispatch(changeGridMode(GridMode.ManageClips));
          dispatch(updateMoment(activeMoment));
          if (idsToRemove?.length) {
            dispatch(
              removeMoments({
                taggingSessionId: taggingSessionId,
                momentIds: idsToRemove,
              })
            );
          }
          dispatch(recalculateMomentsForVideo(videoId));
        },
      })
    );
  };

const canModifyClips = (state: RootState) => {
  const isAlreadyModifying = isModifyingClipsSelector(state);
  if (isAlreadyModifying) {
    return false;
  }

  const gridMode = gridModeSelector(state);
  if (gridMode !== GridMode.ManageClips) {
    console.error(`Cannot manage clips while not in "Manage Clips" mode.`);
    return false;
  }

  const hasPermissions = isCoachOrAdmin();
  if (!hasPermissions) {
    console.error(`User does not have permission to modify clips.`);
    return false;
  }

  return true;
};

const validateSplitTime = (
  splitTimeInputMs: number | undefined,
  startTimeMs: number,
  endTimeMs: number,
  state: RootState
) => {
  const player = videoPlayerSelector(state);
  const currentTimeMs = player?.currentTimeMs;

  let splitTimeMs = startTimeMs + (splitTimeInputMs ?? currentTimeMs);
  if (!isValidSplitLocation(startTimeMs, endTimeMs, splitTimeMs)) {
    console.warn(`Invalid split location: startTime=${startTimeMs}, endTime=${endTimeMs}, splitTime=${splitTimeMs}.`);
    return null;
  }

  const savedEffects = currentPlayableEffectsSelector(state) ?? [];
  const unsavedEffects = (unsavedEffectsSelector(state)?.toJS() as IEffect[]) ?? [];
  const effects = [...savedEffects, ...unsavedEffects];

  const effectsOnSplitBoundary = effects.filter((effect) => {
    const diffMs = Math.abs(effect.startTimeMs - splitTimeMs);
    return diffMs < EFFECT_BOUNDARY_OFFSET_MS;
  });
  if (effectsOnSplitBoundary?.length > 0) {
    // Move the split time slightly to not split on the same frame as an effect
    const maxEffectStartTimeMs = Math.max(...effectsOnSplitBoundary.map((effect) => effect.startTimeMs));
    splitTimeMs = maxEffectStartTimeMs + EFFECT_BOUNDARY_OFFSET_MS;
  }

  return Math.floor(splitTimeMs);
};

export const isValidSplitLocation = (startTimeMs: number, endTimeMs: number, splitTimeMs: number) => {
  const firstClipLength = splitTimeMs - startTimeMs;
  const secondClipLength = endTimeMs - splitTimeMs;

  // The resulting clips must be larger than MANUAL_MINIMUM_CLIP_DURATION_MS
  if (firstClipLength < MANUAL_MINIMUM_CLIP_DURATION_MS || secondClipLength < MANUAL_MINIMUM_CLIP_DURATION_MS) {
    return false;
  }

  // Can't split if the split time is outside of the bounds of the clip
  if (splitTimeMs < startTimeMs || splitTimeMs > endTimeMs) {
    return false;
  }

  return true;
};

/** Merges clips / moments together. This updates the first moment to be longer and deletes the others. This will create a tagging session if it doesn't already exist. */
export const mergeV3Clips =
  (clipIdsToMerge: Array<string>): AppThunk =>
  async (dispatch, getState) => {
    const canModify = canModifyClips(getState());
    if (!canModify) {
      return;
    }

    let clipsToMerge = getClipsFromClipIds(getState(), clipIdsToMerge).sort((a, b) => a.startTimeMs - b.startTimeMs);
    if (clipsToMerge.length <= 1) {
      console.error('Not enough clips to merge.');

      void dispatch(
        showToast({
          message: format('analyze.shared.navigation.edit_clip_error_toast'),
          type: 'error',
        })
      );
      return;
    }

    const clipsHaveMoments = clipsToMerge.every((clip) => clip.moments?.length >= 1);
    if (!clipsHaveMoments) {
      await dispatch(createTaggingSessionFromVirtualClips(clipsToMerge[0].videoId));
      clipsToMerge = getClipsFromClipIds(getState(), clipIdsToMerge).sort((a, b) => a.startTimeMs - b.startTimeMs);
    }

    // We update the first moment to contain the duration of the rest. The rest are deleted.
    const [clipToUpdate, ...clipsToDelete] = clipsToMerge;
    const momentToUpdate = getFirstMoment(clipToUpdate);
    const momentsToDelete = clipsToDelete.flatMap((clip) => clip.moments);
    const momentIdsToDelete = momentsToDelete.map((moment) => moment.id);
    if (!momentToUpdate || !momentsToDelete?.length) {
      void dispatch(
        showToast({
          message: format('analyze.shared.navigation.edit_clip_error_toast'),
          type: 'error',
        })
      );
      return;
    }

    const { teamId, taggingSessionId, videoId } = getMomentContext(getState(), momentToUpdate.id);
    if (!teamId || !taggingSessionId || !videoId) {
      return;
    }

    const sport = sportSelector(getState());
    const newEndTime = Math.max(...momentsToDelete.map((moment) => moment.endTimeMs));
    const momentFactory = new HudlTaggingMomentFactory();
    const updatedMoment = momentFactory.createMoment(sport, {
      id: momentToUpdate.id,
      startTimeMs: momentToUpdate.startTimeMs,
      endTimeMs: newEndTime,
      tags: momentToUpdate.tags,
      videoId: momentToUpdate.videoId,
    });

    // Optimistically update local state
    const playerId = videoPlayerIdSelector(getState());
    const playingIndex = playingIndexSelector(getState());
    const { playables } = playableDataSelector(getState());
    dispatch(mergePlayables(clipToUpdate, clipsToDelete, momentToUpdate.startTimeMs, newEndTime));
    dispatch(updateMoment(updatedMoment));
    dispatch(
      removeMoments({
        taggingSessionId: taggingSessionId,
        momentIds: momentIdsToDelete,
      })
    );
    dispatch(recalculateMomentsForVideo(videoId));
    dispatch(clearSelectedClips());

    try {
      await dispatch(updateMomentMutation(videoId, teamId, taggingSessionId, [updatedMoment]));
    } catch (ex) {
      console.error(ex);

      dispatch(viActions.setPlayables(playerId, playables, true, playingIndex));
      dispatch(updateMoment(momentToUpdate));
      dispatch(
        addMoments({
          taggingSessionId: taggingSessionId,
          moments: momentsToDelete,
        })
      );
      dispatch(setSelectedClipIds(clipIdsToMerge));
      dispatch(recalculateMomentsForVideo(videoId));
      void dispatch(
        showToast({
          message: format('analyze.shared.navigation.edit_clip_error_toast'),
          type: 'error',
        })
      );
      return;
    }

    try {
      await dispatch(deleteMomentsMutation(videoId, teamId, taggingSessionId, momentIdsToDelete));
    } catch (ex) {
      console.error(ex);

      dispatch(
        addMoments({
          taggingSessionId: taggingSessionId,
          moments: momentsToDelete,
        })
      );
      dispatch(setSelectedClipIds(clipIdsToMerge));
      dispatch(recalculateMomentsForVideo(videoId));
      void dispatch(
        showToast({
          message: format('analyze.shared.navigation.edit_clip_error_toast'),
          type: 'error',
        })
      );
      return;
    }

    // Undo Action, reverts the previous local and server changes
    dispatch(
      showToast({
        message: format('module.grid.header.successful_merge_message', { clipCount: clipsToMerge.length }),
        actionText: format('analyze.undo'),
        type: 'success',
        onActionClick: async () => {
          await dispatch(updateMomentMutation(videoId, teamId, taggingSessionId, [momentToUpdate]));
          const restoredMoments = await dispatch(
            addMomentsMutation(videoId, teamId, taggingSessionId, momentsToDelete, sport)
          );

          dispatch(viActions.setPlayables(playerId, playables, true, playingIndex));

          // They technically could leave the manage clips mode, so make sure they're still there
          dispatch(changeGridMode(GridMode.ManageClips));
          dispatch(updateMoment(momentToUpdate));
          dispatch(
            addMoments({
              taggingSessionId: taggingSessionId,
              moments: restoredMoments,
            })
          );
          dispatch(setSelectedClipIds(clipIdsToMerge));
          dispatch(recalculateMomentsForVideo(videoId));
        },
      })
    );
  };

// We have to merge playables so that we can properly play the newly merged clip
const mergePlayables =
  (
    clipToUpdate: VirtualClip,
    clipsToDelete: Array<VirtualClip>,
    newStartTimeMs: number,
    newEndTimeMs: number
  ): AppThunk =>
  (dispatch, getState) => {
    const { playables } = playableDataSelector(getState());
    const universalClipIdToUpdate = buildUniversalClipId(clipToUpdate);
    const universalClipIdsToDelete = clipsToDelete.map(buildUniversalClipId);

    const mergedPlayables: VspaPlayable[] = [];
    let newPlayingIndex = 0;

    playables.forEach((playable, index) => {
      const playableUniversalClipId = buildUniversalClipId(playable);
      if (universalClipIdsToDelete.indexOf(playableUniversalClipId) !== -1) {
        return;
      }

      if (playableUniversalClipId === universalClipIdToUpdate) {
        newPlayingIndex = index;
        mergedPlayables.push({
          ...playable,
          startTimeMs: newStartTimeMs,
          endTimeMs: newEndTimeMs,
          durationMs: newEndTimeMs - newStartTimeMs,
          streams: playable.streams.map((stream) => ({
            ...stream,
            startTimeMs: newStartTimeMs,
            endTimeMs: newEndTimeMs,
          })),
        });
        return;
      }

      mergedPlayables.push(playable);
    });

    const playerId = videoPlayerIdSelector(getState());
    dispatch(viActions.setPlayables(playerId, mergedPlayables, true, newPlayingIndex));
  };

// We have to split playables so that we can properly play the newly split clip
const splitPlayables =
  (clipToSplit: VirtualClip, splitTimeMs: number): AppThunk =>
  (dispatch, getState) => {
    const { playables } = playableDataSelector(getState());
    const universalClipIdToSplit = buildUniversalClipId(clipToSplit);

    let newPlayingIndex = 0;

    const newPlayables: VspaPlayable[] = [];
    playables.forEach((playable, index) => {
      const playableUniversalClipId = buildUniversalClipId(playable);

      if (playableUniversalClipId === universalClipIdToSplit) {
        // Playable to shrink
        newPlayables.push({
          ...playable,
          startTimeMs: clipToSplit.startTimeMs,
          endTimeMs: splitTimeMs,
          durationMs: splitTimeMs - clipToSplit.startTimeMs,
          streams: playable.streams.map((stream) => ({
            ...stream,
            startTimeMs: clipToSplit.startTimeMs,
            endTimeMs: splitTimeMs,
          })),
        });

        // Newly split playable
        newPlayables.push({
          ...playable,
          startTimeMs: splitTimeMs,
          endTimeMs: clipToSplit.endTimeMs,
          durationMs: clipToSplit.endTimeMs - splitTimeMs,
          streams: playable.streams.map((stream) => ({
            ...stream,
            startTimeMs: splitTimeMs,
            endTimeMs: clipToSplit.endTimeMs,
          })),
        });

        // We want to play the newly created playable
        newPlayingIndex = index + 1;
        return;
      }

      newPlayables.push(playable);
    });

    const playerId = videoPlayerIdSelector(getState());
    dispatch(viActions.setPlayables(playerId, newPlayables, true, newPlayingIndex));
  };
