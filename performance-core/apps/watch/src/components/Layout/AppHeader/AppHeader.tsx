import { memo } from 'react';

import classNames from 'classnames';
import { useSelector } from 'react-redux';
import BetaVsClassicDataEntryPreference from 'shared/components/Onboarding/BetaVsClassicDataEntryPreference/BetaVsClassicDataEntryPreference';
import BetaVsClassicFilterPreference from 'shared/components/Onboarding/BetaVsClassicFilterPreference/BetaVsClassicFilterPreference';
import FilterMenuOnboarding from 'shared/components/Onboarding/FilterMenuOnboarding/FilterMenuOnboarding';
import { LibraryPopoverOnboarding } from 'shared/components/Onboarding/LibraryOnboarding/LibraryPopoverOnboarding/LibraryPopoverOnboarding';
import { useHudlDocumentTitle, useTitleForSelectedItems } from 'shared/utils/useHudlDocumentTitle';

import { Sport } from '@hudl/hudl-domain-types';
import { ModuleDisplay } from '@hudl/performance-core-domain';
import { Divider, Note, Text } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import BackButton from './BackButton/BackButton';
import DownloadButton from './DownloadButton/DownloadButton';
import { FiltersModuleButton } from './FiltersModuleButton/FiltersModuleButton';
import LibraryButton from './LibraryButton/LibraryButton';
import { MobileModuleButtons } from './MobileModuleButtons/MobileModuleButtons';
import SearchButton from './SearchButton/SearchButton';
import ShareButton from './ShareButton/ShareButton';
import useGetTeamLibraryItems from './useGetTeamLibraryItems';
import { V3VideoExperienceButton } from './V3VideoExperienceButton/V3VideoExperienceButton';
import AnalyzeSupport from 'analyze/components/shared/AnalyzeSupport/AnalyzeSupport';
import FilterMenu from 'analyze/components/shared/FilterMenu/FilterMenu';
import { hasContentSelector, selectedShareableSelector } from 'store/analyze/app/selectedVideo/selectedVideoSelectors';
import { teamInUIContextSelector } from 'store/analyze/app/team/teamSelectors';
import 'store/analyze/domain/taggingSessions/taggingSessionSelectors';
import { isAIGeneratedSessionsSelector } from 'store/analyze/domain/taggingSessions/taggingSessionSelectors';
import { sportSelector } from 'store/analyze/domain/teams/teamSelectors';
import { isMobileSelector } from 'store/analyze/ui/environment/environmentSelectors';
import { mobilePageTypeSelector } from 'store/analyze/ui/layout/layoutSelectors';
import { isFiltersModuleAllowed, libraryModuleDisplaySelector } from 'store/analyze/ui/modules/moduleSelectors';
import { MobilePageType } from 'store/analyze/ui/modules/moduleSlice';

import styles from './styles.module.scss';

export const AppHeader = memo(() => {
  const libraryModuleDisplay = useSelector(libraryModuleDisplaySelector);
  const mobilePageType = useSelector(mobilePageTypeSelector);
  const teamInContext = useSelector(teamInUIContextSelector);
  const selectedShareable = useSelector(selectedShareableSelector);
  const hasSelectedContent = useSelector(hasContentSelector);
  const isMobile = useSelector(isMobileSelector);
  const sport = useSelector(sportSelector);

  const hasTeamButNoSelectionWhileOverlay =
    !hasSelectedContent && !!teamInContext && libraryModuleDisplay === ModuleDisplay.Overlay;
  const hasSelectionWhileNotFullscreen = hasSelectedContent && libraryModuleDisplay !== ModuleDisplay.Main;
  const hideBreadcrumbForMobileLibrary = isMobile && mobilePageType !== MobilePageType.Video;
  const showContent =
    (hasSelectionWhileNotFullscreen || hasTeamButNoSelectionWhileOverlay) && !hideBreadcrumbForMobileLibrary;
  const showBetaVsClassicPreference = sport === Sport.Football;

  // This block will fetch the content for the selected V3 team when a page is refreshed while viewing a video.
  // This only needs to be done to help populate the content module overlay and the selected library items.
  const { isLoading, libraryItems } = useGetTeamLibraryItems(showContent);

  const shouldShowNewFiltersModule = useSelector(isFiltersModuleAllowed);
  const isAISession = useSelector(isAIGeneratedSessionsSelector);

  const centerSearchBarIsVisible = sport === Sport.Volleyball;

  const headerClasses = classNames(styles.header, {
    [styles.centerSearchBarIsVisible]: centerSearchBarIsVisible,
  });

  const title = useTitleForSelectedItems(libraryItems, selectedShareable);
  useHudlDocumentTitle(title);
  if (hideBreadcrumbForMobileLibrary) {
    return null;
  }

  const betaBanner = (
    <Note type="warning" size="xsmall" className={styles.betaBanner}>
      {isMobile ? format('analyze.navbar.aiBreakdowns.beta') : format('analyze.navbar.aiBreakdowns.beta.warning')}
    </Note>
  );

  if (isMobile) {
    return (
      <div className={styles.header}>
        <div className={styles.leftHeader}>
          <BackButton />
          <FilterMenu isMobile />
        </div>

        <Text level="micro" className={styles.title}>
          {title}
        </Text>

        <div className={styles.rightHeader}>
          {hasSelectionWhileNotFullscreen && (
            <>
              {isAISession && betaBanner}
              <AnalyzeSupport />
              <MobileModuleButtons />
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={headerClasses}>
      <div className={styles.leftHeader}>
        <BackButton />

        <Divider orientation="vertical" />

        <LibraryPopoverOnboarding>
          <LibraryButton />
        </LibraryPopoverOnboarding>
        {sport === Sport.Volleyball ? (
          <Text level="micro" className={styles.title} qaId="library-title">
            {title}
          </Text>
        ) : (
          <>
            {shouldShowNewFiltersModule ? (
              <FiltersModuleButton />
            ) : (
              <FilterMenuOnboarding>
                <FilterMenu />
              </FilterMenuOnboarding>
            )}
          </>
        )}
      </div>
      {sport === Sport.Volleyball ? (
        <div className={styles.centerHeader}>
          <div className={styles.searchFilterGroup}>
            {shouldShowNewFiltersModule ? (
              <FiltersModuleButton />
            ) : (
              <FilterMenuOnboarding>
                <FilterMenu />
              </FilterMenuOnboarding>
            )}

            <div className={styles.searchFilterDivider} />
            <SearchButton />
          </div>
        </div>
      ) : (
        <Text level="micro" className={styles.title} qaId="library-title">
          {title}
        </Text>
      )}
      <div className={styles.rightHeader}>
        {isAISession && betaBanner}
        <V3VideoExperienceButton />

        <BetaVsClassicDataEntryPreference isDisabled={isLoading || !showBetaVsClassicPreference}>
          <BetaVsClassicFilterPreference isDisabled={isLoading || !showBetaVsClassicPreference}>
            <AnalyzeSupport />
          </BetaVsClassicFilterPreference>
        </BetaVsClassicDataEntryPreference>

        <ShareButton />
        <DownloadButton />
      </div>
    </div>
  );
});

AppHeader.displayName = 'AppHeader';
