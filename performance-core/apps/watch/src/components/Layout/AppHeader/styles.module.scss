.header {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  column-gap: var(--u-space-half);
  padding: var(--space-quarter) var(--space-quarter) var(--space-three-quarter);

  &.centerSearchBarIsVisible {
    grid-template-columns: auto 1fr auto;
  }
}

.leftHeader,
.rightHeader,
.centerHeader {
  height: 100%;
  display: flex;
  flex-direction: row;
  gap: var(--u-space-half);
  align-items: center;
}

.leftHeader,
.rightHeader {
  :global(.LibraryButton),
  :global(button),
  > * {
    align-self: center;
  }
}

.rightHeader {
  justify-content: flex-end;
}

.centerHeader {
  display: flex;
  align-items: center;
}

.title {
  font-weight: var(--u-font-weight-bold);
  text-align: center;
  min-width: 0;

  // Dealing with really long titles
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.betaBanner {
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
}

.betaBannerText {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.searchFilterGroup {
  display: flex;
  align-items: center;
  background-color: var(--u-color-bg-level1);
  border: 0;
  border-radius: 6px;
  height: 36px;
  overflow: hidden;
  flex-grow: 1;
  min-width: 0;
  margin-right: 20px;
  margin-left: 20px;
}

.searchFilterGroup:focus-within {
  border: 1px solid var(--u-color-selection-outline);
}

.searchFilterGroup :global(.custom-search.u-form__item) {
  border: none; // remove inner border since group wraps it now
  border-radius: 0; // we'll handle radius globally
  flex-grow: 1;
  min-width: 0;
}

.searchFilterDivider {
  width: 1px;
  height: 100%;
  background-color: var(--u-color-divider);
}
