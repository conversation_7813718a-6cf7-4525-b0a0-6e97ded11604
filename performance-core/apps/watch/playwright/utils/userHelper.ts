import { Sport, UserType } from '../types';

export type Team = {
  id: string;
  internalId: string;
  type: 'Classic' | 'V3';
};

export type User = {
  username: string;
  id: string;
  firstName: string;
  lastName: string;
  role: 'ADMINISTRATOR' | 'COACH' | 'PARTICIPANT';
  teamIds: string[];
};

export const performanceCoreTeams: Record<Sport, Team> = {
  Baseball: {
    internalId: '722644',
    id: 'VGVhbTcyMjY0NA==',
    type: 'V3',
  },
  Basketball: {
    internalId: '724117',
    id: 'VGVhbTcyNDExNw==',
    type: 'V3',
  },
  Football: {
    internalId: '721596',
    id: 'VGVhbTcyMTU5Ng==',
    type: 'Classic',
  },
  IceHockey: {
    internalId: '816709',
    id: 'VGVhbTgxNjcwOQ==',
    type: 'V3',
  },
  Lacrosse: {
    internalId: '816708',
    id: 'VGVhbTgxNjcwOA==',
    type: 'V3',
  },
  Soccer: {
    internalId: '720795',
    id: 'VGVhbTcyMDc5NQ==',
    type: 'V3',
  },
  Softball: {
    internalId: '722645',
    id: 'VGVhbTcyMjY0NQ==',
    type: 'V3',
  },
  Volleyball: {
    internalId: '816707',
    id: 'VGVhbTgxNjcwNw==',
    type: 'V3',
  },
  Wrestling: {
    internalId: '720793',
    id: 'VGVhbTcyMDc5Mw==',
    type: 'V3',
  },
};

export const performanceCoreUsers: Record<UserType, User> = {
  PerformanceCoreOrgAdmin: {
    username: '<EMAIL>',
    role: 'ADMINISTRATOR',
    id: '19269750',
    teamIds: ['722644', '724117', '721596', '816709', '816708', '720795', '722645', '816707', '720793'],
    firstName: 'All Sports',
    lastName: 'New Hudl School Admin',
  },
  PerformanceCoreTeamAdmin: {
    username: '<EMAIL>',
    role: 'ADMINISTRATOR',
    id: '26838338',
    teamIds: ['722644', '724117', '721596', '816709', '816708', '720795', '722645', '816707', '720793'],
    firstName: 'Hudl QA',
    lastName: 'Performance Core',
  },
  PerformanceCoreCoach: {
    username: '<EMAIL>',
    role: 'COACH',
    id: '26838452',
    teamIds: ['722644', '724117', '721596', '816709', '816708', '720795', '722645', '816707', '720793'],
    firstName: 'Hudl QA',
    lastName: 'Playwright Coach',
  },
  PerformanceCoreAthlete: {
    username: '<EMAIL>',
    role: 'PARTICIPANT',
    id: '26839071',
    teamIds: ['722644', '724117', '721596', '816709', '816708', '720795', '722645', '816707', '720793'],
    firstName: 'Hudl QA',
    lastName: 'Playwright Athlete',
  },
};

export const getUserByUsername = (username: string): User | undefined => {
  return Object.values(performanceCoreUsers).find((user) => user.username === username);
};

export const getTeamBySportType = (sportType: Sport): Team => {
  return performanceCoreTeams[sportType] || undefined;
};

export const userHasAccessToTeam = (user: User, { internalId }: Team): boolean => {
  return user.teamIds.includes(internalId);
};
