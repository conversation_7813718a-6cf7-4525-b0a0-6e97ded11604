import { expect, test } from '../../../../fixtures/analyze/filtersFixture';

/**
 * Test suite for verifying the behavior of active filters in the video player side panel.
 * Covers various sports and ensures filters are applied correctly, moment data updates accordingly,
 * and filter counts behave as expected.
 */

test.use({
  storageState: 'playwright/.auth/hudlTeamAdmin.json',
  browserName: 'chromium',
  channel: 'chromium',
});

test.describe('Active Filters Behavior', () => {
  test('Should apply insights and shared filters, reflect active filter count, and show matching moments - Softball', async ({
    libraryBasePage,
    generalFilters,
    softballInsightsPanel,
    filtersUtils,
  }) => {
    // Navigate to the Softball team library and open the test video
    await filtersUtils.openVideoAndFilters('709222');

    // Apply a "Pitching" filter and select a pitcher
    await softballInsightsPanel.pitching.click();
    await softballInsightsPanel.pitchedBy('#99 Unknown Athlete').click();

    // Confirm the pitcher appears in the active filters
    await expect(libraryBasePage.genericTextLocator('Pitched By: #99 Unknown').first()).toBeVisible();

    // Add another pitcher and verify both are shown
    await softballInsightsPanel.pitchedBy('#10 Unknown Athlete').click();
    await expect(
      libraryBasePage.genericTextLocator('Pitched By: #99 Unknown Athlete, #10 Unknown Athlete').first()
    ).toBeVisible();

    // Apply a "Batting" filter and confirm total active filter count
    await softballInsightsPanel.batting.click();
    await libraryBasePage.genericLabelLocator('Fly Ball').click();
    await expect(generalFilters.sidePanelFiltersCount).toHaveText('2 Filters');

    // Clear all filters and verify the filter panel button is reset
    await generalFilters.clearFiltersButton.click();
    await expect(generalFilters.filterSidePanelButton).toBeVisible();
  });

  test('Should dismiss filters one at a time and update moments grid accordingly - Soccer', async ({
    soccerFilters,
    generalGridPanel,
    soccerInsightsPanel,
    soccerGridPanel,
    filtersUtils,
  }) => {
    // Navigate to the Soccer team library and open the test video
    await filtersUtils.openVideoAndFilters('720795');

    // Open the insights panel
    await soccerInsightsPanel.openInsightsPanel();

    // Apply a "Goals" filter with a specific player and attacking outcome
    await soccerInsightsPanel.goals.first().click();
    await soccerInsightsPanel.scoutPlayerInvolvement('#5 Julie Ertz').click();
    await soccerInsightsPanel.scoutAttackingOutcomes('Shots').click();

    // Open the grid view and verify moments match expected outcome
    await generalGridPanel.gridViewButton.click();
    await soccerGridPanel.soccerSequences.click();
    await expect(soccerGridPanel.sequenceOutcomeColumn('1')).toContainText('Shot, Goal');
    await expect(soccerGridPanel.sequenceOutcomeColumn('2')).toContainText('Shot, Goal');

    // Remove "Key Moments" filter and validate the updated grid counts
    await soccerFilters.removeFilterButton.first().click();
    await expect(soccerGridPanel.soccerMoments.first()).toContainText('11 Moments');
    await expect(soccerGridPanel.soccerSequences.first()).toContainText('6 Sequences');

    // Remove "Attacking Outcomes" filter and validate player moments still appear
    await soccerFilters.removeFilterButton.nth(1).click();
    await soccerGridPanel.soccerMoments.click();
    await expect(soccerGridPanel.soccerMoments.first()).toContainText('17 Moments');
    await expect(soccerGridPanel.soccerMomentDataChipSegment(4, 0, 'Goal', '#5 J. Ertz')).toBeVisible();
    await expect(soccerGridPanel.soccerMomentDataChipSegment(6, 0, 'Pass', '#5 J. Ertz')).toBeVisible();
  });
});
