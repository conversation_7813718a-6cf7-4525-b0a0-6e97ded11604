import { createRandomWords } from 'playwright-shared';

import { expect, test } from '../../../../fixtures/analyze/filtersFixture';

/**
 * Test suite for saved filters functionality in the video player side panel.
 * Covers creation, renaming, duplicate handling, and validation of filters across different sports.
 */

test.use({
  storageState: 'playwright/.auth/hudlTeamAdmin.json',
  browserName: 'chromium',
  channel: 'chromium',
});

test.describe('Saved Filters Behavior', () => {
  // Generate random names
  const firstFilterName = createRandomWords(1);
  const secondFilterName = createRandomWords(1);

  // Cleanup saved filters after each test if they exist
  test.afterEach(async ({ generalFilters }) => {
    try {
      const firstFilterLocator = generalFilters.savedFilterOption(firstFilterName);
      const secondFilterLocator = generalFilters.savedFilterOption(secondFilterName);

      if (await firstFilterLocator.isVisible()) {
        await generalFilters.deleteSavedFilterButton(firstFilterName).click();
        await expect(firstFilterLocator).not.toBeVisible();
      }

      if (await secondFilterLocator.isVisible()) {
        await generalFilters.deleteSavedFilterButton(secondFilterName).click();
        await expect(secondFilterLocator).not.toBeVisible();
      }
    } catch (err) {
      console.warn('Cleanup failed in afterEach:', err);
    }
  });

  test('Should create, rename, and prevent duplicate saved filters - Soccer', async ({
    filtersUtils,
    generalFilters,
    soccerInsightsPanel,
  }) => {
    // Navigate to the Soccer team library and open the test video
    await filtersUtils.openVideoAndFilters('720795');

    // Open the insights panel
    await soccerInsightsPanel.openInsightsPanel();

    // Apply an insights zone filter and a team filter
    await soccerInsightsPanel.scoutLocationChartZone('11').click();
    await generalFilters.teamsInputDropdown.click();
    await generalFilters.teamsFilterOption('player_involvement_card_automation').click();

    // Save the first custom filter
    await generalFilters.saveFiltersButton.click();
    await generalFilters.saveFiltersInputField.fill(firstFilterName);
    await generalFilters.saveFiltersInputField.press('Enter');

    // Attempt to save a duplicate filter and verify error is shown
    await generalFilters.page.waitForLoadState('networkidle');
    await generalFilters.saveFiltersButton.click();
    await generalFilters.saveFiltersInputField.fill(firstFilterName);
    await generalFilters.saveFiltersInputField.press('Enter');
    await expect(generalFilters.filterNameAlreadyExistErrorText).toBeVisible();
    await expect(generalFilters.savedFilterOption(firstFilterName).nth(1)).not.toBeVisible();

    // Clear input, use a different name, and save a second filter
    await generalFilters.saveFiltersInputField.press('Escape');
    await generalFilters.saveFiltersButton.click();
    await generalFilters.saveFiltersInputField.fill(secondFilterName);
    await generalFilters.saveFiltersInputField.press('Enter');

    // Attempt to rename the second filter using the first filter's name and verify duplicate prevention
    await generalFilters.editSavedFilterButton(secondFilterName).click();
    await generalFilters.saveFiltersInputField.clear();
    await generalFilters.saveFiltersInputField.fill(firstFilterName);
    await expect(generalFilters.filterNameAlreadyExistErrorText).toBeVisible();
    await generalFilters.saveFiltersInputField.press('Escape');
  });
});
