import { expect, mergeTests } from '@playwright/test';

import { performanceCoreModuleLayoutsMockTest } from '../../fixtures/mockFixtures/uiConfigurationFixtures';
import { baseballTaggingSessionMockTest } from '../../fixtures/mockFixtures/v3TaggingSessionFixtures';
import { Sport } from '../../types';
import { performanceCoreUsers } from '../../utils/userHelper';

const test = mergeTests(baseballTaggingSessionMockTest, performanceCoreModuleLayoutsMockTest);

test.use({
  requiresVideoPlayback: true,
  user: performanceCoreUsers.PerformanceCoreTeamAdmin,
  sport: Sport.Baseball,
});

test.describe('Grid Editing', () => {
  test.describe('Diamond Sports', () => {
    test('should update clip context bar when grid is updated', async ({
      testUser,
      videoPage,
      _mockVideoAndGrid,
      _mockBaseballTaggingSessionDefault,
    }) => {
      await videoPage.navigationToVideoPlayer(testUser.team.internalId, 'VmlkZW82NDQyZWZkOWJmMjUyYzEzNjA3N2Q2YmM=');
      // Verify initial clip data
      await expect(videoPage.clipContextBar.clipContextMoment('clip-count')).toHaveText('1 / 6');
      await expect(videoPage.clipContextBar.clipContextMoment('period')).toHaveText('▲ 1');
      await expect(videoPage.clipContextBar.clipContextMoment('count')).toHaveText('0–0');
      await expect(videoPage.clipContextBar.clipContextMoment('pitcher')).toHaveText('P: #7');
      await expect(videoPage.clipContextBar.clipContextMoment('batter')).toHaveText('B: #7 C. Ferguson');
      await expect(videoPage.clipContextBar.clipContextMoment('outs')).toHaveText('1 Out');
      await expect(videoPage.clipContextBar.clipContextMoment('score')).toHaveText('R: 0–0');
      await expect(videoPage.clipContextBar.clipContextMoment('results')).toHaveText('Ground Ball');

      // Change clip
      await videoPage.gridComponent.clickGridRow(6);
      await expect(videoPage.clipContextBar.clipContextMoment('clip-count')).toHaveText('6 / 6');
      await expect(videoPage.clipContextBar.clipContextMoment('period')).toHaveText('▲ 1');
      await expect(videoPage.clipContextBar.clipContextMoment('count')).toHaveText('2–1');
      await expect(videoPage.clipContextBar.clipContextMoment('pitcher')).toHaveText('P: #7');
      await expect(videoPage.clipContextBar.clipContextMoment('batter')).toHaveText('B: #5 G. Shumaker');
      await expect(videoPage.clipContextBar.clipContextMoment('outs')).toHaveText('1 Out');
      await expect(videoPage.clipContextBar.clipContextMoment('score')).toHaveText('R: 0–0');
      await expect(videoPage.clipContextBar.clipContextMoment('results')).toHaveText('Fly Ball');

      // Update clip
      await videoPage.gridComponent.clickGridCell('pitchResult', '6');
      await videoPage.gridComponent.selectValueInGridCell('Foul Ball');
      await videoPage.gridComponent.clickGridCell('batter', '6');
      await videoPage.gridComponent.selectValueInGridCell('#2');

      // navigte grid cells to make sure runnerOnFirst column is in view
      await videoPage.gridComponent.navigateGridUntilCellIsVisible('videoTime', '6');

      await videoPage.gridComponent.clickGridCell('runnerOnFirst', '6');
      await videoPage.gridComponent.selectValueInGridCell('Out At First');

      // verify clip context bar updated
      await expect(videoPage.clipContextBar.clipContextMoment('period')).toHaveText('▲ 1');
      await expect(videoPage.clipContextBar.clipContextMoment('count')).toHaveText('2–1');
      await expect(videoPage.clipContextBar.clipContextMoment('pitcher')).toHaveText('P: #7');
      await expect(videoPage.clipContextBar.clipContextMoment('batter')).toHaveText('B: #2');
      await expect(videoPage.clipContextBar.clipContextMoment('outs')).toHaveText('2 Outs');
      await expect(videoPage.clipContextBar.clipContextMoment('score')).toHaveText('R: 0–0');
      await expect(videoPage.clipContextBar.clipContextMoment('results')).toHaveText('Foul Ball');
    });
  });
});
