import { expect, mergeTests } from '@playwright/test';

import { classicFacadeGetClipsMockTest } from '../../fixtures/mockFixtures/classicFacadeFixtures';
import { performanceCoreModuleLayoutsMockTest } from '../../fixtures/mockFixtures/uiConfigurationFixtures';
import { Sport } from '../../types';
import { performanceCoreUsers } from '../../utils/userHelper';
import { SortOrder } from 'analyze/components/shared/Grid/Grid.types';

const test = mergeTests(classicFacadeGetClipsMockTest, performanceCoreModuleLayoutsMockTest);

test.use({
  requiresVideoPlayback: true,
  user: performanceCoreUsers.PerformanceCoreTeamAdmin,
  sport: Sport.Football,
});

test.describe('Grid Autofill', () => {
  test('should autofill DN and DIST when YARD LN value is entered', async ({
    testUser,
    videoPage,
    _mockClassicClipsEmptyData,
    _mockVideoAndGrid,
  }) => {
    await videoPage.navigationToVideoPlayer(testUser.team.internalId, '66856039');
    await videoPage.gridComponent.clickGridCell('YARD LN', '2');
    await videoPage.gridComponent.enterValueInGridCell('YARD LN', '2', '-25');
    // update these assertions to .toContainClass when playwright is upgraded to v1.52
    await expect(videoPage.gridComponent.gridCell('DN', '2')).toHaveClass(/.*isAutofilled.*/);
    await expect(videoPage.gridComponent.gridCell('DIST', '2')).toHaveClass(/.*isAutofilled.*/);
    await expect(videoPage.gridComponent.gridCell('DN', '2')).toHaveText('1');
    await expect(videoPage.gridComponent.gridCell('DIST', '2')).toHaveText('10');
  });

  test('should work when grid column sort is applied', async ({
    testUser,
    videoPage,
    _mockClassicClipsEmptyData,
    _mockVideoAndGrid,
  }) => {
    await videoPage.navigationToVideoPlayer(testUser.team.internalId, '66856039');
    await videoPage.gridComponent.sortGridColumn('ODK', SortOrder.FrequencyAscending);

    await videoPage.gridComponent.clickGridCell('YARD LN', '2');
    await videoPage.gridComponent.enterValueInGridCell('YARD LN', '2', '-25');
    // update these assertions to .toContainClass when playwright is upgraded to v1.52
    await expect(videoPage.gridComponent.gridCell('DN', '2')).toHaveClass(/.*isAutofilled.*/);
    await expect(videoPage.gridComponent.gridCell('DIST', '2')).toHaveClass(/.*isAutofilled.*/);
    await expect(videoPage.gridComponent.gridCell('DN', '2')).toHaveText('1');
    await expect(videoPage.gridComponent.gridCell('DIST', '2')).toHaveText('10');
  });

  test('should work when insights filter is applied', async ({
    testUser,
    videoPage,
    _mockClassicClipsEmptyData,
    _mockVideoInsightsAndGrid,
  }) => {
    await videoPage.navigationToVideoPlayer(testUser.team.internalId, '66856039');

    await videoPage.insightsModuleODKFilter('O').click();

    await videoPage.gridComponent.clickGridCell('YARD LN', '2');
    await videoPage.gridComponent.enterValueInGridCell('YARD LN', '2', '-25');
    // update these assertions to .toContainClass when playwright is upgraded to v1.52
    await expect(videoPage.gridComponent.gridCell('DN', '2')).toHaveClass(/.*isAutofilled.*/);
    await expect(videoPage.gridComponent.gridCell('DIST', '2')).toHaveClass(/.*isAutofilled.*/);
    await expect(videoPage.gridComponent.gridCell('DN', '2')).toHaveText('1');
    await expect(videoPage.gridComponent.gridCell('DIST', '2')).toHaveText('10');
  });
});
