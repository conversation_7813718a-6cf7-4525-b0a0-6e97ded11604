import { expect, mergeTests } from '@playwright/test';

import { getOwnTeamInfoQueryMockTest } from '../../fixtures/mockFixtures/teamFixtures';
import { performanceCoreModuleLayoutsMockTest } from '../../fixtures/mockFixtures/uiConfigurationFixtures';
import { baseballTaggingSessionMockTest } from '../../fixtures/mockFixtures/v3TaggingSessionFixtures';
import { Sport } from '../../types';
import { performanceCoreUsers } from '../../utils/userHelper';

const test = mergeTests(
  performanceCoreModuleLayoutsMockTest,
  baseballTaggingSessionMockTest,
  getOwnTeamInfoQueryMockTest
);

test.use({
  requiresVideoPlayback: true,
  user: performanceCoreUsers.PerformanceCoreTeamAdmin,
  sport: Sport.Baseball,
});

test.describe('Test Performance Core Mocking', () => {
  test.describe('with assist data', () => {
    test('load assist tagged baseball game', async ({
      videoPage,
      testUser,
      _mockBaseballTaggingSessionDefault,
      _mockVideoAndInsights,
    }) => {
      await videoPage.navigationToVideoPlayer(testUser.team.internalId, 'VmlkZW82NDQyZWZkOWJmMjUyYzEzNjA3N2Q2YmM=');
      await expect(videoPage.videoControls.volume).toBeVisible();
      await expect(videoPage.insightsModuleNoDataState).not.toBeVisible();
    });
  });

  test.describe('with no assist data', () => {
    test('load baseball game with no tagged data', async ({
      testUser,
      videoPage,
      _mockBaseballTaggingSessionNoAssistData,
      _mockVideoAndInsights,
    }) => {
      await videoPage.navigationToVideoPlayer(testUser.team.internalId, 'VmlkZW82NDQyZWZkOWJmMjUyYzEzNjA3N2Q2YmM=');
      await expect(videoPage.insightsModuleNoDataState).toBeVisible();
    });
  });

  test.describe('mock variants no assist data, no athlete audio', () => {
    test.use({
      user: performanceCoreUsers.PerformanceCoreAthlete,
    });
    test.skip('load untagged baseball game with disabled audio for athletes', async ({
      testUser,
      videoPage,
      _mockVideoAndInsights,
      _mockNoAthleteAudio,
      _mockBaseballTaggingSessionNoAssistData,
    }) => {
      await videoPage.navigationToVideoPlayer(testUser.team.internalId, 'VmlkZW82NDQyZWZkOWJmMjUyYzEzNjA3N2Q2YmM=');
      await expect(videoPage.insightsModuleNoDataState).toBeVisible();
      await expect(videoPage.videoControls.volume).toHaveClass(/.*iconInactive.*/);
    });
  });
});
