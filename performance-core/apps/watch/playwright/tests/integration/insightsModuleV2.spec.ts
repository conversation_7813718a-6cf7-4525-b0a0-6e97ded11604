import { expect, mergeTests } from '@playwright/test';

import { performanceCoreModuleLayoutsMockTest } from '../../fixtures/mockFixtures/uiConfigurationFixtures';
import { basketballTaggingSessionMockTest } from '../../fixtures/mockFixtures/v3TaggingSessionFixtures';
import { Sport } from '../../types';

const test = mergeTests(performanceCoreModuleLayoutsMockTest, basketballTaggingSessionMockTest);

test.use({
  requiresVideoPlayback: true,
  sport: Sport.Basketball,
});

test.describe('Test insights module with moment generator', () => {
  test.describe('default moment generated tagging session', () => {
    test('Load default basketball data, filter insights module, and verify data in grid', async ({
      videoPage,
      testUser,
      _mockBasketballTaggingSessionDefault,
      _mockVideoInsightsAndGrid,
    }) => {
      await videoPage.navigationToVideoPlayer(testUser.team.internalId, 'VmlkZW82NDQ5NzRmZmViYzRiMjE0NTg2YTgxM2Q=');
      await videoPage.insightsModuleFilter('period_Q3').click();
      await expect(videoPage.gridComponent.gridCell('videoTime', '4')).toBeVisible();
    });
  });

  test.describe('shotsTaken moment generated tagging session', () => {
    test('Load custom basketball data, filter insights module, and verify data in grid', async ({
      videoPage,
      testUser,
      _mockVideoInsightsAndGrid,
      _mockBasketballTaggingSessionShotsTakenFromMomentGenerator,
    }) => {
      await videoPage.navigationToVideoPlayer(testUser.team.internalId, 'VmlkZW82NDQ5NzRmZmViYzRiMjE0NTg2YTgxM2Q=');
      await videoPage.insightsModuleFilter('scoutteam_phase_Offense').click();
      await expect(videoPage.gridComponent.gridKeyMomentsPill(0).first()).toHaveText('Substitution, Allen (#13)');
    });
  });
});
