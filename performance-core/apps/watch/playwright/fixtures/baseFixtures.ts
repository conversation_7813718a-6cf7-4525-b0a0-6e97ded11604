import { test as base } from '@playwright/test';
import { createRandomVideoId, CustomTestFixture } from 'playwright-shared';

import { Sport } from '../types';
import {
  getTeamBySportType,
  getUserByUsername,
  performanceCoreUsers,
  Team,
  User,
  userHasAccessToTeam,
} from '../utils/userHelper';

/**
 * Base set of test-scoped fixtures that are shared with all other Performance Core fixtures
 */
export type PerformanceCoreBaseFixtures = {
  /**
   *
   */
  generatedVideoId: string;
  /**
   * Optional test fixture to determine what sport a test is being run against.
   *  - Defaults to American Football {@link Sport.Football}.
   */
  sport: Sport;
  /**
   * Test fixture to get additional user information for the test being executed.
   * This will look up the user by username in the {@link performanceCoreUsers} object.
   * - Depends on the following optional test fixtures
   *   - {@link PerformanceCoreBaseFixtures.sport}
   *   - {@link PerformanceCoreBaseFixtures.user}
   */
  testUser: {
    user: User;
    team: Team;
  };
  /**
   * Optional test fixture to determine what test user to use for a test
   *  - Defaults to {@link performanceCoreUsers.PerformanceCoreAdmin}.
   */
  user: User;
};

export type RequiresVideoPlaybackWorkerFixture = {
  /**
   * Optional Worker fixture to determine whether or not the test requires video playback.
   * - Defaults to false.
   *   - If true, the test will use a browser channel that supports video playback (ex: Chrome)
   *   - If false, the test will use the default channel specified in the Playwright configuration (ex: Chromium)
   * - Used by our overridden PlaywrightWorkerOptions.channel fixture in {@link performanceCoreBaseFixtures}
   */
  requiresVideoPlayback: boolean;
};

/**
 * Map of reversed array key-value pairs for the {@link performanceCoreUsers} object to look up the <UserType> key by a given <User> value.
 * - ex: userKeyLookup.get(performanceCoreUsers.PerformanceCoreAdmin) will return 'PerformanceCoreAdmin'
 * - This is used to determine the storage state file name for the Playwright context fixture based on the {@link PerformanceCoreBaseFixtures.user} fixture value.
 *    - const storageState = `./playwright/.auth/${userKeyLookup.get(user)}.json`;
 */
const userKeyLookup = new Map(Object.entries(performanceCoreUsers).map(([key, value]) => [value, key]));
const isUIMode = process.env.UI_MODE === 'true';

/**
 * Implementation of the Performance Core base fixtures.
 * - Uses {@link CustomTestFixture} type with the following Test (T) and Worker (W) fixtures included:
 *   - Test (T) fixtures: {@link PerformanceCoreBaseFixtures}
 *   - Worker (W) fixtures: {@link RequiresVideoPlaybackWorkerFixture}
 */
export const performanceCoreBaseFixtures: CustomTestFixture<
  PerformanceCoreBaseFixtures,
  RequiresVideoPlaybackWorkerFixture
> = {
  user: [performanceCoreUsers.PerformanceCoreTeamAdmin, { option: true }],
  sport: [Sport.Football, { option: true }],
  requiresVideoPlayback: [false, { scope: 'worker' }],

  // Override the Playwright default context fixture with our own implementation to include storage state
  context: async ({ browser, user }, use) => {
    const storageState = `./playwright/.auth/${userKeyLookup.get(user)}.json`;
    const context = await browser.newContext({ storageState });
    try {
      await use(context);
    } finally {
      // Ensure the context is closed after use
      await context.close();
    }
  },

  // Override the Playwright default page fixture with our own implementation that is dependent on our conxtext fixture above that includes storage state
  page: async ({ context }, use) => {
    const page = await context.newPage();
    try {
      await use(page);
    } finally {
      // Ensure the page is closed after use
      await page.unrouteAll({ behavior: 'wait' });
      await page.close();
    }
  },

  // Override the Playwright default channel fixture with our own implementation that is dependent on our requiresVideoPlayback fixture.
  channel: async ({ channel, requiresVideoPlayback }, use) => {
    if (requiresVideoPlayback) {
      // Default to Chrome Beta for these tests. Could make this configurable as a test/fixture option if needed.
      await use('chrome');
      // throw new Error(
      //   'Video playback is not supported in Chromium. Must configure project to use a "chrome" or "chrome-*" channel for these tests.'
      // );
    } else {
      await use(channel);
    }
  },
  // Override the Playwright default launchOptions fixture with our own implementation that is dependent isUIMode
  // If isUIMode is true, we use the new headless mode for video playback
  launchOptions: async ({ launchOptions }, use) => {
    if (!isUIMode) {
      await use(launchOptions);
    } else {
      // Use new headless mode for video playback in UI mode
      await use({
        ...launchOptions,
        args: [
          ...(launchOptions?.args || []),
          '--headless=new', // Use new headless mode
          '--disable-gpu', // Disables GPU hardware acceleration
          '--disable-software-rasterizer', // Prevents Chrome from blocking media rendering in headless mode
        ],
      });
    }
  },
  testUser: async ({ user, sport }, use) => {
    const testUser = getUserByUsername(user.username);
    const testTeam = getTeamBySportType(sport);
    if (!testUser) {
      throw new Error(
        `No test user found for username: ${user.username}. Check the performanceCoreUsers object for available users.`
      );
    }
    if (testTeam.internalId && !userHasAccessToTeam(testUser, testTeam)) {
      throw new Error(
        `Test user: ${user.username} does not have access to ${testTeam.internalId}. Check the performanceCoreUsers object for available teams.`
      );
    }

    await use({ user: testUser, team: testTeam });
  },

  // eslint-disable-next-line no-empty-pattern -- {} is needed instead of _ for UI Mode
  generatedVideoId: async ({}, use) => {
    const videoId = createRandomVideoId();
    await use(videoId);
  },
};

/**
 * Playwright test instance for the {@link performanceCoreBaseFixtures} object
 * Extends the following Test and Worker fixtures:
 *  - Test Fixtures: {@link PerformanceCoreBaseFixtures}
 *  - Worker Fixtures:{@link RequiresVideoPlaybackWorkerFixture}
 *
 * @example
 * ```typescript
 * import { performanceCoreBaseTest as test } from './path/to/fixtures';
 *
 * test('example test', async ({ page, testUser, sport }) => {
 *
 *
 */ export const performanceCoreBaseTest = base.extend<PerformanceCoreBaseFixtures, RequiresVideoPlaybackWorkerFixture>(
  performanceCoreBaseFixtures
);
