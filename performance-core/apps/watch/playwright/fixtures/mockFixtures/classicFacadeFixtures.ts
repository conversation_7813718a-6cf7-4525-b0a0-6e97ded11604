import { test as base } from '@playwright/test';
import { CustomTestFixture } from 'playwright-shared';

import * as DataRepositories from '../../dataRepositories';
import * as Mocks from '../../mocks';
import { BaseMockTestFixtures, BaseMockWorkerFixtures, mockBaseFixtures } from './mockBaseFixture';

/**
 * Test-scoped mock fixtures for the /clips ClassicFacade REST endpoint.
 */
export type ClassicFacadeGetClipsMockFixtures = {
  /**
   * Mock fixture that returns a default set of clips with data for the /clips ClassicFacade REST endpoint.
   */
  _mockClassicClipsDefault: void;
  /**
   * Mock fixture that returns an set of clips with empty data for the /clips ClassicFacade REST endpoint.
   */
  _mockClassicClipsEmptyData: void;
};

/**
 * Implemention of the mock fixtures for the /clips ClassicFacade REST endpoint.
 * - Uses {@link CustomTestFixture} type with the following Test (T) and Worker (W) fixtures included:
 *   - Test (T) fixtures: {@link ClassicFacadeGetClipsMockFixtures} and {@link BaseMockTestFixtures}
 *   - Worker (W) fixtures:{@link BaseMockWorkerFixtures}
 */
export const classicFacadeGetClipsMockFixtures: CustomTestFixture<
  ClassicFacadeGetClipsMockFixtures & BaseMockTestFixtures,
  BaseMockWorkerFixtures
> = {
  ...mockBaseFixtures,
  _mockClassicClipsDefault: async ({ page }, use) => {
    await Mocks.mockGetClips(page, () => DataRepositories.getClassicClipsForCutupDefault());
    await use();
  },
  _mockClassicClipsEmptyData: async ({ page }, use) => {
    await Mocks.mockGetClips(page, () => DataRepositories.getClassicClipsForCutupEmptyData());
    await use();
  },
};

/**
 * Playwright test instance for the {@link classicFacadeGetClipsMockFixtures} object
 * Extends the following Test and Worker fixtures:
 *  - Test Fixtures: {@link ClassicFacadeGetClipsMockFixtures} and {@link BaseMockTestFixtures}
 *  - Worker Fixtures:{@link BaseMockWorkerFixtures}
 *
 * @example
 * ```typescript
 * import { classicFacadeGetClipsMockTest as test } from './path/to/fixtures';
 *
 * test('example test', async ({ mockClassicClipsEmptyData }) => {
 *
 */
export const classicFacadeGetClipsMockTest = base.extend<
  ClassicFacadeGetClipsMockFixtures & BaseMockTestFixtures,
  BaseMockWorkerFixtures
>(classicFacadeGetClipsMockFixtures);
