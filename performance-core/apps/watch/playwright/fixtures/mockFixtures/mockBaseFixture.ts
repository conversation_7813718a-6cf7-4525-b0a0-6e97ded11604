import { test as base } from '@playwright/test';
import { CustomTestFixture } from 'playwright-shared';

import * as DataRepositories from '../../dataRepositories';
import * as Mocks from '../../mocks';
import { PerformanceCoreBaseFixtures, RequiresVideoPlaybackWorkerFixture } from '../baseFixtures';
import { PageObjectFixtures, pageObjectFixtures } from '../pageObjectFixtures';

/**
 * Test-scoped base mock fixtures for Performance Core
 */
export type MockBaseFixtures = {
  /**
   * Optional test fixture that is used to determine whether or not to mock the responses for /updateBreakdownData REST and UpdateMomentTagsMutation GraphQL Operaton.
   * - Defaults to true.
   *  - Used by the following fixtures:
   *    - {@link MockBaseAutoFixtures.mockClassicUpdateBreakdownData}
   *    - {@link MockBaseAutoFixtures.mockV3UpdateMomentTags}
   */
  _mockGridUpdateRequests: boolean;
};

/**
 * Auto-scoped mock fixtures that are set up even if test doesn't list them directly
 */
export type MockBaseAutoFixtures = {
  /**
   * Mocks the response for the Classic updateBreakdownData REST endpoint
   *  - Depends on value of {@link MockBaseFixtures._mockGridUpdateRequests}
   */
  _mockClassicUpdateBreakdownData: void;
  /**
   * Mocks the response for the V3 UpdateMomentTagsMutation GraphQL Operation
   *  - Depends on value of {@link MockBaseFixtures._mockGridUpdateRequests}
   */
  _mockV3UpdateMomentTags: void;
};

/**
 * Combined type that includes all of the shared test fixtures to make available in all mock test fixtures
 *  - Includes Test Fixtures: {@link MockBaseFixtures}, {@link MockBaseAutoFixtures}, {@link PerformanceCoreBaseFixtures}, and {@link PageObjectFixtures}
 */
export type BaseMockTestFixtures = MockBaseFixtures &
  MockBaseAutoFixtures &
  PerformanceCoreBaseFixtures &
  PageObjectFixtures;

/**
 * Combined type that includes all of the shared worker fixtures to make available in all mock test fixtures
 * Includes Worker Fixtures: {@link RequiresVideoPlaybackWorkerFixture}
 */
export type BaseMockWorkerFixtures = RequiresVideoPlaybackWorkerFixture;

/**
 * Implementation of the base mock fixtures.
 * - Uses {@link CustomTestFixture} type with the following Test (T) and Worker (W) fixtures included:
 *   - Test (T) fixtures: {@link BaseMockTestFixtures}
 *   - Worker (W) fixtures: {@link BaseMockWorkerFixtures}
 */

export const mockBaseFixtures: CustomTestFixture<BaseMockTestFixtures, BaseMockWorkerFixtures> = {
  // performanceCoreBaseFixtures is included with ...pageObjectFixtures so we don't need to include it here again
  // but we do need to include the PerformanceCoreBaseFixtures type in the BaseMockTestFixtures still
  // because we need the requiresVideoPlayback worker fixture to be available
  ...pageObjectFixtures,
  _mockGridUpdateRequests: [true, { option: true }],
  _mockClassicUpdateBreakdownData: [
    async ({ page, _mockGridUpdateRequests }, use) => {
      if (_mockGridUpdateRequests) {
        await Mocks.mockUpdateBreakdownData(page);
      }
      await use();
    },
    { auto: true },
  ],
  _mockV3UpdateMomentTags: [
    async ({ page, _mockGridUpdateRequests }, use) => {
      if (_mockGridUpdateRequests) {
        await Mocks.mockUpdateMomentTags(page, () => DataRepositories.updateMomentTags());
      }
      await use();
    },
    { auto: true },
  ],
};

/**
 * Playwright test instance for the {@link mockBaseFixtures} object
 * Extends the following Test and Worker fixtures:
 *  - Test Fixtures: {@link BaseMockTestFixtures}
 *  - Worker Fixtures:{@link BaseMockWorkerFixtures}
 *
 * @example
 * ```typescript
 * import { mockBaseTest as test } from './path/to/fixtures';
 *
 * test.use({mockGridUpdateRequests: true});
 * test('example test', async ({ page }) => {
 *
 *
 */ export const mockBaseTest = base.extend<BaseMockTestFixtures, BaseMockWorkerFixtures>(mockBaseFixtures);
