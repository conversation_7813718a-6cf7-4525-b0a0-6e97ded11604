import { test as base } from '@playwright/test';
import { CustomTestFixture } from 'playwright-shared';

import * as DataRepositories from '../../dataRepositories';
import * as Mocks from '../../mocks';
import { shotsTakenTaggingSession } from '../../utils/taggingSessionGeneration/generatedTaggingSessions/basketballTaggingSessions';
import { BaseMockTestFixtures, BaseMockWorkerFixtures, mockBaseFixtures } from './mockBaseFixture';

/**
 *
 * Test-scoped mock fixtures by sport type for the GetV3VideoAndTaggingData GraphQL Operation
 */
type V3TaggingSessionMockFixtures = {
  Baseball: {
    /**
     * Mock fixture that returns a default set of tagging session data for a V3 baseball video
     */
    _mockBaseballTaggingSessionDefault: void;
    /**
     * Mock fixture that returns an empty tagging session for a V3 baseball video
     */
    _mockBaseballTaggingSessionNoAssistData: void;
  };
  Basketball: {
    /**
     * Mock fixture that returns a default set of tagging session data for a V3 basketball video
     */
    _mockBasketballTaggingSessionDefault: void;
    /**
     * Mock fixture that returns a {@link shotsTakenTaggingSession} V3 basketbal tagging session from the moment generator for a V3 basketball video
     */
    _mockBasketballTaggingSessionShotsTakenFromMomentGenerator: void;
  };
};

// Convenience types for readability of the fixture and test fixture objects that these are included in
type BaseballTestFixtures = V3TaggingSessionMockFixtures['Baseball'] & BaseMockTestFixtures;
type BasketballTestFixtures = V3TaggingSessionMockFixtures['Basketball'] & BaseMockTestFixtures;

/**
 * Implementation of the Baseball specific mock fixtures for the GetV3VideoAndTaggingData GraphQL Operation
 * - Uses {@link CustomTestFixture} type with the following Test (T) and Worker (W) fixtures included:
 *   - Test (T) fixtures: {@link GetOwnTeamInfoQueryMockFixtures} and {@link BaseballTestFixtures}
 *   - Worker (W) fixtures: {@link BaseMockWorkerFixtures}
 *
 */
export const baseballV3TaggingSessionMockFixtures: CustomTestFixture<BaseballTestFixtures, BaseMockWorkerFixtures> = {
  ...mockBaseFixtures,
  _mockBaseballTaggingSessionDefault: async ({ page }, use) => {
    await Mocks.mockGetV3VideosAndTaggingData(page, () => DataRepositories.getBaseballTaggingSession());
    await use();
  },
  _mockBaseballTaggingSessionNoAssistData: async ({ page }, use) => {
    await Mocks.mockGetV3VideosAndTaggingData(page, () =>
      DataRepositories.getTaggingSessionWithNoAssistData(DataRepositories.getBaseballTaggingSession())
    );
    await use();
  },
};

/**
 * Implementation of the Basketball specific mock fixtures for the GetV3VideoAndTaggingData GraphQL Operation
 * - Uses {@link CustomTestFixture} type with the following Test (T) and Worker (W) fixtures included:
 *   - Test (T) fixtures: {@link GetOwnTeamInfoQueryMockFixtures} and {@link BasketballTestFixtures}
 *   - Worker (W) fixtures: {@link BaseMockWorkerFixtures}
 */
export const basketballV3TaggingSessionMockFixtures: CustomTestFixture<BasketballTestFixtures, BaseMockWorkerFixtures> =
  {
    ...mockBaseFixtures,
    _mockBasketballTaggingSessionDefault: async ({ page }, use) => {
      await Mocks.mockGetV3VideosAndTaggingData(page, () => DataRepositories.getBasketballTaggingSession());
      await use();
    },
    _mockBasketballTaggingSessionShotsTakenFromMomentGenerator: async ({ page }, use) => {
      await Mocks.mockGetV3VideosAndTaggingData(page, () =>
        DataRepositories.getTaggingSessionWithMoments(
          DataRepositories.getBasketballTaggingSession(),
          shotsTakenTaggingSession.getMoments()
        )
      );
      await use();
    },
  };

/**
 * Playwright test instance for the {@link baseballV3TaggingSessionMockFixtures} object
 * Extends the following Test and Worker fixtures:
 *  - Test Fixtures: {@link BaseballTestFixtures}
 *  - Worker Fixtures:{@link BaseMockWorkerFixtures}
 *
 * @example
 * ```typescript
 * import { baseballTaggingSessionMockTest as test } from './path/to/fixtures';
 *
 * test('example test', async ({ mockBaseballTaggingSessionDefault }) => {
 *
 *
 */
export const baseballTaggingSessionMockTest = base.extend<BaseballTestFixtures, BaseMockWorkerFixtures>(
  baseballV3TaggingSessionMockFixtures
);

/**
 * Playwright test instance for the {@link basketballV3TaggingSessionMockFixtures} object
 * Extends the following Test and Worker fixtures:
 *  - Test Fixtures: {@link BasketballTestFixtures}
 *  - Worker Fixtures:{@link BaseMockWorkerFixtures}
 *
 * @example
 * ```typescript
 * import { basketballTaggingSessionMockTest as test } from './path/to/fixtures';
 *
 * test('example test', async ({ mockBasketballTaggingSessionDefault }) => {
 *
 *
 */
export const basketballTaggingSessionMockTest = base.extend<BasketballTestFixtures, BaseMockWorkerFixtures>(
  basketballV3TaggingSessionMockFixtures
);
