import { test as base } from '@playwright/test';
import { CustomTestFixture } from 'playwright-shared';

import { ConfigurationType } from '@hudl/performance-core-domain';

import * as DataRepositories from '../../dataRepositories';
import * as Mocks from '../../mocks';
import { BaseMockTestFixtures, BaseMockWorkerFixtures, mockBaseFixtures } from './mockBaseFixture';

/**
 * Test-scoped mock fixtures for the WebVspaGetUiConfigurationsR1 GraphQL Operation
 * with the PERFORMANCE_CORE_MODULE_LAYOUTS configuration type.
 */
export type PerformanceCoreModuleLayoutsMockFixtures = {
  /**
   * Mock fixture that returns a user's PERFORMANCE_CORE_MODULE_LAYOUTS UI Configuration to have only the video module displayed when navigating to a Performance Core video
   * - Depends on {@link BaseMockTestFixtures.testUser} and {@link BaseMockTestFixtures.sport} fixtures
   */
  _mockVideoOnly: void;
  /**
   * Mock fixture that returns a user's PERFORMANCE_CORE_MODULE_LAYOUTS UI Configuration to have the video and grid modules displayed when navigating to a Performance Core video
   * - Depends on {@link BaseMockTestFixtures.testUser} and {@link BaseMockTestFixtures.sport} fixtures
   */
  _mockVideoAndGrid: void;
  /**
   * Mock fixture that returns a user's PERFORMANCE_CORE_MODULE_LAYOUTS UI Configuration to have the video and timeline modules displayed when navigating to a Performance Core video
   * - Depends on {@link BaseMockTestFixtures.testUser} and {@link BaseMockTestFixtures.sport} fixtures
   */
  _mockVideoAndTimeline: void;
  /**
   * Mock fixture that returns a user's PERFORMANCE_CORE_MODULE_LAYOUTS UI Configuration to have the video and insights modules displayed when navigating to a Performance Core video
   * - Depends on {@link BaseMockTestFixtures.testUser} and {@link BaseMockTestFixtures.sport} fixtures
   */
  _mockVideoAndInsights: void;
  /**
   * Mock fixture that returns a user's PERFORMANCE_CORE_MODULE_LAYOUTS UI Configuration to have the video, insights, and grid modules displayed when navigating to a Performance Core video
   * - Depends on {@link BaseMockTestFixtures.testUser} and {@link BaseMockTestFixtures.sport} fixtures
   */
  _mockVideoInsightsAndGrid: void;
  /**
   * Mock fixture that returns a user's PERFORMANCE_CORE_MODULE_LAYOUTS UI Configuration to have the video, insights, and timeline modules displayed when navigating to a Performance Core video
   * - Depends on {@link BaseMockTestFixtures.testUser} and {@link BaseMockTestFixtures.sport} fixtures
   */
  _mockVideoInsightsAndTimeline: void;
};

/**
 * Implementation of the mock fixtures for the WebVspaGetUiConfigurationsR1 GraphQL Operation with the PERFORMANCE_CORE_MODULE_LAYOUTS configuration type.
 * - Uses {@link CustomTestFixture} type with the following Test (T) and Worker (W) fixtures included:
 *   - Test (T) fixtures: {@link PerformanceCoreModuleLayoutsMockFixtures} and {@link BaseMockTestFixtures}
 *   - Worker (W) fixtures: {@link BaseMockWorkerFixtures}
 */
export const performanceCoreModuleLayoutsMockFixtures: CustomTestFixture<
  PerformanceCoreModuleLayoutsMockFixtures & BaseMockTestFixtures,
  BaseMockWorkerFixtures
> = {
  ...mockBaseFixtures,
  _mockVideoOnly: async ({ page, testUser, sport }, use) => {
    await Mocks.mockUiConfiguration(
      page,
      testUser.team.internalId,
      ConfigurationType.PERFORMANCE_CORE_MODULE_LAYOUTS,
      () => DataRepositories.getPerformanceCoreModuleLayoutsVideoOnly(sport)
    );
    await use();
  },
  _mockVideoAndGrid: async ({ page, testUser, sport }, use) => {
    await Mocks.mockUiConfiguration(
      page,
      testUser.team.internalId,
      ConfigurationType.PERFORMANCE_CORE_MODULE_LAYOUTS,
      () => DataRepositories.getPerformanceCoreModuleLayoutsVideoAndGrid(sport)
    );
    await use();
  },
  _mockVideoAndTimeline: async ({ page, testUser, sport }, use) => {
    await Mocks.mockUiConfiguration(
      page,
      testUser.team.internalId,
      ConfigurationType.PERFORMANCE_CORE_MODULE_LAYOUTS,
      () => DataRepositories.getPerformanceCoreModuleLayoutsVideoAndTimeline(sport)
    );
    await use();
  },
  _mockVideoAndInsights: async ({ page, testUser, sport }, use) => {
    await Mocks.mockUiConfiguration(
      page,
      testUser.team.internalId,
      ConfigurationType.PERFORMANCE_CORE_MODULE_LAYOUTS,
      () => DataRepositories.getPerformanceCoreModuleLayoutsVideoAndInsights(sport)
    );
    await use();
  },
  _mockVideoInsightsAndGrid: async ({ page, testUser, sport }, use) => {
    await Mocks.mockUiConfiguration(
      page,
      testUser.team.internalId,
      ConfigurationType.PERFORMANCE_CORE_MODULE_LAYOUTS,
      () => DataRepositories.getPerformanceCoreModuleLayoutsVideoInsightsAndGrid(sport)
    );
    await use();
  },
  _mockVideoInsightsAndTimeline: async ({ page, testUser, sport }, use) => {
    await Mocks.mockUiConfiguration(
      page,
      testUser.team.internalId,
      ConfigurationType.PERFORMANCE_CORE_MODULE_LAYOUTS,
      () => DataRepositories.getPerformanceCoreModuleLayoutsVideoInsightsAndTimeline(sport)
    );
    await use();
  },
};

/**
 * Playwright test instance for the {@link performanceCoreModuleLayoutsMockFixtures} object
 * Extends the following Test and Worker fixtures:
 *  - Test Fixtures: {@link PerformanceCoreModuleLayoutsMockFixtures} and {@link BaseMockTestFixtures}
 *  - Worker Fixtures:{@link BaseMockWorkerFixtures}
 *
 * @example
 * ```typescript
 * import { performanceCoreModuleLayoutsMockTest as test } from './path/to/fixtures';
 *
 * test('example test', async ({ mockVideoOnly }) => {
 *
 *
 */
export const performanceCoreModuleLayoutsMockTest = base.extend<
  PerformanceCoreModuleLayoutsMockFixtures & BaseMockTestFixtures,
  BaseMockWorkerFixtures
>(performanceCoreModuleLayoutsMockFixtures);
