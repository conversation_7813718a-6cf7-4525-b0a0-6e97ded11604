import { test as base } from '@playwright/test';
import { CustomTestFixture } from 'playwright-shared';

import * as DataRepositories from '../../dataRepositories';
import * as Mocks from '../../mocks';
import { BaseMockTestFixtures, BaseMockWorkerFixtures, mockBaseFixtures } from './mockBaseFixture';

/**
 * Test-scoped mock fixtures for the GetOwnTeamInfo GraphQL Operation.
 */
export type GetOwnTeamInfoQueryMockFixtures = {
  /**
   * Mock fixture for the GetOwnTeamInfoQuery GraphQL Operation that sets canHearAthleteAudio property to false.
   */
  _mockNoAthleteAudio: void;
};

/**
 * Implementation of the mock fixtures for the GetOwnTeamInfoQuery GraphQL Operation.
 * - Uses {@link CustomTestFixture} type with the following Test (T) and Worker (W) fixtures included:
 *   - Test (T) fixtures: {@link GetOwnTeamInfoQueryMockFixtures} and {@link BaseMockTestFixtures}
 *   - Worker (W) fixtures: {@link BaseMockWorkerFixtures}
 */
export const getOwnTeamInfoQueryMockFixtures: CustomTestFixture<
  GetOwnTeamInfoQueryMockFixtures & BaseMockTestFixtures,
  BaseMockWorkerFixtures
> = {
  ...mockBaseFixtures,
  _mockNoAthleteAudio: async ({ page }, use) => {
    await Mocks.mockGetOwnTeamInfoQuery(page, () => DataRepositories.getOwnTeamInfoNoAthleteAudio());
    await use();
  },
};

/**
 * Playwright test instance for the {@link getOwnTeamInfoQueryMockFixtures} object
 * Extends the following Test and Worker fixtures:
 *  - Test Fixtures: {@link GetOwnTeamInfoQueryMockFixtures} and {@link BaseMockTestFixtures}
 *  - Worker Fixtures:{@link BaseMockWorkerFixtures}
 *
 * @example
 * ```typescript
 * import { getOwnTeamInfoQueryMockTest as test } from './path/to/fixtures';
 *
 * test('example test', async ({ mockNoAthleteAudio }) => {
 *
 *
 */
export const getOwnTeamInfoQueryMockTest = base.extend<
  GetOwnTeamInfoQueryMockFixtures & BaseMockTestFixtures,
  BaseMockWorkerFixtures
>(getOwnTeamInfoQueryMockFixtures);
