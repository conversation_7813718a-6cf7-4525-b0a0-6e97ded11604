import { type Locator, type Page } from '@playwright/test';

/**
 * This class provides methods to interact with various elements in the performance core library.
 */

export class LibraryBasePage {
  public readonly page: Page;
  public readonly libraryGridMode: Locator;
  public readonly libraryListMode: Locator;
  public readonly playLibraryItems: Locator;
  public readonly deleteButton: Locator;
  public readonly confirmDeleteButton: Locator;

  // Dynamic Locators
  public videoTitleButton: (videoTitle: string) => Locator;
  public videoSelectToggle: (videoTitle: string) => Locator;
  public genericTextLocator: (textValue: string) => Locator;
  public genericLabelLocator: (labelValue: string) => Locator;

  public constructor(page: Page) {
    this.page = page;
    this.libraryGridMode = page.getByTestId('library-item-list-grid-mode');
    this.libraryListMode = page.getByTestId('library-item-list-list-mode');
    this.playLibraryItems = page.getByTestId('library-items-actions-play');
    this.deleteButton = page.getByTestId('library-items-actions-delete');
    this.confirmDeleteButton = page.getByTestId('bulk-delete-modal-delete');
    this.videoTitleButton = (videoTitle: string) => this.page.getByTestId(`${videoTitle}-title-button`);
    this.videoSelectToggle = (videoTitle: string) => this.page.getByTestId(`libraryitem-${videoTitle}-toggle`);
    this.genericTextLocator = (textValue: string) => page.getByText(textValue);
    this.genericLabelLocator = (labelValue: string) => page.getByLabel(labelValue);
  }

  /**
   * ACTIONS
   */

  /**
   * Navigates to the teams' library page.
   * @returns {Promise<void>} A promise that resolves when navigation is complete.
   */
  public async navigateToTeamLibraryPage(teamId: string): Promise<void> {
    await Promise.all([
      this.page.goto(`/watch/team/${teamId}/analyze?automation=Y`),
      this.page.waitForResponse(
        (response) => response.url().includes(`/watch/team/${teamId}/analyze`) && response.status() === 200
      ),
    ]);
  }

  /**
   * Navigates to the teams' library page whilst also passing in a moment query cache key.
   * @returns {Promise<void>} A promise that resolves when navigation is complete.
   */
  public async navigateToWatchViewWithMomentQueryCacheKey(teamId: string, momentQueryCacheKey: string): Promise<void> {
    await Promise.all([
      this.page.goto(`/watch/team/${teamId}/analyze?mqck=${momentQueryCacheKey}`),
      this.page.waitForResponse(
        (response) => response.url().includes(`/watch/team/${teamId}/analyze`) && response.status() === 200
      ),
    ]);
  }

  /**
   * Navigates to the teams' library page whilst also passing in a moments query.
   * @returns {Promise<void>} A promise that resolves when navigation is complete.
   */
  public async navigateToWatchViewWithMomentsQuery(teamId: string, momentsQuery: string): Promise<void> {
    await Promise.all([
      this.page.goto(`/watch/team/${teamId}/analyze?mq=${momentsQuery}`),
      this.page.waitForResponse(
        (response) => response.url().includes(`/watch/team/${teamId}/analyze`) && response.status() === 200
      ),
    ]);
  }
}
