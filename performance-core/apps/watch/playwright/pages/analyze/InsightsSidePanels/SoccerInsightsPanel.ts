import { type Locator, type Page } from '@playwright/test';

import { GeneralInsightsPanel } from './GeneralInsightsPanel';

/**
 * This class provides methods to interact with various soccer elements on Insights side panel within PerfCore video player
 */

export class SoccerInsightsPanel extends GeneralInsightsPanel {
  public readonly goals: Locator;

  // Dynamic Locators
  public scoutPlayerInvolvement: (athleteName: string) => Locator;
  public scoutAttackingOutcomes: (outcome: string) => Locator;
  public scoutLocationChartZone: (zoneNumber: string) => Locator;

  public constructor(page: Page) {
    super(page);
    this.goals = page.getByTestId('keystats_Goals').getByText('Goals');

    // Dynamic Locators
    this.scoutPlayerInvolvement = (athleteName: string): Locator =>
      this.page.getByTestId('scoutteam_involvedathletes').getByText(athleteName);
    this.scoutAttackingOutcomes = (outcome: string): Locator =>
      this.page.getByTestId('scoutteam_sequencesendingwith').getByText(outcome);
    this.scoutLocationChartZone = (zoneNumber: string): Locator =>
      this.page.getByTestId(`scoutTeam_locationChart_zone ${zoneNumber}`);
  }
}
