import { expect, type Locator, type Page } from '@playwright/test';

/**
 * This class provides methods to interact with various general elements on Insights side panel within PerfCore video player
 */

export class GeneralInsightsPanel {
  public readonly page: Page;
  public readonly openInsightsFullScreen: Locator;
  public readonly insightModuleHeader: Locator;
  public readonly insightsModuleButton: Locator;

  public constructor(page: Page) {
    this.page = page;
    this.openInsightsFullScreen = page.getByTestId('insights-module-enter-fullscreen');
    this.insightModuleHeader = page.getByTestId('insights-module-Insights-mode');
    this.insightsModuleButton = page.getByTestId('insights-module-button');
  }

  public async openInsightsPanel(): Promise<void> {
    try {
      // Check if it's already open
      await expect(this.insightModuleHeader).toBeVisible({ timeout: 500 });
    } catch (e) {
      await this.insightsModuleButton.click();
    }
  }
}
