import setup from '@playwright/test';
import { logInUser } from 'playwright-shared';

import { performanceCoreUsers } from './utils/userHelper';

setup('authenticate all performance core users', async ({ browser }) => {
  await Promise.all(
    Object.entries(performanceCoreUsers).map(async ([user, { username }]) => {
      // create a new context for each user to avoid shared state
      // and user auth being written to the wrong file
      const context = await browser.newContext();
      const page = await context.newPage();
      await logInUser({ page, username, fileName: user });
    })
  );
});

setup('authenticate as hudl team admin', async ({ page }) => {
  await logInUser({ page: page, username: '<EMAIL>', fileName: `hudlTeamAdmin` });
});

setup('authenticate as classic admin', async ({ page }) => {
  // Perform authentication steps. Replace these actions with your own.
  await logInUser({ page: page, username: '<EMAIL>', fileName: `classicAdmin` });
});

setup('authenticate as basketball admin', async ({ page }) => {
  // Perform authentication steps. Replace these actions with your own.
  await logInUser({ page: page, username: '<EMAIL>', fileName: `basketballAdmin` });
});

setup('authenticate as football admin', async ({ page }) => {
  // Perform authentication steps. Replace these actions with your own.
  await logInUser({ page: page, username: '<EMAIL>', fileName: `footballAdmin` });
});
