/**
 * Re-exports all mock modules for easier imports throughout the application.
 * This centralizes mock implementations for GraphQL responses.
 *
 * Usage:
 * import { SampleMock } from '../mocks';
 */

export * from './classicfacade/classicfacadeMocks';
export * from './customersettings/customerSettingsMocks';
export * from './library/libraryMocks';
export * from './taggingsessions/taggingSessionsMocks';
export * from './teams/teamsMocks';
export * from './video/videoMocks';
