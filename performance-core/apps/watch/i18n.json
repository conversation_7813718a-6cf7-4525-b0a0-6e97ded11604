{"version": 1.1, "pre-load-external-sets": [], "sets": {"watch": {"keys": ["testing.hello", "analyze.breadcrumb.selected.text", "analyze.breadcrumb.create_saved_view_conversation.tooltip.text", "analyze.collaborate.create_saved_view_conversation.start_message.text", "analyze.module.content.edit_playlist_title_error_toast", "analyze.module.content.edit_file_title_error_toast", "analyze.module.content.create_tagging_session_error_toast", "analyze.module.content.selectable_actions.send_to_assist", "analyze.module.navigation.playlist.update_clip_title_error_toast", "analyze.module.navigation.playlist.clip.generic_title", "analyze.undo", "analyze.domain.column_definitions.shared.duration.header_label", "analyze.domain.column_definitions.shared.duration.cell_description", "analyze.domain.column_definitions.shared.select.header_description", "analyze.domain.column_definitions.shared.select.cell_description", "analyze.domain.column_definitions.shared.clip_title.header_label", "analyze.domain.column_definitions.shared.clip_title.cell_default_label", "analyze.domain.column_definitions.shared.moments.header_label", "analyze.domain.column_definitions.shared.game.header_label", "analyze.domain.column_definitions.shared.video_time.header_label", "analyze.domain.column_definitions.shared.video_time.cell_description", "analyze.domain.column_definitions.shared.date.header_label", "analyze.domain.column_definitions.shared.date.cell_description", "analyze.domain.column_definitions.shared.details.header_label", "analyze.domain.column_definitions.shared.comment.cell_description", "analyze.domain.column_definitions.shared.comment.has_comment_cell_description", "analyze.domain.column_definitions.shared.effect.cell_description", "analyze.domain.column_definitions.shared.effect.has_effect_cell_description", "analyze.domain.column_definitions.shared.videoTitle.header_label", "analyze.domain.column_definitions.shared.videoTitle.cell_description", "analyze.domain.column_definitions.baseballSoftball.hitType.header_label", "analyze.domain.column_definitions.baseballSoftball.swingType.header_label", "analyze.domain.column_definitions.baseballSoftball.pitchResult.header_label", "analyze.domain.column_definitions.baseballSoftball.battingResult.header_label", "analyze.domain.column_definitions.baseballSoftball.runnerOnFirst.header_label", "analyze.domain.column_definitions.baseballSoftball.runnerOnSecond.header_label", "analyze.domain.column_definitions.baseballSoftball.runnerOnThird.header_label", "analyze.domain.column_definitions.baseballSoftball.pitchType.header_label", "analyze.domain.column_definitions.baseballSoftball.outCount.header_label", "analyze.domain.column_definitions.baseballSoftball.onBase.header_label", "analyze.domain.column_definitions.baseballSoftball.onBase.cell_description", "analyze.domain.column_definitions.baseballSoftball.inning.header_label", "analyze.domain.column_definitions.baseballSoftball.pitcher.header_label", "analyze.domain.column_definitions.baseballSoftball.batter.header_label", "analyze.domain.column_definitions.baseballSoftball.score.header_label", "analyze.domain.column_definitions.baseballSoftball.score.cell_description", "analyze.domain.column_definitions.baseballSoftball.count.header_label", "analyze.domain.column_definitions.basketball.errorMessages.sameAthleteAssist", "analyze.domain.column_definitions.basketball.errorMessages.tooManyAthletes", "analyze.domain.column_definitions.basketball.errorMessages.tooFewAthletes", "analyze.domain.column_definitions.soccer.errorMessages.sameAthleteAssist", "analyze.domain.column_definitions.volleyball.rallyDetails.header_label", "analyze.shared.insights.no_clips", "analyze.shared.insights.no_stats_available", "analyze.shared.insights.no_clips_match_filter", "analyze.shared.insights.clear_filter", "analyze.shared.insights.clips_no_data_for_playlists", "analyze.shared.insights.video_selection_no_data", "analyze.shared.insights.baseball_softball_phase_card_title", "analyze.shared.insights.soccer.scoutTeam_group_title", "analyze.shared.insights.soccer.opponentTeam_group_title", "performance-core.shared.add-new-value", "performance-core.shared.athletes", "performance-core.shared.cancel", "performance-core.shared.clip-count-lowercase", "performance-core.shared.clip-count-uppercase", "performance-core.shared.coaches", "performance-core.shared.count-of-total", "performance-core.shared.delete", "performance-core.shared.done", "performance-core.shared.edit", "performance-core.shared.opponent", "performance-core.shared.play", "performance-core.shared.named-highlights", "performance-core.shared.remove", "performance-core.shared.restore", "performance-core.shared.retry", "performance-core.shared.save", "performance-core.shared.share", "performance-core.shared.team-admins", "performance-core.shared.team-highlights", "performance-core.shared.view", "performance-core.shared.your-highlights", "performance-core.clip-creator.save-clip", "performance-core.clip-creator.duration", "performance-core.clip-creator.duration-total", "performance-core.clip-creator.find-a-playlist-or-highlight", "performance-core.clip-creator.create-from-search-query", "performance-core.clip-creator.create-playlist", "performance-core.clip-creator.new-playlist-name", "performance-core.clip-creator.send-to-highlights", "performance-core.clip-creator.send-to-team-highlights", "performance-core.clip-creator.send-to-your-highlights", "performance-core.clip-creator.save-to-another", "performance-core.clip-creator.open-playlist", "performance-core.clip-creator.may-take-a-few-minutes", "performance-core.clip-creator.problem-saving-to-highlights", "performance-core.clip-creator.only-one-clip-at-a-time", "performance-core.clip-creator.only-one-video-at-a-time", "performance-core.clip-creator.failure-message.problem-loading-playlists", "performance-core.share-modal.no-matching-results", "performance-core.share-modal.add-individuals-or-groups", "performance-core.share-modal.create-view", "performance-core.share-modal.groups", "performance-core.share-modal.search", "performance-core.share-modal.title-input-placeholder", "performance-core.share-modal.notify-members", "performance-core.share-modal.permission-level.owner", "performance-core.share-modal.permission-level.can-contribute", "performance-core.share-modal.permission-level.can-contribute.description", "performance-core.share-modal.permission-level.can-view", "performance-core.share-modal.permission-level.can-view.description", "performance-core.share-modal.permission-level.can-edit", "performance-core.share-modal.permission-level.can-edit.description", "performance-core.share-modal.permission-level.can-manage", "performance-core.share-modal.permission-level.can-manage.description", "performance-core.share-modal.disabled-reason.permissions-are-role-based", "performance-core.share-modal.disabled-reason.team-admins-always-manage", "performance-core.share-modal.disabled-reason.owner", "performance-core.share-modal.disabled-reason.athletes-can-only-add", "performance-core.share-modal.success-message.generic-saved", "performance-core.share-modal.success-message.generic-shared", "performance-core.share-modal.success-message.added-one", "performance-core.share-modal.success-message.added-multiple", "performance-core.share-modal.failure-message.unable-to-update-permissions", "performance-core.share-modal.failure-message.unable-to-save-report", "performance-core.share-modal.failure-message.unable-to-share-report", "performance-core.moment-details.nothing-selected", "performance-core.moment-details.previous-moment", "performance-core.moment-details.next-moment", "performance-core.moment-details.new-moment", "performance-core.moment-details.saved-but-hidden-by-filters", "performance-core.moment-details.clear-filters", "performance-core.moment-details.derived-moments-disclaimer", "performance-core.moment-details.placeholder.select-an-athlete", "performance-core.moment-details.placeholder.select-athletes", "performance-core.moment-details.placeholder.select-a-team", "performance-core.moment-details.placeholder.select-a-type", "performance-core.moment-details.placeholder.select-a-value", "performance-core.moment-details.hudl-types", "performance-core.moment-details.custom-types", "performance-core.moment-details.type-to-add-custom-types", "performance-core.moment-details.athlete-groups.other", "performance-core.moment-details.athlete-groups.roster", "performance-core.moment-details.attributes", "performance-core.moment-details.flag-menu.flags", "performance-core.moment-details-popover.quick-edit", "performance-core.moment-details-popover.edit-more-fields", "performance-core.moment-details-popover.view-more-fields", "performance-core.moment-details-popover.confirm-delete", "performance-core.moment-details-popover.number-of-moments-deleted", "performance-core.moment-details-popover.error_toast.delete", "performance-core.moment-details-popover.error_toast.restore", "shared.sort-and-filter-form.sort_placeholder", "shared.sort-and-filter-form.filter-condition_placeholder", "shared.feedback.modal.how_can_we_improve", "shared.feedback.modal.no_personal_info", "shared.feedback.modal.feedback_type_label", "shared.feedback.modal.feedback_type_placeholder", "shared.feedback.modal.comments_label", "shared.feedback.modal.placeholder", "shared.feedback.modal.placeholder_secondary", "shared.feedback.modal.couldnt_submit_feedback", "shared.feedback.modal.send_feedback", "shared.feedback.modal.sending", "shared.feedback.modal.sent", "shared.feedback.modal.option.library", "shared.feedback.modal.option.video", "shared.feedback.modal.option.playlists", "shared.feedback.modal.option.reports", "shared.feedback.modal.option.other", "shared.analyze_support.option.video_tutorials", "shared.analyze_support.option.whats_new", "shared.analyze_support.option.give_feedback", "shared.analyze_support.option.help_center", "shared.analyze_support.option.get_one_on_one_training", "shared.analyze_support.onboarding_header.pro_tip", "shared.analyze_support.onboarding_content.add_clips_to_playlist", "shared.analyze_support.onboarding_content.edit-grid-cells", "shared.analyze_support.onboarding_content.reorder_clips_in_playlist", "shared.analyze_support.onboarding_content.share_playlist", "shared.analyze_support.onboarding_content.manually_segment_clips", "shared.analyze_support.onboarding_content.media_segment_clips", "shared.analyze_support.onboarding_content.search_for_video.part1", "shared.analyze_support.onboarding_content.search_for_video.part2", "shared.analyze_support.onboarding_content.select_library_video.part1", "shared.analyze_support.onboarding_content.select_library_video.part2", "shared.analyze_support.onboarding_content.library_filter_pane.part1", "shared.analyze_support.onboarding_content.library_filter_pane.part2", "shared.analyze_support.onboarding_content.library_filter_button.part1", "shared.analyze_support.onboarding_content.library_filter_button.part2", "shared.analyze_support.onboarding_content.library_more_actions.part1", "shared.analyze_support.onboarding_content.library_more_actions.part2", "shared.analyze_support.onboarding_content.library_popover.part1", "shared.analyze_support.onboarding_content.library_popover.part2", "shared.analyze_support.onboarding_content.library_popover.part3", "shared.analyze_support.onboarding_content.library_toggle_grid_view.part1", "shared.analyze_support.onboarding_content.library_toggle_grid_view.part2", "shared.analyze_support.onboarding_content.library_bulk_select.part1", "shared.analyze_support.onboarding_content.library_bulk_select.shift_key", "shared.analyze_support.onboarding_content.library_bulk_select.part2", "shared.analyze_support.onboarding_content.library_video_experience_toggle.part1", "shared.analyze_support.onboarding_content.library_video_experience_toggle.part2", "shared.analyze_support.onboarding_content.library_preferred_video_experience_part1", "shared.analyze_support.onboarding_content.library_preferred_video_experience_part2", "shared.analyze_support.onboarding_content.grid_sort", "shared.analyze_support.onboarding_content.module_buttons", "shared.analyze_support.onboarding_storage_update.storage_update", "shared.analyze_support.onboarding_storage_update.dont_show_again", "shared.analyze_support.onboarding_storage_update.cleaned_up_storage", "shared.analyze_support.update_items.send_to_assist.title", "shared.analyze_support.update_items.send_to_assist.description", "shared.analyze_support.update_items.send_to_assist.step1", "shared.analyze_support.update_items.send_to_assist.step2", "shared.analyze_support.update_items.add_slides.title", "shared.analyze_support.update_items.add_slides.description", "shared.analyze_support.update_items.add_slides.step1", "shared.analyze_support.update_items.add_slides.step2", "shared.analyze_support.update_items.afb_angle_selector.title", "shared.analyze_support.update_items.afb_angle_selector.description", "shared.analyze_support.update_items.afb_angle_selector.step1", "shared.analyze_support.update_items.afb_angle_selector.step2", "shared.analyze_support.update_items.pop_out_video_player.title", "shared.analyze_support.update_items.pop_out_video_player.description", "shared.analyze_support.update_items.pop_out_video_player.step1", "shared.analyze_support.update_items.pop_out_video_player.step2", "shared.analyze_support.update_items.pop_out_video_player.step3", "shared.analyze_support.update_items.library.title", "shared.analyze_support.update_items.library.description", "shared.analyze_support.update_items.library.step1", "shared.analyze_support.update_items.library.step2", "shared.analyze_support.update_items.library.search.title", "shared.analyze_support.update_items.library.search.description", "shared.analyze_support.update_items.library.search.description.v2", "shared.analyze_support.update_items.library.search.step0", "shared.analyze_support.update_items.library.search.step1", "shared.analyze_support.update_items.library.search.step2", "shared.analyze_support.update_items.library.sortOptions.title", "shared.analyze_support.update_items.library.sortOptions.description", "shared.analyze_support.update_items.library.sortOptions.step1", "shared.analyze_support.update_items.library.sortOptions.step1.v2", "shared.analyze_support.update_items.library.sortOptions.step2", "shared.analyze_support.update_items.library.sortOptions.step3", "shared.analyze_support.update_items.library.viewformationsreports.title", "shared.analyze_support.update_items.library.viewformationsreports.description", "shared.analyze_support.update_items.library.viewformationsreports.step1", "shared.analyze_support.update_items.library.viewformationsreports.step2", "shared.analyze_support.update_items.library.viewformationsreports.step3", "shared.analyze_support.update_items.library.playmulti.title", "shared.analyze_support.update_items.library.playmulti.description", "shared.analyze_support.update_items.library.playmulti.step1", "shared.analyze_support.update_items.library.playmulti.step2", "shared.analyze_support.update_items.library.filter.title", "shared.analyze_support.update_items.library.filter.description", "shared.analyze_support.update_items.library.filter.step1", "shared.analyze_support.update_items.library.filter.step2", "shared.analyze_support.update_items.library.filter.step3", "shared.analyze_support.update_items.spray_chart.title", "shared.analyze_support.update_items.spray_chart.description", "shared.analyze_support.update_items.spray_chart.step1", "shared.analyze_support.update_items.spray_chart.step2", "shared.analyze_support.update_items.spray_chart.step3", "shared.analyze_support.update_items.spray_chart.step4", "shared.analyze_support.update_items.playlist_effects.title", "shared.analyze_support.update_items.playlist_effects.description", "shared.analyze_support.update_items.playlist_effects.step1", "shared.analyze_support.update_items.playlist_effects.step2", "shared.analyze_support.update_items.playlist_effects.step3", "shared.analyze_support.update_items.playlist_effects.step4", "shared.analyze_support.update_items.playback_window.title", "shared.analyze_support.update_items.playback_window.description", "shared.analyze_support.update_items.playback_window.step1", "shared.analyze_support.update_items.playback_window.step2", "shared.analyze_support.update_items.playback_window.step3", "shared.analyze_support.update_items.pitching_batting_reports.title", "shared.analyze_support.update_items.pitching_batting_reports.description", "shared.analyze_support.update_items.pitching_batting_reports.step1", "shared.analyze_support.update_items.pitching_batting_reports.step2", "shared.analyze_support.update_items.pitching_batting_reports.step3", "shared.analyze_support.update_items.pitching_batting_reports.step4", "shared.analyze_support.update_items.manage_your_storage.title", "shared.analyze_support.update_items.manage_your_storage.description", "shared.analyze_support.update_items.manage_your_storage.step1", "shared.analyze_support.update_items.manage_your_storage.step2", "shared.analyze_support.update_items.manage_your_storage.step3", "shared.analyze_support.update_items.choose_your_video_experience.title", "shared.analyze_support.update_items.choose_your_video_experience.description", "shared.analyze_support.update_items.choose_your_video_experience.step1", "shared.analyze_support.update_items.choose_your_video_experience.step2", "shared.analyze_support.update_items.choose_your_video_experience.step3", "shared.analyze_support.update_items.choose_your_video_experience.step4", "shared.analyze_support.update_items.choose_your_video_experience.step5", "shared.analyze_support.update_items.tools_to_manage_storage.title", "shared.analyze_support.update_items.tools_to_manage_storage.description", "shared.analyze_support.update_items.tools_to_manage_storage.step1", "shared.analyze_support.update_items.tools_to_manage_storage.step2", "shared.analyze_support.update_items.tools_to_manage_storage.step3", "shared.analyze_support.update_items.tools_to_manage_storage.step4", "shared.analyze_support.update_items.tools_to_manage_storage.step5", "shared.analyze_support.update_items.tools_to_manage_storage.step5.learn_more_link", "shared.analyze_support.update_items.upgraded_library.title", "shared.analyze_support.update_items.upgraded_library.description", "shared.analyze_support.update_items.upgraded_library.step1", "shared.analyze_support.update_items.upgraded_library.step1.tutorials_link", "shared.analyze_support.update_items.upgraded_library.step2", "shared.analyze_support.update_items.layout_controls.title", "shared.analyze_support.update_items.layout_controls.description", "shared.analyze_support.update_items.layout_controls.step1", "shared.analyze_support.update_items.layout_controls.step2", "shared.analyze_support.update_items.deleted_video_reports.title", "shared.analyze_support.update_items.deleted_video_reports.description", "shared.analyze_support.update_items.deleted_video_reports.step1", "shared.analyze_support.update_items.deleted_video_reports.step2", "shared.analyze_support.update_items.deleted_video_reports.step3", "shared.analyze_support.update_items.deleted_video_reports.step4", "shared.analyze_support.update_items.custom_report_tabs.title", "shared.analyze_support.update_items.custom_report_tabs.description", "shared.analyze_support.update_items.custom_report_tabs.step1", "shared.analyze_support.update_items.custom_report_tabs.step2", "shared.analyze_support.update_items.custom_report_tabs.step3", "shared.analyze_support.update_items.custom_report_tabs.step4", "shared.analyze_support.update_items.more_powerful_custom_labels.title", "shared.analyze_support.update_items.more_powerful_custom_labels.description", "shared.analyze_support.update_items.more_powerful_custom_labels.step1", "shared.analyze_support.update_items.more_powerful_custom_labels.step2", "shared.analyze_support.update_items.more_powerful_custom_labels.step3", "shared.analyze_support.update_items.more_powerful_custom_labels.step4", "shared.analyze_support.update_items.classic_custom_reports.title", "shared.analyze_support.update_items.classic_custom_reports.description", "shared.analyze_support.update_items.classic_custom_reports.step1", "shared.analyze_support.update_items.classic_custom_reports.step2", "shared.analyze_support.update_items.classic_custom_reports.step3", "shared.analyze_support.update_items.comment_on_playlist_clips.title", "shared.analyze_support.update_items.comment_on_playlist_clips.description", "shared.analyze_support.update_items.comment_on_playlist_clips.step1", "shared.analyze_support.update_items.comment_on_playlist_clips.step2", "shared.analyze_support.update_items.comment_on_playlist_clips.step3", "analyze.keyboard_shortcut.playback", "analyze.keyboard_shortcut.clip_selected", "analyze.keyboard_shortcut.managing_clips", "analyze.keyboard.shortcut.effect_selected", "analyze.keyboard.shortcut.manage_clips_mode", "analyze.keyboard.shortcut.split_clip", "module.collaboration.title", "module.comments.no_comments", "module.comments.comment_not_found", "module.comments.replies.reply_count", "module.comments.comment_group.header.comment_count", "module.comments.comment.header.coach_prefix", "module.comments.comment.reply.no_reply", "module.comments.comment.reply.has_replies", "module.comments.comment.action.delete", "module.comments.comment.action.delete_thread", "module.comments.comment.action.delete_thread.no_replies", "module.comments.comment.action.delete_thread.with_replies", "module.comments.input.placeholder", "module.comments.input.placeholder.reply", "module.comments.toast.delete_thread.success", "module.comments.toast.delete_comment.success", "module.comments.toast.delete_comment.error", "module.comments.toast.restore_comment.error", "module.comments.toast.send_comment.error", "module.filters.title", "module.filters.selected_filters_count_text", "module.filters.active_filters", "module.filters.active_filters.clear", "module.filters.active_filters.save", "module.filters.saved_filters", "module.filters.saved_filter_name_input", "module.filters.saved_filter_name_error", "module.filters.create_saved_filter_error", "module.filters.show_fewer", "module.timeline.title", "module.timeline.tooltip", "module.timeline.header.add-data", "module.grid.title", "module.grid.clip_header_text", "module.grid.selected_clip_header_text", "module.grid.warning_tooltip.header", "module.library.title", "module.library.notice.assist.rejection.assist_cta_let_us_know", "module.library.notice.assist.rejection.assist_cta_provide_additional_instructions", "module.library.notice.assist.rejection.assist_cta_review_game_details", "module.library.notice.assist.rejection.assist_cta_resubmitting_this_video", "module.library.notice.assist.rejection.exchange_cta_request_exchange", "module.library.notice.assist.rejection.contact_support", "module.library.notice.assist.rejection.contact_support_link", "module.library.notice.assist.rejection.primary_event_title_default", "module.library.notice.assist.rejection.primary_excessive_video", "module.library.notice.assist.rejection.primary_generic", "module.library.notice.assist.rejection.primary_illegible_jerseys", "module.library.notice.assist.rejection.primary_incomplete_roster", "module.library.notice.assist.rejection.primary_incomplete_video", "module.library.notice.assist.rejection.primary_incorrect_team_colors", "module.library.notice.assist.rejection.primary_poor_quality", "module.library.notice.assist.rejection.primary_poor_quality_illegible_jerseys", "module.library.notice.assist.rejection.primary_recording_location", "module.library.notice.assist.rejection.primary_unordered_video", "module.library.notice.assist.rejection.primary_unordered_excessive_video", "module.library.notice.assist.rejection.primary_unordered_imcomplete_video", "module.library.notice.assist.rejection.recording_recommendations", "module.library.notice.assist.rejection.recording_recommendations_link", "module.library.notice.assist.rejection.roster_cta_review_roster", "module.library.notice.assist.rejection.secondary_action_let_us_know_team_stats", "module.library.notice.assist.rejection.secondary_action_please_upload_correct_order_resubmit", "module.library.notice.assist.rejection.secondary_action_review_details_resubmit", "module.library.notice.assist.rejection.secondary_action_review_roster_resubmit", "module.library.notice.assist.rejection.secondary_action_upload_correct_order_resubmit", "module.library.notice.assist.rejection.secondary_action_upload_or_request_exchange", "module.library.notice.assist.rejection.secondary_action_upload_resubmit_or_instructions", "module.library.notice.assist.rejection.secondary_explanation_cannot_provide_accurate_stats", "module.library.notice.assist.rejection.secondary_explanation_duration", "module.library.notice.assist.rejection.secondary_explanation_duration_unordered", "module.library.notice.assist.rejection.secondary_explanation_generic", "module.library.notice.assist.rejection.secondary_explanation_inaccurate_roster", "module.library.notice.assist.rejection.secondary_explanation_multiple_games", "module.library.notice.assist.rejection.secondary_explanation_no_player_stats", "module.library.notice.assist.rejection.secondary_explanation_unordered_multiple_games", "module.library.notice.assist.rejection.upload_cta_upload_video", "module.library.notice.assist.rejection.upload_cta_upload_new_video", "module.library.items.not_allowed.tooltip", "module.library.items.files_not_allowed.tooltip", "module.library.items.free_video.tooltip", "module.library.items.free_video.tooltip_v2", "module.library.items.storage_usage.free", "module.library.items.storage_usage.hours_used", "module.library.items.storage_usage.hours_used.includes_free", "module.library.items.storage_usage.hours_used_with_angles", "module.library.items.storage_usage.hours_used_with_angles.includes_free", "module.library.items.actions.share", "module.library.items.actions.rename", "module.library.items.actions.delete", "module.library.items.actions.restore", "module.library.items.actions.send_to_assist", "module.library.items.actions.send_to_assist.tooltip.practice_video_not_allowed", "module.library.items.actions.send_to_assist.tooltip.has_game_only_assist_package", "module.library.items.actions.send_to_assist.tooltip.has_no_active_assist_package", "module.library.items.actions.details_module", "module.library.items.actions.open_details", "module.library.items.actions.download", "module.library.items.actions.email_download_link", "module.library.items.actions.copy_or_move", "module.library.items.actions.show_box_score", "module.library.items.actions.edit_video", "module.library.items.actions.export", "module.library.items.actions.change_teams", "module.library.items.actions.edit_item", "module.library.items.angles_deleted", "module.library.items.empty_state.message", "module.library.items.empty_state.upload_video", "module.library.items.empty_state.upload_something", "module.library.items.empty_state.filters.primary_message", "module.library.items.empty_state.filters.secondary_message", "module.library.items.empty_state.filters.action", "module.library.items.empty_state.search.primary_message", "module.library.items.empty_state.search.secondary_message", "module.library.items.empty_state.search.action", "module.library.items.empty_state.search_and_filters.primary_message", "module.library.items.empty_state.search_and_filters.secondary_message", "module.library.items.empty_state.search_and_filters.action", "module.library.items.empty_state.humanperformance.secondary_message", "module.library.items.export.field_selection", "module.library.items.export.field_selection.field_set_only", "module.library.items.export.field_selection.all_fields", "module.library.items.export.include_empty_fields", "module.library.items.header.group.event_type", "module.library.items.header.storage_warning.header", "module.library.items.header.storage_warning.header_v2", "module.library.items.header.storage_warning.text", "module.library.items.header.storage_warning.text_v2", "module.library.items.header.practice_deletion.critical.header", "module.library.items.header.practice_deletion.warning.header", "module.library.items.header.practice_deletion.text", "module.library.items.header.practice_deletion.action", "module.library.items.icons.game_exchanged_via_focus_exchange_network.tooltip.pt1", "module.library.items.icons.game_exchanged_via_focus_exchange_network.tooltip.pt2", "module.library.items.icons.video_angles.tooltip", "module.library.items.header.multi_angle_enforcement.actions.get_help", "module.library.items.header.multi_angle_enforcement.actions.request", "module.library.items.header.multi_angle_enforcement.header", "module.library.items.header.multi_angle_enforcement.text", "module.library.items.header.storage_limit_enforcement.learn_more", "module.library.items.header.storage_limit_enforcement.critical.header", "module.library.items.header.storage_limit_enforcement.warning.header", "module.library.items.header.storage_limit_enforcement.critical.text", "module.library.items.header.storage_limit_enforcement.warning.text", "module.library.items.multi_action_bar.selected", "module.library.items.count", "module.library.items.multi_action_bar.selected.short", "module.library.items.action_bar.delete", "module.library.items.action_bar.hours_used", "module.library.items.action_bar.clear", "module.library.items.action_bar.done_review_state", "module.library.items.action_bar.play", "module.library.items.action_bar.restore", "module.library.items.action_bar.view_reports", "module.library.items.action_bar.play.storage_disabled_tooltip", "module.library.items.action_bar.play.status_disabled_tooltip", "module.library.items.action_bar.play.tooltip", "module.library.items.action_bar.download.status_disabled_tooltip", "module.library.items.action_bar.download.mixed_status_tooltip", "module.library.items.action_bar.intercut", "module.library.items.action_bar.intercut.tooltip", "module.library.items.action_bar.play.mixed_library_type_tooltip", "module.library.items.action_bar.play.bulk_select_limit", "module.library.items.action_bar.play.multiple_videos_not_supported_tooltip", "module.library.items.action_bar.view_reports.mixed_library_type_tooltip", "module.library.items.action_bar.view_reports.bulk_select_limit", "module.library.items.action_bar.view_reports.unsupported_type_tooltip", "module.library.items.search_bar.placeholder", "module.library.items.section.yourLibraryItems", "module.library.items.section.last7days", "module.library.items.section.last30days", "module.library.items.section.daysUntilExpire", "module.library.items.section.suggestedContent", "module.library.items.section.suggestedContent.note", "module.library.items.section.suggestedContent.undoDismiss", "module.library.items.section.suggestedContent.yourMoments", "module.library.items.section.deleted_last7days", "module.library.items.section.deleted_last14days", "module.library.items.section.action_required", "module.library.items.delete_session.modal.header", "module.library.items.delete_session.modal.explanation", "module.library.items.delete_session.modal.action", "module.library.items.delete_session.toast.success", "module.library.items.delete_session.toast.error", "module.library.items.move_session.modal.header", "module.library.items.move_session.modal.explanation", "module.library.items.move_session.modal.action", "module.library.items.move_session.modal.team_label", "module.library.items.move_session.modal.select_placeholder", "module.library.items.move_session.toast.success", "module.library.items.move_session.toast.error", "module.library.items.status.uploading", "module.library.items.status.processing", "module.library.items.status.calculating", "module.library.items.status.balltime_processing", "module.library.items.status.failed", "module.library.items.assist.sent_to.tooltip", "module.library.items.assist.sent_to.tooltip.justnow", "module.library.items.assist.sent_to.tooltip.minutesago", "module.library.items.assist.sent_to.tooltip.hoursago", "module.library.items.status.get_help", "module.library.items.mobile.library", "module.library.items.mobile.filter", "module.library.items.mobile.filters", "module.library.items.mobile.items.view", "module.library.items.date_created_label", "module.library.items.admin_support_link", "module.library.items.angle_delete_modal.all_angles", "module.library.items.angle_delete_modal.all_angles_v2", "module.library.items.bulk_delete.cancel", "module.library.items.bulk_delete.delete", "module.library.items.bulk_action.deleted_items.toast", "module.library.items.bulk_action.restored_items.toast", "module.library.items.bulk_action.deleted_items.error_toast", "module.library.items.bulk_action.restored_items.error_toast", "module.library.items.bulk_action.deleted_items.error_toast.select_fewer", "module.library.items.bulk_action.restored_items.error_toast.select_fewer", "module.library.items.bulk_action.deleted_items.error_toast_v2", "module.library.items.bulk_action.restored_items.error_toast_v2", "module.library.items.bulk_delete.toast.learn_more", "module.library.items.bulk_delete.modal_text", "module.library.items.bulk_action.toast.few_minutes", "module.library.items.bulk_delete.modal_text_v2", "module.library.items.bulk_delete.modal_text_v3", "module.library.items.bulk_delete.modal_text_v4", "module.library.items.bulk_delete.modal_text_v5", "module.library.items.bulk_delete.files_only_modal_text", "module.library.items.angle_delete_modal.secondary_angles", "module.library.items.angle_delete_modal.reports_note", "module.library.items.angle_delete_modal.hours_saved", "module.library.items.angle_delete_modal.hours_saved_v2", "module.library.items.bulk_delete.toast.error_toast_message", "module.library.items.bulk_delete.toast.prefix", "module.library.items.bulk_delete.toast.items", "module.library.items.bulk_delete.toast.recently_deleted", "module.library.items.bulk_delete.toast.recently_deleted_old", "module.library.items.bulk_delete.toast.recently_deleted_v2", "module.library.items.bulk_delete.toast.recently_deleted_v3", "module.library.items.bulk_delete.toast.recently_deleted_with_hours", "module.library.items.playlist.clip_count_label", "module.library.items.restore.toast", "module.library.items.retention_policy.note", "module.library.items.retention_policy.note_v2", "module.library.items.has_assist_data", "module.library.items.has_data", "module.library.items.score.result_win", "module.library.items.score.result_loss", "module.library.items.score.result_tie", "module.library.items.restricted.tooltip", "module.library.items.athletes.restricted.tooltip", "module.library.items.event_type.match", "module.library.items.event_type.practice", "module.library.suggested_assist_submission", "module.library.groups.clear_all", "module.library.groups.count", "module.library.groups.select_all", "module.library.groups.type.title", "module.library.groups.type.game", "module.library.groups.type.scout", "module.library.groups.type.playlist", "module.library.groups.type.practice", "module.library.groups.type.file", "module.library.groups.type.other", "module.library.groups.type.saved_view", "module.library.groups.type.hp_session", "module.library.groups.event.title", "module.library.groups.event.unscheduled", "module.library.groups.activity.title", "module.library.groups.activity.recently_deleted", "module.library.group.team.title", "module.library.group.team.sort", "module.library.group.team.notice.message", "module.library.group.team.notice.link_text", "module.library.group.folder.title", "module.library.group.folder.notice.message", "module.library.group.label.title", "module.library.group.label.notice.message", "module.library.group.label.notice.message.create_label", "module.library.group.label.create_label.duplicate_name_error", "module.library.group.label.create_label.limit_reached_tooltip", "module.library.group.label.create_label.error_toast", "module.library.group.label.create_label.limit_reached_toast", "module.library.group.label.actions.add-sublabel", "module.library.group.label.actions.rename", "module.library.group.label.actions.delete", "module.library.group.label.rename_label.error_toast", "module.library.group.label.rename_label.success_toast", "module.library.group.label.delete_label.modal.header", "module.library.group.label.delete_label.modal.text", "module.library.group.label.delete_label.modal.checkbox_label", "module.library.group.label.delete_label.error_toast", "module.library.group.label.delete_label.success_toast", "module.library.group.label.move_label.error_toast", "module.library.group.hours_used", "module.library.group.team.show_more.button.text", "module.library.group.team.show_fewer.button.text", "module.library.group.event.show_more.button.text", "module.library.group.event.show_fewer.button.text", "module.library.group.generic.show_fewer.button.text", "module.library.group.generic.show_more.button.text", "module.library.humanperformance.edit_session_button", "module.library.humanperformance.raw_session_title", "module.library.humanperformance.view_more_button", "module.library.storage.hours_per_team", "module.library.storage.hours_used.header", "module.library.storage.hours_used.header_v2", "module.library.storage.hours_used.message", "module.library.storage.learn_more", "module.library.storage.usage_button", "module.library.storage.free_up_space", "module.library.storage.free_up_space.modal.copy", "module.library.storage.free_up_space.modal.old_video_advice", "module.library.storage.free_up_space.modal.secondary_angles", "module.library.storage.free_up_space.modal.secondary_angles.usage", "module.library.storage.free_up_space.modal.secondary_angles.tagged_data_disclaimer", "module.library.storage.free_up_space.modal.old_practice_video.old_video_inclusion", "module.library.storage.free_up_space.modal.secondary_angles.button.review_and_delete", "module.library.storage.free_up_space.modal.confirm", "module.library.storage.free_up_space.modal.secondary_angles.need_more", "module.library.storage.free_up_space.modal.old_practice_video", "module.library.video_type.match", "module.library.modal.video_details.label", "module.library.modal.video_details.no_event", "module.library.modal.video_details.event_date", "module.library.modal.details_module.label", "module.library.modal.details_module.no_event", "module.library.modal.details_module.event_date", "module.library.experience_toggle.button.text", "module.library.experience_toggle.tooltip.text", "module.library.experience_toggle.tooltip.multiple_videos_text", "module.library.video_details.open_details", "module.library.video_details.title", "module.library.video_details.title.error.empty_state", "module.library.video_details.type", "module.library.video_details.type.match", "module.library.video_details.type.game", "module.library.video_details.type.scout", "module.library.video_details.type.practice", "module.library.video_details.events", "module.library.video_details.labels", "module.library.video_details.angles", "module.library.video_details.angles.primary", "module.library.video_details.angles.tooltip", "module.library.details_module.open_details", "module.library.details_module.update_error_toast", "module.library.details_module.update_success_toast", "module.library.details_module.labels.add_labels_success_toast", "module.library.details_module.title", "module.library.details_module.title.error.empty_state", "module.library.details_module.type", "module.library.details_module.type.match", "module.library.details_module.type.game", "module.library.details_module.type.scout", "module.library.details_module.type.practice", "module.library.details_module.events", "module.library.details_module.labels", "module.library.details_module.angles", "module.library.details_module.angles.tooltip", "module.library.details_module.angles.tooltip_v2", "module.library.details_module.angles.restore.learn_more", "module.library.details_module.angles.actions.deleted", "module.library.details_module.angles.actions.deleting", "module.library.details_module.angles.actions.delete_failed", "module.library.details_module.angles.actions.restore", "module.library.details_module.angles.actions.restored", "module.library.details_module.angles.actions.restoring", "module.library.details_module.angles.actions.restore_failed", "module.library.details_module.video_player.preview", "module.library.experience_modal.header", "module.library.experience_modal.continue", "module.library.experience_modal.no_change", "module.library.experience_modal.options.info", "module.library.experience_modal.options.new_experience.header", "module.library.experience_modal.options.new_experience.alt_text", "module.library.experience_modal.options.new_experience.new_badge", "module.library.experience_modal.options.new_experience.description.part1", "module.library.experience_modal.options.new_experience.description.part2", "module.library.experience_modal.options.new_experience.description.part3", "module.library.experience_modal.options.old_experience.header", "module.library.experience_modal.options.old_experience.alt_text", "module.library.experience_modal.options.old_experience.description.part1", "module.library.experience_modal.options.old_experience.description.part2", "module.library.experience_modal.options.old_experience.description.part3", "module.library.multiple_videos_experience_modal.header", "module.library.multiple_videos_experience_modal.description.part1", "module.library.multiple_videos_experience_modal.description.part2", "module.library.multiple_videos_experience_modal.description.part3", "module.library.multiple_videos_experience_modal.dismiss_button", "module.library.sort.group_title", "module.library.sort.option.last_uploaded", "module.library.sort.option.newest_uploads", "module.library.sort.option.first_uploaded", "module.library.sort.option.oldest_uploads", "module.library.sort.option.name", "module.library.sort.option.title", "module.list.title", "module.details.shared_with", "module.details.shared_with.can_manage", "module.details.shared_with.team_admins", "module.details.shared_with.can_view", "module.details.shared_with.can_contribute", "module.details.shared_with.can_edit", "module.details.shared_with.can_manage", "module.details.shared_with.is_owner", "module.grid.header.manage_clips", "module.grid.header.merge_clips", "module.grid.header.clear-selection", "module.grid.header.add-data", "module.grid.header.reorder", "module.grid.header.consecutive_clips_message", "module.grid.header.successful_merge_message", "module.grid.header.done", "module.grid.clip_split_type.count", "module.grid.clip_split_type.seconds", "module.grid.clip_split_type.minutes", "module.grid.clip_split.header", "module.grid.clip_split.by_count", "module.grid.clip_split.by_time", "module.grid.clip_split.create_clips", "module.grid.clip_split.successful_message", "module.grid.clip_manual_split.successful_message", "module.grid.custom_filter.sortby", "module.insights.title", "module.insights.tooltip.generic_count", "module.insights.tooltip.clip_count", "module.insights.tooltip.play_count", "module.insights.tooltip.pitch_count", "module.insights.tooltip.sequence_count", "module.insights.tooltip.moment_count", "module.insights.ungrouped_stats_group_card_title", "module.insights.visualization.spray_chart.lines", "module.insights.visualization.spray_chart.zones", "module.insights.visualization.spray_chart.zones_key.label_low", "module.insights.visualization.spray_chart.zones_key.label_high", "module.insights.visualization.unknown", "module.insights.button.view_all", "module.insights.button.view_less", "module.insights.card.custom_filter_cancel", "module.insights.card.report.editor.report_on", "module.insights.card.report.editor.by", "module.insights.card.report.editor.and", "module.insights.reports_search_placeholder", "module.insights.reports_list_view.rename", "module.insights.reports_list_view.delete", "module.insights.reports_list_view.close", "module.insights.reports_list_view.open_reports", "module.insights.reports_list_view.other_reports", "module.video.clip_context_bar.play_count", "module.video.clip_context_bar.score", "module.video.clip_context_bar.period", "module.video.clip_context_bar.pitcher", "module.video.clip_context_bar.batter", "module.video.clip_context_bar.no_data", "module.video.clip_context_bar.outs", "module.video.clip_context_bar.runs", "module.video.clip_context_bar.count", "module.video.clip_context_bar.set", "module.video.v3_video_experience_button.text", "module.video.v3_video_experience_button.multiple_videos_tooltip.text", "module.reports.shared.feedback.header", "module.reports.shared.feedback.label", "module.reports.shared.feedback.label_short", "module.reports.shared.titles.overview", "module.reports.shared.titles.ai_insights", "module.reports.shared.ai_insights.warning", "module.reports.football.titles.formations", "module.reports.baseballSoftball.titles.pitching", "module.reports.baseballSoftball.titles.batting", "module.reports.ai.feedback.why_helpful", "module.reports.ai.feedback.why_not_helpful", "module.reports.ai.summarize_game", "module.reports.ai.analyze_athlete", "module.details.data", "module.details.data.feedType.hudl_tags", "module.details.data.feedType.sportscode", "module.details.manage_data", "module.details.manage_data_sportscode", "module.details.manage_data_hudl", "module.details.manage_data.modal.header", "module.details.manage_data.modal.actions.done", "module.details.manage_data.modal.no_data_message", "module.details.manage_data.modal.no_data_upload_message", "module.details.manage_data.modal.no_data_sportscode_message", "module.details.manage_data.modal.deleted", "module.details.manage_data.taggingSessionSummary.last_edited", "module.details.manage_data.taggingSessionSummary.last_edited_by_you", "module.details.manage_data.taggingSessionSummary.last_edited_by_user", "module.details.manage_data.taggingSessionSummary.completed_by_assist", "module.details.manage_data.taggingSessionSummary.uploaded", "module.details.manage_data.taggingSessionSummary.uploaded_by_you", "module.details.manage_data.taggingSessionSummary.uploaded_by_user", "module.details.manage_data.upload.upload_xml", "module.details.manage_data.upload.uploading", "module.details.manage_data.upload.uploaded", "module.details.manage_data.upload.upload_failed", "module.details.manage_data.upload.file_processing_error_toast_message", "module.details.reports", "module.details.reports.box_score_link", "module.details.reports.matrix_link", "module.details.reports.no_data_message.text", "module.details.reports.no_data_message.send_to_assist_button", "module.details.reports.no_data_message.multiple_button_text", "module.details.reports.no_data_message.manual_tag_button", "module.details.reports.button.more", "module.details.header.open_file", "module.library.details_module.create_schedule_entry_success_toast", "module.library.details_module.create_schedule_entry_error_toast", "module.library.details_module.load_opponents_error_toast", "analyze.navbar.aiBreakdowns.beta", "analyze.navbar.aiBreakdowns.beta.warning", "watch.error.fetch", "watch.error.fetch.retry", "watch.error.fetch.generic", "watch.layout.header.full.game", "watch.layout.header.moments", "watch.moments.grid.header.number", "watch.moments.grid.header.time", "watch.moments.grid.header.period", "watch.moments.grid.header.moments", "watch.moments.grid.header.date", "watch.moments.grid.header.game", "moment.soccer.passChain", "moment.soccer.endPeriod", "moment.soccer.shot", "moment.soccer.ownGoal", "moment.soccer.cross", "moment.soccer.foul", "moment.soccer.goalkeeping", "watch.progress_bar.split", "analyze.moment.tag.win", "analyze.moment.tag.loss", "analyze.playlist.delete_alert_message", "analyze.playlist.remove_clips_message", "playlist.delete_playlist_panel.undo_button", "playlist.delete_playlist_panel.delete_button", "playlist.playlist_popover.add_title", "playlist.playlist_popover.remove_title", "actions.playlist.add_clips_to_playlist.success_toast_message", "actions.playlist.add_clips_to_playlist.error_toast_message", "actions.playlist.delete_playlist_clip.remove_error_toast_message", "action.playlist.create_playlist.default_name", "action.playlist.create_playlist.button", "action.playlist.create_playlist.not_saved", "actions.playlist.create_playlist.saving_error_toast_message", "actions.playlist.create_playlist.saving_success_toast_message", "actions.playlist.delete_playlist.deleting_error_toast_message", "actions.clips.add_clips_to_playlists_and_highlights.multiple_success_toast_message", "actions.clips.add_clips_to_playlists_and_highlights.success_toast_message", "actions.clips.add_clips_to_playlists_and_highlights.error_toast_message", "actions.effects.delete_effects.delete_effects_toast_message", "actions.effects.restore_effects.restore_effects_toast_message", "actions.effects.restore_effects.restore_effects_error_toast_message", "watch.analyze.share.saved_view.modal.description_1", "watch.analyze.share.saved_view.modal.description_2", "watch.analyze.share.saved_view.modal.share", "watch.analyze.share.saved_view.modal.save", "watch.analyze.share.saved_view.modal.cancel", "watch.analyze.share.saved_view.modal.done", "watch.analyze.share.saved_view.modal.create_view", "analyze.export.download", "watch.analyze.export.download.modal.clips.header", "watch.analyze.export.download.modal.clips.description.video", "watch.analyze.export.download.modal.clips.description.clip.single", "watch.analyze.export.download.modal.clips.description.clip.multiple", "watch.analyze.export.download.modal.clips.description.playlist.single", "watch.analyze.export.download.modal.clips.description.playlist.multiple", "watch.analyze.export.download.modal.clips.note.title", "watch.analyze.export.download.modal.clips.note.message", "watch.analyze.export.download.modal.clips.clips.question", "watch.analyze.export.download.modal.clips.single_playlist.question", "watch.analyze.export.download.modal.clips.multiple_playlists.question", "watch.analyze.export.download.modal.clips.option.single", "watch.analyze.export.download.modal.clips.option.single_playlist.multiple", "watch.analyze.export.download.modal.clips.option.multiple_playlists.multiple", "watch.analyze.export.download.modal.button.primary", "analyze.components.basketball.avatar-stack-athletes-description", "shared.feedback.button.early_access", "shared.feedback.button.feedback", "shared.feedback.button.feedback.short", "shared.feedback.modal.cancel", "shared.feedback.disclaimer", "shared.feedback.send_feedback", "shared.feedback.sent", "shared.feedback.failed", "shared.feedback.failed_toast", "shared.loop_control.playback", "shared.loop_control.video_playback", "shared.loop_control.playback_settings", "shared.loop_control.normal", "shared.loop_control.autoskip", "shared.loop_control.loop", "shared.loop_control.loop-clip", "shared.loop_control.play-all-clips", "shared.loop_control_source_effects", "shared.keyboard_shortcut.generic.key.control", "shared.keyboard_shortcut.generic.hold_capitalized", "shared.keyboard_shortcut.generic.key.space", "shared.keyboard_shortcut.generic.key_description.play_pause", "shared.keyboard_shortcut.generic.key_description.fast_forward", "shared.keyboard_shortcut.generic.key_description.fast_rewind", "shared.keyboard_shortcut.generic.key_description.slow_forward", "shared.keyboard_shortcut.generic.key_description.slow_rewind", "shared.keyboard_shortcut.generic.key_description.forward", "shared.keyboard_shortcut.generic.key_description.rewind", "shared.keyboard_shortcut.generic.key_description.next_clip", "shared.keyboard_shortcut.generic.key_description.previous_clip", "shared.keyboard_shortcut.generic.key_description.restart_clip", "shared.keyboard_shortcut.generic.key_description.copy", "shared.keyboard_shortcut.generic.key_description.paste", "shared.keyboard_shortcut.generic.key_description.duplicate", "video.toolbar.delete", "video.toolbar.drawCircle", "video.toolbar.drawFreehand", "video.toolbar.hide", "video.toolbar.drawArrow", "video.toolbar.drawLine", "video.toolbar.drawTBar", "video.toolbar.addText", "video.toolbar.float_video", "videocontrols.addEffects", "analyze.shared.navigation.edit_clip_error_toast", "effects.deleteError", "effects.saveError", "effects.notice.header", "effects.notice.text", "effects.notice.buttonText", "analyze.playlist_popover.label.add_to.as_standard_button", "analyze.playlist_popover.label.add_to.as_standard_button_v2", "analyze.playlist_popover.label.add_to.checked", "analyze.playlist_popover.label.add_to.unchecked", "analyze.playlist_popover.label.add_to.unchecked_v2", "analyze.playlist_popover.label.remove_clips.as_standard_button", "analyze.playlist_popover.athlete_highlights.title", "analyze.playlist.send_to_sportscode", "analyze.shared.download.modal.title", "analyze.shared.download.modal.info.video", "analyze.shared.download.modal.info.xchange", "analyze.shared.download.modal.info.presentation", "analyze.shared.download.modal.format.question", "analyze.shared.download.modal.format.v3.stats", "analyze.shared.download.modal.format.v3.video_options.question", "analyze.shared.download.modal.format.v3.video_options.video_and_stats", "analyze.shared.download.modal.format.v3.video_options.video_only", "analyze.shared.download.modal.format.v3.single_video", "analyze.shared.download.modal.format.v3.multiple_videos", "analyze.shared.download.modal.format.v3.multiple_videos_v2", "analyze.shared.download.modal.format.classic.single_video", "analyze.shared.download.modal.format.classic.multiple_videos", "analyze.shared.download.modal.buttons.download", "analyze.shared.download.success_toast_message", "analyze.shared.download.error_toast_message", "analyze.shared.errorBoundary.backToHome", "analyze.shared.errorBoundary.noAccessToTeam.header", "analyze.shared.errorBoundary.noAccessToTeam.message", "analyze.highlights.highlights_modal_confirmation.clips_too_long.body", "module.library.libraryItem.assist.rejection_reason.default", "module.library.libraryItem.assist.rejection_reason.prefix_verbiage", "module.library.libraryItem.assist.rejection_reason.PoorVideoQuality", "module.library.libraryItem.assist.rejection_reason.BadRecordingLocationOrAngle", "module.library.libraryItem.assist.rejection_reason.IncompleteVideo", "module.library.libraryItem.assist.rejection_reason.VideoOutOfOrder", "module.library.libraryItem.assist.rejection_reason.ExcessiveVideoDuration", "module.library.libraryItem.assist.rejection_reason.VideoRotated", "module.library.libraryItem.assist.rejection_reason.IncorrectTeamColors", "module.library.libraryItem.assist.rejection_reason.SwitchedHomeAndAwayTeams", "module.library.libraryItem.assist.rejection_reason.IncompleteRoster", "module.library.libraryItem.assist.rejection_reason.UnreadableScoreSheet", "module.library.libraryItem.assist.rejection_reason.IllegibleJersey", "module.library.libraryItem.assist.rejection_reason.NoHashMarks", "module.library.rewind.redirect_modal.message", "module.library.rewind.redirect_modal.learn_more_button", "module.library.rewind.redirect_modal.close_button"]}}, "base-language": {"testing.hello": "hello in English", "analyze.breadcrumb.selected.text": "{videoCount, number, integer} {videoCount, plural, one {Video} other {Videos}} Selected", "analyze.breadcrumb.create_saved_view_conversation.tooltip.text": "Your conversation is saved on this Saved View.", "analyze.collaborate.create_saved_view_conversation.start_message.text": "Saved View with {videoCount} videos", "analyze.module.content.edit_playlist_title_error_toast": "Unable to update playlist title. Please try again", "analyze.module.content.edit_file_title_error_toast": "Unable to update file title. Please try again.", "analyze.module.content.create_tagging_session_error_toast": "Unable to save data to video.", "analyze.module.content.selectable_actions.send_to_assist": "Send to Assist", "analyze.module.navigation.playlist.update_clip_title_error_toast": "Unable to update clip title. Please try again.", "analyze.module.navigation.playlist.clip.generic_title": "Clip at {clipTime}", "analyze.undo": "Undo", "analyze.domain.column_definitions.shared.duration.header_label": "Duration", "analyze.domain.column_definitions.shared.duration.cell_description": "Duration is calculated from the length of the clip", "analyze.domain.column_definitions.shared.select.header_description": "Select all clips.", "analyze.domain.column_definitions.shared.select.cell_description": "Select a single clip", "analyze.domain.column_definitions.shared.clip_title.header_label": "Clip <PERSON>", "analyze.domain.column_definitions.shared.clip_title.cell_default_label": "Clip <PERSON>", "analyze.domain.column_definitions.shared.moments.header_label": "Moments", "analyze.domain.column_definitions.shared.game.header_label": "Game", "analyze.domain.column_definitions.shared.video_time.header_label": "Video Time", "analyze.domain.column_definitions.shared.video_time.cell_description": "Video time is calculated from the timestamp of the clip.", "analyze.domain.column_definitions.shared.date.header_label": "Date", "analyze.domain.column_definitions.shared.date.cell_description": "Date is determined by the schedule entry.", "analyze.domain.column_definitions.shared.details.header_label": "Details", "analyze.domain.column_definitions.shared.comment.cell_description": "The clip does not have any comments yet.", "analyze.domain.column_definitions.shared.comment.has_comment_cell_description": "The clip has {count} {count, plural, one {comment} other {comments}}.", "analyze.domain.column_definitions.shared.effect.cell_description": "The clip does not have any effects yet.", "analyze.domain.column_definitions.shared.effect.has_effect_cell_description": "The clip has {count} {count, plural, one {effect} other {effects}}.", "analyze.domain.column_definitions.shared.videoTitle.header_label": "Video Title", "analyze.domain.column_definitions.shared.videoTitle.cell_description": "Video title is determined by the name in the library.", "analyze.domain.column_definitions.baseballSoftball.hitType.header_label": "Hit Type", "analyze.domain.column_definitions.baseballSoftball.swingType.header_label": "Swing Type", "analyze.domain.column_definitions.baseballSoftball.pitchResult.header_label": "<PERSON><PERSON> Result", "analyze.domain.column_definitions.baseballSoftball.battingResult.header_label": "Batting Result", "analyze.domain.column_definitions.baseballSoftball.runnerOnFirst.header_label": "1B Runner", "analyze.domain.column_definitions.baseballSoftball.runnerOnSecond.header_label": "2B Runner", "analyze.domain.column_definitions.baseballSoftball.runnerOnThird.header_label": "3B Runner", "analyze.domain.column_definitions.baseballSoftball.pitchType.header_label": "Pitch Type", "analyze.domain.column_definitions.baseballSoftball.outCount.header_label": "Outs", "analyze.domain.column_definitions.baseballSoftball.onBase.header_label": "On Base", "analyze.domain.column_definitions.baseballSoftball.onBase.cell_description": "On base is calculated from runners data.", "analyze.domain.column_definitions.baseballSoftball.inning.header_label": "Inning", "analyze.domain.column_definitions.baseballSoftball.pitcher.header_label": "Pitcher", "analyze.domain.column_definitions.baseballSoftball.batter.header_label": "<PERSON><PERSON>", "analyze.domain.column_definitions.baseballSoftball.score.header_label": "Score", "analyze.domain.column_definitions.baseballSoftball.score.cell_description": "Score is calculated from batting and runners data.", "analyze.domain.column_definitions.baseballSoftball.count.header_label": "Count", "analyze.domain.column_definitions.basketball.errorMessages.sameAthleteAssist": "The same athlete is shooting and assisting their own shot.", "analyze.domain.column_definitions.basketball.errorMessages.tooManyAthletes": "There are too many athletes on the court.", "analyze.domain.column_definitions.basketball.errorMessages.tooFewAthletes": "There are too few athletes on the court.", "analyze.domain.column_definitions.soccer.errorMessages.sameAthleteAssist": "The same athlete is assisting their own event.", "analyze.domain.column_definitions.volleyball.rallyDetails.header_label": "Rally Details", "analyze.shared.insights.no_clips": "The video you've selected contains no clips.", "analyze.shared.insights.clear_filter": "Clear Filter", "analyze.shared.insights.no_clips_match_filter": "No clips match your filters", "analyze.shared.insights.no_stats_available": "There are currently no stats available for {sportName}. We are working hard to bring you stats.", "analyze.shared.insights.clips_no_data_for_playlists": "Reports aren’t available for playlists yet. Stay tuned!", "analyze.shared.insights.video_selection_no_data": "Your video selection doesn't contain any data. Add data to get key insights and view reports.", "analyze.shared.insights.soccer.scoutTeam_group_title": "{teamInContext}", "analyze.shared.insights.soccer.opponentTeam_group_title": "{teamInContext}'s {teamCount, plural, one {Opponent} other {Opponents}}", "analyze.shared.insights.baseball_softball_phase_card_title": "{teamInContext} is", "performance-core.shared.add-new-value": "Add \"{value}\"", "performance-core.shared.athletes": "Athletes", "performance-core.shared.cancel": "Cancel", "performance-core.shared.clip-count-lowercase": "{clipCount, number, integer} {clipCount, plural, one {clip} other {clips}}", "performance-core.shared.clip-count-uppercase": "{clipCount, number, integer} {clipCount, plural, one {Clip} other {Clips}}", "performance-core.shared.coaches": "Coaches", "performance-core.shared.count-of-total": "{count} of {total}", "performance-core.shared.delete": "Delete", "performance-core.shared.done": "Done", "performance-core.shared.edit": "Edit", "performance-core.shared.opponent": "Opponent", "performance-core.shared.play": "Play", "performance-core.shared.named-highlights": "{name}'s Highlights", "performance-core.shared.remove": "Remove", "performance-core.shared.restore": "Rest<PERSON>", "performance-core.shared.retry": "Retry", "performance-core.shared.save": "Save", "performance-core.shared.share": "Share", "performance-core.shared.team-admins": "Team Admins", "performance-core.shared.team-highlights": "Team Highlights", "performance-core.shared.view": "View", "performance-core.shared.your-highlights": "Your Highlights", "performance-core.clip-creator.save-clip": "Save Clip", "performance-core.clip-creator.duration": "{seconds}s", "performance-core.clip-creator.duration-total": "{seconds}s total", "performance-core.clip-creator.find-a-playlist-or-highlight": "Find a playlist or highlight…", "performance-core.clip-creator.create-from-search-query": "Create \"{searchQuery}\"", "performance-core.clip-creator.create-playlist": "Create Playlist", "performance-core.clip-creator.new-playlist-name": "New Playlist Name", "performance-core.clip-creator.send-to-highlights": "Send to Highlights", "performance-core.clip-creator.send-to-team-highlights": "Send to Team Highlights", "performance-core.clip-creator.send-to-your-highlights": "Send to Your Highlights", "performance-core.clip-creator.save-to-another": "Save to Another", "performance-core.clip-creator.open-playlist": "Open Playlist", "performance-core.clip-creator.may-take-a-few-minutes": "Saved. It may take a few minutes to update.", "performance-core.clip-creator.problem-saving-to-highlights": "There was a problem saving to highlights.", "performance-core.clip-creator.only-one-clip-at-a-time": "Only one clip may be edited at a time.", "performance-core.clip-creator.only-one-video-at-a-time": "Editing clips is not available when multiple videos are selected.", "performance-core.clip-creator.failure-message.problem-loading-playlists": "There was a problem loading your playlists.", "performance-core.share-modal.no-matching-results": "No matching results", "performance-core.share-modal.add-individuals-or-groups": "Add individuals or groups…", "performance-core.share-modal.create-view": "Create View", "performance-core.share-modal.groups": "Groups", "performance-core.share-modal.search": "Search", "performance-core.share-modal.title-input-placeholder": "Title", "performance-core.share-modal.notify-members": "Notify Members", "performance-core.share-modal.permission-level.owner": "Owner", "performance-core.share-modal.permission-level.can-contribute": "Can Contribute", "performance-core.share-modal.permission-level.can-contribute.description": "Can comment and draw", "performance-core.share-modal.permission-level.can-view": "Can View", "performance-core.share-modal.permission-level.can-view.description": "Can see and open content", "performance-core.share-modal.permission-level.can-edit": "Can Edit", "performance-core.share-modal.permission-level.can-edit.description": "Can rename and edit content", "performance-core.share-modal.permission-level.can-manage": "Can Manage", "performance-core.share-modal.permission-level.can-manage.description": "Can share and delete content", "performance-core.share-modal.disabled-reason.permissions-are-role-based": "Permissions are based on user role.", "performance-core.share-modal.disabled-reason.team-admins-always-manage": "Team admins can always manage all content", "performance-core.share-modal.disabled-reason.owner": "This person's permissions can't be changed because they are the owner of this content", "performance-core.share-modal.disabled-reason.athletes-can-only-add": "Athletes can only add permissions", "performance-core.share-modal.success-message.generic-saved": "Saved <bold>{title}</bold>", "performance-core.share-modal.success-message.generic-shared": "Shared <bold>{title}</bold>", "performance-core.share-modal.success-message.added-one": "Shared <bold>{title}</bold> with <bold>{name}</bold>", "performance-core.share-modal.success-message.added-multiple": "Shared <bold>{title}</bold> with <bold>{name}</bold> and <bold>{count} {count, plural, one {other} other {others}}</bold>", "performance-core.share-modal.failure-message.unable-to-update-permissions": "Unable to update sharing permissions. Please try again.", "performance-core.share-modal.failure-message.unable-to-save-report": "Unable to save report. Please try again.", "performance-core.share-modal.failure-message.unable-to-share-report": "Unable to share report. Please try again.", "performance-core.moment-details.nothing-selected": "Nothing Selected", "performance-core.moment-details.previous-moment": "Previous Moment", "performance-core.moment-details.next-moment": "Next Moment", "performance-core.moment-details.new-moment": "New Moment", "performance-core.moment-details.saved-but-hidden-by-filters": "The moment was saved, but it's currently hidden by filters.", "performance-core.moment-details.clear-filters": "Clear Filters", "performance-core.moment-details.derived-moments-disclaimer": "{momentTitle} is calculated from other moments and cannot be edited.", "performance-core.moment-details.placeholder.select-an-athlete": "Select an athlete", "performance-core.moment-details.placeholder.select-athletes": "Select athletes", "performance-core.moment-details.placeholder.select-a-team": "Select a team", "performance-core.moment-details.placeholder.select-a-type": "Select a type", "performance-core.moment-details.placeholder.select-a-value": "Select a value", "performance-core.moment-details.hudl-types": "Hudl Types", "performance-core.moment-details.custom-types": "Custom Types", "performance-core.moment-details.type-to-add-custom-types": "Type to add custom types", "performance-core.moment-details.athlete-groups.other": "Other", "performance-core.moment-details.athlete-groups.roster": "<PERSON><PERSON><PERSON>", "performance-core.moment-details.attributes": "Attributes", "performance-core.moment-details.flag-menu.flags": "Flags", "performance-core.moment-details-popover.quick-edit": "Quick Edit", "performance-core.moment-details-popover.edit-more-fields": "Edit more fields", "performance-core.moment-details-popover.view-more-fields": "View more fields", "performance-core.moment-details-popover.confirm-delete": "Deleting this moment will remove it from video navigation and filtering, and may impact stats. Do you want to continue?", "performance-core.moment-details-popover.number-of-moments-deleted": "You deleted {count} {count, plural, one {moment} other {moments}}.", "performance-core.moment-details-popover.error_toast.delete": "Unable to delete {count, number, integer} {count, plural, one {moment} other {moments}}.", "performance-core.moment-details-popover.error_toast.restore": "Unable to restore {count, number, integer} {count, plural, one {moment} other {moments}}.", "shared.sort-and-filter-form.sort_placeholder": "Select a ...", "shared.sort-and-filter-form.filter-condition_placeholder": "Select a condition", "shared.feedback.modal.how_can_we_improve": "How can we improve the new Hudl?", "shared.feedback.modal.no_personal_info": "(Please do not include any personal information in your response.)", "shared.feedback.modal.feedback_type_label": "What would you like to give us feedback about?", "shared.feedback.modal.feedback_type_placeholder": "Please select one...", "shared.feedback.modal.comments_label": "Comments", "shared.feedback.modal.placeholder": "Give us feedback...", "shared.feedback.modal.placeholder_secondary": "Share your thoughts...", "shared.feedback.modal.couldnt_submit_feedback": "Couldn't submit your feedback. Please try again.", "shared.feedback.modal.send_feedback": "Send Feedback", "shared.feedback.modal.sending": "Sending…", "shared.feedback.modal.sent": "<PERSON><PERSON>", "shared.feedback.modal.option.library": "Library", "shared.feedback.modal.option.video": "Video", "shared.feedback.modal.option.playlists": "Playlists", "shared.feedback.modal.option.reports": "Reports", "shared.feedback.modal.option.other": "Other", "shared.analyze_support.option.video_tutorials": "Video Tutorials", "shared.analyze_support.option.whats_new": "What's New?", "shared.analyze_support.option.give_feedback": "<PERSON>", "shared.analyze_support.option.help_center": "Help Center", "shared.analyze_support.option.get_one_on_one_training": "Get 1-on-1 Training", "shared.analyze_support.onboarding_header.pro_tip": "Pro Tip", "shared.analyze_support.onboarding_content.add_clips_to_playlist": "Add video to a new playlist, existing playlist, or highlights.", "shared.analyze_support.onboarding_content.edit-grid-cells": "Double-click on a grid cell to make edits.", "shared.analyze_support.onboarding_content.reorder_clips_in_playlist": "Need to adjust the order of your clips? Use the reorder button to drag clips to another position.", "shared.analyze_support.onboarding_content.share_playlist": "Start collaborating by sharing this playlist with a group or individuals on your team.", "shared.analyze_support.onboarding_content.manually_segment_clips": "Need to split or merge clips? Use the manage clips button to make changes.", "shared.analyze_support.onboarding_content.media_segment_clips": "Your clips were created based on when you started and stopped the camera while recording.", "shared.analyze_support.onboarding_content.search_for_video.part1": "Looking for something specific?", "shared.analyze_support.onboarding_content.search_for_video.part2": "Search by video name to find it quickly.", "shared.analyze_support.onboarding_content.select_library_video.part1": "Click play to watch video.", "shared.analyze_support.onboarding_content.select_library_video.part2": "For multiple videos, use the circle to select each item, then click play in the action bar.", "shared.analyze_support.onboarding_content.library_filter_pane.part1": "Use filters to narrow your search.", "shared.analyze_support.onboarding_content.library_filter_pane.part2": "Click on an item (e.g., a type or event) to add or remove it as a filter.", "shared.analyze_support.onboarding_content.library_filter_button.part1": "Use filters to narrow your search.", "shared.analyze_support.onboarding_content.library_filter_button.part2": "Using the filter button, select an item (e.g., a type or event) to add or remove it as a filter.", "shared.analyze_support.onboarding_content.library_more_actions.part1": "Use the menu for more actions,", "shared.analyze_support.onboarding_content.library_more_actions.part2": "like share, rename, send to <PERSON><PERSON>, and more.", "shared.analyze_support.onboarding_content.library_popover.part1": "Use the content dropdown to", "shared.analyze_support.onboarding_content.library_popover.part2": "quickly change your video selection", "shared.analyze_support.onboarding_content.library_popover.part3": "—no need to go back to the library.", "shared.analyze_support.onboarding_content.library_toggle_grid_view.part1": "Choose how items are shown in your library", "shared.analyze_support.onboarding_content.library_toggle_grid_view.part2": "use the icons to swap between a grid or list view.", "shared.analyze_support.onboarding_content.library_bulk_select.part1": "Hold", "shared.analyze_support.onboarding_content.library_bulk_select.shift_key": "shift", "shared.analyze_support.onboarding_content.library_bulk_select.part2": "to select a range of items.", "shared.analyze_support.onboarding_content.library_video_experience_toggle.part1": "Not quite ready for the new video + data experience?", "shared.analyze_support.onboarding_content.library_video_experience_toggle.part2": "Use this toggle to change your default when playing a single video.", "shared.analyze_support.onboarding_content.library_preferred_video_experience_part1": "Use the toggle to change your default to the new experience.", "shared.analyze_support.onboarding_content.library_preferred_video_experience_part2": "Analyze multiple videos alongside dynamic stats and filters.", "shared.analyze_support.onboarding_content.grid_sort": "Click on a column header to sort the grid. Keep clicking to change or remove the sort.", "shared.analyze_support.onboarding_content.module_buttons": "Click the icons to show or hide tools in your layout.", "shared.analyze_support.onboarding_storage_update.storage_update": "Storage Update", "shared.analyze_support.onboarding_storage_update.dont_show_again": "Don't show this again", "shared.analyze_support.onboarding_storage_update.cleaned_up_storage": "<bold>We cleaned up your storage</bold> by deleting secondary Focus angles. You can restore them here.", "shared.analyze_support.update_items.send_to_assist.title": "Send to Assist", "shared.analyze_support.update_items.send_to_assist.description": "Watch every pitch alongside play-by-play data, broken down by <PERSON><PERSON>sist.", "shared.analyze_support.update_items.send_to_assist.step1": "👉🏽 From your library, click the \"...\" menu on any video.", "shared.analyze_support.update_items.send_to_assist.step2": "👉🏽 Click \"Send to Assist\" to open the Assist submission form.", "shared.analyze_support.update_items.add_slides.title": "Add Slides", "shared.analyze_support.update_items.add_slides.description": "Add slides to a playlist by uploading an image, PDF or PowerPoint file.", "shared.analyze_support.update_items.add_slides.step1": "👉🏽 In the \"Breakdown\" layout, with the grid on the bottom, click \"Add Slide.\"", "shared.analyze_support.update_items.add_slides.step2": "👉🏽 Select a file, or drag and drop a file into the dialog.", "shared.analyze_support.update_items.afb_angle_selector.title": "Select Video Angles for Playback", "shared.analyze_support.update_items.afb_angle_selector.description": "Watch the video that matters most by selecting one or multiple angles.", "shared.analyze_support.update_items.afb_angle_selector.step1": "👉🏽 When watching video, click on the angle icon to view all available angles.", "shared.analyze_support.update_items.afb_angle_selector.step2": "👉🏽 Select or deselect any angle to include it in the playback for each clip.", "shared.analyze_support.update_items.pop_out_video_player.title": "Watch Video in a Separate Window", "shared.analyze_support.update_items.pop_out_video_player.description": "Maximize your screen space so you can see more data alongside video.", "shared.analyze_support.update_items.pop_out_video_player.step1": "👉🏽 While watching video, click on the new window icon in the video control bar.", "shared.analyze_support.update_items.pop_out_video_player.step2": "💡 You can use all the same video controls and effects while it’s in a separate window.", "shared.analyze_support.update_items.pop_out_video_player.step3": "💡 To bring the video back, simply close the video window.", "shared.analyze_support.update_items.library.title": "More Flexible Library", "shared.analyze_support.update_items.library.description": "Get to content faster with a library organized around flexible filtering options.", "shared.analyze_support.update_items.library.step1": "👉🏽 In the library, select one or more filters from the list on the left.", "shared.analyze_support.update_items.library.step2": "💡 Additional options are on the way, such as filtering by folder, athlete, sharing status and more.", "shared.analyze_support.update_items.library.search.title": "Search Your Library", "shared.analyze_support.update_items.library.search.description": "Type to quickly find content anywhere in your library.", "shared.analyze_support.update_items.library.search.description.v2": "Quickly find content anywhere in your library.", "shared.analyze_support.update_items.library.search.step0": "👉🏽 In the library, start typing a video title into the search bar.", "shared.analyze_support.update_items.library.search.step1": "💡 Filters will constrain your search, helping you find exactly what you need.", "shared.analyze_support.update_items.library.search.step2": "💡 Search results will only match titles right now, but will match additional details in the future.", "shared.analyze_support.update_items.library.sortOptions.title": "Sort Your Library", "shared.analyze_support.update_items.library.sortOptions.description": "Organize which content you see first in your library.", "shared.analyze_support.update_items.library.sortOptions.step1": "👉🏽 In your library, click on the “Last Uploaded” dropdown located in the top right corner.", "shared.analyze_support.update_items.library.sortOptions.step1.v2": "👉🏽 In your library, click on the “Newest Uploads” dropdown located in the top right corner.", "shared.analyze_support.update_items.library.sortOptions.step2": "👉🏽 Select how you want your library content to be sorted.", "shared.analyze_support.update_items.library.sortOptions.step3": "💡 You can sort by name and upload date now, and will have more options in the future.", "shared.analyze_support.update_items.library.viewformationsreports.title": "View Formation Reports", "shared.analyze_support.update_items.library.viewformationsreports.description": "Get a comprehensive look at formations, with the ability to dig deeper on each formation", "shared.analyze_support.update_items.library.viewformationsreports.step1": "👉🏽 In the “Analyze” layout, click on the “Formation” tab to view the report.", "shared.analyze_support.update_items.library.viewformationsreports.step2": "👉🏽 Click on any stat to filter video and data. Filters will persist across each report tab.", "shared.analyze_support.update_items.library.viewformationsreports.step3": "💡 We’re still making improvements, leave us feedback using the “BETA” button at the top of the report.", "shared.analyze_support.update_items.library.playmulti.title": "Play Multiple Videos", "shared.analyze_support.update_items.library.playmulti.description": "Watch and analyze multiple videos at once.", "shared.analyze_support.update_items.library.playmulti.step1": "👉🏽 In the library, click the circle next to any video to select it.", "shared.analyze_support.update_items.library.playmulti.step2": "👉🏽 Select any number of videos, then click Play.", "shared.analyze_support.update_items.library.filter.title": "Find Video with Filters", "shared.analyze_support.update_items.library.filter.description": "Get to content faster with a library organized around flexible filtering options.", "shared.analyze_support.update_items.library.filter.step1": "👉🏽 In the library, select one or more filters from the left.", "shared.analyze_support.update_items.library.filter.step2": "💡 Use the team filter to find videos related to past opponents across seasons.", "shared.analyze_support.update_items.library.filter.step3": "💡 Additional options are on the way, such as filtering by athlete, sharing status and more.", "shared.analyze_support.update_items.spray_chart.title": "Spray Chart", "shared.analyze_support.update_items.spray_chart.description": "See batting tendencies, including hit type and location, so you can adjust your offensive or defensive strategy.", "shared.analyze_support.update_items.spray_chart.step1": "👉🏽 Select the “Analyze” or “Breakdown” layout to view tendencies.", "shared.analyze_support.update_items.spray_chart.step2": "👉🏽 Find the “Hit Type” and “Hit Location” cards listed under the “Batting” section.", "shared.analyze_support.update_items.spray_chart.step3": "💡 Use the “Lines” chart to see each individual hit location and type (indicated by color).", "shared.analyze_support.update_items.spray_chart.step4": "💡 Use the “Zones” chart to see all hits in a specific area of the field.", "shared.analyze_support.update_items.playlist_effects.title": "Updated Playlist Tools", "shared.analyze_support.update_items.playlist_effects.description": "Reorder clips, give them custom titles, and add your own effects.", "shared.analyze_support.update_items.playlist_effects.step1": "👉🏽 Click the \"Reorder\" button to reorder clips in a playlist.", "shared.analyze_support.update_items.playlist_effects.step2": "👉🏽 Double-click a clip's title to rename it.", "shared.analyze_support.update_items.playlist_effects.step3": "👉🏽 Add helpful lines, circles, and text effects to playlist video.", "shared.analyze_support.update_items.playlist_effects.step4": "💡 Your video effects from last year work in the new Hudl.", "shared.analyze_support.update_items.playback_window.title": "<PERSON><PERSON> in a Playlist", "shared.analyze_support.update_items.playback_window.description": "Focus on the most important video by adding custom trim effects to any clip in a playlist.", "shared.analyze_support.update_items.playback_window.step1": "👉🏽 Use the circular handles on the progress bar to adjust the playback range.", "shared.analyze_support.update_items.playback_window.step2": "💡 Your trim will automatically save any time you adjust the position of a handle.", "shared.analyze_support.update_items.playback_window.step3": "💡 Only effects inside the trim will show up on the video during playback.", "shared.analyze_support.update_items.pitching_batting_reports.title": "Pitching and Batting Reports", "shared.analyze_support.update_items.pitching_batting_reports.description": "Get a comprehensive look at pitching and batting, with the ability to dig deeper for each.", "shared.analyze_support.update_items.pitching_batting_reports.step1": "👉🏽 Select one or more items in the library and click “View Reports”.", "shared.analyze_support.update_items.pitching_batting_reports.step2": "👉🏽 While watching video in the “Analyze” layout, click the “Batting” or “Pitching” tab.", "shared.analyze_support.update_items.pitching_batting_reports.step3": "👉🏽 Click any stat to filter video and data. Filters remain active even if you switch reports.", "shared.analyze_support.update_items.pitching_batting_reports.step4": "💡 We’re still making improvements—click the yellow “BETA” button to leave feedback.", "shared.analyze_support.update_items.manage_your_storage.title": "Tools to Manage Your Storage", "shared.analyze_support.update_items.manage_your_storage.description": "See storage hours for each video in your library, and easily restore any video for 14 days after it's been deleted.", "shared.analyze_support.update_items.manage_your_storage.step1": "👉🏽 In your library, locate the \"Manage Storage\" indicator on the bottom left. Click the expand button to see storage hours for each video.", "shared.analyze_support.update_items.manage_your_storage.step2": "👉🏽 Click on the circle next to one or multiple items to select it, then download or delete.", "shared.analyze_support.update_items.manage_your_storage.step3": "💡 You can download any video before deleting it.", "shared.analyze_support.update_items.choose_your_video_experience.title": "Choose Your Video Experience", "shared.analyze_support.update_items.choose_your_video_experience.description": "Default to the new video + data experience, or play video in the old experience.", "shared.analyze_support.update_items.choose_your_video_experience.step1": "👉🏽 In your library, use the toggle at the top right to update your preference.", "shared.analyze_support.update_items.choose_your_video_experience.step2": "👉🏽 Keep the toggle in the green “on” position for video + data.", "shared.analyze_support.update_items.choose_your_video_experience.step3": "👉🏽 Switch the toggle to the gray “off” position for video only.", "shared.analyze_support.update_items.choose_your_video_experience.step4": "💡 Use the new experience to watch multiple videos at once. ", "shared.analyze_support.update_items.choose_your_video_experience.step5": "💡 The new experience displays dynamic stats and filters alongside the video.", "shared.analyze_support.update_items.tools_to_manage_storage.title": "More Tools to Manage Your Storage", "shared.analyze_support.update_items.tools_to_manage_storage.description": "See all angles for each video, with options to download or delete individual angles.", "shared.analyze_support.update_items.tools_to_manage_storage.step1": "👉🏽 For any item, click on the angle icon or use the three-dot menu to open the details.", "shared.analyze_support.update_items.tools_to_manage_storage.step2": "👉🏽 Under \"Angles\", download or delete individual angles.", "shared.analyze_support.update_items.tools_to_manage_storage.step3": "💡 Primary angles from Focus Exchange Network game or scout video are free.", "shared.analyze_support.update_items.tools_to_manage_storage.step4": "💡 You can restore any deleted angles in “Recently Deleted” for 14 days.", "shared.analyze_support.update_items.tools_to_manage_storage.step5": "💡 Delete secondary angles to free up space without losing access to data. Learn more about {learnMoreLink}.", "shared.analyze_support.update_items.tools_to_manage_storage.step5.learn_more_link": "managing your library", "shared.analyze_support.update_items.upgraded_library.title": "Upgraded Library", "shared.analyze_support.update_items.upgraded_library.description": "Stay organized and find video faster with filter and sort options, more detailed information about your video, and better storage management tools.", "shared.analyze_support.update_items.upgraded_library.step1": "👉🏽 Check out our {tutorialsLink} if you need help.", "shared.analyze_support.update_items.upgraded_library.step1.tutorials_link": "tutorials", "shared.analyze_support.update_items.upgraded_library.step2": "💡 Additional improvements, like folders, are coming soon.", "shared.analyze_support.update_items.layout_controls.title": "Updated Layout Controls", "shared.analyze_support.update_items.layout_controls.description": "Use the simplified toolbar to find the tools you need faster.", "shared.analyze_support.update_items.layout_controls.step1": "👉🏽 When watching video, click the buttons on the right edge of the screen to toggle tools like insights and grid.", "shared.analyze_support.update_items.layout_controls.step2": "👉🏽 Click the library icon at the top left to access your library.", "shared.analyze_support.update_items.deleted_video_reports.title": "Review stats from deleted games", "shared.analyze_support.update_items.deleted_video_reports.description": "Free up storage without losing access to data.", "shared.analyze_support.update_items.deleted_video_reports.step1": "👉🏽 In the “Reports” tab, click “View Other Games” in the filter panel.", "shared.analyze_support.update_items.deleted_video_reports.step2": "👉🏽 Select one or more games to see stats.", "shared.analyze_support.update_items.deleted_video_reports.step3": "💡 When making a selection, deleted games will have “no video” in the title.", "shared.analyze_support.update_items.deleted_video_reports.step4": "💡 Only deleted games that were tagged by Hudl Assist will be available.", "shared.analyze_support.update_items.custom_report_tabs.title": "Easily Access Custom Reports", "shared.analyze_support.update_items.custom_report_tabs.description": "Get one-click custom reports for any column, with the ability to see multiple custom reports alongside each other for any video.", "shared.analyze_support.update_items.custom_report_tabs.step1": "👉🏽 Hover on any insights card, then click the expand icon or use the “...” menu to open a custom report.", "shared.analyze_support.update_items.custom_report_tabs.step2": "👉🏽 To close a custom report, use the dismiss button within the report tab.", "shared.analyze_support.update_items.custom_report_tabs.step3": "💡 Customize the pivot fields in your report by clicking on the edit (pencil) icon.", "shared.analyze_support.update_items.custom_report_tabs.step4": "💡 Any custom reports you created in Classic will appear as tabs here soon.", "shared.analyze_support.update_items.more_powerful_custom_labels.title": "More Powerful Custom Labels", "shared.analyze_support.update_items.more_powerful_custom_labels.description": "Add structure to your library by nesting labels. Plus, you can now easily rename or delete custom labels.", "shared.analyze_support.update_items.more_powerful_custom_labels.step1": "👉🏽 In your library, locate the label section of your filter panel.", "shared.analyze_support.update_items.more_powerful_custom_labels.step2": "👉🏽 Click “Create a label” or use the plus icon to add a label.", "shared.analyze_support.update_items.more_powerful_custom_labels.step3": "💡 To add a sublabel, use the three-dot menu on any existing label.", "shared.analyze_support.update_items.more_powerful_custom_labels.step4": "💡 Assign labels using drag and drop or by opening the details for an item.", "shared.analyze_support.update_items.classic_custom_reports.title": "Custom Reports from Classic", "shared.analyze_support.update_items.classic_custom_reports.description": "View and manage custom reports created in Classic.", "shared.analyze_support.update_items.classic_custom_reports.step1": "👉🏽 With “Insights” open, click the list button to see all your reports.", "shared.analyze_support.update_items.classic_custom_reports.step2": "👉🏽 Click a report to open it, and use the edit (pencil) icon to change the pivot fields.", "shared.analyze_support.update_items.classic_custom_reports.step3": "💡 Custom reports are always available, for any selection of video.", "shared.analyze_support.update_items.comment_on_playlist_clips.title": "Comment on Playlist Clips", "shared.analyze_support.update_items.comment_on_playlist_clips.description": "Collaborate, share feedback and discuss key plays directly in Hudl.", "shared.analyze_support.update_items.comment_on_playlist_clips.step1": "💬 While watching video, click the “Comments” icon on the right.", "shared.analyze_support.update_items.comment_on_playlist_clips.step2": "✏️ Type a comment to leave feedback—it’ll appear on the clip.", "shared.analyze_support.update_items.comment_on_playlist_clips.step3": "↪ Click “Reply” to keep the conversation in one thread.", "analyze.keyboard_shortcut.playback": "When editing data, hold shift to access playback shortcuts.", "analyze.keyboard_shortcut.clip_selected": "With a clip selected...", "analyze.keyboard_shortcut.managing_clips": "Managing Clips", "analyze.keyboard.shortcut.effect_selected": "With an effect selected...", "analyze.keyboard.shortcut.manage_clips_mode": "When in manage clips mode...", "analyze.keyboard.shortcut.split_clip": "Split Clip", "module.collaboration.title": "Comments", "module.comments.no_comments": "Comments will appear here", "module.comments.comment_not_found": "That comment has been deleted or doesn’t exist.", "module.comments.replies.reply_count": "{count} {count, plural, one {reply} other {replies}}", "module.comments.comment_group.header.comment_count": "{count} {count, plural, one {comment} other {comments}}", "module.comments.comment.header.coach_prefix": "Coach {lastName}", "module.comments.comment.reply.no_reply": "Reply", "module.comments.comment.reply.has_replies": "{count} {count, plural, one {Reply} other {Replies}}", "module.comments.comment.action.delete": "Delete", "module.comments.comment.action.delete_thread": "Delete Comment & Replies", "module.comments.comment.action.delete_thread.no_replies": "Delete comment", "module.comments.comment.action.delete_thread.with_replies": "Delete comment and {replyCount, plural, one {reply} other {replies}}", "module.comments.input.placeholder": "Write a comment...", "module.comments.input.placeholder.reply": "Reply", "module.comments.toast.delete_thread.success": "You deleted the thread.", "module.comments.toast.delete_comment.success": "You deleted the comment.", "module.comments.toast.delete_comment.error": "Unable to delete comment. Please try again.", "module.comments.toast.restore_comment.error": "Unable to restore comment. Please try again.", "module.comments.toast.send_comment.error": "Unable to send comment. Please try again.", "module.filters.title": "Filters", "module.filters.selected_filters_count_text": "{filterCount, number, integer} {filterCount, plural, one {Filter} other {Filters}}", "module.filters.active_filters": "Active Filters", "module.filters.active_filters.clear": "Clear", "module.filters.active_filters.save": "Save", "module.filters.saved_filters": "Saved Filters", "module.filters.saved_filter_name_input": "Saved Filter Name", "module.filters.saved_filter_name_error": "Saved filter name already exists.", "module.filters.create_saved_filter_error": "Unable to create saved filter.", "module.filters.show_fewer": "Show Fewer", "module.timeline.title": "Timeline", "module.timeline.tooltip": "Support for multiple videos is coming soon.", "module.timeline.header.add-data": "Add Data", "module.grid.title": "Grid", "module.grid.clip_header_text": "{clipCount, number, integer} {clipCount, plural, one {Clip} other {Clips}}", "module.grid.selected_clip_header_text": "{selectedCount} Selected", "module.grid.warning_tooltip.header": "Needs Editing", "module.library.title": "Library", "module.library.notice.assist.rejection.assist_cta_let_us_know": "Let us know", "module.library.notice.assist.rejection.assist_cta_provide_additional_instructions": "provide additional instructions", "module.library.notice.assist.rejection.assist_cta_review_game_details": "review the game details", "module.library.notice.assist.rejection.assist_cta_resubmitting_this_video": "re-submitting this video", "module.library.notice.assist.rejection.exchange_cta_request_exchange": "request an exchange", "module.library.notice.assist.rejection.contact_support": "Questions? {supportLink} is here to help.", "module.library.notice.assist.rejection.contact_support_link": "Hudl Support", "module.library.notice.assist.rejection.primary_event_title_default": "your game", "module.library.notice.assist.rejection.primary_excessive_video": "<PERSON><PERSON> Assist declined {eventName} due to video length", "module.library.notice.assist.rejection.primary_generic": "<PERSON><PERSON> declined {eventName}", "module.library.notice.assist.rejection.primary_illegible_jerseys": "<PERSON><PERSON> declined {eventName} due to illegible jersey numbers", "module.library.notice.assist.rejection.primary_incomplete_roster": "<PERSON><PERSON> declined {eventName} due to an inaccurate roster", "module.library.notice.assist.rejection.primary_incomplete_video": "<PERSON><PERSON> declined {eventName} due to incomplete video", "module.library.notice.assist.rejection.primary_incorrect_team_colors": "<PERSON><PERSON> declined {eventName} due to incorrect jersey colors", "module.library.notice.assist.rejection.primary_poor_quality": "Hudl Assist declined {eventName} due to poor quality video", "module.library.notice.assist.rejection.primary_poor_quality_illegible_jerseys": "<PERSON>dl Assist declined {eventName} due to poor quality video and illegible jersey numbers", "module.library.notice.assist.rejection.primary_recording_location": "<PERSON>dl Assist declined {eventName} due to recording location", "module.library.notice.assist.rejection.primary_unordered_video": "<PERSON><PERSON> Assist declined {eventName} due to out of order video", "module.library.notice.assist.rejection.primary_unordered_excessive_video": "<PERSON><PERSON> Assist declined {eventName} due to video length and order", "module.library.notice.assist.rejection.primary_unordered_imcomplete_video": "<PERSON><PERSON> declined {eventName} due to incomplete and out of order video", "module.library.notice.assist.rejection.recording_recommendations": "When you have a minute, check out these quick {recommendationsLink}.", "module.library.notice.assist.rejection.recording_recommendations_link": "recording recommendations", "module.library.notice.assist.rejection.roster_cta_review_roster": "review your roster", "module.library.notice.assist.rejection.secondary_action_let_us_know_team_stats": "{assist<PERSON><PERSON>} if you would like us to only tag team stats for your game.", "module.library.notice.assist.rejection.secondary_action_please_upload_correct_order_resubmit": "Please {upload<PERSON>ta} in the correct order and re-submit.", "module.library.notice.assist.rejection.secondary_action_review_details_resubmit": "Please {assist<PERSON><PERSON>} before re-submitting this video.", "module.library.notice.assist.rejection.secondary_action_review_roster_resubmit": "Please {rosterCta} before {assist<PERSON><PERSON>}.", "module.library.notice.assist.rejection.secondary_action_upload_correct_order_resubmit": "{uploadCta} in the correct order and re-submit.", "module.library.notice.assist.rejection.secondary_action_upload_or_request_exchange": "{uploadCta} of the game or {exchangeCta} from your opponent.", "module.library.notice.assist.rejection.secondary_action_upload_resubmit_or_instructions": "{upload<PERSON>ta} and re-submit, or {assist<PERSON><PERSON>} for your analyst.", "module.library.notice.assist.rejection.secondary_explanation_cannot_provide_accurate_stats": "We're unable to provide accurate stats with this video.", "module.library.notice.assist.rejection.secondary_explanation_duration": "This video is only {duration} long.", "module.library.notice.assist.rejection.secondary_explanation_duration_unordered": "This video is only {duration} long and is out of order.", "module.library.notice.assist.rejection.secondary_explanation_generic": "We've sent you an email with further details.", "module.library.notice.assist.rejection.secondary_explanation_inaccurate_roster": "A current roster is necessary for accurate stats.", "module.library.notice.assist.rejection.secondary_explanation_multiple_games": "This video contains multiple games.", "module.library.notice.assist.rejection.secondary_explanation_no_player_stats": "We're unable to tag player stats for your team.", "module.library.notice.assist.rejection.secondary_explanation_unordered_multiple_games": "This video contains multiple games and is out of order.", "module.library.notice.assist.rejection.upload_cta_upload_video": "upload video", "module.library.notice.assist.rejection.upload_cta_upload_new_video": "Upload new video", "module.library.items.not_allowed.tooltip": "Can't be viewed with other video types selected.", "module.library.items.files_not_allowed.tooltip": "Files cannot be opened here at this time.", "module.library.items.free_video.tooltip": "Free with Focus Exchange Network", "module.library.items.free_video.tooltip_v2": "Video from the Focus Exchange Network doesn’t count towards storage.", "module.library.items.storage_usage.free": "Free", "module.library.items.storage_usage.hours_used": "{hoursUsed} hrs", "module.library.items.storage_usage.hours_used.includes_free": "{hoursUsed} hrs + {freeHours} free", "module.library.items.storage_usage.hours_used_with_angles": "Using {hoursUsed} {hoursUsed, plural, one {hour} other {hours}} of storage for {paidAngles} {paidAngles, plural, one {angle} other {angles}}.", "module.library.items.storage_usage.hours_used_with_angles.includes_free": "Using {hoursUsed} {hoursUsed, plural, one {hour} other {hours}} of storage for {paidAngles} {paidAngles, plural, one {angle} other {angles}}, with {freeAngles} free Focus Exchange Network {freeAngles, plural, one {angle} other {angles}}.", "module.library.items.actions.share": "Share", "module.library.items.actions.rename": "<PERSON><PERSON>", "module.library.items.actions.delete": "Delete", "module.library.items.actions.restore": "Rest<PERSON>", "module.library.items.actions.send_to_assist": "Send to Assist", "module.library.items.actions.send_to_assist.tooltip.practice_video_not_allowed": "Assist doesn’t accept practice video.", "module.library.items.actions.send_to_assist.tooltip.has_game_only_assist_package": "You can only send game video to Assist. Contact Hudl to update your subscription.", "module.library.items.actions.send_to_assist.tooltip.has_no_active_assist_package": "Assist subscription required. Contact Hudl to update your subscription.", "module.library.items.actions.video_details": "Video Details", "module.library.items.actions.open_details": "Open Details", "module.library.items.actions.download": "Download", "module.library.items.actions.email_download_link": "Email Download Link", "module.library.items.actions.copy_or_move": "Copy or Move", "module.library.items.actions.show_box_score": "Box Score", "module.library.items.actions.edit_video": "Edit", "module.library.items.actions.export": "Export Data", "module.library.items.actions.change_teams": "Move", "module.library.items.actions.edit_item": "Edit Session", "module.library.items.angles_deleted": "{deleted}/{total} angles deleted", "module.library.items.export.field_selection": "Which fields?", "module.library.items.export.field_selection.field_set_only": "Only fields in \"{currentFieldSetName}\"", "module.library.items.export.field_selection.all_fields": "All fields", "module.library.items.export.include_empty_fields": "Include empty fields", "module.library.items.empty_state.message": "You don't have any content yet.", "module.library.items.empty_state.upload_video": "Upload a game, scout or practice video to get started.", "module.library.items.empty_state.upload_something": "Upload Something", "module.library.items.empty_state.filters.primary_message": "No items match your current filter selection.", "module.library.items.empty_state.filters.secondary_message": "Try adjusting a filter or starting over.", "module.library.items.empty_state.filters.action": "Clear All Filters", "module.library.items.empty_state.search.primary_message": "No results found for \"{searchText}\".", "module.library.items.empty_state.search.secondary_message": "Check your spelling or try another search.", "module.library.items.empty_state.search.action": "Clear Search", "module.library.items.empty_state.search_and_filters.primary_message": "No results found for \"{searchText}\" matching your current filter selection.", "module.library.items.empty_state.search_and_filters.secondary_message": "Try adjusting a filter or changing your search.", "module.library.items.empty_state.search_and_filters.action": "Clear All Filters & Search", "module.library.items.empty_state.humanperformance.secondary_message": "To get started, upload a session from your smart station.", "module.library.items.header.group.event_type": "Event & Type", "module.library.items.header.storage_warning.header": "You're out of space.", "module.library.items.header.storage_warning.header_v2": "You're out of storage.", "module.library.items.header.storage_warning.text": "Newly uploaded video will be locked until you free up space.", "module.library.items.header.storage_warning.text_v2": "To free up space, filter to your oldest videos and try deleting the ones you no longer need.", "module.library.items.header.practice_deletion.critical.header": "You're over your storage limit.", "module.library.items.header.practice_deletion.warning.header": "You're approaching your storage limit.", "module.library.items.header.practice_deletion.text": "Practice video from previous seasons is taking up {storageHours} {storageHours, plural, one {hour} other {hours}}. Do you want to delete it?", "module.library.items.header.practice_deletion.action": "Review & Delete Video", "module.library.items.icons.game_exchanged_via_focus_exchange_network.tooltip.pt1": "This game was automatically exchanged via the", "module.library.items.icons.game_exchanged_via_focus_exchange_network.tooltip.pt2": "Focus Exchange Network", "module.library.items.icons.video_angles.tooltip": "Video Angles: ", "module.library.items.header.multi_angle_enforcement.actions.get_help": "Get Help", "module.library.items.header.multi_angle_enforcement.actions.request": "Request Upgrade", "module.library.items.header.multi_angle_enforcement.header": "Storage Update", "module.library.items.header.multi_angle_enforcement.text": "Secondary video angles contribute to your storage limit.", "module.library.items.header.storage_limit_enforcement.learn_more": "Learn More", "module.library.items.header.storage_limit_enforcement.critical.header": "You are out of storage.", "module.library.items.header.storage_limit_enforcement.warning.header": "Easily manage your library.", "module.library.items.header.storage_limit_enforcement.critical.text": "Try deleting some videos to free up space. You can always download before deleting.", "module.library.items.header.storage_limit_enforcement.warning.text": "To stay on top of your storage limit, we've made it easy to download and delete items.", "module.library.items.multi_action_bar.selected": "{itemCount, number, integer} {itemCount, plural, one {Item Selected} other {Items Selected}}", "module.library.items.multi_action_bar.selected.short": "{itemCount, number, integer} {itemCount, plural, one {Selected} other {Selected}}", "module.library.items.count": "{itemCount, number, integer} {itemCount, plural, one {Item} other {Items}}", "module.library.items.action_bar.delete": "Delete", "module.library.items.action_bar.hours_used": "{hoursUsed} {hoursUsed, plural, one {hr} other {hrs}}", "module.library.items.action_bar.clear": "Clear", "module.library.items.action_bar.done_review_state": "Done", "module.library.items.action_bar.play": "Play", "module.library.items.action_bar.restore": "Rest<PERSON>", "module.library.items.action_bar.view_reports": "View Reports", "module.library.items.action_bar.play.storage_disabled_tooltip": "Some videos are locked and can't be viewed.", "module.library.items.action_bar.play.status_disabled_tooltip": "The selected {count, plural, one {item} other {items}} cannot be played at this time.", "module.library.items.action_bar.play.tooltip": "Playlists can't be viewed with other video types.", "module.library.items.action_bar.download.status_disabled_tooltip": "The selected {count, plural, one {item} other {items}} cannot be downloaded at this time.", "module.library.items.action_bar.download.mixed_status_tooltip": "The selected items cannot be downloaded together.", "module.library.items.action_bar.intercut": "Intercut", "module.library.items.action_bar.intercut.tooltip": "Select only video types to intercut (max of 7).", "module.library.items.action_bar.play.mixed_library_type_tooltip": "The selected items cannot be played together.", "module.library.items.action_bar.play.bulk_select_limit": "Select {limit} or less to play.", "module.library.items.action_bar.play.multiple_videos_not_supported_tooltip": "Multiple videos cannot be played together.", "module.library.items.action_bar.view_reports.mixed_library_type_tooltip": "The selected items cannot be viewed together.", "module.library.items.action_bar.view_reports.bulk_select_limit": "Select {limit} or less to view reports.", "module.library.items.action_bar.view_reports.unsupported_type_tooltip": "Cannot view report for the selected {count, plural, one {item} other {items}}.", "module.library.items.search_bar.placeholder": "Search...", "module.library.items.section.yourLibraryItems": "Your Library Items", "module.library.items.section.last7days": "Last 7 Days", "module.library.items.section.last30days": "Last 30 Days", "module.library.items.section.suggestedContent": "Recommended for You", "module.library.items.section.suggestedContent.undoDismiss": "You removed 1 item", "module.library.items.section.suggestedContent.yourMoments": "Your Moments - {eventTitle} - {date, date, medium}", "module.library.items.section.suggestedContent.note": "This content is recommended based on your team’s activity, schedule or recent uploads. It will be visible here for 14 days.", "module.library.items.section.daysUntilExpire": "Expires in {daysUntilExpire} {daysUntilExpire, plural, one {day} other {days}}", "module.library.items.section.deleted_last7days": "Deleted in Last 7 Days", "module.library.items.section.deleted_last14days": "Deleted in Last 14 Days", "module.library.items.section.action_required": "Action Required", "module.library.items.delete_session.modal.header": "Delete Session", "module.library.items.delete_session.modal.explanation": "Once deleted, you can not retrieve it.", "module.library.items.delete_session.modal.action": "Delete", "module.library.items.delete_session.toast.success": "The session has been deleted.", "module.library.items.delete_session.toast.error": "Unable to delete session. Try again.", "module.library.items.move_session.modal.header": "Move Session to Different Team", "module.library.items.move_session.modal.explanation": "Once moved, only the selected team will have access to the session in their library.", "module.library.items.move_session.modal.action": "Move", "module.library.items.move_session.modal.team_label": "Team", "module.library.items.move_session.modal.select_placeholder": "Select a team", "module.library.items.move_session.toast.success": "The session has been moved to team {teamName}", "module.library.items.move_session.toast.error": "Unable to move session. Try again.", "module.library.items.status.uploading": "Uploading...", "module.library.items.status.processing": "Processing...", "module.library.items.status.calculating": "Processing...", "module.library.items.status.balltime_processing": "Video is processing", "module.library.items.status.failed": "Failed", "module.library.items.assist.sent_to.tooltip": "Sent to Assist {timeSince}", "module.library.items.assist.sent_to.tooltip.justnow": "just now", "module.library.items.assist.sent_to.tooltip.minutesago": "{minutes, number, integer} {minutes, plural, one {min} other {min}} ago", "module.library.items.assist.sent_to.tooltip.hoursago": "{hours, number, integer} hr ago", "module.library.items.status.get_help": "Get Help", "module.library.items.mobile.library": "Library", "module.library.items.mobile.filter": "Filter", "module.library.items.mobile.filters": "{filterCount, number, integer} {filterCount, plural, one {Filter} other {Filters}}", "module.library.items.mobile.items.view": "View {itemCount, number, integer} {itemCount, plural, one {Item} other {Items}}", "module.library.items.date_created_label": "{date, date, medium}", "module.library.items.admin_support_link": "Admin Link", "module.library.items.angle_delete_modal.all_angles": "Delete all video angles and data", "module.library.items.angle_delete_modal.all_angles_v2": "Delete all video angles", "module.library.items.angle_delete_modal.secondary_angles": "Delete only secondary angles and keep data", "module.library.items.angle_delete_modal.reports_note": "Hudl Assist data won’t be deleted. You’ll still be able to access it in the “Reports” tab.", "module.library.items.angle_delete_modal.hours_saved": "+{hoursUsed}{hoursUsed, plural, one {hr} other {hrs}}", "module.library.items.angle_delete_modal.hours_saved_v2": "+{hoursUsed}{hoursUsed, plural, one { hr} other { hrs}}", "module.library.items.bulk_delete.cancel": "Cancel", "module.library.items.bulk_delete.delete": "Delete", "module.library.items.bulk_action.deleted_items.toast": "You deleted <bold>{numOfItems, number, integer} {numOfItems, plural, one {item} other {items}}</bold>.", "module.library.items.bulk_action.restored_items.toast": "You restored <bold>{numOfItems, number, integer} {numOfItems, plural, one {item} other {items}}</bold>.", "module.library.items.bulk_action.deleted_items.error_toast": "Unable to delete <bold>{numOfItems, number, integer} {numOfItems, plural, one {item} other {items}}</bold>.", "module.library.items.bulk_action.restored_items.error_toast": "Unable to restore <bold>{numOfItems, number, integer} {numOfItems, plural, one {item} other {items}}</bold>.", "module.library.items.bulk_action.deleted_items.error_toast_v2": "Unable to delete {numOfItems, plural, one {item} other {all items}}.", "module.library.items.bulk_action.restored_items.error_toast_v2": "Unable to restore {numOfItems, plural, one {item} other {all items}}.", "module.library.items.bulk_action.deleted_items.error_toast.select_fewer": "Try selecting fewer items, then deleting.", "module.library.items.bulk_action.restored_items.error_toast.select_fewer": "Try selecting fewer items, then restoring.", "module.library.items.bulk_delete.toast.error_toast_message": "Unable to delete. Please try again.", "module.library.items.bulk_delete.toast.learn_more": "Learn More", "module.library.items.bulk_delete.toast.prefix": "You deleted", "module.library.items.bulk_delete.toast.items": "{numOfItems, number, integer} {numOfItems, plural, one {item} other {items}}", "module.library.items.bulk_delete.toast.recently_deleted": "They will be in \"Recently Deleted\" for 180 days.", "module.library.items.bulk_delete.toast.recently_deleted_old": "They will be in \"Recently Deleted\" for 180 days.", "module.library.items.bulk_delete.toast.recently_deleted_v2": "They will be in \"Recently Deleted\" for 14 days.", "module.library.items.bulk_delete.toast.recently_deleted_v3": "{numOfItems, plural, one {It} other {They}} will be in \"Recently Deleted\" for 14 days.", "module.library.items.bulk_delete.toast.recently_deleted_with_hours": "You deleted {numOfItems, number, integer} {numOfItems, plural, one {item} other {items}}{hoursUsed, select, 0 {} other { ({hoursUsed} {hoursUsed, plural, one {hr} other {hrs}})}}. It may take a few minutes to update them. They will be in \"Recently Deleted\" for 14 days.", "module.library.items.bulk_action.toast.few_minutes": "It may take a few minutes to update{numOfItems, plural, one {} other { them}}.", "module.library.items.bulk_delete.modal_text": "By deleting these items, any report data associated will also be removed. You can restore any item after it's been deleted. Do you want to continue?", "module.library.items.bulk_delete.modal_text_v2": "By deleting these items, any report data associated will also be removed. You can restore any item for 14 days after it's been deleted. Do you want to continue?", "module.library.items.bulk_delete.modal_text_v3": "By deleting these items, any report data associated will also be removed. You can restore any item for 180 days after it's been deleted. Do you want to continue?", "module.library.items.bulk_delete.modal_text_v4": "By deleting these items, any report data associated will also be removed—it may take a few minutes. You can restore any item after it's been deleted. Do you want to continue?", "module.library.items.bulk_delete.modal_text_v5": "Deleting these items may take a few minutes. You can restore any item after it's been deleted. Do you want to continue?", "module.library.items.bulk_delete.files_only_modal_text": "Deleting these items may take a few minutes. You can restore any item after it's been deleted. Do you want to continue?", "module.library.items.playlist.clip_count_label": "{clipCount, number, integer} {clipCount, plural, one {CLIP} other {CLIPS}}", "module.library.items.restore.toast": "You restored {itemCount, number, integer} {itemCount, plural, one {item} other {items}}", "module.library.items.retention_policy.note": "You have up to 14 days to restore deleted video.", "module.library.items.retention_policy.note_v2": "You have up to 14 days to restore deleted content.", "module.library.items.has_assist_data": "Has <bold>Assist</bold> data", "module.library.items.has_data": "Has data", "module.library.items.score.result_win": "W {scoreOne} - {scoreTwo}", "module.library.items.score.result_loss": "L {scoreOne} - {scoreTwo}", "module.library.items.score.result_tie": "T {scoreOne} - {scoreTwo}", "module.library.items.restricted.tooltip": "Preview your locked video and details", "module.library.items.athletes.restricted.tooltip": "Your team has reached the storage limit. Contact your coach.", "module.library.items.event_type.match": "Match", "module.library.items.event_type.practice": "Practice", "module.library.suggested_assist_submission": "<bold>Get key insights from {title}.</bold> Send to Hudl Assist to see detailed stats and interactive reports—at no additional cost.", "module.library.groups.clear_all": "Clear All", "module.library.groups.count": "{filterCount, number, integer} {filterCount, plural, one {Filter} other {Filters}} Selected", "module.library.groups.select_all": "Select All", "module.library.groups.type.title": "Type", "module.library.groups.type.game": "Game", "module.library.groups.type.scout": "Scout", "module.library.groups.type.playlist": "Playlist", "module.library.groups.type.practice": "Practice", "module.library.groups.type.file": "File", "module.library.groups.type.other": "Other", "module.library.groups.type.saved_view": "Saved View", "module.library.groups.type.hp_session": "Physical Data", "module.library.groups.event.title": "Event", "module.library.groups.event.unscheduled": "Unscheduled", "module.library.groups.activity.title": "Activity", "module.library.groups.activity.recently_deleted": "Recently Deleted", "module.library.group.team.title": "Team", "module.library.group.team.sort": "Schedule", "module.library.group.team.notice.message": "Teams in your schedule appear here.", "module.library.group.team.notice.link_text": "Add your schedule", "module.library.group.team.show_more.button.text": "More Teams", "module.library.group.team.show_fewer.button.text": "Fewer Teams", "module.library.group.event.show_more.button.text": "More Seasons", "module.library.group.event.show_fewer.button.text": "Fewer Seasons", "module.library.group.generic.show_fewer.button.text": "Show Fewer", "module.library.group.generic.show_more.button.text": "Show More", "module.library.group.folder.title": "Folder", "module.library.group.folder.notice.message": "Use folders to group things your way.", "module.library.group.label.title": "Label", "module.library.group.label.notice.message": "{create<PERSON><PERSON>l} to start organizing your library.", "module.library.group.label.notice.message.create_label": "Create a label", "module.library.group.label.create_label.duplicate_name_error": "Label name already exists.", "module.library.group.label.create_label.limit_reached_tooltip": "Label limit reached. Try deleting any labels you aren't using to add more.", "module.library.group.label.create_label.error_toast": "Unable to create label.", "module.library.group.label.create_label.limit_reached_toast": "<bold>Label limit reached.</bold> Try deleting any labels you aren't using to add more.", "module.library.group.label.actions.add-sublabel": "Add Sublabel", "module.library.group.label.actions.rename": "<PERSON><PERSON>", "module.library.group.label.actions.delete": "Delete", "module.library.group.label.rename_label.error_toast": "Unable to rename label.", "module.library.group.label.rename_label.success_toast": "Label renamed to <bold>{name}</bold>.", "module.library.group.label.delete_label.modal.header": "Delete Label", "module.library.group.label.delete_label.modal.text": "Deleting this label will remove it from all items. Do you want to continue?", "module.library.group.label.delete_label.modal.checkbox_label": "Also delete {count} sublabels", "module.library.group.label.delete_label.error_toast": "Unable to delete label. Please try again.", "module.library.group.label.delete_label.success_toast": "You deleted <bold>{name}</bold>.", "module.library.group.label.move_label.error_toast": "Unable to move label. Please try again.", "module.library.group.hours_used": "{hoursUsed} {hoursUsed, plural, one {hr} other {hrs}}", "module.library.humanperformance.edit_session_button": "Edit Session", "module.library.humanperformance.raw_session_title": "Untitled Session", "module.library.humanperformance.view_more_button": "View more", "module.library.storage.hours_per_team": "{hoursUsed} hrs", "module.library.storage.hours_used.header": "Storage", "module.library.storage.hours_used.header_v2": "Manage Storage", "module.library.storage.hours_used.message": "{storageUsageHours} of {storageLimitHours} hrs ({storagePercent}%) used", "module.library.storage.usage_button": "Usage", "module.library.storage.learn_more": "Learn more about <a>managing storage</a>.", "module.library.storage.free_up_space": "Free up Space", "module.library.storage.free_up_space.modal.copy": "We noticed there's some video you may not need anymore — try deleting it to make space for new video.", "module.library.storage.free_up_space.modal.old_video_advice": "We recommend filtering to your oldest videos and deleting the ones you no longer need. Check back here for more suggestions soon!", "module.library.storage.free_up_space.modal.secondary_angles": "Secondary Angles", "module.library.storage.free_up_space.modal.secondary_angles.usage": "Free up {hoursUsed} {hoursUsed, plural, one {hour} other {hours}} of space", "module.library.storage.free_up_space.modal.secondary_angles.tagged_data_disclaimer": "Keep tagged data", "module.library.storage.free_up_space.modal.old_practice_video.old_video_inclusion": "Includes video older than one year", "module.library.storage.free_up_space.modal.secondary_angles.button.review_and_delete": "Review & Delete", "module.library.storage.free_up_space.modal.confirm": "Okay", "module.library.storage.free_up_space.modal.secondary_angles.need_more": "Need more space? <upgrade>Request an upgrade</upgrade>.", "module.library.storage.free_up_space.modal.old_practice_video": "Old Practice Video", "module.library.video_type.match": "Match", "module.library.modal.video_details.label": "Event", "module.library.modal.video_details.no_event": "None", "module.library.modal.video_details.event_date": "{date, date, medium}", "module.library.modal.details_module.label": "Event", "module.library.modal.details_module.no_event": "None", "module.library.modal.details_module.event_date": "{date, date, medium}", "module.library.experience_toggle.button.text": "Play video in new experience", "module.library.experience_toggle.tooltip.text": "Change your default video experience. Playing multiple videos is only available in the new video experience.", "module.library.experience_toggle.tooltip.multiple_videos_text": "Playing multiple videos is only available in the new video experience.", "module.library.video_details.title": "Title", "module.library.video_details.title.error.empty_state": "Please enter a name", "module.library.video_details.type": "Type", "module.library.video_details.type.match": "Match", "module.library.video_details.type.game": "Game", "module.library.video_details.type.scout": "Scout", "module.library.video_details.type.practice": "Practice", "module.library.video_details.events": "Event", "module.library.details_module.update_error_toast": "Failed to update video details", "module.library.details_module.update_success_toast": "Video details updated", "module.library.details_module.labels.add_labels_success_toast": "Labeled <bold>{count, number, integer} {count, plural, one {item} other {items}}</bold> as \"{labelName}\"", "module.library.details_module.title": "Title", "module.library.details_module.title.error.empty_state": "Please enter a name", "module.library.details_module.type": "Type", "module.library.details_module.type.match": "Match", "module.library.details_module.type.game": "Game", "module.library.details_module.type.scout": "Scout", "module.library.details_module.type.practice": "Practice", "module.library.details_module.events": "Event", "module.library.details_module.labels": "Labels", "module.library.details_module.angles": "<PERSON><PERSON>", "module.library.details_module.angles.tooltip": "Manage the video angles available for this video. Primary angles are determined by sport and cannot be changed.", "module.library.details_module.angles.tooltip_v2": "Manage your video angles. Deleted angles can be restored for up to 14 days. ", "module.library.details_module.angles.actions.deleted": "Deleted", "module.library.details_module.angles.actions.deleting": "Deleting", "module.library.details_module.angles.actions.delete_failed": "Delete Failed", "module.library.details_module.angles.actions.restore": "Rest<PERSON>", "module.library.details_module.angles.actions.restored": "Restored", "module.library.details_module.angles.actions.restoring": "Restoring", "module.library.details_module.angles.actions.restore_failed": "Restore Failed", "module.library.details_module.video_player.preview": "Preview", "module.library.video_details.labels": "Labels", "module.library.video_details.angles.primary": "Primary", "module.library.video_details.angles": "<PERSON><PERSON>", "module.library.details_module.angles.restore.learn_more": "Learn More", "module.library.video_details.angles.tooltip": "Manage the video angles available for this video. Primary angles are determined by sport and cannot be changed.", "module.library.experience_modal.header": "You've Changed Your Video Preference", "module.library.experience_modal.continue": "Continue", "module.library.experience_modal.no_change": "Don't Change Preference", "module.library.experience_modal.options.info": "When you play a single video, it will now open in the old video experience rather than the new one. Here is the difference:", "module.library.experience_modal.options.new_experience.header": "New Experience - Video + Data", "module.library.experience_modal.options.new_experience.alt_text": "New experience screenshot", "module.library.experience_modal.options.new_experience.new_badge": "NEW", "module.library.experience_modal.options.new_experience.description.part1": "Video and data side by side", "module.library.experience_modal.options.new_experience.description.part2": "Dynamic stats and filters", "module.library.experience_modal.options.new_experience.description.part3": "Watch multiple videos at once", "module.library.experience_modal.options.old_experience.header": "Old Experience - Video Only", "module.library.experience_modal.options.old_experience.alt_text": "Old experience screenshot", "module.library.experience_modal.options.old_experience.description.part1": "Video and data separate", "module.library.experience_modal.options.old_experience.description.part2": "Standard video filters", "module.library.experience_modal.options.old_experience.description.part3": "Watch a single video at once", "module.library.multiple_videos_experience_modal.header": "Previewing the New Video Experience", "module.library.multiple_videos_experience_modal.description.part1": "You selected multiple videos,", "module.library.multiple_videos_experience_modal.description.part2": "{part1Bold} which can only be viewed together in <PERSON><PERSON>’s new video experience—alongside dynamic stats and filters.", "module.library.multiple_videos_experience_modal.description.part3": "To play video in the old experience, return to the library and limit your selection to a single item.", "module.library.multiple_videos_experience_modal.dismiss_button": "Got It", "module.library.sort.group_title": "SORT BY", "module.library.sort.option.last_uploaded": "Last Uploaded", "module.library.sort.option.newest_uploads": "Newest Uploads", "module.library.sort.option.first_uploaded": "First Uploaded", "module.library.sort.option.oldest_uploads": "Oldest Uploads", "module.library.sort.option.name": "Name", "module.library.sort.option.title": "Title", "module.list.title": "List", "module.details.shared_with": "Shared With", "module.details.shared_with.team_admins": "Team Admins", "module.details.shared_with.can_view": "{name} can view", "module.details.shared_with.can_contribute": "{name} can contribute", "module.details.shared_with.can_edit": "{name} can edit", "module.details.shared_with.can_manage": "{name} can manage", "module.details.shared_with.is_owner": "{name} is the owner", "module.grid.header.manage_clips": "Manage Clips", "module.grid.header.merge_clips": "<PERSON><PERSON>", "module.grid.header.clear-selection": "Clear Selection", "module.grid.header.add-data": "Add Data", "module.grid.header.reorder": "Reorder", "module.grid.header.consecutive_clips_message": "Selected clips must be consecutive to merge them.", "module.grid.header.successful_merge_message": "You merged {clipCount} clips", "module.grid.header.done": "Done", "module.grid.clip_split_type.count": "Clips", "module.grid.clip_split_type.seconds": "Seconds", "module.grid.clip_split_type.minutes": "Minutes", "module.grid.clip_split.header": "Need more control?", "module.grid.clip_split.by_count": "Auto-clip for", "module.grid.clip_split.by_time": "Auto-clip every", "module.grid.clip_split.create_clips": "Create C<PERSON>s", "module.grid.clip_split.successful_message": "You created {clipCount} clips", "module.grid.clip_manual_split.successful_message": "New clip created at {splitTime}.", "module.grid.custom_filter.sortby": "Sort By", "module.insights.title": "Insights", "module.insights.tooltip.generic_count": "{statLabel} ({count, number, integer} {countLabel})", "module.insights.tooltip.clip_count": "{statLabel} ({count, number, integer} {count, plural, one {clip} other {clips}})", "module.insights.tooltip.play_count": "{statLabel} ({count, number, integer} {count, plural, one {play} other {plays}})", "module.insights.tooltip.pitch_count": "{statLabel} ({count, number, integer} {count, plural, one {pitch} other {pitches}})", "module.insights.tooltip.sequence_count": "{statLabel} ({count, number, integer} {count, plural, one {sequence} other {sequences}})", "module.insights.tooltip.moment_count": "{statLabel} ({count, number, integer} {count, plural, one {moment} other {moments}})", "module.insights.ungrouped_stats_group_card_title": "Ungrouped Stats", "module.insights.visualization.spray_chart.lines": "Lines", "module.insights.visualization.spray_chart.zones": "Zones", "module.insights.visualization.spray_chart.zones_key.label_low": "Fewer Hits", "module.insights.visualization.spray_chart.zones_key.label_high": "More Hits", "module.insights.visualization.unknown": "Unknown", "module.insights.button.view_all": "View All", "module.insights.button.view_less": "View Less", "module.insights.card.custom_filter_cancel": "Cancel", "module.insights.card.report.editor.report_on": "Report On", "module.insights.card.report.editor.by": "By", "module.insights.card.report.editor.and": "And", "module.insights.reports_search_placeholder": "Find a report...", "module.insights.reports_list_view.rename": "<PERSON><PERSON>", "module.insights.reports_list_view.delete": "Delete", "module.insights.reports_list_view.close": "Close", "module.insights.reports_list_view.open_reports": "Open Reports", "module.insights.reports_list_view.other_reports": "Other Reports", "module.video.clip_context_bar.play_count": "{clipNumber} / {clipTotal}", "module.video.clip_context_bar.score": "{scoreTeamOne}–{scoreTeamTwo}", "module.video.clip_context_bar.period": "Period {period}", "module.video.clip_context_bar.pitcher": "P: ", "module.video.clip_context_bar.batter": "B: ", "module.video.clip_context_bar.no_data": "No Data", "module.video.clip_context_bar.outs": "{outs} {outs, plural, one {Out} other {Outs}}", "module.video.clip_context_bar.runs": "{runs} {runs, plural, one {Run} other {Runs}}", "module.video.clip_context_bar.count": "{balls}–{strikes}", "module.video.clip_context_bar.set": "Set {set}", "module.video.v3_video_experience_button.text": "Open in Old Experience", "module.video.v3_video_experience_button.multiple_videos_tooltip.text": "Multiple videos can only be viewed together in the new experience.", "module.reports.shared.feedback.header": "Send Reports Beta Feedback", "module.reports.shared.feedback.label": "What can we improve? Your feedback will help inform the future of reports — please do not include any personal information in your response.", "module.reports.shared.feedback.label_short": "What can we improve? Your feedback will help inform the future of reports — please do not include any personal information.", "module.reports.shared.titles.overview": "Overview", "module.reports.shared.titles.ai_insights": "AI Insights", "module.reports.shared.ai_insights.warning": "AI Insights is a beta feature and may be inaccurate.", "module.reports.football.titles.formations": "Formations", "module.reports.baseballSoftball.titles.pitching": "Pitching", "module.reports.baseballSoftball.titles.batting": "Batting", "module.reports.ai.feedback.why_helpful": "Why was this helpful?", "module.reports.ai.feedback.why_not_helpful": "Why wasn't this helpful?", "module.reports.ai.summarize_game": "Summarize the game", "module.reports.ai.analyze_athlete": "Analyze an athlete", "module.details.data": "Data", "module.details.data.feedType.hudl_tags": "Hudl Tags", "module.details.data.feedType.sportscode": "Sportscode XML ({count})", "module.details.manage_data": "Manage Data", "module.details.manage_data_sportscode": "to upload Sportscode XML", "module.details.manage_data_hudl": "to upload XML", "module.details.manage_data.modal.header": "Manage Data for {title}", "module.details.manage_data.modal.actions.done": "Done", "module.details.manage_data.modal.no_data_message": "No data added yet.", "module.details.manage_data.modal.no_data_upload_message": "Upload one or more XML files.", "module.details.manage_data.modal.no_data_sportscode_message": "Upload one or more Sportscode XML files.", "module.details.manage_data.modal.deleted": "Deleted", "module.details.manage_data.taggingSessionSummary.last_edited": "Last Edited {date, date, medium}", "module.details.manage_data.taggingSessionSummary.last_edited_by_you": "Last Edited {date, date, medium} by You", "module.details.manage_data.taggingSessionSummary.last_edited_by_user": "Last Edited {date, date, medium} by {username}", "module.details.manage_data.taggingSessionSummary.completed_by_assist": "Completed {date, date, medium} by <PERSON><PERSON>", "module.details.manage_data.taggingSessionSummary.uploaded": "Uploaded {date, date, medium}", "module.details.manage_data.taggingSessionSummary.uploaded_by_you": "Uploaded {date, date, medium} by You", "module.details.manage_data.taggingSessionSummary.uploaded_by_user": "Uploaded {date, date, medium} by {username}", "module.details.manage_data.upload.upload_xml": "Upload XML", "module.details.manage_data.upload.uploading": "Uploading", "module.details.manage_data.upload.uploaded": "Uploaded", "module.details.manage_data.upload.upload_failed": "Upload Failed", "module.details.manage_data.upload.file_processing_error_toast_message": "There was a problem processing the file. Please check the format.", "module.details.reports": "Reports", "module.details.reports.box_score_link": "View Box Score", "module.details.reports.matrix_link": "View Matrix Report", "module.details.reports.no_data_message.text": "Your video selection doesn’t contain any data. Add data to get key insights and view reports.", "module.details.reports.no_data_message.send_to_assist_button": "Send to Assist", "module.details.reports.no_data_message.multiple_button_text": "{sendToAssistButton} or {manualTagButton}", "module.details.reports.no_data_message.manual_tag_button": "Tag Manually", "module.details.reports.button.more": "More", "module.details.header.open_file": "Open File", "module.library.details_module.create_schedule_entry_success_toast": "New event added.", "module.library.details_module.create_schedule_entry_error_toast": "Could not create event. Please try again.", "module.library.details_module.load_opponents_error_toast": "Could not fetch opponents. Please try again.", "analyze.navbar.aiBreakdowns.beta": "Beta", "analyze.navbar.aiBreakdowns.beta.warning": "AI Breakdowns is a beta feature.", "watch.error.fetch": "Error fetching data", "watch.error.fetch.retry": "Retry", "watch.error.fetch.generic": "Generic Data Fetch Error", "watch.layout.header.full.game": "Full game", "watch.layout.header.moments": "Moments", "watch.moments.grid.header.number": "#", "watch.moments.grid.header.time": "Time", "watch.moments.grid.header.period": "Period", "watch.moments.grid.header.moments": "Moments", "watch.moments.grid.header.date": "Date", "watch.moments.grid.header.game": "Game", "moment.soccer.passChain": "Pass Chain", "moment.soccer.endPeriod": "End Period", "moment.soccer.shot": "Shot", "moment.soccer.ownGoal": "Own Goal", "moment.soccer.cross": "Cross", "moment.soccer.foul": "F<PERSON>l", "moment.soccer.goalkeeping": "Goalkeeping", "watch.progress_bar.split": "Split", "analyze.moment.tag.win": "Win", "analyze.moment.tag.loss": "Loss", "analyze.playlist.delete_alert_message": "Removing this clip from {selectedPlaylistTitle} will also delete the playlist. Would you like to remove the clip and delete the playlist?", "analyze.playlist.remove_clips_message": "You removed {clipCount} {clipCount, plural, one {clip} other {clips}}.", "playlist.delete_playlist_panel.undo_button": "Undo", "playlist.delete_playlist_panel.delete_button": "Delete", "playlist.playlist_popover.add_title": "Add To", "playlist.playlist_popover.remove_title": "Remove From", "actions.playlist.add_clips_to_playlist.success_toast_message": "Added {clipCount} {clipCount, plural, one {clip} other {clips}} to playlist {playlistTitle}.", "actions.playlist.add_clips_to_playlist.error_toast_message": "The {clipCount, plural, one {clip} other {clips}} couldn't be added", "actions.playlist.delete_playlist_clip.remove_error_toast_message": "The clip couldn't be removed", "actions.playlist.create_playlist.saving_error_toast_message": "The playlist couldn't be saved", "actions.playlist.create_playlist.saving_success_toast_message": "The playlist {playlistTitle} was created with {clipCount} {clipCount, plural, one {clip} other {clips}}.", "action.playlist.create_playlist.default_name": "Video/Playlist Name", "action.playlist.create_playlist.button": "New Playlist", "action.playlist.create_playlist.not_saved": "Your new playlist wasn't saved.", "actions.playlist.delete_playlist.deleting_error_toast_message": "The playlist couldn't be deleted", "actions.clips.add_clips_to_playlists_and_highlights.multiple_success_toast_message": "Added {clipCount} {clipCount, plural, one {clip} other {clips}} to <bold>{title}</bold> and {numberAdditionalSuccessfulRequests} {numberAdditionalSuccessfulRequests, plural, one {other} other {others}}", "actions.clips.add_clips_to_playlists_and_highlights.success_toast_message": "Added {clipCount} {clipCount, plural, one {clip} other {clips}} to <bold>{title}</bold>", "actions.clips.add_clips_to_playlists_and_highlights.error_toast_message": "The clip couldn't be added", "actions.effects.delete_effects.delete_effects_toast_message": "{effectsCount, plural, one {Effect} other {Effects}} deleted.", "actions.effects.restore_effects.restore_effects_toast_message": "{effectsCount, plural, one {Effect} other {Effects}} restored.", "actions.effects.restore_effects.restore_effects_error_toast_message": "Error restoring effects.", "watch.analyze.share.saved_view.modal.description_1": "This <a>Saved View</a> will share exactly what you see—including all selected videos, your screen layout, active filters, and reports.", "watch.analyze.share.saved_view.modal.description_2": "Any effects will be copied over. Conversations and effects on a Saved View don't impact the original videos.", "watch.analyze.share.saved_view.modal.share": "Share", "watch.analyze.share.saved_view.modal.save": "Save", "watch.analyze.share.saved_view.modal.cancel": "Cancel", "watch.analyze.share.saved_view.modal.done": "Done", "watch.analyze.share.saved_view.modal.create_view": "Create View", "analyze.export.download": "Download", "watch.analyze.export.download.modal.clips.header": "Get Download Link", "watch.analyze.export.download.modal.clips.description.video": "When the video is ready, we'll send you an email with a link to download it.", "watch.analyze.export.download.modal.clips.description.clip.single": "When the clip is ready, we'll send you an email with a link to download it.", "watch.analyze.export.download.modal.clips.description.clip.multiple": "When the clips are ready, we'll send you an email with a download link.", "watch.analyze.export.download.modal.clips.description.playlist.single": "When the playlist is ready, we'll send you an email with a download link.", "watch.analyze.export.download.modal.clips.description.playlist.multiple": "When the playlists are ready, we'll send you an email with a download link.", "watch.analyze.export.download.modal.clips.note.title": "Note: ", "watch.analyze.export.download.modal.clips.note.message": "This may take some time, so keep an eye on your inbox.", "watch.analyze.export.download.modal.clips.clips.question": "You've selected {numClips} clips. How do you want to download them?", "watch.analyze.export.download.modal.clips.single_playlist.question": "This playlist contains {numClips} clips. How do you want to download them?", "watch.analyze.export.download.modal.clips.multiple_playlists.question": "These playlists contain {numClips} clips in total. How do you want to download them?", "watch.analyze.export.download.modal.clips.option.single": "As a single video", "watch.analyze.export.download.modal.clips.option.single_playlist.multiple": "As {numClips} individual videos", "watch.analyze.export.download.modal.clips.option.multiple_playlists.multiple": "As individual videos", "watch.analyze.export.download.modal.button.primary": "Get Download Link", "analyze.components.basketball.avatar-stack-athletes-description": "{athletesNumber} athletes", "shared.feedback.button.early_access": "We're still making improvements.", "shared.feedback.button.feedback": "Click here to give us feedback.", "shared.feedback.button.feedback.short": "<PERSON>", "shared.feedback.modal.cancel": "Cancel", "shared.feedback.disclaimer": "This feedback will be sent directly to Hudl's product team. Please do not include any personal information.", "shared.feedback.send_feedback": "Send Feedback", "shared.feedback.sent": "<PERSON><PERSON>", "shared.feedback.failed": "Failed", "shared.feedback.failed_toast": "Please try again.", "shared.loop_control.playback": "Playback", "shared.loop_control.video_playback": "Video Playback", "shared.loop_control.playback_settings": "Playback Settings:", "shared.loop_control.normal": "Normal", "shared.loop_control.autoskip": "Auto-Skip", "shared.loop_control.loop": "Loop", "shared.loop_control.loop-clip": "Loop Clip", "shared.loop_control.play-all-clips": "Play all Clips", "shared.loop_control_source_effects": "Effects", "shared.keyboard_shortcut.generic.key.control": "ctrl", "shared.keyboard_shortcut.generic.hold_capitalized": "Hold", "shared.keyboard_shortcut.generic.key.space": "space", "shared.keyboard_shortcut.generic.key_description.play_pause": "Play/Pause", "shared.keyboard_shortcut.generic.key_description.fast_forward": "Fast Forward", "shared.keyboard_shortcut.generic.key_description.fast_rewind": "Fast Rewind", "shared.keyboard_shortcut.generic.key_description.slow_forward": "Slow Forward", "shared.keyboard_shortcut.generic.key_description.slow_rewind": "Slow Rewind", "shared.keyboard_shortcut.generic.key_description.forward": "Forward 5s", "shared.keyboard_shortcut.generic.key_description.rewind": "Rewind 5s", "shared.keyboard_shortcut.generic.key_description.next_clip": "Next Clip", "shared.keyboard_shortcut.generic.key_description.previous_clip": "Previous Clip", "shared.keyboard_shortcut.generic.key_description.restart_clip": "<PERSON><PERSON>", "shared.keyboard_shortcut.generic.key_description.copy": "Copy", "shared.keyboard_shortcut.generic.key_description.paste": "Paste", "shared.keyboard_shortcut.generic.key_description.duplicate": "Duplicate", "video.toolbar.drawCircle": "Draw Circle", "video.toolbar.drawFreehand": "Draw Freehand", "video.toolbar.delete": "Delete", "video.toolbar.hide": "Hide Effects Toolbar", "video.toolbar.drawArrow": "Draw Arrow", "video.toolbar.drawLine": "Draw Line", "video.toolbar.drawTBar": "Draw Block", "video.toolbar.addText": "Add Text", "video.toolbar.float_video": "Open video in a new window", "videocontrols.addEffects": "Add Effects", "analyze.shared.navigation.edit_clip_error_toast": "Unable to update value. Please try again.", "effects.deleteError": "A problem occurred while trying to delete.", "effects.saveError": "A problem occurred while trying to save.", "effects.notice.header": "Some effects are only visible in the video + data experience.", "effects.notice.text": "They may not transfer over or may appear differently in the video only experience. Anyone who has access to the video can see effects.", "effects.notice.buttonText": "Learn More", "analyze.playlist_popover.label.add_to.as_standard_button": "Add To...", "analyze.playlist_popover.label.add_to.as_standard_button_v2": "Save {clipCount, plural, one {Clip} other {Clips}}", "analyze.playlist_popover.label.add_to.checked": "Added", "analyze.playlist_popover.label.add_to.unchecked": "Add to", "analyze.playlist_popover.label.add_to.unchecked_v2": "Save Clip", "analyze.playlist_popover.label.remove_clips.as_standard_button": "<PERSON><PERSON><PERSON>", "analyze.playlist_popover.athlete_highlights.title": "Athlete Highlights", "analyze.playlist.send_to_sportscode": "Open in Sportscode", "analyze.shared.download.modal.title": "Download Options", "analyze.shared.download.modal.info.video": "You'll receive an email with a link to download your video once the files are ready — it may take a few minutes.", "analyze.shared.download.modal.info.xchange": "You'll receive an email with a link to download your Xchange file once the files are ready — it may take a few minutes.", "analyze.shared.download.modal.info.presentation": "You'll receive an email with a link to download your presentation once the files are ready — it may take a few minutes.", "analyze.shared.download.modal.format.question": "How do you want your video organized?", "analyze.shared.download.modal.format.v3.stats": "Stats", "analyze.shared.download.modal.format.v3.video_options.question": "There is tagging data on the game(s) you selected. Would you like to download your stats or just your video?", "analyze.shared.download.modal.format.v3.video_options.video_and_stats": "Video and stats", "analyze.shared.download.modal.format.v3.video_options.video_only": "Video only", "analyze.shared.download.modal.format.v3.single_video": "For each playlist, a single video with all clips", "analyze.shared.download.modal.format.v3.multiple_videos": "For each playlist, every clip has its own video", "analyze.shared.download.modal.format.v3.multiple_videos_v2": "For each playlist, every clip as its own video", "analyze.shared.download.modal.format.classic.single_video": "For each video, a single file with all clips", "analyze.shared.download.modal.format.classic.multiple_videos": "For each video, every clip has its own file", "analyze.shared.download.modal.buttons.download": "Download", "analyze.shared.download.success_toast_message": "Download {itemsCount, plural, one {link} other {links}} sent to {email}. {itemsCount, plural, one {It} other {They}} may take a few minutes to arrive.", "analyze.shared.download.error_toast_message": "Unable to download. Please try again.", "analyze.shared.errorBoundary.backToHome": "Back to Home", "analyze.shared.errorBoundary.noAccessToTeam.header": "You don’t have access to this team.", "analyze.shared.errorBoundary.noAccessToTeam.message": "Please reach out to your coach or admin as your permissions may have changed.", "analyze.highlights.highlights_modal_confirmation.clips_too_long.body": "To keep your highlight quick and engaging, we'll automatically trim long clips to the last {seconds} seconds.", "module.library.libraryItem.assist.rejection_reason.default": "<PERSON><PERSON> couldn't tag this video.", "module.library.libraryItem.assist.rejection_reason.prefix_verbiage": "<PERSON><PERSON> couldn't tag this video because ", "module.library.libraryItem.assist.rejection_reason.PoorVideoQuality": "the quality is too low.", "module.library.libraryItem.assist.rejection_reason.BadRecordingLocationOrAngle": "recording angle is unclear.", "module.library.libraryItem.assist.rejection_reason.IncompleteVideo": "it's incomplete.", "module.library.libraryItem.assist.rejection_reason.VideoOutOfOrder": "it's out of order.", "module.library.libraryItem.assist.rejection_reason.ExcessiveVideoDuration": "the duration is too long.", "module.library.libraryItem.assist.rejection_reason.VideoRotated": "the video is rotated.", "module.library.libraryItem.assist.rejection_reason.IncorrectTeamColors": "team colors are incorrect.", "module.library.libraryItem.assist.rejection_reason.SwitchedHomeAndAwayTeams": "home and away teams are switch.", "module.library.libraryItem.assist.rejection_reason.IncompleteRoster": "the roster is incomplete.", "module.library.libraryItem.assist.rejection_reason.UnreadableScoreSheet": "the score sheet isn't readable.", "module.library.libraryItem.assist.rejection_reason.IllegibleJersey": "jerseys aren't readable.", "module.library.libraryItem.assist.rejection_reason.NoHashMarks": "the field markings aren't visible.", "module.library.rewind.redirect_modal.message": "Introducing Hudl Rewind, an end of season recap that celebrates your 2025 volleyball accomplishments. Open the Hudl app, view the experience in your library, and share your season's stats!", "module.library.rewind.redirect_modal.learn_more_button": "Learn More", "module.library.rewind.redirect_modal.close_button": "Close"}}