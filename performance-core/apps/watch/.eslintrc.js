module.exports = {
  extends: '@hudl',
  ignorePatterns: ['/*', '!/src', '!/playwright'],
  parserOptions: {
    project: './tsconfig.json',
    tsconfigRootDir: __dirname,
  },
  rules: {
    // The following rules are custom configuration for this app
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }], // Allow unused variables that start with an underscore. Used for Playwright fixture test arguments that appear unused.
    'no-console': ['error', { allow: ['warn', 'error'] }], // Allow console.warn and console.error, but not the other console methods
    '@typescript-eslint/explicit-function-return-type': [
      'error',
      {
        // 2553 errors for missing return type on function
        allowFunctionsWithoutTypeParameters: true,
      },
    ],
    '@typescript-eslint/no-use-before-define': 'off', // This rule encourages moving helper functions to the top of file, which often makes it harder to read
    '@typescript-eslint/no-misused-promises': 'off', // Often too strict and usually annoying to resolve with how often we dispatch async thunks

    // These rules have existing failures in the codebase, so we set them to 'warn' to avoid breaking the build
    // These rules are not currently enforced, but we still expect engineers to fix them as they work on the codebase
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    '@typescript-eslint/no-unsafe-assignment': 'warn',
    '@typescript-eslint/no-unsafe-argument': 'warn',
    '@typescript-eslint/no-unsafe-call': 'warn',
    '@typescript-eslint/no-unsafe-member-access': 'warn',
    '@typescript-eslint/no-unsafe-enum-comparison': 'warn',
    '@typescript-eslint/no-unsafe-return': 'warn',
    'react/jsx-no-bind': 'warn',
    'jsx-a11y/click-events-have-key-events': 'warn',
    'jsx-a11y/media-has-caption': 'warn',
    'jsx-a11y/no-autofocus': 'warn',
    'jsx-a11y/no-noninteractive-element-interactions': 'warn',
    'jsx-a11y/no-static-element-interactions': 'warn',
  },
};
