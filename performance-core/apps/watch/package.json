{"name": "watch", "version": "0.1.0", "private": false, "contributors": [{"name": "Web Gems, <PERSON> Brand Here, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 404, <PERSON>", "url": "https://hudl.slack.com/archives/C0479456U9H", "channel": "#performance-core-eng", "qaChannel": "#performance-core-qa"}], "scripts": {"antioch": "tsx ../../../internal/scripts/src/antioch-runner.ts --port 8082 -p 10", "build": "vite build", "build-storybook": "storybook build --stats-json", "build:analyze": "webpack-bundle-analyzer build/stats.json", "build:verify": "node ../../../internal/scripts/verify-bundle-stats.js", "chromatic": "chromatic --exit-once-uploaded --storybook-build-dir storybook-static --storybook-base-dir performance-core/apps/watch --project-token $WATCH_CHROMATIC_PROJECT_TOKEN", "chromatic:skip": "chromatic --skip --project-token $WATCH_CHROMATIC_PROJECT_TOKEN", "clean": "rimraf build node_modules/.cache storybook-static", "dev": "vite-node ../../../internal/scripts/src/copy-dev-config.ts && concurrently \"pnpm run types:watch\" \"vite\"", "download-screenshots": "tsx ../../../internal/scripts/src/upload-screenshots.ts --download --app watch", "generate-gql": "graphql-codegen --config codegen.yml", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lint:styles": "stylelint './src/**/*.scss' --quiet --rdd --risd --rd --config './stylelint.config.js'", "lint:styles:fix": "stylelint './src/**/*.scss' --quiet --rdd --risd --rd --fix --config './stylelint.config.js'", "madge": "madge --circular src/index.tsx", "nuke": "pnpm run clean && rimraf node_modules", "playwright": "tsx ../../../internal/scripts/src/playwright-runner.ts -p 8082 -d app", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "preview": "vite preview", "release": "release-vite-app", "sentry:release": ". ../../../internal/config/sentry_release.sh", "storybook": "storybook dev -p 9005", "test": "vitest", "test:ci": "vitest --coverage --silent", "test:nowatch": "vitest --watch=false", "test:update": "vitest --update", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src", "upload-screenshots": "tsx ../../../internal/scripts/src/upload-screenshots.ts --app watch", "vr-playwright": "tsx ../../../internal/scripts/src/playwright-runner.ts -p 8082 --config ./playwright/vr-playwright.config.ts --app watch"}, "dependencies": {"@auth0/auth0-react": "2.1.0", "@auth0/auth0-spa-js": "2.1.2", "@draft-js-plugins/editor": "4.1.4", "@draft-js-plugins/mention": "5.2.2", "@formatjs/intl": "2.10.1", "@hudl/analytics": "workspace:*", "@hudl/details-module": "workspace:*", "@hudl/frontends-environment": "workspace:*", "@hudl/frontends-logging": "workspace:*", "@hudl/hudl-domain-types": "workspace:*", "@hudl/performance-core-domain": "workspace:*", "@hudl/performance-core-layout": "workspace:*", "@hudl/performance-core-shared": "workspace:*", "@hudl/platform-i18n-adapter": "workspace:*", "@hudl/schedules-edit": "workspace:*", "@hudl/uniform-modules": "workspace:*", "@hudl/uniform-web": "workspace:*", "@hudl/video-interface": "workspace:*", "@local/webnav": "workspace:*", "@radix-ui/react-accordion": "1.2.0", "@reduxjs/toolkit": "2.6.1", "@rtk-query/graphql-request-base-query": "2.3.1", "@sentry/browser": "7.77.0", "@sentry/react": "7.77.0", "@sentry/tracing": "7.77.0", "@snowplow/browser-tracker": "3.24.4", "@tanstack/react-virtual": "3.11.3", "@types/react-transition-group": "4.4.12", "autosize": "4.0.2", "classnames": "2.3.1", "date-fns": "2.29.3", "draft-js": "0.11.7", "frontends-features": "workspace:*", "frontends-i18n": "workspace:*", "frontends-preload": "workspace:*", "frontends-shared": "workspace:*", "fuse.js": "^7.1.0", "graphql-request": "4.3.0", "history": "5.3.0", "immer": "9.0.6", "immutable": "4.2.2", "invariant": "2.2.1", "lodash": "4.17.21", "lodash.throttle": "4.1.1", "moment": "2.29.4", "mousetrap": "1.6.5", "omit-deep-lodash": "1.1.5", "prop-types": "15.8.1", "rc-tooltip": "3.7.3", "react": "18.3.1", "react-beautiful-dnd": "13.1.1", "react-dom": "18.2.0", "react-draggable": "4.4.6", "react-intl": "6.6.4", "react-layout-masonry": "1.1.0", "react-nanny": "2.11.0", "react-onclickoutside": "6.12.2", "react-redux": "9.1.2", "react-resize-detector": "9.1.0", "react-router-dom": "6.22.3", "react-select": "5.8.0", "react-text-transition": "^3.1.0", "react-to-print": "2.15.1", "react-tooltip": "4.1.2", "react-transition-group": "4.4.5", "redux": "5.0.1", "redux-sentry-middleware": "0.1.8", "redux-thunk": "3.1.0", "remove-accents": "0.4.2", "reselect": "5.1.0"}, "devDependencies": {"@graphql-codegen/add": "5.0.3", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/typescript": "4.0.6", "@graphql-codegen/typescript-operations": "4.2.0", "@graphql-codegen/typescript-resolvers": "4.0.6", "@graphql-codegen/typescript-rtk-query": "3.1.1", "@hudl/eslint-config": "workspace:*", "@hudl/playwright-config": "workspace:*", "@hudl/stylelint-config": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "@types/autosize": "3.0.7", "@types/draft-js": "0.11.4", "@types/invariant": "2.2.30", "@types/lodash": "4.14.149", "@types/lodash.throttle": "4.1.6", "@types/mousetrap": "1.6.8", "@types/node-fetch": "2.6.6", "@types/omit-deep-lodash": "1.1.1", "@types/prop-types": "15.7.14", "@types/rc-tooltip": "3.7.3", "@types/react-beautiful-dnd": "13.1.2", "@types/react-onclickoutside": "6.7.3", "@types/redux-mock-store": "1.0.6", "@vitest/coverage-v8": "3.2.4", "config": "workspace:*", "eslint": "8.45.0", "graphql-codegen-typescript-mock-data": "3.2.2", "jsdom": "24.0.0", "madge": "7.0.0", "node-fetch": "2.7.0", "playwright-shared": "workspace:*", "redux-mock-store": "1.5.4", "resize-observer-polyfill": "1.5.1", "stylelint": "16.13.0", "type-fest": "4.30.2", "vite": "5.4.7", "vitest-canvas-mock": "0.3.3", "vitest-fail-on-console": "0.7.0"}}