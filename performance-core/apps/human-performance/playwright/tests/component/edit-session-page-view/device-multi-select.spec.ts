import { Colors } from '../../../enums/enums';
import { expect, test } from '../../../fixtures/human-performance.fixture';

test.describe('edit session component - device multi-select', () => {
  test.beforeEach(async ({ editSessionPage }) => {
    await editSessionPage.goToAthleteDropdownMultipledevices();
  });

  test('devices are listed in descending order', async ({ editSessionPage }) => {
    await expect(editSessionPage.deviceId.nth(0)).toHaveText('20');
    await expect(editSessionPage.deviceId.nth(1)).toHaveText('5');
    await expect(editSessionPage.deviceId.nth(2)).toHaveText('1');
  });

  test('the input border sets the correct color based on interaction', async ({ editSessionPage }) => {
    await editSessionPage.athleteSelect.first().click();
    await editSessionPage.athleteList.getByText('#20 J. Doe').click();

    // expect blue border for focus input
    await expect(editSessionPage.athleteSelect.first()).toHaveBorderColor(Colors.Focused);
    await editSessionPage.clearFocus();

    // expect grey border for valid unfocused input
    await expect(editSessionPage.athleteSelect.first()).toHaveBorderColor(Colors.Default);

    await editSessionPage.athleteSelect.first().click();
    await editSessionPage.athleteList.getByText('#4 J. Bloggs').click();
    await editSessionPage.clearFocus();

    // expect red border for unfocused error input
    await expect(editSessionPage.athleteSelect.first()).toHaveBorderColor(Colors.Error);
  });
});
