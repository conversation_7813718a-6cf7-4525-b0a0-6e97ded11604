import { useCallback, useEffect, useMemo, useState } from 'react';

import { getCurrentTeamId } from '@hudl/performance-core-domain';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spinner, Tab, <PERSON>b<PERSON><PERSON>, <PERSON>b<PERSON>anel, Tabs, Text } from '@hudl/uniform-web';
import { Button, ButtonStatus } from '@hudl/uniform-web-button-legacy';
import { WebNav } from '@local/webnav';
import { format } from 'frontends-i18n';

import {
  AccelerationUnitOfMeasure,
  HumanPerformanceTeamProfileSettings,
  HumanPerformanceZone,
  SpeedUnitOfMeasure,
  UpdateTeamProfileSettingsInput, // UpdateTeamProfileSettingsInput,
  useWebHumanPerformanceGetTeamInfoR1Query,
  useWebHumanPerformanceHumanPerformanceTeamSettingsR1Query,
  useWebHumanPerformanceUpdateTeamProfileSettingsMutation,
} from '../../graphql/api.generated';
import { usePageEntityTracker } from '../../snowplow/SnowplowHooks';
import { MonitoringSettingsSource } from '../../utils/constants';
import { AccelerationSettings } from './MetricPages/AccelerationSettings';
import { HeartRateSettings } from './MetricPages/HeartRateSettings';
import { SpeedSettings } from './MetricPages/SpeedSettings';
import TeamSettingsModal from './Modals/TeamSettingsModal';
import TeamSettingInput from './TeamSettingInput/TeamSettingInput';
import { bulkConvertSettings } from './UnitConverter';

import styles from './styles.module.scss';

export default function TeamSettingsView() {
  const teamId = getCurrentTeamId();

  usePageEntityTracker('Zones & Thresholds');

  // Fetch team info
  const { data: teamInfo, isLoading: isLoadingTeamInfo } = useWebHumanPerformanceGetTeamInfoR1Query({
    teamId,
  });

  const encodedTeamId = teamInfo?.team?.id ?? '';

  // Fetch settings for the team, only when encodedTeamId is available
  const { data: settingsForTeam, isLoading: isLoadingSettingsForTeam } =
    useWebHumanPerformanceHumanPerformanceTeamSettingsR1Query(
      { teamId: encodedTeamId },
      { skip: !encodedTeamId || isLoadingTeamInfo }
    );

  const [updateSettingsMutation, { isLoading: isMutationLoading, isSuccess: isMutationSuccess }] =
    useWebHumanPerformanceUpdateTeamProfileSettingsMutation();

  // Page State
  const [sortedSettings, setSettings] = useState<HumanPerformanceTeamProfileSettings>();
  const [speedUnitOfMeasure, setSpeedUnitOfMeasure] = useState<SpeedUnitOfMeasure>();
  const [accelerationUnitOfMeasure, setAccelerationUnitOfMeasure] = useState<AccelerationUnitOfMeasure>();
  const [pendingChanges, setPendingChanges] = useState<boolean>(false);
  const [saveButtonStatus, setButtonStatus] = useState<ButtonStatus | undefined>(undefined);
  const [isError, setIsError] = useState<boolean>(false);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  useEffect(() => {
    if (!settingsForTeam || isLoadingSettingsForTeam) return;

    let hpSettings = settingsForTeam.humanPerformanceSettingsForTeam?.teamProfileSettings;

    if (!hpSettings) return;

    const teamSpeedUnit =
      settingsForTeam.humanPerformanceSettingsForTeam?.speedUnitOfMeasure ?? SpeedUnitOfMeasure.KilometersPerHour;
    const teamAccelerationUnit =
      settingsForTeam.humanPerformanceSettingsForTeam?.accelerationUnitOfMeasure ??
      AccelerationUnitOfMeasure.MetersPerSecondSquared;

    if (hpSettings && teamSpeedUnit !== SpeedUnitOfMeasure.KilometersPerHour) {
      hpSettings = bulkConvertSettings(
        hpSettings,
        'speed',
        teamSpeedUnit,
        ['highSpeedRunningAbs', 'sprintAbs', 'sprintMaxAbs'] as (keyof HumanPerformanceTeamProfileSettings)[],
        SpeedUnitOfMeasure.KilometersPerHour,
        1
      );
    }

    setSettings(hpSettings);
    setSpeedUnitOfMeasure(teamSpeedUnit);
    setAccelerationUnitOfMeasure(teamAccelerationUnit);

    const source = settingsForTeam.humanPerformanceSettingsForTeam?.monitorSettings?.source;
    if (source === MonitoringSettingsSource.SPRO) {
      setIsModalOpen(true);
    }
  }, [isLoadingSettingsForTeam, settingsForTeam]);

  useEffect(() => {
    setButtonStatus(isMutationLoading ? 'spinning' : undefined);
  }, [isMutationLoading, isMutationSuccess, pendingChanges]);

  const getFinalSettings = useCallback(() => {
    if (!sortedSettings) return;
    let settingsToSave = sortedSettings;

    // Convert speed settings back to KPH if neccessary
    if (speedUnitOfMeasure != SpeedUnitOfMeasure.KilometersPerHour) {
      settingsToSave = bulkConvertSettings(
        settingsToSave,
        'speed',
        SpeedUnitOfMeasure.KilometersPerHour,
        ['highSpeedRunningAbs', 'sprintAbs', 'sprintMaxAbs'] as (keyof HumanPerformanceTeamProfileSettings)[],
        speedUnitOfMeasure,
        1
      );
    }

    // Convert acceleration settings back to MPS2 if neccessary
    if (accelerationUnitOfMeasure != AccelerationUnitOfMeasure.MetersPerSecondSquared) {
      settingsToSave = bulkConvertSettings(
        settingsToSave,
        'acceleration',
        AccelerationUnitOfMeasure.MetersPerSecondSquared,
        ['highIntensityAccelerationAbs'] as (keyof HumanPerformanceTeamProfileSettings)[],
        accelerationUnitOfMeasure,
        1
      );
    }

    return settingsToSave;
  }, [accelerationUnitOfMeasure, sortedSettings, speedUnitOfMeasure]);

  const onSave = useCallback(() => {
    const settingsToSave = getFinalSettings();

    if (!settingsToSave) return;

    const orgId = teamInfo?.team?.organization?.id ?? '';
    const updateInput: UpdateTeamProfileSettingsInput = {
      ...settingsToSave,
      teamId: encodedTeamId,
      orgId,
      accelerationUnitOfMeasure: accelerationUnitOfMeasure ?? AccelerationUnitOfMeasure.MetersPerSecondSquared,
      speedUnitOfMeasure: speedUnitOfMeasure ?? SpeedUnitOfMeasure.KilometersPerHour,
    };

    updateSettingsMutation({ input: updateInput })
      .then(() => window.location.reload)
      .then(() => setPendingChanges(false));
  }, [
    accelerationUnitOfMeasure,
    encodedTeamId,
    getFinalSettings,
    speedUnitOfMeasure,
    teamInfo?.team?.organization?.id,
    updateSettingsMutation,
  ]);

  /* Update Settings Functions */
  const updateZone = (inputId: string, newValue: string, key: string) => {
    const [, zoneIndex, property] = inputId.split('-');

    setSettings((prevSettings) => {
      if (!prevSettings) {
        return;
      }

      const settingsKey = key as keyof HumanPerformanceTeamProfileSettings;
      const currentZones = [...(prevSettings[settingsKey] as HumanPerformanceZone[])];
      const zoneKey = Number(zoneIndex);
      const propertyKey = property as keyof HumanPerformanceZone;

      currentZones[zoneKey] = {
        ...currentZones[zoneKey],
        [propertyKey]: Number(newValue),
      };

      return {
        ...prevSettings,
        [settingsKey]: currentZones,
      };
    });

    setPendingChanges(true);
  };

  const updateThreshold = (inputId: string, newValue: string) => {
    setSettings((prevSettings) => {
      if (!prevSettings) return;
      return {
        ...prevSettings,
        [inputId as keyof HumanPerformanceTeamProfileSettings]: Number(newValue),
      };
    });

    setPendingChanges(true);
  };

  const updateSpeedUnitOfMeasure = (u: string) => {
    setSpeedUnitOfMeasure(u as SpeedUnitOfMeasure);
    setPendingChanges(true);
  };

  const updateAccelerationUnitOfMeasure = (u: string) => {
    setAccelerationUnitOfMeasure(u as AccelerationUnitOfMeasure);
    setPendingChanges(true);
  };

  /* Add/Delete Zone Functions */
  const addZone = (key: keyof HumanPerformanceTeamProfileSettings) => {
    setSettings((prevSettings) => {
      if (!prevSettings) {
        return;
      }

      // Make a shallow copy of the current `speedAbs` array
      const originalZones = (prevSettings[key] as HumanPerformanceZone[]) ?? [];
      const zonesToUpdate = [...originalZones];

      // Add the new zone to the copied array
      zonesToUpdate.push({
        entryValue: 0,
        exitValue: 0,
      });

      // Return the updated state with the new array
      return {
        ...prevSettings,
        [key]: zonesToUpdate,
      };
    });

    setPendingChanges(true);
  };

  const removeZone = (key: keyof HumanPerformanceTeamProfileSettings, index: number) => {
    setSettings((prevSettings) => {
      if (!prevSettings) {
        return;
      }

      const originalZones = (prevSettings[key] as HumanPerformanceZone[]) ?? [];
      const zonesToUpdate = [...originalZones];

      zonesToUpdate.splice(index, 1);

      return {
        ...prevSettings,
        [key]: zonesToUpdate,
      };
    });

    setPendingChanges(true);
  };

  /* Table Building Functions */
  const formatInputValue = (value: number): string => {
    return parseFloat(value.toFixed(1)).toString();
  };

  const buildZoneRows = useCallback(
    (
      key: keyof HumanPerformanceTeamProfileSettings,
      updateFunction: (inputId: string, newValue: string) => void,
      allowNegative: boolean = false
    ) => {
      const zones: HumanPerformanceZone[] = sortedSettings?.[key] as HumanPerformanceZone[];
      if (!zones) return;

      const zoneRows: Rows = zones.map((zone, i) => {
        const isUninitialized = zone.entryValue === 0 && zone.exitValue === 0;
        return {
          id: `zone-${i}-row`,
          data: [
            {
              value: `zone-${i}-name-col`,
              element: <span>{`Zone ${i + 1}`}</span>,
            },
            {
              value: `zone-${i}-entry-col`,
              element: (
                <TeamSettingInput
                  onChange={updateFunction}
                  inputId={`zone-${i}-entryValue`}
                  value={isUninitialized ? undefined : formatInputValue(zone.entryValue)}
                  onError={(e: boolean) => setIsError(e)}
                  allowNegative={allowNegative}
                  maxValue={isUninitialized ? undefined : zone.exitValue}
                  nextInputId={isUninitialized ? `zone-${i}-exitValue` : undefined}
                  openAutomatically={isUninitialized}
                />
              ),
            },
            {
              value: `zone-${i}-exit-col`,
              element: (
                <TeamSettingInput
                  onChange={updateFunction}
                  inputId={`zone-${i}-exitValue`}
                  value={isUninitialized ? undefined : formatInputValue(zone.exitValue)}
                  onError={(e: boolean) => setIsError(e)}
                  allowNegative={allowNegative}
                  minValue={isUninitialized ? undefined : zone.entryValue}
                />
              ),
            },
            {
              value: `zone-${i}-remove-col`,
              element: (
                <Button
                  buttonType="cancel"
                  size={'small'}
                  icon={<IconRemove size="small" />}
                  onClick={() => removeZone(key, i)}
                  isDisabled={zones.length <= 1}
                />
              ),
            },
          ],
        };
      });

      return zoneRows ?? [];
    },
    [sortedSettings]
  );

  const thresholdNameKeys: Record<string, string> = {
    ['highSpeedRunningAbs']: 'edit_team_settings.threshold_name_highSpeedRunningAbs',
    ['highSpeedRunningRel']: 'edit_team_settings.threshold_name_highSpeedRunningRel',
    ['sprintAbs']: 'edit_team_settings.threshold_name_sprintAbs',
    ['sprintRel']: 'edit_team_settings.threshold_name_sprintRel',
    ['sprintMaxAbs']: 'edit_team_settings.threshold_name_sprintMaxAbs',
    ['highIntensityHeartRateRel']: 'edit_team_settings.threshold_name_highIntensityHeartRateRel',
    ['highIntensityAccelerationAbs']: 'edit_team_settings.threshold_name_highIntensityAccelAbs',
  };

  const buildThresholdRows = (thresholds: Record<string, number>, allowNegative: boolean = false) => {
    return Object.entries(thresholds).map(([key, threshold]) => {
      return {
        id: `${key}-row`,
        data: [
          {
            value: `${key}-name-col`,
            element: <span>{format(thresholdNameKeys[key])}</span>,
          },
          {
            value: `${key}-value-col`,
            element: (
              <TeamSettingInput
                onChange={updateThreshold}
                inputId={key}
                onError={(e: boolean) => setIsError(e)}
                value={formatInputValue(threshold)}
                allowNegative={allowNegative}
              />
            ),
          },
        ],
      };
    });
  };

  const settingsValid = useMemo(() => {
    if (!sortedSettings) {
      return false;
    }
    const zoneGroups = [
      sortedSettings.speedAbs,
      sortedSettings.speedRel,
      sortedSettings.accelerationAbs,
      sortedSettings.heartRateRel,
    ];
    return zoneGroups.every((zoneGroup) => zoneGroup.every((row) => row.entryValue < row.exitValue));
  }, [sortedSettings]);

  /* Table Header Builders */
  const zoneHeaderWithSubtitle = (title: string, subTitle: string) => {
    return (
      <div className={styles.headerWithSubtitle}>
        <span>{title}</span>
        <Text level={'micro'}>{subTitle}</Text>
      </div>
    );
  };

  if (!sortedSettings) {
    return (
      <div className={styles.spinnerContainer}>
        <Spinner size="medium" />
      </div>
    );
  }

  if (!teamInfo || !encodedTeamId) {
    return <div>{format('edit_team_settings.team_info_error')}</div>;
  }

  if (!settingsForTeam) {
    return <div>{format('edit_team_settings.settings')}</div>;
  }

  return (
    <>
      <TeamSettingsModal isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} />
      <div className={styles.container}>
        <WebNav isVisible />
        <Button
          className={styles.saveButton}
          onClick={onSave}
          status={saveButtonStatus}
          isDisabled={!pendingChanges || isError || !settingsValid}
        >
          {format('edit_team_settings.save_button')}
        </Button>

        <Tabs orientation="vertical" className={styles.tabs}>
          <TabList>
            <Tab className={styles.tabListItem} id="0" key={'speed'}>
              <Text>{format('edit_team_settings.metric_name.speed')} </Text>
            </Tab>
            <Tab className={styles.tabListItem} id="1" key={'acceleration'}>
              <Text>{format('edit_team_settings.metric_name.acceleration')} </Text>
            </Tab>
            <Tab className={styles.tabListItem} id="2" key={'heartRate'}>
              <Text>{format('edit_team_settings.metric_name.heart_rate')} </Text>
            </Tab>
          </TabList>
          <TabPanel id="0" className={styles.tabPanel}>
            <SpeedSettings
              sortedSettings={sortedSettings}
              updateSettings={setSettings}
              unitOfMeasure={speedUnitOfMeasure}
              buildThresholdRows={buildThresholdRows}
              buildZoneRows={buildZoneRows}
              updateZone={updateZone}
              updateUnitOfMeasure={updateSpeedUnitOfMeasure}
              zoneHeaderWithSubtitle={zoneHeaderWithSubtitle}
              addZone={addZone}
            />
          </TabPanel>
          <TabPanel id="1" className={styles.tabPanel}>
            <AccelerationSettings
              sortedSettings={sortedSettings}
              unitOfMeasure={accelerationUnitOfMeasure}
              buildThresholdRows={buildThresholdRows}
              buildZoneRows={buildZoneRows}
              updateZone={updateZone}
              updateUnitOfMeasure={updateAccelerationUnitOfMeasure}
              addZone={addZone}
            />
          </TabPanel>
          <TabPanel id="2" className={styles.tabPanel}>
            <HeartRateSettings
              sortedSettings={sortedSettings}
              addZone={addZone}
              zoneHeaderWithSubtitle={zoneHeaderWithSubtitle}
              buildThresholdRows={buildThresholdRows}
              buildZoneRows={buildZoneRows}
              updateZone={updateZone}
            />
          </TabPanel>
        </Tabs>
      </div>
    </>
  );
}
