{
  "folders": [
    {
      "path": "./apps/watch",
    },
    {
      "path": "./packages",
    },
    {
      "path": "../shared/domain-types",
    },
    {
      "path": "../shared/details-module",
    },
    {
      "path": "../shared/analytics",
    },
    {
      "path": "../uniform",
    },
  ],
  "settings": {
    "turboRun": "~/.nvm/nvm-exec pnpm exec turbo",
    "turboFilters": "--filter=watch --filter=human-performance --filter=performance-core-domain --filter=performance-core-shared --filter=domain-types",
    "turboArguments": "--output-logs=new-only --token=yZ4v6S86hNDbYV",
    "git.openRepositoryInParentFolders": "always",
    "search.useParentIgnoreFiles": true,
    "[javascript][javascriptreact][json][jsonc][typescript][typescriptreact][yaml][html][markdown][mdx][scss][graphql]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
      "editor.formatOnSave": true,
    },
    "editor.codeActionsOnSave": {
      "source.organizeImports": "never",
    },
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Performance Core build",
        "detail": "Build all Performance Core projects",
        "type": "shell",
        "command": "${config:turboRun} build ${config:turboFilters} ${config:turboArguments}",
        "options": {
          "cwd": "${workspaceFolder}/..",
        },
        "problemMatcher": [],
      },
      {
        "label": "Performance Core clean",
        "detail": "Cleans all Performance Core projects",
        "type": "shell",
        "command": "${config:turboRun} clean ${config:turboFilters}",
        "options": {
          "cwd": "${workspaceFolder}/..",
        },
        "problemMatcher": [],
      },
      {
        "label": "Performance Core dev",
        "detail": "Cleans all Performance Core projects",
        "type": "shell",
        "command": "${config:turboRun} dev ${config:turboFilters}",
        "options": {
          "cwd": "${workspaceFolder}/..",
        },
        "problemMatcher": [],
      },
      {
        "label": "Performance Core lint",
        "detail": "Lint all Performance Core projects",
        "type": "shell",
        "command": "${config:turboRun} lint ${config:turboFilters} ${config:turboArguments}",
        "options": {
          "cwd": "${workspaceFolder}/..",
        },
        "problemMatcher": [],
      },
      {
        "label": "Performance Core nuke",
        "detail": "Nukes all Performance Core projects",
        "type": "shell",
        "command": "${config:turboRun} nuke ${config:turboFilters}",
        "options": {
          "cwd": "${workspaceFolder}/..",
        },
        "problemMatcher": [],
      },
      {
        "label": "Performance Core test",
        "detail": "Tests all Performance Core projects",
        "type": "shell",
        "command": "${config:turboRun} test ${config:turboFilters} ${config:turboArguments} -- --watch=false",
        "options": {
          "cwd": "${workspaceFolder}/..",
        },
        "problemMatcher": [],
      },
      {
        "label": "Performance Core Storybooks",
        "detail": "Run Storybook for all Performance Core projects",
        "type": "shell",
        "command": "${config:turboRun} storybook ${config:turboFilters}",
        "options": {
          "cwd": "${workspaceFolder}/..",
        },
        "problemMatcher": [],
      },
    ],
  },
}
