{"version": 1.1, "pre-load-external-sets": [], "sets": {"hudl-performance-core-domain": {"keys": ["module.filters.ice-hockey.all-even-strength", "module.filters.ice-hockey.all-faceoffs", "module.filters.ice-hockey.all-infractions", "module.filters.ice-hockey.all-penalties", "module.filters.ice-hockey.all-periods", "module.filters.ice-hockey.all-power-plays", "module.filters.ice-hockey.all-shots", "module.filters.ice-hockey.blocked", "module.filters.ice-hockey.blocks", "module.filters.ice-hockey.defense", "module.filters.ice-hockey.defensive", "module.filters.ice-hockey.empty-net", "module.filters.ice-hockey.failed-power-plays", "module.filters.ice-hockey.goals", "module.filters.ice-hockey.icing", "module.filters.ice-hockey.major", "module.filters.ice-hockey.match", "module.filters.ice-hockey.minor", "module.filters.ice-hockey.misconduct", "module.filters.ice-hockey.missed", "module.filters.ice-hockey.neutral", "module.filters.ice-hockey.offensive", "module.filters.ice-hockey.offsides", "module.filters.ice-hockey.period-1", "module.filters.ice-hockey.period-2", "module.filters.ice-hockey.period-3", "module.filters.ice-hockey.overtime", "module.filters.ice-hockey.saved", "module.filters.ice-hockey.saves", "module.filters.ice-hockey.successful-power-plays", "module.filters.ice-hockey.team-strengths", "module.filters.ice-hockey.zones", "module.filters.shared.additional-tags", "module.filters.shared.custom", "module.insights.wrestling_athletes_group_card_title", "module.insights.wrestling_team_card_title", "module.insights.wrestling_wrestlers_card_title", "module.insights.wrestling_situations_group_card_title", "module.insights.wrestling_period_card_title", "module.insights.wrestling_position_card_title", "module.insights.wrestling_firstTakedown_card_title", "module.insights.wrestling_results_group_card_title", "module.insights.wrestling_pointType_card_title", "module.insights.wrestling_penaltyType_card_title", "module.insights.wrestling_stoppageType_card_title", "module.insights.wrestling_pointsPerPeriod_card_title", "module.insights.wrestling_win_loss_group_card_title", "module.insights.wrestling_win_loss_card_title", "module.insights.wrestling_win_by_card_title", "module.insights.wrestling_loss_by_card_title", "module.insights.periods.halfNumber", "module.insights.periods.thirdNumber", "module.insights.periods.quarterNumber", "module.insights.periods.overtimeNumber", "module.insights.periods.overtime", "module.insights.periods.tieBreaker", "module.insights.periods.ultimateTiebreaker", "module.insights.baseballSoftball.card.pitchedBy.title", "module.insights.baseballSoftball.card.plateAppearanceBy.title", "module.insights.baseballSoftball.card.phase.title", "module.insights.baseballSoftball.card.pitchType.title", "module.insights.baseballSoftball.card.inning.title", "module.insights.baseballSoftball.card.count.title", "module.insights.baseballSoftball.card.pitchResult.title", "module.insights.baseballSoftball.card.runnersOnBase.title", "module.insights.baseballSoftball.card.battingResult.title", "module.insights.baseballSoftball.card.outs.title", "module.insights.baseballSoftball.card.hitType.title", "module.insights.baseballSoftball.card.outsAt.title", "module.insights.baseballSoftball.card.playType.title", "module.insights.baseballSoftball.card.hitLocation.title", "module.insights.baseballSoftball.card.swingType.title", "module.insights.baseballSoftball.card.batters.title", "module.insights.baseballSoftball.card.pitchers.title", "module.insights.baseballSoftball.cardGroup.pitching.title", "module.insights.baseballSoftball.cardGroup.batting.title", "module.insights.baseballSoftball.cardGroup.situations.title", "module.insights.baseballSoftball.cardGroup.ungrouped.title", "module.insights.baseballSoftball.statDefinition.pitchedBy.friendlyName", "module.insights.baseballSoftball.statDefinition.plateAppearanceBy.friendlyName", "module.insights.baseballSoftball.statDefinition.phase.friendlyName", "module.insights.baseballSoftball.statDefinition.phase.batting", "module.insights.baseballSoftball.statDefinition.phase.pitching", "module.insights.baseballSoftball.statDefinition.pitchType.friendlyName", "module.insights.baseballSoftball.statDefinition.inning.friendlyName", "module.insights.baseballSoftball.statDefinition.balls.friendlyName", "module.insights.baseballSoftball.statDefinition.strikes.friendlyName", "module.insights.baseballSoftball.statDefinition.pitchResult.friendlyName", "module.insights.baseballSoftball.statDefinition.occupiedBases.friendlyName", "module.insights.baseballSoftball.statDefinition.battingResult.friendlyName", "module.insights.baseballSoftball.statDefinition.outs.friendlyName", "module.insights.baseballSoftball.statDefinition.hitType.friendlyName", "module.insights.baseballSoftball.statDefinition.outsAt.friendlyName", "module.insights.baseballSoftball.statDefinition.playType.friendlyName", "module.insights.baseballSoftball.statDefinition.hitLocationZone.friendlyName", "module.insights.baseballSoftball.statDefinition.swingType.friendlyName", "module.insights.baseballSoftball.statDefinition.atBats.friendlyName", "module.insights.baseballSoftball.statDefinition.atBats.description", "module.insights.baseballSoftball.statDefinition.hits.friendlyName", "module.insights.baseballSoftball.statDefinition.hits.description", "module.insights.baseballSoftball.statDefinition.plateAppearances.friendlyName", "module.insights.baseballSoftball.statDefinition.plateAppearances.description", "module.insights.baseballSoftball.statDefinition.basesOnBalls.friendlyName", "module.insights.baseballSoftball.statDefinition.basesOnBalls.description", "module.insights.baseballSoftball.statDefinition.strikeouts.friendlyName", "module.insights.baseballSoftball.statDefinition.strikeouts.description", "module.insights.baseballSoftball.statDefinition.battingAverage.friendlyName", "module.insights.baseballSoftball.statDefinition.battingAverage.description", "module.insights.baseballSoftball.statDefinition.battingAverage.formula.numerator", "module.insights.baseballSoftball.statDefinition.battingAverage.formula.denominator", "module.insights.baseballSoftball.statDefinition.onBasePercentage.friendlyName", "module.insights.baseballSoftball.statDefinition.onBasePercentage.description", "module.insights.baseballSoftball.statDefinition.onBasePercentage.formula.numerator", "module.insights.baseballSoftball.statDefinition.onBasePercentage.formula.denominator", "module.insights.baseballSoftball.statDefinition.sluggingPercentage.friendlyName", "module.insights.baseballSoftball.statDefinition.sluggingPercentage.description", "module.insights.baseballSoftball.statDefinition.sluggingPercentage.formula.numerator", "module.insights.baseballSoftball.statDefinition.sluggingPercentage.formula.denominator", "module.insights.baseballSoftball.statDefinition.basesOnBallsThrown.friendlyName", "module.insights.baseballSoftball.statDefinition.basesOnBallsThrown.description", "module.insights.baseballSoftball.statDefinition.strikeoutsThrown.friendlyName", "module.insights.baseballSoftball.statDefinition.strikeoutsThrown.description", "module.insights.baseballSoftball.statDefinition.runsAllowed.friendlyName", "module.insights.baseballSoftball.statDefinition.runsAllowed.description", "module.insights.baseballSoftball.statDefinition.totalPitchesThrown.friendlyName", "module.insights.baseballSoftball.statDefinition.totalPitchesThrown.description", "module.insights.baseballSoftball.statDefinition.hitsAllowed.friendlyName", "module.insights.baseballSoftball.statDefinition.hitsAllowed.description", "module.insights.baseballSoftball.statDefinition.inningsPitched.friendlyName", "module.insights.baseballSoftball.statDefinition.inningsPitched.description", "module.insights.baseballSoftball.statDefinition.inningsPitched.formula.numerator", "module.insights.baseballSoftball.statDefinition.inningsPitched.formula.denominator", "module.insights.baseballSoftball.statDefinition.pitchingGamesPlayed.friendlyName", "module.insights.baseballSoftball.statDefinition.pitchingGamesPlayed.description", "module.insights.baseballSoftball.statDefinition.singles.friendlyName", "module.insights.baseballSoftball.statDefinition.singles.description", "module.insights.baseballSoftball.statDefinition.doubles.friendlyName", "module.insights.baseballSoftball.statDefinition.doubles.description", "module.insights.baseballSoftball.statDefinition.triples.friendlyName", "module.insights.baseballSoftball.statDefinition.triples.description", "module.insights.baseballSoftball.statDefinition.homeRuns.friendlyName", "module.insights.baseballSoftball.statDefinition.homeRuns.description", "module.insights.baseballSoftball.statDefinition.onBasePlusSluggingPercentage.friendlyName", "module.insights.baseballSoftball.statDefinition.onBasePlusSluggingPercentage.description", "module.insights.baseballSoftball.statDefinition.onBasePlusSluggingPercentage.formula", "module.insights.baseballSoftball.statDefinition.pitchesSeenPerPlateAppearance.friendlyName", "module.insights.baseballSoftball.statDefinition.pitchesSeenPerPlateAppearance.description", "module.insights.baseballSoftball.statDefinition.pitchesSeenPerPlateAppearance.formula.numerator", "module.insights.baseballSoftball.statDefinition.pitchesSeenPerPlateAppearance.formula.denominator", "module.insights.baseballSoftball.statDefinition.pitchingGamesStarted.friendlyName", "module.insights.baseballSoftball.statDefinition.pitchingGamesStarted.description", "module.insights.baseballSoftball.statDefinition.battingAverageAllowed.friendlyName", "module.insights.baseballSoftball.statDefinition.battingAverageAllowed.description", "module.insights.baseballSoftball.statDefinition.battingAverageAllowed.formula.numerator", "module.insights.baseballSoftball.statDefinition.battingAverageAllowed.formula.denominator", "module.insights.baseballSoftball.statDefinition.walksPlusHitsPerInningsPitched.friendlyName", "module.insights.baseballSoftball.statDefinition.walksPlusHitsPerInningsPitched.description", "module.insights.baseballSoftball.statDefinition.walksPlusHitsPerInningsPitched.formula.numerator", "module.insights.baseballSoftball.statDefinition.walksPlusHitsPerInningsPitched.formula.denominator", "module.insights.baseballSoftball.statDefinition.strikePercentage.friendlyName", "module.insights.baseballSoftball.statDefinition.strikePercentage.description", "module.insights.baseballSoftball.statDefinition.pitchesPerInningPitched.friendlyName", "module.insights.baseballSoftball.statDefinition.pitchesPerInningPitched.description", "module.insights.baseballSoftball.statDefinition.pitchesPerInningPitched.formula.numerator", "module.insights.baseballSoftball.statDefinition.pitchesPerInningPitched.formula.denominator", "module.insights.baseballSoftball.statDefinition.pitchesPerBatterFaced.friendlyName", "module.insights.baseballSoftball.statDefinition.pitchesPerBatterFaced.description", "module.insights.baseballSoftball.statDefinition.pitchesPerBatterFaced.formula.numerator", "module.insights.baseballSoftball.statDefinition.pitchesPerBatterFaced.formula.denominator", "module.insights.baseballSoftball.statDefinition.firstPitchStrikePercentage.friendlyName", "module.insights.baseballSoftball.statDefinition.firstPitchStrikePercentage.description", "module.insights.baseballSoftball.statDefinition.stolenBasesAllowed.friendlyName", "module.insights.baseballSoftball.statDefinition.stolenBasesAllowed.description", "module.insights.baseballSoftball.statDefinition.runnersCaughtStealing.friendlyName", "module.insights.baseballSoftball.statDefinition.runnersCaughtStealing.description", "module.insights.baseballSoftball.statDefinition.stolenBasesAllowedPercentage.friendlyName", "module.insights.baseballSoftball.statDefinition.stolenBasesAllowedPercentage.description", "module.timeline.baseballSoftball.occupiedBases.loaded", "module.timeline.baseballSoftball.occupiedBases.none", "module.timeline.baseballSoftball.occupiedBases.firstOnly", "module.timeline.baseballSoftball.occupiedBases.secondOnly", "module.timeline.baseballSoftball.occupiedBases.thirdOnly", "module.timeline.baseballSoftball.occupiedBases.firstAndSecond", "module.timeline.baseballSoftball.occupiedBases.firstAndThird", "module.timeline.baseballSoftball.occupiedBases.secondAndThird", "module.timeline.baseballSoftball.occupiedBases.loaded", "module.timeline.soccer.team1", "module.timeline.soccer.team2", "module.timeline.baseballSoftball.outs", "module.timeline.moments", "module.timeline.ice-hockey.strength", "module.library.libraryType.file.label", "module.library.libraryType.shareable.label", "module.library.libraryType.suggestion.label", "module.library.libraryType.humanPerformanceSession.label", "module.library.libraryVideoType.game.label", "module.library.libraryVideoType.wrestling.game.label", "module.library.libraryVideoType.scout.label", "module.library.libraryVideoType.practice.label", "module.library.libraryVideoType.endOfSeason.label", "module.library.libraryVideoType.clinic.label", "module.library.libraryVideoType.misc.label", "module.library.libraryVideoType.playlist.label", "module.grid.tabs.soccer.sequence", "module.grid.tabs.soccer.moment", "module.grid.tabs.volleyball.contact", "module.grid.tabs.volleyball.rally"]}}, "base-language": {"module.filters.ice-hockey.all-even-strength": "All Even Strength", "module.filters.ice-hockey.all-faceoffs": "All Faceoffs", "module.filters.ice-hockey.all-infractions": "All Infractions", "module.filters.ice-hockey.all-penalties": "All Penalties", "module.filters.ice-hockey.all-periods": "All Periods", "module.filters.ice-hockey.all-power-plays": "All Power Plays", "module.filters.ice-hockey.all-shots": "All Shots", "module.filters.ice-hockey.blocked": "Blocked", "module.filters.ice-hockey.blocks": "Blocks", "module.filters.ice-hockey.defense": "Defense", "module.filters.ice-hockey.defensive": "Defensive", "module.filters.ice-hockey.empty-net": "Empty Net", "module.filters.ice-hockey.failed-power-plays": "Failed Power Plays", "module.filters.ice-hockey.goals": "Goals", "module.filters.ice-hockey.icing": "Icing", "module.filters.ice-hockey.major": "Major", "module.filters.ice-hockey.match": "Match", "module.filters.ice-hockey.minor": "Minor", "module.filters.ice-hockey.misconduct": "Misconduct", "module.filters.ice-hockey.missed": "Missed", "module.filters.ice-hockey.neutral": "Neutral", "module.filters.ice-hockey.offensive": "Offensive", "module.filters.ice-hockey.offsides": "Offsides", "module.filters.ice-hockey.period-1": "P1", "module.filters.ice-hockey.period-2": "P2", "module.filters.ice-hockey.period-3": "P3", "module.filters.ice-hockey.overtime": "OT", "module.filters.ice-hockey.saved": "Saved", "module.filters.ice-hockey.saves": "Saves", "module.filters.ice-hockey.successful-power-plays": "Successful Power Plays", "module.filters.ice-hockey.team-strengths": "{teamAStrength}v{teamBStrength}", "module.filters.ice-hockey.zones": "Zones", "module.filters.shared.additional-tags": "Additional Tags", "module.filters.shared.custom": "Custom", "module.insights.wrestling_athletes_group_card_title": "Athletes", "module.insights.wrestling_team_card_title": "Team", "module.insights.wrestling_wrestlers_card_title": "Wrestlers", "module.insights.wrestling_situations_group_card_title": "Situations", "module.insights.wrestling_period_card_title": "Period", "module.insights.wrestling_position_card_title": "Position", "module.insights.wrestling_firstTakedown_card_title": "First Takedown", "module.insights.wrestling_results_group_card_title": "Results", "module.insights.wrestling_pointType_card_title": "Point Scored By", "module.insights.wrestling_penaltyType_card_title": "Penalty Type", "module.insights.wrestling_stoppageType_card_title": "Stoppage Type", "module.insights.wrestling_pointsPerPeriod_card_title": "Points Per Period", "module.insights.wrestling_win_loss_group_card_title": "Win / Loss", "module.insights.wrestling_win_loss_card_title": "Win / Loss", "module.insights.wrestling_win_by_card_title": "Win By", "module.insights.wrestling_loss_by_card_title": "Loss By", "module.insights.periods.halfNumber": "H{number}", "module.insights.periods.thirdNumber": "{number}", "module.insights.periods.quarterNumber": "Q{number}", "module.insights.periods.overtimeNumber": "O{number}", "module.insights.periods.overtime": "OT", "module.insights.periods.tieBreaker": "TB{number}", "module.insights.periods.ultimateTiebreaker": "UTB", "module.insights.baseballSoftball.card.pitchedBy.title": "Pitched By", "module.insights.baseballSoftball.card.plateAppearanceBy.title": "Plate Appearance By", "module.insights.baseballSoftball.card.phase.title": "Phase", "module.insights.baseballSoftball.card.pitchType.title": "Pitch Type", "module.insights.baseballSoftball.card.inning.title": "Inning", "module.insights.baseballSoftball.card.count.title": "Count", "module.insights.baseballSoftball.card.pitchResult.title": "<PERSON><PERSON> Result", "module.insights.baseballSoftball.card.runnersOnBase.title": "Runners On Base", "module.insights.baseballSoftball.card.battingResult.title": "Batting Result", "module.insights.baseballSoftball.card.outs.title": "Outs", "module.insights.baseballSoftball.card.hitType.title": "Hit Type", "module.insights.baseballSoftball.card.outsAt.title": "Outs At", "module.insights.baseballSoftball.card.playType.title": "Play Type", "module.insights.baseballSoftball.card.hitLocation.title": "Hit Location", "module.insights.baseballSoftball.card.swingType.title": "Swing Type", "module.insights.baseballSoftball.card.batters.title": "Bat<PERSON>", "module.insights.baseballSoftball.card.pitchers.title": "Pitchers", "module.insights.baseballSoftball.cardGroup.pitching.title": "Pitching", "module.insights.baseballSoftball.cardGroup.batting.title": "Batting", "module.insights.baseballSoftball.cardGroup.situations.title": "Situations", "module.insights.baseballSoftball.cardGroup.ungrouped.title": "Ungrouped Stats", "module.insights.baseballSoftball.statDefinition.pitchedBy.friendlyName": "Pitched By", "module.insights.baseballSoftball.statDefinition.plateAppearanceBy.friendlyName": "Plate Appearance By", "module.insights.baseballSoftball.statDefinition.phase.friendlyName": "Phase", "module.insights.baseballSoftball.statDefinition.phase.batting": "Batting", "module.insights.baseballSoftball.statDefinition.phase.pitching": "Pitching", "module.insights.baseballSoftball.statDefinition.pitchType.friendlyName": "Pitch Type", "module.insights.baseballSoftball.statDefinition.inning.friendlyName": "Inning", "module.insights.baseballSoftball.statDefinition.balls.friendlyName": "Balls", "module.insights.baseballSoftball.statDefinition.strikes.friendlyName": "Strikes", "module.insights.baseballSoftball.statDefinition.pitchResult.friendlyName": "<PERSON><PERSON> Result", "module.insights.baseballSoftball.statDefinition.occupiedBases.friendlyName": "Occupied Bases", "module.insights.baseballSoftball.statDefinition.battingResult.friendlyName": "Batting Result", "module.insights.baseballSoftball.statDefinition.outs.friendlyName": "Outs", "module.insights.baseballSoftball.statDefinition.hitType.friendlyName": "Hit Type", "module.insights.baseballSoftball.statDefinition.outsAt.friendlyName": "Outs At", "module.insights.baseballSoftball.statDefinition.playType.friendlyName": "Play Type", "module.insights.baseballSoftball.statDefinition.hitLocationZone.friendlyName": "Hit Location Zone", "module.insights.baseballSoftball.statDefinition.swingType.friendlyName": "Swing Type", "module.insights.baseballSoftball.statDefinition.atBats.friendlyName": "AB", "module.insights.baseballSoftball.statDefinition.atBats.description": "The total number of <b>at-bats.</b>", "module.insights.baseballSoftball.statDefinition.hits.friendlyName": "H", "module.insights.baseballSoftball.statDefinition.hits.description": "The total number of <b>hits.</b>", "module.insights.baseballSoftball.statDefinition.plateAppearances.friendlyName": "PA", "module.insights.baseballSoftball.statDefinition.plateAppearances.description": "The total number of <b>plate appearances.</b>", "module.insights.baseballSoftball.statDefinition.basesOnBalls.friendlyName": "BB", "module.insights.baseballSoftball.statDefinition.basesOnBalls.description": "<b>Base on balls:</b> the total number of walks.", "module.insights.baseballSoftball.statDefinition.strikeouts.friendlyName": "K", "module.insights.baseballSoftball.statDefinition.battingAverage.friendlyName": "Avg", "module.insights.baseballSoftball.statDefinition.battingAverage.description": "<b>Batting average:</b> hits divided by at-bats.", "module.insights.baseballSoftball.statDefinition.battingAverage.formula.numerator": "H", "module.insights.baseballSoftball.statDefinition.battingAverage.formula.denominator": "AB", "module.insights.baseballSoftball.statDefinition.onBasePercentage.friendlyName": "OBP", "module.insights.baseballSoftball.statDefinition.onBasePercentage.description": "<b>On-base percentage:</b> the sum of hits, walks, and hit by pitch divided by plate appearances.", "module.insights.baseballSoftball.statDefinition.onBasePercentage.formula.numerator": "H + BB + Hit by <PERSON>ch", "module.insights.baseballSoftball.statDefinition.onBasePercentage.formula.denominator": "PA", "module.insights.baseballSoftball.statDefinition.sluggingPercentage.friendlyName": "SLG", "module.insights.baseballSoftball.statDefinition.sluggingPercentage.description": "<b>Slugging percentage:</b> the total bases divided by at-bats.", "module.insights.baseballSoftball.statDefinition.sluggingPercentage.formula.numerator": "Total Bases", "module.insights.baseballSoftball.statDefinition.sluggingPercentage.formula.denominator": "AB", "module.insights.baseballSoftball.statDefinition.basesOnBallsThrown.friendlyName": "BB", "module.insights.baseballSoftball.statDefinition.basesOnBallsThrown.description": "<b>Base on balls:</b> the total number of walks.", "module.insights.baseballSoftball.statDefinition.strikeoutsThrown.friendlyName": "K", "module.insights.baseballSoftball.statDefinition.strikeoutsThrown.description": "The total number of <b>strikeouts.</b>", "module.insights.baseballSoftball.statDefinition.strikeouts.description": "The total number of <b>strikeouts.</b>", "module.insights.baseballSoftball.statDefinition.runsAllowed.friendlyName": "R", "module.insights.baseballSoftball.statDefinition.runsAllowed.description": "The total number of <b>runs allowed.</b>", "module.insights.baseballSoftball.statDefinition.totalPitchesThrown.friendlyName": "#P", "module.insights.baseballSoftball.statDefinition.totalPitchesThrown.description": "The number of <b>total pitches</b> thrown.", "module.insights.baseballSoftball.statDefinition.hitsAllowed.friendlyName": "H", "module.insights.baseballSoftball.statDefinition.hitsAllowed.description": "The total number of <b>hits allowed.</b>", "module.insights.baseballSoftball.statDefinition.inningsPitched.friendlyName": "IP", "module.insights.baseballSoftball.statDefinition.inningsPitched.description": "<b>Inn<PERSON> pitched:</b> the total outs recorded divided by 3.", "module.insights.baseballSoftball.statDefinition.inningsPitched.formula.numerator": "Total Outs Recorded", "module.insights.baseballSoftball.statDefinition.inningsPitched.formula.denominator": "3", "module.insights.baseballSoftball.statDefinition.pitchingGamesPlayed.friendlyName": "GP", "module.insights.baseballSoftball.statDefinition.pitchingGamesPlayed.description": "<b>Games pitched:</b> the total games played.", "module.insights.baseballSoftball.statDefinition.singles.friendlyName": "1B", "module.insights.baseballSoftball.statDefinition.singles.description": "The total number of <b>singles.</b>", "module.insights.baseballSoftball.statDefinition.doubles.friendlyName": "2B", "module.insights.baseballSoftball.statDefinition.doubles.description": "The total number of <b>doubles.</b>", "module.insights.baseballSoftball.statDefinition.triples.friendlyName": "3B", "module.insights.baseballSoftball.statDefinition.triples.description": "The total number of <b>triples.</b>", "module.insights.baseballSoftball.statDefinition.homeRuns.friendlyName": "HR", "module.insights.baseballSoftball.statDefinition.homeRuns.description": "The total number of <b>home runs.</b>", "module.insights.baseballSoftball.statDefinition.onBasePlusSluggingPercentage.friendlyName": "OPS", "module.insights.baseballSoftball.statDefinition.onBasePlusSluggingPercentage.description": "<b>On-base slugging percentage:</b> the sum of on-base percentage and slugging percentage.", "module.insights.baseballSoftball.statDefinition.onBasePlusSluggingPercentage.formula": "OBP + SLG", "module.insights.baseballSoftball.statDefinition.pitchesSeenPerPlateAppearance.friendlyName": "PS/PA", "module.insights.baseballSoftball.statDefinition.pitchesSeenPerPlateAppearance.description": "<b>Pitches seen per plate appearance:</b> the total number pitches seen divided by total plate appearances.", "module.insights.baseballSoftball.statDefinition.pitchesSeenPerPlateAppearance.formula.numerator": "#P", "module.insights.baseballSoftball.statDefinition.pitchesSeenPerPlateAppearance.formula.denominator": "PA", "module.insights.baseballSoftball.statDefinition.pitchingGamesStarted.friendlyName": "GS", "module.insights.baseballSoftball.statDefinition.pitchingGamesStarted.description": "The total number of <b>games started.</b>", "module.insights.baseballSoftball.statDefinition.battingAverageAllowed.friendlyName": "BAA", "module.insights.baseballSoftball.statDefinition.battingAverageAllowed.description": "<b>Opponent batting average:</b> the hits allowed divided by at-bats faced.", "module.insights.baseballSoftball.statDefinition.battingAverageAllowed.formula.numerator": "H", "module.insights.baseballSoftball.statDefinition.battingAverageAllowed.formula.denominator": "AB", "module.insights.baseballSoftball.statDefinition.walksPlusHitsPerInningsPitched.friendlyName": "WHIP", "module.insights.baseballSoftball.statDefinition.walksPlusHitsPerInningsPitched.description": "<b>Walks plus hits per innings pitched:</b> the sum of walks and hits divided by innings pitched.", "module.insights.baseballSoftball.statDefinition.walksPlusHitsPerInningsPitched.formula.numerator": "BB + H", "module.insights.baseballSoftball.statDefinition.walksPlusHitsPerInningsPitched.formula.denominator": "IP", "module.insights.baseballSoftball.statDefinition.strikePercentage.friendlyName": "S%", "module.insights.baseballSoftball.statDefinition.strikePercentage.description": "<b>Strike percentage:</b> the total number of strikes thrown.", "module.insights.baseballSoftball.statDefinition.pitchesPerInningPitched.friendlyName": "P/IP", "module.insights.baseballSoftball.statDefinition.pitchesPerInningPitched.description": "<b>Pitches per inning:</b> the total pitches divided by innings pitched.", "module.insights.baseballSoftball.statDefinition.pitchesPerInningPitched.formula.numerator": "#P", "module.insights.baseballSoftball.statDefinition.pitchesPerInningPitched.formula.denominator": "IP", "module.insights.baseballSoftball.statDefinition.pitchesPerBatterFaced.friendlyName": "P/BF", "module.insights.baseballSoftball.statDefinition.pitchesPerBatterFaced.description": "<b>Pitches per batter faced:</b> the total pitches divided by total batters faced.", "module.insights.baseballSoftball.statDefinition.pitchesPerBatterFaced.formula.numerator": "#P", "module.insights.baseballSoftball.statDefinition.pitchesPerBatterFaced.formula.denominator": "Total Batters Faced", "module.insights.baseballSoftball.statDefinition.firstPitchStrikePercentage.friendlyName": "FPS%", "module.insights.baseballSoftball.statDefinition.firstPitchStrikePercentage.description": "<b>First pitch strike percentage:</b> the total number of first pitch strikes.", "module.insights.baseballSoftball.statDefinition.stolenBasesAllowed.friendlyName": "SB", "module.insights.baseballSoftball.statDefinition.stolenBasesAllowed.description": "The total number of <b>stolen bases allowed.</b>", "module.insights.baseballSoftball.statDefinition.runnersCaughtStealing.friendlyName": "CS", "module.insights.baseballSoftball.statDefinition.runnersCaughtStealing.description": "The total number of <b>runners caught stealing.</b>", "module.insights.baseballSoftball.statDefinition.stolenBasesAllowedPercentage.friendlyName": "SB%", "module.insights.baseballSoftball.statDefinition.stolenBasesAllowedPercentage.description": "<b>Stolen bases allowed percentage:</b> the total percentage of stolen bases allowed.", "module.timeline.baseballSoftball.occupiedBases.none": "On Base: None", "module.timeline.baseballSoftball.occupiedBases.firstOnly": "On Base: 1st", "module.timeline.baseballSoftball.occupiedBases.secondOnly": "On Base: 2nd", "module.timeline.baseballSoftball.occupiedBases.thirdOnly": "On Base: 3rd", "module.timeline.baseballSoftball.occupiedBases.firstAndSecond": "On Base: 1st, 2nd", "module.timeline.baseballSoftball.occupiedBases.firstAndThird": "On Base: 1st, 3rd", "module.timeline.baseballSoftball.occupiedBases.secondAndThird": "On Base: 2nd, 3rd", "module.timeline.baseballSoftball.occupiedBases.loaded": "On Base: 1st, 2nd, 3rd", "module.timeline.soccer.team1": "Team 1", "module.timeline.soccer.team2": "Team 2", "module.timeline.baseballSoftball.outs": "{outs} {outs, plural, one {Out} other {Outs}}", "module.timeline.moments": "Moments", "module.timeline.ice-hockey.strength": "Strength", "module.library.libraryType.file.label": "File", "module.library.libraryType.shareable.label": "Saved View", "module.library.libraryType.suggestion.label": "Suggestion", "module.library.libraryType.humanPerformanceSession.label": "Physical Data", "module.library.libraryVideoType.game.label": "Game", "module.library.libraryVideoType.wrestling.game.label": "Match", "module.library.libraryVideoType.scout.label": "Scout", "module.library.libraryVideoType.practice.label": "Practice", "module.library.libraryVideoType.endOfSeason.label": "End", "module.library.libraryVideoType.clinic.label": "<PERSON><PERSON>", "module.library.libraryVideoType.misc.label": "Misc", "module.library.libraryVideoType.playlist.label": "Playlist", "module.grid.tabs.soccer.sequence": "{items} {items, plural, one {Sequence} other {Sequences}}", "module.grid.tabs.soccer.moment": "{items} {items, plural, one {Moment} other {Moments}}", "module.grid.tabs.volleyball.contact": "{items} {items, plural, one {Contact} other {Contacts}}", "module.grid.tabs.volleyball.rally": "{items} {items, plural, one {Rally} other {Rallies}}"}}