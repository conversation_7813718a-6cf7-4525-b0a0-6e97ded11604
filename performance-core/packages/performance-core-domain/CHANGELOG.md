# Changelog

## [12.86.0]

- Updates soccerDataChip for readability
- I18n Unknown player

## [12.85.3]

- Adds `getFilteredPointsForV3` which gets the original moment location coordinates for soccer.

## [12.85.2]

- Adds `isSoccerShotChart` check to StatsEngine.

## [12.85.1]

- Adds `InsideBox` and `OutsideBox` stats to soccer

## [12.85.0]

- Adds new `statDefinitions` for Ice Hockey filtering

## [12.84.0]

- Adds `allBallOuts` stat in `createSoccerStats`.
- Add Goalkeeping Throws, Goalkeeping Kicks, and Set Piece Throw Ins to `allReceptions` stat in `createSoccerStats`.

## [12.83.2]

- Adds `Custom` to `IceHockeyTimelineTrackMomentTypes`
- Fixing a couple missed `undefined` checks that would throw errors

## [12.83.1]

- Adds optional `isAssistPlus` field to CardConfig.

## [12.83.0]

- Added `ScoutTeamShotLocations` stats to soccer stat definitions
- Added functionality to draw a full or half soccer pitch SVG

## [12.82.0]

- Adds `momentBoundaryEditingModuleState`

## [12.81.1]

- Adds optional `taggingLevel` field to CardConfig.

## [12.81.0]

- Added `Points` and `Lines` stats to soccer stat definitions
- Moved `parseLocationString`, `drawableLine`, and `getFilteredPoints` to performance-core-domain package
- Refactored `getPlayerZoneStats` for clarity

## [12.80.3]

- Add `statDefinitionExists` function

## [12.80.2]

- Added set pieces and penalty kicks to goal and shot stats in Soccer.

## [12.80.1]

- Adds `taggingSession` as an internal URL param to switch between TaggingSessions in Performance Core

## [12.80.0]

- Added `videoAndTimelineModuleState` layout constant

## [12.79.1]

- Included mobile stat keys when getting soccer zone location data

## [12.79.0]

- Added sorting capability to event filters for football
- Added `ClassicSeasonEventSortDirection`

## [12.78.0]

- Adds `subject` as an optional arg to `calculateCardStats` and `hideStat` functions
- Hides some soccer stats based on the subject

## [12.77.0]

- Fixed broken type exports for graphql types
- Updated `WebLibraryGetViewerR6Query` types

## [12.76.0]

- Added `videoAndCommentsAndGridModuleState` layout constant

## [12.75.0]

- Adds new `generateNewPlaylistLibraryItem` util function

## [12.74.0]

- Added Media Status Filters For Library Query
- Added `MediaStatus` enum

## [12.73.0]

- Adds new `Comment` (`cm`) URL Parameter Key

## [12.72.0]

- Adds `List` module

## [12.71.2]

- Adds new `Sequence` entry to `TimelineTrackItemsDisplayMode`

## [12.71.1]

- Modifies `canEditDetails` operation to also check `Manage` level permissions

## [12.71.0]

- Removed moment properties code

## [12.70.1]

- Adds new subject filter types and adjusts the way filter mappings are handled for soccer. Allows sports to pass in filter mapping logic through `getMappedFilters`

## [12.70.0]

- Adds `DataChip` interface and moment data chips for ice hockey
- Adds moment properties to define editable-properties for moments
- Adds initial moment properties for ice hockey moments

## [12.69.0]

- Adds `getNonRosterAthleteLabel` for AmFb

## [12.68.1]

- Adds `unsuccessfulPasses`, `unsuccessfulCrosses`, `offTargetShots`, `shotsBlocked`, and `savedShots` stats for soccer
- Modifies `allShotsBlocked` and `allBlockedShots` to be inline with the naming scheme used for scout/oppontent team stats

## [12.68.0]

- Updating V3 Playlists to include `updatedAt`

## [12.67.2]

- Only maps scoped player if the scoped player filter is applied

## [12.67.1]

- Adds card key and card definition for Soccer Locations card

## [12.67.0]

- Added `canCreateEffects` operation to `LibraryContentOperations`

## [12.66.2]

- Adds additional stats for soccer Athlete Insights Module

## [12.66.1]

- Cleans up the usage of `filterMap` in the stats engine so that it handles any filter mapping correctly.

## [12.66.0]

- Adds new timeline for Ice Hockey

## [12.65.0]

- Added `FiltersModule` information to `urlLayoutConstants` and `urlLayoutTypes`

## [12.64.0]

- Add `fileLibraryItemUtils`

## [12.63.1]

- Adds new stats for soccer Athlete Insights Module
- Fixes the StatsEngine `filterMap` implementation. This ensures that the correct clips are returned from `getFilteredClips` based on all key/value pairs in the `filterMap` from a stat definition.

## [12.63.0]

- Updating `VirtualClip` type to include `source`

## [12.62.0]

- Add `ReturnYards.ts` to create ranges for return yards card
- Added numerous URL param keys for new cards
- Added `hidden` card display state
- Added `ODKPassthrough`, `Penalty`, `Penalty Yards`, `PlayTypePassthrough` `ResultsPassthrough`, `Return Yards`, `Returner`, and `Kicker` cards
- Added associated card keys to `FootballCardKey.ts`
- Added `PenaltyYards`, `HasPenalty`, `ReturnYardsRange`, `Returner`, and `Kicker` to `FootballMomentKeys.ts`
- Added stat definiitons for the new cards
- Added assocaited stat keys for the new cards
- Added `isCustom` to `InsightsModuleReportsConfiguration`

## [12.61.0]

- Added base stat defintions for all sports
- Added `customType` and `customDescriptors` stat types for all sports
- Configured base stat definition as fallback for unsupported sports

## [12.60.0]

- Allow returning `0` results from the StatsEngine's `getFilteredClips`
- Added Ice Hockey stats to the StatsEngine.

## [12.59.0]

- Add default labels to timeline items using `getI18nLabel` method from `HudlTaggingMoment`
- Add optional sport and gender to `CreateTrackDataArgs`

## [12.58.0]

- Remove `UniformSport` and replace with Uniform export `AvatarTeamSport`

## [12.57.1]

- Updating all test files to end in `.test.ts` instead of `--tests.ts`

## [12.57.0]

- Added `getReportLinks` and logic for showing report links

## [12.56.0]

- Adds content field to InsightsModuleReportsConfiguration

## [12.55.0]

- Updating `DetailsModule` `Mode` to always be `Items`.

## [12.54.0]

- Adds labels to wrestling timeline configuration

## [12.53.0]

- Added `customPivotReportPrefix` and `temporaryPivotReportPrefix`;
- Added `getEphemeralTabCardKey` and `getPivotReportId` utility functions
- Added `pivots` to and removed `isCustom` from `InsightsModuleReportsConfiguration`

## [12.52.0]

- Adds labels to softball and baseball timeline configuration

## [12.51.0]

- Added `Floating` to `ModuleDisplay`

## [12.50.0]

- Added `videoAndStatsAndTimelineModuleState`

## [12.49.0]

- Changes to InsightsModuleReportsConfiguration interface to support custom reports

## [12.48.0]

- Added `LibraryLabelTreeNodeFilter`.
- Added `LibraryContentLabelKey`.

## [12.47.0]

- Extend library search to support indexed Human Performance sessions
- Add new `LibraryContent` type `RawHumanPerformanceSession`

## [12.46.0]

- Use updated Vite config with `vite-plugin-lib-inject-css`.

## [12.45.0]

- Removing models and types associated with the `treeView`.

## [12.43.0]

- Updated Vite config.

## [12.43.0]

- Added an optional `date` property to `LeafTreeNode`

## [12.42.0]

- Migrate to Vite and ensure exports are available from root

## [12.41.1]

- Added an optional `shouldShowInputContainer` field to `InternalTreeNode`

## [12.41.0]

- Added 5 point item support per period to `createWrestlingStats`

## [12.40.0]

- Added `ModuleName.Video` and `ModuleName.Details`
- Removed `shouldApplyDefaultLayout` and removed legacy URL parsing

## [12.39.0]

- Added logic for Classic content-owner permissions.
- Moved parameters of `transformLibrarySearchResult` into an options object.
  - Added `userId` as a required value. This is used to enforce owner permissions.
  - Added default values to various options. The default value is `false` for any existing options.

## [12.38.0]

- Added `LibraryContentSuggestion` to libraryContent file.

## [12.37.0]

- Added `VideoIdFilter` to filters model

## [12.36.0]

- Bump version of `@hudl/platform-i18n-adapter`

## [12.35.0]

- Added support for Automatic Athlete Moments `ath` URL parameter

## [12.34.0]

- Enable `isolatedModules` in tsconfig and export types using `type` keyword

## [12.33.0]

- Bump version of `@hudl/platform-i18n-adapter`

## [12.32.0]

- Added `playbackUrl` and `durationMS` to `LibraryContent`

## [12.31.0]

- Exported `getModuleStateFromModulesArray`

## [12.30.0]

- Added `canDownload` to `LibraryContentOperations`

## [12.29.1]

- Bumping the version to reference the correct `domain-types` version

## [12.29.0]

- Added score information to library video fragment

## [12.28.0]

- Added support for `canShowBoxScore` operation for `Video` library type
- Added `canShowReportMatrix` operation for `Video` library type

## [12.27.0]

- Added `isLibraryContentFile` type guard

## [12.26.0]

- Added `PERFORMANCE_CORE_MODULE_LAYOUTS` to `ConfigurationType`
- Added `Player` as a `ModuleMode`

## [12.25.0]

- Added `taggingSessionSummaries` to `LibraryContent`

## [12.24.0]

- Changed `downloadUri` to `downloadUrl` for LibraryContentFile

## [12.23.0]

- Allow `managePermissions` for files

## [12.22.0]

## Added

- Added `downloadUrl` to `LibraryContent`

## [12.21.1]

### Added

- Added internalId to `LibraryContent`, `ItemListVideoFragment`, `ItemListPlaylistFragment`, and `ItemListFileFragment`

## [12.21.0]

## Added

- Added stats for Soccer Lines Insight Card
- Renamed `Zone Classification`

## [12.20.0]

### Added

- Added `canAddLabels` and `canRemoveLabels` to LibraryContentOperations
- Added new `LibraryLabelFilter` filter
- Replaced `LibraryFolder` with `LibraryLabel` + renamings from "folder" to "label"

## [12.19.0]

### Added

- Added `AssistRejectionReason` LibraryContent

## [12.18.1]

### Added

- LabelObject to LibraryContent

## [12.18.0]

### Added

- Added `canRestore` and `canAddClips` to LibraryContentOperations

## [12.17.1]

### Added

- Added `deletedAt` to transformShareableResultToLibraryContent

## [12.17.0]

### Added

- Added support for AssistSubmission property to Video and Cutup Library Items

## [12.16.0]

### Added

- Added libraryItemProcessingStatusUtils and CutupStatus enum

## [12.15.0]

### Added

- Added `description` and `formula` properties to the `StatDefinition` interface
- Added descriptions and formulas for baseball and softball stats

### Updated

- Merged baseball and softball `statDefinitions` to reduce duplicated code

## [12.14.1]

### Added

- Added `focusExchangeNetworkVideo` field to LibraryContent
- Added code to fill in focusExchangeNetworkVideo field in transformer for v3 videos

## [12.14.0]

### Removed

- Removed `reportsFeaturesParam`

## [12.13.0]

### Added

- Added internationalization for `LibraryType` and `LibraryVideoType` labels

## [12.12.0]

### Added

- Added `Sequence Starting From`, `Sequences Ending in` insights module cards for soccer

## [12.11.0]

### Added

- Added getTypeLabelForLibraryItem utility function

## [12.10.0]

### Changed

- Modified Grid Visualization to calculate overall total based on filtered clips

### Added

- Added CS, SB, and SB% pitching stats for baseball / softball

## [12.9.0]

### Removed

- Removed the `Reports` module mode.

## [12.8.0]

### Added

- Make `InsightsModuleReportsConfiguration.i18nTitleKey` required

## [12.7.0]

### Added

- Added P/BF and FPS% pitching stats for baseball / softball

## Added

- Added `ANALYSIS_LIBRARY_FILTERS` option to UI `ConfigurationType`
- Added `StartExpanded` option to `ShowMoreOptions`

## [12.6.0]

### Changed

- Modified the key stats objects and utils to be configurable on a per sport basis instead of only for basketball
- Added key stat card and stat definitions for soccer

### Added

- Added GS, BAA, WHIP, S% and P/IP pitching stats for baseball / softball

## [12.5.0]

### Added

- Added 1B, 2B, 3B, HR, OPS, and PS/PS batting stats for baseball / softball

## [12.4.0]

### Added

- Added ShowMoreOptions and updated TreeViewData in treeView

## [12.3.0]

### Changed

- Modified properties for reports module mode, made `id` and `i18nTitleKey` optional

## [12.2.0]

### Added

- Added aggregated pitching stats for baseball / softball

## [12.1.0]

### Added

- Added a grid column provider for soccer playlists.

## [12.0.0]

### Added

- Added `InsightsMetadata` model and corresponding property to `PerformanceCoreRequestParameters`.

### Changed

- Renamed `AnalyzeRouteRequestParameters` to `PerformanceCoreRequestParameters`.
- Renamed `decodeAnalyzeRouteInitialParameters` to `decodePerformanceCoreRouteParameters`.
- Renamed `encodeAnalyzeRouteUrlParameters` to `encodePerformanceCoreRouteParameters`.

## [11.7.0]

### Added

- Added `ScoutTeamSequencesEndingWith` and `OpponentTeamsSequencesEndingWith` stats and cards for soccer.

## [11.6.0]

### Added

- Added Reports module mode

## [11.5.0]

### Added

- InsightsModuleReportsConfiguration to support new predefined reports
- Added ability to calculate aggregated stats within the `StatsEngine`
- Added aggregated batting stats for baseball / softball
- Added `AggregateTable` visualization
- Added `formatStatValue` and `statLabelToTeam` to `StatDefinition` to support the new `AggregateTable` visualization
- Added `teamOneId` and `teamTwoId` to `StatClip.teamInfo`
- Added 'useUnfilteredClipsOnly`property to`CardDefinition` to support static stats

## [11.4.0]

### Added

- Added `LibraryContentStatus` to V3 Video Library Content.

## [11.3.0]

### Added

- Added new Insights Module Card Types to Soccer for Performance Core

## [11.2.0]

### Added

- Annotated functions in `base64Utils` which encoded/decoded IDs with `@deprecated` to indicate that pattern of use should be avoided.

### Changed

- Use `Buffer` instead of `window` functions `atob` and `btoa` to encode/decode base64 strings in `base64Utils`.

## [11.1.0]

### Added

- Moved `base64Utils` from `watch` app into `performance-core-domain`.
- Added `encodeString` and `decodeString` functions to `base64Utils`.

## [11.0.0]

### Added

- Removed the `Other` library item type and added `SavedView`

## [10.11.0]

### Added

- Added new general types `Organization`, `Sport`, `Team` and `UniformSport`.
- Added util to fetch current team ID from the URL.
- Moved `Member` type from `watch` app into `performance-core-domain`.

## [10.10.0]

### Changed

- Added reports features route parameter
- # Added reports module state constant

### Added

- Added support for parsing `permissions` for classic library items (i.e. Cutups and Shareables).
  > > > > > > > main

## [10.9.0]

### Changed

- Added optional sport parameter to `LibraryVideoType toLibraryModuleLabel()`

## [10.8.0]

## Added

- Added `eventId` property to `LibraryContent`

## [10.7.0]

## Changed

- Updated Soccer SoccerTaggingMoment to point to new shot moment assisting player tag `receivingPlayer`

## [10.6.0]

### Added

- Added `LibraryContentFile` type which extends `LibraryContent` and is returned by `transformV3FileResultToLibraryContent`.
- Added `File` to `LibraryItemType`.
- Added `transformTypeFiltersToLibraryTypes` for building V3 type filters from the library filters query.

### Changed

- Reordered `LibraryItemType` enum.

## [10.5.1]

### Changed

- Adding better null-checking around library item parsing

## [10.5.0]

### Added

- Added `SwingType` to Insights Module for Baseball/Softball

## [10.4.0]

### Changed

- Changed LibraryEvent.Date to be nullable.
- Added transformer to convert ClassicFacade filter types to LibraryModule types

## [10.3.0]

### Changed

- Changed `RunnersOn` card to `RunnersOnBase`
- Changed `RunnersOnBase` `defaultDisplayState` to `Default`

## [10.2.0]

### Changed

- Added `isClassic` boolean parameter to `transformToLibrarySeasons`.

## [10.1.0]

### Removed

- References to soccer duels

## [10.0.0]

### Added

- Added `LibraryContentOwner` type.
- Added `owner` property to `LibraryContent` and populating it for both v3 and classic items.

### Removed

- Removed `creatorId` property on `LibraryContent`

## [9.33.0]

- Updated dependency from performance-core-i18n to platform-i18n-adapter due to package name change

### Removed

- Removed `creatorId` property on `LibraryContent`

## [9.32.0]

### Changed

- Internationalized labels for baseball and softball stats and cards

## [9.31.0]

### Added

- Assisted by card for soccer

## [9.30.2]

- Added `CUTUP_STATUS` filter to library searches to exclude live tagged cutups from display.

## [9.30.1]

- Remove duplicate addition of missing boolean values in StatsEngine.ts

## [9.30.0]

- Migrate period i18n keys to performance-core-domain i18n.json file

## [9.29.0]

- Add clip counts for Key Stats in basketball

## [9.28.1]

### Changed

- Consuming the latest platform-i18n-adapter package version.

## [9.28.0]

### Changed

- Removing all references to frontends-i18n in favor of the new platform-i18n-adapter package,
  including removing Wrestling's "stopgap" i18n solution.

## [9.27.0]

### Added

- Added custom hover description for Wrestling Team

## [9.26.0]

- Cross receiver card for each team for soccer

## [9.25.0]

- Crossed By card for each team for soccer

## [9.24.0]

- Refactored transformers
- Added cutup support for thumbnails, clip counts, and video types

## [9.23.0]

### Added

- Soccer `CrossesPerPeriod` insights module cards for each team

## [9.22.2]

- Updated Classic search query builder to use new label filters

## [9.22.1]

- For soccer if roster is not available, pass through unmodified player string to Insight Module cards.

## [9.22.0]

### Added

- Adds automation url param to prevent onboarding and product updates for automated tests.

## [9.21.0]

### Added

- Shot Results card for each team for soccer

## [9.20.0]

- Updated Classic search query builder to filter by Cutup types explicitly.

## [9.19.0]

### Added

- Shots Per Period card for each team for soccer
- Shots Per Player card for each team for soccer

## [9.18.0]

- Adds a method to the `StatDefinition` allowing a stat to override the stats engine value provided to cards.

## [9.17.0]

- Add in friendly names for baseball and softball spray chart zones

## [9.16.0]

### Added

- Library Search Predicate for searching by Id
- Added `addGameSectionToSoccerClips` during clip calculation for soccer.
- Added passes per period stats for each soccer team.
- Added passes per period stat histogram cards for each team.

## [9.15.0]

- Initial StatsEngine configuration for soccer.
- Initial Insights module card and cardgroup definitions for soccer.

## [9.14.0]

- Update "Medical Forfeit" to "Injury Default" in tests

## [9.13.1]

### Changed

- Updated `Unkown Athlete` in formatStats and BasketballStats to show jersey number first

## [9.13.0]

### Added

- Add "isMultiVideoOnly" prop to CardGroup

## [9.12.0]

### Added

- Add `perspectiveTeamId` to assign selectedTeamId for wrestling clips
- Add `blockDeselect` option to filter update service

## [9.11.0]

### Added

- Added "Ungrouped Stats" label to Wrestling ungrouped stats card group.

## [9.10.0]

### Added

- Add Athletes section (including Team & Wrestlers cards) to Insights Module for wrestling
- Enforce uniqueness for URL parameter keys

## [9.9.0]

### Changed

- Updated `calculateWinLossStatCounts` function to allow a combination of tagged and untagged video

## [9.8.1]

### Changed

- Updated `runnerOn` and `outs` to minimized for baseball/softball display state

## [9.8.0]

- Add "Win/Loss" card group and cards to insights module for Wrestling

## [9.7.0]

### Changed

- Updated `hitLocation` card definitions to show count for baseball and softball

## [9.6.0]

### Added

- Add Wrestling points per period card to insights module

## [9.5.0]

### Added

- Updated Baseball/Softball default display state to 'default'

## [9.4.0]

### Added

- Added library-module

## [9.3.0]

### Added

- Add "HitLocation" Card to insights module for baseball and softball

## [9.2.0]

### Added

- Add "Position" card to insights module for Wrestling

## [9.1.0]

### Changed

- Updated getCardDefinition to prevent i18n formatting if the title is empty

## [9.0.0]

### Changed

- Renamed to performance-core-domain
- Extracted insight-module logic to it's own directory

## [8.12.0]

### Added

- Add "Results" section to insights module for Wrestling

## [8.11.0]

### Changed

- Made the "Period" card sport-agnostic for insights module and added internationalization

## [8.10.0]

### Changed

- Include empty stats for displaying on cards

## [8.9.0]

### Added

- Setup insights module for wrestling with an example (filter by period)

## [8.8.0]

⚠️ IMPORTANT NOTE ⚠️: By mistake, the version was not updated on <https://github.com/hudl/hudl-frontends/pull/1007/files>. This means that the version 8.8.0 was override by the version 8.9.0. In other words, version 8.8.0 and 8.9.0 contains exactly the same code.

### Added

- Add `OutsAt` and `PlayType` filters for baseball/softball

## [8.7.0]

### Changed

- Add Hit Type to insights module for baseball and softball

## [8.6.0]

### Changed

- Change "Offense" and "Defense" to "Batting" and "Pitching" for baseball and softball

## [8.5.0]

### Changed

- Add "Outs" Card to insights module for baseball and softball

## [8.4.0]

### Changed

- Add "Batting Result" Card to insights module for baseball and softball

## [8.3.0]

### Changed

- Add "Runners On" Card to insights module for baseball and softball

## [8.2.0]

### Changed

- Add Pitch Result to insights module for baseball and softball

## [8.1.0]

### Changed

- Updating createVirtualClips call to new method signature

## [7.10.0]

### Changed

- Updating baseball and softball stat definitions to match new property names

## [7.9.0]

### Added

- Support for dynamic interactions for the Shot Chart card.
- Optional Stat property to allow multiple values for a stat.

## [7.8.0]

### Added

- Shot Chart card for Basketball (This is an experimental feature for now).

## [7.7.1]

### Fixed

- # URL persistence for key stats cards

## [7.7.0]

### Changed

- Updated logic determining basketball phase and turnovers

## [7.6.1]

### Fixed

- `StatsEngine` respects original clip order when returning filtered clips.

## [7.6.0]

### Added

- Add Count Card to baseball and softball

## [7.5.0]

### Added

- Add Inning Card to baseball and softball

## [7.4.0]

### Added

- Add Pitch Type Card to baseball and softball

## [7.3.0]

### Added

- Introduce StatsEngine to baseball and softball

## [7.2.0]

### Added

- Add new Offense/Defense Phase Card to baseball and softball

## [7.1.0]

### Added

- Multi-value clip properties to support multiple result types for a Basketball opportunity

## [7.0.0]

- Import codebase into hudl-frontends monorepo

## [6.3.0]

### Added

- Adds a new Batting Card Group and Plate Appearance Card By for baseball and softball

## [6.2.0]

### Added

- New Basketball stat calculation property for points scored

## [6.1.0]

### Changed

- Allow clipFilterService to parse string moments if their statDefinition is numeric

## [6.0.0]

### Breaking

- Upgraded @hudl/hudl-domain-types peer dependency to ^v16.0.0

## [5.3.0]

### Added

- Adds a new Pitching Card Group and Pitched By Card for baseball and softball

## [5.2.0]

### Added

- Added FilterModeOptions type to Filter object to allow for any/no value filtering InList
- Added FilterModes for GreaterThanOrEqualTo and LessThanOrEqualTo

### Changed

- Modified clipFilterService to acknowledge any/no value options and GTE and LTE modes
- Fixed calculate stats on totals for Pivot Points

### Breaking

- Removes `CustomFilter` from `CardDisplayState`

## [5.1.0]

### Added

- Add support for score calculations.

## [5.0.0]

This change log contains breaking changes since we are removing/adding/renaming some basketball key stats, as well URL parameters keys.

### Changed

- Renamed `Team Stats` to `Key Stats`
- Rename some of basketball stat keys.

### Added

- New Basketball stat calculation property for total rebounds

## [4.4.0]

### Changed

- Splits out Basketball card definitions into Scout Team and Opponent Team Context

## [4.3.0]

### Added

- Add support to Pivot Stat calculation to include custom filters as labels
- Makes FilterMode a StatLabel

## [4.2.0]

### Added

This version takes a step towards unifying the mobile app and VSPA to use the same URL formats for
accessing content in insights. By moving our URL parsing and encoding to here, the addressable parameters to load
a given insight/statistic can be uniform between all systems.

- Refactored the URL services out of VSPA into here so mobile and VSPA can share implementations
- Moved types and constants shared by all products that need insights

## [4.1.0]

### Changed

- Updated baseball and softball clip calculation

## [4.0.0]

### Added

- Adding support for custom filters to filter types
  - Replaces `FilterChoice` with `FilterValue` as a single selected filter value
  - Replaces `FilterSelections` with `Filter`, an object comprising the following:
    - A `FilterKey`
    - An array of `FilterValue`s
    - The `FilterMode` with which the `FilterValue`s are treated, e.g. default mode vs. some custom mode like `inList`
- Removes `CustomFilterSelections`
- Removes `FilterChoiceSummary` as unused

## [3.26.0]

### Added

- Adds support for baseball and softball

## [3.25.0]

### Added

- Adds a `dataTypeForCustomFilters` field to `StatDefinition` to ensure correct data type is accessed when applying custom filters.
- Adds a `CustomFilter` key to `URLParameterKeys`.

## [3.24.0]

### Added

- Adds a `momentPropertyForCustomFilters` field to `StatDefinition` to ensure correct moment property is accessed when applying custom filters.

## [3.23.0] - 2022-09-09

### Added

- Added GitHub Action Workflows for verifying changelog is updated, and package version gets bumped before merging to `main`.

### Changed

- Moved CI/CD off of TeamCity and onto GitHub Actions.
- Package now dual publishes to Nexus and GitHub Packages.
- Migrated project to Yarn2.
- Renamed default branch to `main`.
- Updated imports for `stats/` in Jest tests to allow `yarn compile` and `yarn test` to pass successfully without needing to first perform a `yarn build`.
- Updated imports based on renamed `@hudl/hudl-domain-types` package.

## [3.22.0]

### Added

- Adds a new `CustomFilter` `CardDisplayState`.

## [3.21.1]

### Fixed

- Fix to visualization on Basketball Period Card

## [3.21.0]

### Added

- Add Period (Quarter/Halves/Overtime) card for Basketball

## [3.20.0]

### Added

- Add Steal By card for Basketball

## [3.19.0]

### Added

- Add Offensive Rebound By and Defensive Rebound By cards for Basketball

## [3.18.0]

### Added

- Add Shot By card for Basketball

## [3.17.0]

### Added

- Add cards for Basketball
  - 2 / 3 Field Goal Types
  - Shot Types
  - Shot Taken
  - Inbound Types

## [3.16.0]

### Added

- Add Shot Contested card for Basketball

## [3.15.0]

### Changed

- Update Basketball's Phase card definition to support the stacked bar visualization

## [3.14.0]

### Added

- Add a new stat computation model to support opportunity based filtering for Basketball

## [3.13.0]

### Changed

- Update Card Definitions displayed as open by default to only Offensive and Defensive Tendencies

## [3.12.0]

### Added

- Add Athlete Card for Basketball

## [3.11.0]

### Changed

- Update Box Score stat calculations to take in consideration new combined Steal moment

## [3.10.0]

### Changed

- Update Assist filtering on Team Stats

## [3.9.0]

### Changed

- Update Team Stats for moments that can both start and end a play

## [3.8.0]

### Added

- Add ConfigurationType.ANALYSIS_PRESETS

## [3.7.0]

### Added

- Add Box Score visualization calculations
- Add Box Score Card for Basketball

## [3.6.0]

### Added

- Add Kick Yards card under Special Teams

## [3.5.0]

### Added

- Add ConfigurationType.ANALYSIS_ONBOARDING

## [3.4.0]

### Changed

- Clean up friendly name labels for football

## [3.3.1]

### Changed

- Support Node versions 12.x, 14.x, and 16.x

## [3.3.0]

### Added

- Add Phase card for Basketball

## [3.2.0]

### Added

- Add ConfigurationType.ANALYSIS_SAVED_FILTERS

## [3.1.0]

### Added

- Add Pass Zone card for American Football

## [3.0.0]

### Changed

- Restructure to instead calculate stats on a per card basis.
- Use `calculateCardStats` instead of `calculateCardGroupStats`

## [2.2.0]

### Added

- Add new `URLParameterKeys` for open full card overlay

## [2.1.0]

### Added

- Add in `OverviewOptions` that can be specified for `CardConfig`s
- Add in ability to calculate stats for the `Table` visualization
- Calculate `overviewStats` by default when available
- Bump TS version to `es2019`

## [2.0.0]

### Added

- Adds support for Basketball

### Breaking

- Removes immer and hudl-domain-types dependencies

## [1.0.0]

### Added

- Adds the ANALYSIS_DATA_FIELDS ConfigurationType
- Adds new URLParameterKeys
- Adds new SpecialTeams Football Card Group

### Changed

- Update the signature of pivotGroupStats
- Rename 'Other' card group to 'Ungrouped Stats'

## [0.2.0]

### Added

- Adds URLPrefixes, a module containing URL prefix constants

## [0.1.0]

### Added

- Initial release

[7.9.0]: https://github.com/hudl/hudl-frontends/pull/341
[7.8.0]: https://github.com/hudl/hudl-frontends/pull/301
[7.7.1]: https://github.com/hudl/hudl-frontends/pull/245
[7.7.0]: https://github.com/hudl/hudl-frontends/pull/219
[7.6.1]: https://github.com/hudl/hudl-frontends/pull/222
[7.1.0]: https://github.com/hudl/hudl-frontends/pull/192
[6.2.0]: https://github.com/hudl/npm-insights-module/pull/63
