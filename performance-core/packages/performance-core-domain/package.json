{"name": "@hudl/performance-core-domain", "version": "12.86.0", "private": false, "description": "Code to reduce duplicated logic within various areas of performance core.", "contributors": [{"name": "Web Gems, Your Brand Here, <PERSON><PERSON>ner, BBQ", "url": "https://hudl.slack.com/archives/C0479456U9H", "channel": "#performance-core-eng"}], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**"], "scripts": {"build": "vite build", "clean": "rimraf dist node_modules/.cache", "dev": "vite build --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "nuke": "pnpm run clean && rimraf node_modules", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "release": "release-package", "test": "vitest", "test:ci": "vitest --coverage --silent", "test:nowatch": "vitest --watch=false", "test:update": "vitest --update", "types": "tsc --project tsconfig-declarations.json --sourceRoot $PWD/src", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src"}, "dependencies": {"@hudl/platform-i18n-adapter": "workspace:*", "buffer": "6.0.3", "invariant": "2.2.1", "lodash": "^4.17.21"}, "devDependencies": {"@hudl/eslint-config": "workspace:*", "@hudl/hudl-domain-types": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "@types/invariant": "2.2.30", "@types/lodash": "4.14.202", "@vitest/coverage-v8": "3.2.4", "config": "workspace:*", "eslint": "8.45.0", "jsdom": "24.0.0", "vite": "5.4.7", "vitest-fail-on-console": "^0.7.0"}, "peerDependencies": {"@hudl/hudl-domain-types": ">= 16"}}