import {
  SoccerMomentTypes,
  SoccerPitch,
  type SoccerPitchDimensionsMeters,
  type SoccerPitchPoint,
  SoccerTaggingMoment,
} from '@hudl/hudl-domain-types';
import { format } from '@hudl/platform-i18n-adapter';

import { notEmpty } from '../../../library-module/utils/notEmpty';
import { type Filter, type FilterKey, URLParameterKeys } from '../../domain';
import type { RosterAthlete } from '../../domain/roster';
import { isAthleteSubjectFilter } from '../../utils/subjectUtils';
import type { Stat, StatLabel } from '../domain';
import { isDefined } from '../engine/utility/isDefined';
import SoccerCardKey from './domain/SoccerCardKey';
import SoccerStatKey from './statKeys';

export const I18N_SOCCER_FILTER_PREFIX = 'domainTypes.filter.soccer';

export function soccerStatLabelToFriendlyNameForAthlete(
  statLabel: StatLabel,
  _isShortened?: boolean,
  roster?: Map<string, RosterAthlete>
): string {
  if (typeof statLabel !== 'string') {
    return '?';
  }
  // Player ID  format is 'u' + userId + '-' + teamId
  // Jersey format is 'j' + jerseyNumber + '-' + teamId
  const prefix = statLabel[0];
  // Substring the value to remove the prefix and suffix
  const value = statLabel.substring(1, statLabel.length - 2);
  if (!roster || !roster.size) {
    return statLabel;
  }

  if (prefix === 'x') {
    return 'Unknown Athlete';
  }

  if (prefix === 'u') {
    const athlete = roster.get(value);
    if (!athlete) {
      return 'Unknown Athlete';
    }
    if (athlete.jersey && athlete.fullName) {
      return `#${athlete.jersey} ${athlete.fullName}`;
    }
    if (athlete.jersey) {
      return `#${athlete.jersey}`;
    }
    if (athlete.fullName) {
      return athlete.fullName;
    }
  }

  if (prefix === 'j') {
    if (value && value.length > 2) {
      return `${value}`;
    }
    return `#${value}`;
  }

  if (prefix === 'g') {
    return 'Goalkeeper';
  }

  return 'Unknown Athlete';
}

export const rosterAwareSort = (roster: Map<string, RosterAthlete>) => (a: Stat, b: Stat) => {
  if (typeof a.label !== 'string' || typeof b.label !== 'string') {
    return -1;
  }
  // If the label starts with unknown sort it to the bottom
  const aPrefix = a.label[0];
  const bPrefix = b.label[0];
  const aLabel = a.label.substring(1);
  const bLabel = b.label.substring(1);
  const aAthlete = aPrefix === 'u' ? roster.get(aLabel) : { jersey: aLabel, lastName: '' };
  const bAthlete = bPrefix === 'u' ? roster.get(bLabel) : { jersey: bLabel, lastName: '' };

  // Sort any athletes with an x prefix to the bottom of the list
  if (aPrefix === 'x') {
    return 1;
  }

  if (bPrefix === 'x') {
    return -1;
  }

  if (a.value > b.value) {
    return -1;
  }
  if (a.value < b.value) {
    return 1;
  }
  if (aAthlete && bAthlete) {
    // Sort by value first, then athlete.jersey, then athlete.lastName.
    // If a player is not on the roster, they will be sorted to the bottom of value ties.
    if (aAthlete.jersey && bAthlete.jersey) {
      if (parseInt(aAthlete.jersey, 10) > parseInt(bAthlete.jersey, 10)) {
        return 1;
      }
      if (parseInt(aAthlete.jersey, 10) < parseInt(bAthlete.jersey, 10)) {
        return -1;
      }
    }
    if (aAthlete.lastName && bAthlete.lastName) {
      if (aAthlete.lastName > bAthlete.lastName) {
        return 1;
      }
      if (aAthlete.lastName < bAthlete.lastName) {
        return -1;
      }
    }
  }
  return b.value - a.value;
};

export function getSortFunc<T extends string | Stat>(orderedLabels: string[]) {
  return (a: T, b: T) => {
    const aVal = typeof a === 'string' ? a : a.label.toString();
    const bVal = typeof b === 'string' ? b : b.label.toString();
    const aIndex = orderedLabels.indexOf(aVal);
    const bIndex = orderedLabels.indexOf(bVal);
    if (aIndex === -1 && bIndex === -1) {
      return 0;
    }
    if (aIndex === -1) {
      return 1;
    }
    if (bIndex === -1) {
      return -1;
    }
    return aIndex - bIndex;
  };
}

// Some filter groups are a single string but some have multiple sublabels and are
// encoded with a dash: <group>-<sublabel>
// Ex) shot-saved, shot-blocked, shot-offTarget
export function extractGroupsFromStats(stats: Stat[]): Set<string> {
  const results = stats
    .map((stat) => {
      if (typeof stat.label !== 'string') {
        return;
      }
      const splitLabel = stat.label.split('-');
      if (splitLabel.length > 1) {
        return splitLabel[0];
      }
      return stat.label;
    })
    .filter(notEmpty);

  return new Set(results);
}

// Converts a single string or grouped stat label (e.g., "shot-saved") to a friendly name (e.g., "Shots")
export function filterKeyToFriendlyName<T extends StatLabel | string>(key: T): string {
  if (typeof key === 'string') {
    const splitLabel = key.split('-');
    if (splitLabel.length > 1) {
      return formatFilterKey(splitLabel[1]);
    }
    return formatFilterKey(key);
  }
  return key.toString();
}

function formatFilterKey(key: string): string {
  return format(`${I18N_SOCCER_FILTER_PREFIX}.${key}`);
}

/**
 * Whether the key is a Shot Chart filter
 * @param key Filter key.
 * @returns Whether the stat key is a Shot Chart filter
 */

export const isSoccerShotChart = (key: FilterKey): boolean => key === SoccerCardKey.ShotLocations;

/**
 * Whether the key is a key stat filter
 * @param key Filter key.
 * @returns Whether the stat key is a key stat filter
 */
export const isSoccerKeyStatFilter = (key: string): boolean =>
  key === SoccerStatKey.ScoutTeamShots ||
  key === SoccerStatKey.ScoutTeamPasses ||
  key === SoccerStatKey.ScoutTeamSuccessfulPasses ||
  key === SoccerStatKey.ScoutTeamReceptions ||
  key === SoccerStatKey.ScoutTeamReceptionsFromPasses ||
  key === SoccerStatKey.ScoutTeamReceptionsFromCrosses ||
  key === SoccerStatKey.ScoutTeamInterceptions ||
  key === SoccerStatKey.ScoutTeamBlocks ||
  key === SoccerStatKey.ScoutTeamSaves ||
  key === SoccerStatKey.ScoutTeamGoalkeeperKicks ||
  key === SoccerStatKey.ScoutTeamGoalkeeperThrows ||
  key === SoccerStatKey.ScoutTeamOffsides ||
  key === SoccerStatKey.ScoutTeamFouls ||
  key === SoccerStatKey.ScoutTeamYellowCards ||
  key === SoccerStatKey.ScoutTeamRedCards ||
  key === SoccerStatKey.ScoutTeamGoals ||
  key === SoccerStatKey.ScoutTeamAssists ||
  key === SoccerStatKey.ScoutTeamShotsOnTarget ||
  key === SoccerStatKey.ScoutTeamCrosses ||
  key === SoccerStatKey.ScoutTeamSuccessfulCrosses ||
  key === SoccerStatKey.OpponentTeamShots ||
  key === SoccerStatKey.OpponentTeamPasses ||
  key === SoccerStatKey.OpponentTeamSuccessfulPasses ||
  key === SoccerStatKey.OpponentTeamReceptions ||
  key === SoccerStatKey.OpponentTeamReceptionsFromPasses ||
  key === SoccerStatKey.OpponentTeamReceptionsFromCrosses ||
  key === SoccerStatKey.OpponentTeamInterceptions ||
  key === SoccerStatKey.OpponentTeamBlocks ||
  key === SoccerStatKey.OpponentTeamSaves ||
  key === SoccerStatKey.OpponentTeamGoalkeeperKicks ||
  key === SoccerStatKey.OpponentTeamGoalkeeperThrows ||
  key === SoccerStatKey.OpponentTeamOffsides ||
  key === SoccerStatKey.OpponentTeamFouls ||
  key === SoccerStatKey.OpponentTeamYellowCards ||
  key === SoccerStatKey.OpponentTeamRedCards ||
  key === SoccerStatKey.OpponentTeamGoals ||
  key === SoccerStatKey.OpponentTeamAssists ||
  key === SoccerStatKey.OpponentTeamShotsOnTarget ||
  key === SoccerStatKey.OpponentTeamCrosses ||
  key === SoccerStatKey.OpponentTeamSuccessfulCrosses;

/**
 * Whether the key is from a opponent team stat.
 * @param key Filter key.
 * @returns Whether the key is from Key Stats.
 */
export const isSoccerKeyStats = (key: FilterKey): boolean =>
  key === SoccerCardKey.OpponentTeamKeyStats ||
  key === SoccerCardKey.ScoutTeamKeyStats ||
  key === SoccerCardKey.AllTeamKeyStats;

export const soccerKeyStatsOrShotChartUrlParams = [
  URLParameterKeys.Soccer_ScoutTeam_KeyStats,
  URLParameterKeys.Soccer_OpponentTeam_KeyStats,
];

/**
 * Whether the key is from a scout team stat.
 * @param key Filter key.
 * @returns Whether the key is from a scout team stat.
 */
export const isSoccerScoutTeamStatKey = (key: FilterKey): boolean =>
  key === SoccerCardKey.ScoutTeamInvolvedAthletes ||
  key === SoccerCardKey.ScoutTeamKeyStats ||
  key === SoccerCardKey.ScoutTeamZones ||
  key === SoccerCardKey.ScoutTeamPoints ||
  key === SoccerCardKey.ScoutTeamPassesPerAttack ||
  key === SoccerCardKey.ScoutTeamLines;

/**
 * Whether the key is from a opponent team stat.
 * @param key Filter key.
 * @returns Whether the key is from a opponent team stat.
 */
export const isSoccerOpponentTeamStatKey = (key: FilterKey): boolean =>
  key === SoccerCardKey.OpponentTeamInvolvedAthletes ||
  key === SoccerCardKey.OpponentTeamKeyStats ||
  key === SoccerCardKey.OpponentTeamZones ||
  key === SoccerCardKey.OpponentTeamPoints ||
  key === SoccerCardKey.OpponentTeamPassesPerAttack ||
  key === SoccerCardKey.OpponentTeamLines;

/**
 * Whether the key is a key stat filter
 * @param key Filter key.
 * @returns Whether the stat key is a key stat filter
 */
export const isSoccerPrimaryKeyStatFilter = (key: string): boolean =>
  key === SoccerStatKey.ScoutTeamShots ||
  key === SoccerStatKey.ScoutTeamPasses ||
  key === SoccerStatKey.ScoutTeamSuccessfulPasses ||
  key === SoccerStatKey.ScoutTeamUnsuccessfulPasses ||
  key === SoccerStatKey.ScoutTeamGoalkeeperKicks ||
  key === SoccerStatKey.ScoutTeamGoalkeeperThrows ||
  key === SoccerStatKey.ScoutTeamOffsides ||
  key === SoccerStatKey.ScoutTeamFouls ||
  key === SoccerStatKey.ScoutTeamYellowCards ||
  key === SoccerStatKey.ScoutTeamRedCards ||
  key === SoccerStatKey.ScoutTeamGoals ||
  key === SoccerStatKey.ScoutTeamShotsOnTarget ||
  key === SoccerStatKey.ScoutTeamShotsOffTarget ||
  key === SoccerStatKey.ScoutTeamShotsBlocked ||
  key === SoccerStatKey.ScoutTeamSavedShots ||
  key === SoccerStatKey.ScoutTeamCrosses ||
  key === SoccerStatKey.ScoutTeamSuccessfulCrosses ||
  key === SoccerStatKey.ScoutTeamUnsuccessfulCrosses ||
  key === SoccerStatKey.OpponentTeamShots ||
  key === SoccerStatKey.OpponentTeamPasses ||
  key === SoccerStatKey.OpponentTeamSuccessfulPasses ||
  key === SoccerStatKey.OpponentTeamUnsuccessfulPasses ||
  key === SoccerStatKey.OpponentTeamGoalkeeperKicks ||
  key === SoccerStatKey.OpponentTeamGoalkeeperThrows ||
  key === SoccerStatKey.OpponentTeamOffsides ||
  key === SoccerStatKey.OpponentTeamFouls ||
  key === SoccerStatKey.OpponentTeamYellowCards ||
  key === SoccerStatKey.OpponentTeamRedCards ||
  key === SoccerStatKey.OpponentTeamGoals ||
  key === SoccerStatKey.OpponentTeamShotsOnTarget ||
  key === SoccerStatKey.OpponentTeamShotsOffTarget ||
  key === SoccerStatKey.OpponentTeamShotsBlocked ||
  key === SoccerStatKey.OpponentTeamSavedShots ||
  key === SoccerStatKey.OpponentTeamCrosses ||
  key === SoccerStatKey.OpponentTeamSuccessfulCrosses ||
  key === SoccerStatKey.OpponentTeamUnsuccessfulCrosses ||
  key === SoccerStatKey.AllShots ||
  key === SoccerStatKey.AllPasses ||
  key === SoccerStatKey.AllSuccessfulPasses ||
  key === SoccerStatKey.AllUnsuccessfulPasses ||
  key === SoccerStatKey.AllGoalkeeperKicks ||
  key === SoccerStatKey.AllGoalkeeperThrows ||
  key === SoccerStatKey.AllOffsides ||
  key === SoccerStatKey.AllFouls ||
  key === SoccerStatKey.AllYellowCards ||
  key === SoccerStatKey.AllRedCards ||
  key === SoccerStatKey.AllGoals ||
  key === SoccerStatKey.AllShotsOnTarget ||
  key === SoccerStatKey.AllOffTargetShots ||
  key === SoccerStatKey.AllShotsBlocked ||
  key === SoccerStatKey.AllSavedShots ||
  key === SoccerStatKey.AllCrosses ||
  key === SoccerStatKey.AllSuccessfulCrosses ||
  key === SoccerStatKey.AllUnsuccessfulCrosses;

export const isSoccerSecondaryKeyStatFilter = (key: string): boolean =>
  key === SoccerStatKey.ScoutTeamReceptions ||
  key === SoccerStatKey.ScoutTeamReceptionsFromPasses ||
  key === SoccerStatKey.ScoutTeamReceptionsFromCrosses ||
  key === SoccerStatKey.ScoutTeamInterceptions ||
  key === SoccerStatKey.ScoutTeamInterceptedCrosses ||
  key === SoccerStatKey.ScoutTeamInterceptedPasses ||
  key === SoccerStatKey.ScoutTeamBlocks ||
  key === SoccerStatKey.ScoutTeamBlockedCrosses ||
  key === SoccerStatKey.ScoutTeamBlockedPasses ||
  key === SoccerStatKey.ScoutTeamBlockedShots ||
  key === SoccerStatKey.ScoutTeamSaves ||
  key === SoccerStatKey.ScoutTeamAssists ||
  key === SoccerStatKey.OpponentTeamReceptions ||
  key === SoccerStatKey.OpponentTeamReceptionsFromPasses ||
  key === SoccerStatKey.OpponentTeamReceptionsFromCrosses ||
  key === SoccerStatKey.OpponentTeamInterceptions ||
  key === SoccerStatKey.OpponentTeamInterceptedCrosses ||
  key === SoccerStatKey.OpponentTeamInterceptedPasses ||
  key === SoccerStatKey.OpponentTeamBlocks ||
  key === SoccerStatKey.OpponentTeamBlockedCrosses ||
  key === SoccerStatKey.OpponentTeamBlockedPasses ||
  key === SoccerStatKey.OpponentTeamBlockedShots ||
  key === SoccerStatKey.OpponentTeamSaves ||
  key === SoccerStatKey.OpponentTeamAssists ||
  key === SoccerStatKey.AllReceptions ||
  key === SoccerStatKey.AllReceptionsFromPasses ||
  key === SoccerStatKey.AllReceptionsFromCrosses ||
  key === SoccerStatKey.AllBallOuts ||
  key === SoccerStatKey.AllInterceptions ||
  key === SoccerStatKey.AllInterceptedCrosses ||
  key === SoccerStatKey.AllInterceptedPasses ||
  key === SoccerStatKey.AllBlocks ||
  key === SoccerStatKey.AllBlockedCrosses ||
  key === SoccerStatKey.AllBlockedPasses ||
  key === SoccerStatKey.AllBlockedShots ||
  key === SoccerStatKey.AllSaves ||
  key === SoccerStatKey.AllAssists;

export const isAthleteIncludedFilter = (key: string): boolean =>
  key === SoccerStatKey.ScoutTeamAthleteIncluded || key === SoccerStatKey.OpponentTeamAthleteIncluded;

export const isTeamMomentsFilter = (key: string): boolean =>
  key === SoccerStatKey.ScoutTeamMoments || key === SoccerStatKey.OpponentTeamMoments;

// a non-mappable filter is a filter that should not be mapped to a different filter. This ensures the stats are correctly calculated with these filters applied
export const hasNonMappableFilter = (filters: Filter[]) =>
  filters.find(
    (filter) =>
      isAthleteIncludedFilter(filter.key) || isTeamMomentsFilter(filter.key) || filter.key === SoccerStatKey.PlayerZones
  );

export enum ZoneClassification {
  Primary = 'p',
  Secondary = 's',
  Unknown = 'unknown',
}

export interface PlayerZoneInfo {
  player: string;
  zone: string;
  zoneClassification: ZoneClassification;
}

export function getPlayerZoneInfo(playerZoneString: string): PlayerZoneInfo {
  const parts = playerZoneString.split('_');
  let zc = ZoneClassification.Unknown;
  switch (parts[2]) {
    case ZoneClassification.Primary:
      zc = ZoneClassification.Primary;
      break;
    case ZoneClassification.Secondary:
      zc = ZoneClassification.Secondary;
      break;
    default:
      break;
  }

  return {
    player: parts[0],
    zone: parts[1],
    zoneClassification: zc,
  };
}

export const parseLocationString = (location: string) => {
  const points = location.split('__');
  const startingPoint = points[0];
  const endingPoint = points.length > 1 ? points[points.length - 1] : null;
  const [team, player, x, y, playerType, success, momentType] = startingPoint.split('_');
  let endingTeam;
  let endingPlayer;
  let endX;
  let endY;
  let endingPlayerType;
  let endingSuccess;
  if (endingPoint) {
    [endingTeam, endingPlayer, endX, endY, endingPlayerType, endingSuccess] = endingPoint.split('_');
  }

  return {
    team,
    player,
    x: parseFloat(x),
    y: parseFloat(y),
    playerType,
    momentType,
    success: Boolean(parseInt(success, 10)),
    endingTeam,
    endingPlayer,
    endX: endX ? parseFloat(endX) : null,
    endY: endY ? parseFloat(endY) : null,
    endingPlayerType,
    endingSuccess: endingSuccess ? Boolean(parseInt(endingSuccess, 10)) : null,
  };
};

const SOCCER_PLAYER_FILTER_KEYS = {
  SCOUT_TEAM: {
    INVOLVED_ATHLETES: SoccerStatKey.ScoutTeamInvolvedAthletes,
    ATHLETE_SUBJECT: SoccerStatKey.ScoutTeamAthleteSubject,
    ATHLETE_INCLUDED: SoccerStatKey.ScoutTeamAthleteIncluded,
  },
  OPPONENT_TEAM: {
    INVOLVED_ATHLETES: SoccerStatKey.OpponentTeamInvolvedAthletes,
    ATHLETE_SUBJECT: SoccerStatKey.OpponentTeamAthleteSubject,
    ATHLETE_INCLUDED: SoccerStatKey.OpponentTeamAthleteIncluded,
  },
};

const getSoccerPlayerFilterKeys = (teamContext: string | undefined): string[] => {
  if (!teamContext)
    return [
      ...Object.values(SOCCER_PLAYER_FILTER_KEYS.SCOUT_TEAM),
      ...Object.values(SOCCER_PLAYER_FILTER_KEYS.OPPONENT_TEAM),
    ];

  const teamKeys = teamContext === '1' ? SOCCER_PLAYER_FILTER_KEYS.SCOUT_TEAM : SOCCER_PLAYER_FILTER_KEYS.OPPONENT_TEAM;
  return [teamKeys.INVOLVED_ATHLETES];
};

const getKeyStatFilters = (clipFilters: Filter[], teamContext: string | undefined) => {
  const teamFilter = teamContext ? (v: string) => v.startsWith(teamContext) : () => true;

  return {
    primaryKeyStatsFilter: clipFilters
      .map((f) => ({
        ...f,
        values: f.values.filter((v) => isSoccerPrimaryKeyStatFilter(v.toString()) && teamFilter(v.toString())),
      }))
      .filter((f) => f.values.length > 0),
    secondaryKeyStatsFilter: clipFilters
      .map((f) => ({
        ...f,
        values: f.values.filter((v) => isSoccerSecondaryKeyStatFilter(v.toString()) && teamFilter(v.toString())),
      }))
      .filter((f) => f.values.length > 0),
  };
};

const filterStatsByClassification = (
  stats: Stat[],
  primaryKeyStatsFilter: Filter[],
  secondaryKeyStatsFilter: Filter[],
  getClassification: (stat: Stat) => string,
  unsortedActiveMoments?: SoccerTaggingMoment[]
): Stat[] => {
  // If no key stat filters, return all zones
  if (primaryKeyStatsFilter.length === 0 && secondaryKeyStatsFilter.length === 0) {
    return stats;
  }

  // If only primary filters
  if (primaryKeyStatsFilter.length > 0 && secondaryKeyStatsFilter.length === 0) {
    return stats.filter((stat) => getClassification(stat) === ZoneClassification.Primary);
  }

  // If only secondary filters
  if (secondaryKeyStatsFilter.length > 0 && primaryKeyStatsFilter.length === 0) {
    return stats.filter((stat) => getClassification(stat) === ZoneClassification.Secondary);
  }

  const filterValueToMomentTypes: Record<string, string[]> = {
    [SoccerStatKey.ScoutTeamGoals]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamAssists]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamShotsOnTarget]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamShotsOffTarget]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamShotsBlocked]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamSavedShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.ScoutTeamSuccessfulCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.ScoutTeamUnsuccessfulCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.ScoutTeamPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.ScoutTeamSuccessfulPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.ScoutTeamUnsuccessfulPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.ScoutTeamReceptions]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross],
    [SoccerStatKey.ScoutTeamReceptionsFromPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.ScoutTeamReceptionsFromCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.ScoutTeamInterceptions]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross],
    [SoccerStatKey.ScoutTeamInterceptedCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.ScoutTeamInterceptedPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.ScoutTeamBlocks]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross, SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamBlockedCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.ScoutTeamBlockedPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.ScoutTeamBlockedShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamSaves]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.ScoutTeamGoalkeeperKicks]: [SoccerMomentTypes.Goalkeeping],
    [SoccerStatKey.ScoutTeamGoalkeeperThrows]: [SoccerMomentTypes.Goalkeeping],
    [SoccerStatKey.ScoutTeamOffsides]: [SoccerMomentTypes.Offside],
    [SoccerStatKey.ScoutTeamFouls]: [SoccerMomentTypes.Foul],
    [SoccerStatKey.ScoutTeamYellowCards]: [SoccerMomentTypes.Foul],
    [SoccerStatKey.ScoutTeamRedCards]: [SoccerMomentTypes.Foul],
    [SoccerStatKey.OpponentTeamGoals]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamAssists]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamShotsOnTarget]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamShotsOffTarget]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamShotsBlocked]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamSavedShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.OpponentTeamSuccessfulCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.OpponentTeamUnsuccessfulCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.OpponentTeamPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.OpponentTeamSuccessfulPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.OpponentTeamUnsuccessfulPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.OpponentTeamReceptions]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross],
    [SoccerStatKey.OpponentTeamReceptionsFromPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.OpponentTeamReceptionsFromCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.OpponentTeamInterceptions]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross],
    [SoccerStatKey.OpponentTeamInterceptedCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.OpponentTeamInterceptedPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.OpponentTeamBlocks]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross, SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamBlockedCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.OpponentTeamBlockedPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.OpponentTeamBlockedShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamSaves]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.OpponentTeamGoalkeeperKicks]: [SoccerMomentTypes.Goalkeeping],
    [SoccerStatKey.OpponentTeamGoalkeeperThrows]: [SoccerMomentTypes.Goalkeeping],
    [SoccerStatKey.OpponentTeamOffsides]: [SoccerMomentTypes.Offside],
    [SoccerStatKey.OpponentTeamFouls]: [SoccerMomentTypes.Foul],
    [SoccerStatKey.OpponentTeamYellowCards]: [SoccerMomentTypes.Foul],
    [SoccerStatKey.OpponentTeamRedCards]: [SoccerMomentTypes.Foul],
    [SoccerStatKey.AllGoals]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllAssists]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllShotsOnTarget]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllOffTargetShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllShotsBlocked]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllSavedShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.AllSuccessfulCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.AllUnsuccessfulCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.AllPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.AllSuccessfulPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.AllUnsuccessfulPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.AllReceptions]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross],
    [SoccerStatKey.AllReceptionsFromPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.AllReceptionsFromCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.AllBallOuts]: [SoccerMomentTypes.BallOut],
    [SoccerStatKey.AllInterceptions]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross],
    [SoccerStatKey.AllInterceptedCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.AllInterceptedPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.AllBlocks]: [SoccerMomentTypes.Pass, SoccerMomentTypes.Cross, SoccerMomentTypes.Shot],
    [SoccerStatKey.AllBlockedCrosses]: [SoccerMomentTypes.Cross],
    [SoccerStatKey.AllBlockedPasses]: [SoccerMomentTypes.Pass],
    [SoccerStatKey.AllBlockedShots]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllSaves]: [SoccerMomentTypes.Shot],
    [SoccerStatKey.AllGoalkeeperKicks]: [SoccerMomentTypes.Goalkeeping],
    [SoccerStatKey.AllGoalkeeperThrows]: [SoccerMomentTypes.Goalkeeping],
    [SoccerStatKey.AllOffsides]: [SoccerMomentTypes.Offside],
    [SoccerStatKey.AllFouls]: [SoccerMomentTypes.Foul],
    [SoccerStatKey.AllYellowCards]: [SoccerMomentTypes.Foul],
    [SoccerStatKey.AllRedCards]: [SoccerMomentTypes.Foul],
  };

  // If both types of filters
  return stats.filter((stat) => {
    const statMoment = unsortedActiveMoments?.find((moment) => stat.momentIds.includes(moment.id));
    const classification = getClassification(stat);

    let includedByPrimaryFilters = false;
    if (classification === ZoneClassification.Primary) {
      const primaryFilterMomentTypes = primaryKeyStatsFilter
        .flatMap((filter) => filter.values)
        .flatMap((value) => filterValueToMomentTypes[String(value)] ?? [])
        .filter(isDefined);

      includedByPrimaryFilters = primaryFilterMomentTypes.includes(
        statMoment?.setPieceSubType ? statMoment.setPieceSubType : (statMoment?.type ?? '')
      );
    }

    let includedBySecondaryFilters = false;
    if (classification === ZoneClassification.Secondary) {
      const secondaryFilterMomentTypes = secondaryKeyStatsFilter
        .flatMap((filter) => filter.values)
        .flatMap((value) => filterValueToMomentTypes[String(value)] ?? [])
        .filter(isDefined);

      includedBySecondaryFilters = secondaryFilterMomentTypes.includes(
        statMoment?.setPieceSubType ? statMoment.setPieceSubType : (statMoment?.type ?? '')
      );
    }

    return includedByPrimaryFilters || includedBySecondaryFilters || !statMoment;
  });
};

export const getPlayerZoneStats = (
  clipFilters: Filter[],
  playerZoneStats?: Stat[],
  teamContext?: string,
  unsortedActiveMoments?: SoccerTaggingMoment[]
): Stat[] => {
  // Get relevant player filters based on team context
  const relevantPlayerFilterKeys = getSoccerPlayerFilterKeys(teamContext);
  const playerFilters = clipFilters.filter((f) => relevantPlayerFilterKeys.includes(f.key));

  // Get key stat filters
  const { primaryKeyStatsFilter, secondaryKeyStatsFilter } = getKeyStatFilters(clipFilters, teamContext);

  // Get initial player zones
  let playerZones = playerZoneStats?.flat() || [];

  // Apply player filters if any exist
  if (playerFilters.length) {
    const subjectFilters = playerFilters.filter(
      (f) => f.key === SoccerStatKey.ScoutTeamAthleteSubject || f.key === SoccerStatKey.OpponentTeamAthleteSubject
    );
    const filtersToApply = subjectFilters.length ? subjectFilters : playerFilters;
    playerZones = playerZones?.filter((pz) => {
      const playerZoneInfo = getPlayerZoneInfo(pz.label.toString());
      return filtersToApply.some((filter) => filter.values.includes(playerZoneInfo.player));
    });
  }

  // Apply key stat filters
  return filterStatsByClassification(
    playerZones,
    primaryKeyStatsFilter,
    secondaryKeyStatsFilter,
    (stat) => getPlayerZoneInfo(stat.label.toString()).zoneClassification,
    unsortedActiveMoments
  );
};

export const getFilteredPoints = (
  pointsStats: Stat[],
  clipFilters: Filter[],
  teamContext: string | undefined,
  unsortedActiveMoments?: SoccerTaggingMoment[]
): Stat[] => {
  // Get player filter based on team context
  const relevantPlayerFilterKeys = getSoccerPlayerFilterKeys(teamContext);
  const playerFilters = clipFilters.filter((f) => relevantPlayerFilterKeys.includes(f.key));

  // Get key stat filters
  const { primaryKeyStatsFilter, secondaryKeyStatsFilter } = getKeyStatFilters(clipFilters, teamContext);

  // Get initial points with value > 0
  let points = pointsStats.flat().filter((stat) => stat.value > 0);

  // Apply player filter if it exists
  if (playerFilters.length) {
    points = points.filter((stat) => {
      const { player } = parseLocationString(stat.label.toString());
      return playerFilters.some((filter) => filter.values.includes(player));
    });
  }

  const subjectFilter = playerFilters.find((f) => isAthleteSubjectFilter(f.key));
  if (subjectFilter) {
    points = points.filter((stat) => {
      const { player } = parseLocationString(stat.label.toString());
      return subjectFilter.values.includes(player);
    });
  }

  // Apply classification filters
  return filterStatsByClassification(
    points,
    primaryKeyStatsFilter,
    secondaryKeyStatsFilter,
    (stat) => {
      const { playerType = '' } = parseLocationString(stat.label.toString());
      return playerType;
    },
    unsortedActiveMoments
  );
};

// gets the filtered points for V3 moments with the original coordinates
export const getFilteredPointsForV3 = (
  pointsStats: Stat[],
  clipFilters: Filter[],
  teamContext: string | undefined,
  allMoments?: SoccerTaggingMoment[]
): Stat[] => {
  const filteredStats = getFilteredPoints(pointsStats, clipFilters, teamContext, allMoments);

  return filteredStats.map((stat) => {
    const moment = allMoments?.find((m) => stat.momentIds.includes(m.id));

    const originalX = moment?.originalShotLocationX;
    const originalY = moment?.originalShotLocationY;

    if (originalX && originalY) {
      const { x, y } = parseLocationString(stat.label.toString());
      const newLabel = stat.label
        .toString()
        .replace(
          `${x.toFixed(4)}_${y.toFixed(4)}`,
          `${parseFloat(originalX).toFixed(4)}_${parseFloat(originalY).toFixed(4)}`
        );

      return {
        ...stat,
        label: newLabel,
      };
    }

    return stat;
  });
};

export interface SoccerPitchOutlinePaths {
  outline: string;
  centerMarker: string;
  penaltyMarkerOne: string;
  penaltyMarkerTwo: string;
}

export function getSoccerPitchSvgPaths(
  pitchDimensions: SoccerPitchDimensionsMeters,
  origin: SoccerPitchPoint,
  pitchSidesBorderPadding: number,
  pitchTopBorderPadding: number,
  isHalfPitch: boolean = false
): SoccerPitchOutlinePaths {
  // Constants for pitch dimensions in Meters
  const pitchWidth = pitchDimensions.pitchWidth;
  const pitchHeight = isHalfPitch ? pitchDimensions.pitchHeight / 2 : pitchDimensions.pitchHeight;
  const penaltyBoxWidth = pitchDimensions.penaltyBoxWidth;
  const penaltyBoxHeight = pitchDimensions.penaltyBoxHeight;
  const sixYardBoxWidth = pitchDimensions.sixYardBoxWidth;
  const sixYardBoxHeight = pitchDimensions.sixYardBoxHeight;
  const goalWidth = pitchDimensions.goalWidth;
  const goalDepth = pitchDimensions.goalDepth;
  const centerCircleRadius = pitchDimensions.centerCircleRadius;
  const penaltyArcRadius = pitchDimensions.penaltyArcRadius;
  const penaltyMarkerSize = pitchDimensions.penaltyMarkerSize;
  const cornerRadius = pitchDimensions.cornerRadius;
  const centerDotRadius = pitchDimensions.centerDotRadius;

  // Extract origin coordinates
  let [originX, originY] = origin;
  originY += pitchTopBorderPadding;
  originX += pitchSidesBorderPadding;

  // Initialize the path string
  let path = '';

  // Draw pitch outline
  path += `M ${originX} ${originY} `;
  path += `H ${originX + pitchWidth} `;
  path += `V ${originY + pitchHeight} `;
  path += `H ${originX} `;
  path += `V ${originY} `;

  // Draw penalty boxes
  path += `M ${originX + (pitchWidth - penaltyBoxWidth) / 2} ${originY} `;
  path += `V ${originY + penaltyBoxHeight} `;
  path += `H ${originX + (pitchWidth + penaltyBoxWidth) / 2} `;
  path += `V ${originY} `;

  if (!isHalfPitch) {
    // Draw bottom penalty box for full pitch
    path += `M ${originX + (pitchWidth - penaltyBoxWidth) / 2} ${originY + pitchHeight} `;
    path += `V ${originY + pitchHeight - penaltyBoxHeight} `;
    path += `H ${originX + (pitchWidth + penaltyBoxWidth) / 2} `;
    path += `V ${originY + pitchHeight} `;
  }

  // Draw six-yard boxes
  path += `M ${originX + (pitchWidth - sixYardBoxWidth) / 2} ${originY} `;
  path += `V ${originY + sixYardBoxHeight} `;
  path += `H ${originX + (pitchWidth + sixYardBoxWidth) / 2} `;
  path += `V ${originY} `;

  if (!isHalfPitch) {
    // Draw bottom six-yard box for full pitch
    path += `M ${originX + (pitchWidth - sixYardBoxWidth) / 2} ${originY + pitchHeight} `;
    path += `V ${originY + pitchHeight - sixYardBoxHeight} `;
    path += `H ${originX + (pitchWidth + sixYardBoxWidth) / 2} `;
    path += `V ${originY + pitchHeight} `;
  }

  // Draw goals
  const goalStartXTop = originX + (pitchWidth - goalWidth) / 2;
  const goalStartYTop = originY - goalDepth;

  path += `M ${goalStartXTop} ${goalStartYTop} `;
  path += `H ${goalStartXTop + goalWidth} `;
  path += `V ${goalStartYTop + goalDepth} `;
  path += `M ${goalStartXTop} ${originY} `;
  path += `V ${goalStartYTop} `;

  if (!isHalfPitch) {
    // Draw bottom goal for full pitch
    const goalStartXBottom = originX + (pitchWidth - goalWidth) / 2;
    const goalStartYBottom = originY + pitchHeight + goalDepth;

    path += `M ${goalStartXBottom} ${goalStartYBottom} `;
    path += `H ${goalStartXBottom + goalWidth} `;
    path += `V ${goalStartYBottom - goalDepth} `;
    path += `M ${goalStartXBottom} ${originY + pitchHeight}`;
    path += `V ${goalStartYBottom} `;
  }

  // Draw half line (only for full pitch)
  if (!isHalfPitch) {
    path += `M ${originX} ${originY + pitchHeight / 2} `;
    path += `H ${originX + pitchWidth} `;
  }

  // Draw center circle
  if (isHalfPitch) {
    // Draw center circle (top half only)
    path += `M ${originX + pitchWidth / 2 - centerCircleRadius} ${originY + pitchHeight} `;
    path += `A ${centerCircleRadius} ${centerCircleRadius} 0 0 1 ${originX + pitchWidth / 2 + centerCircleRadius} ${originY + pitchHeight} `;
  } else {
    // Draw full center circle
    const halfLineX = originX + pitchWidth / 2;
    path += `M ${halfLineX} ${originY + pitchHeight / 2} `;
    path += `m -${centerCircleRadius} 0 `;
    path += `a ${centerCircleRadius} ${centerCircleRadius} 0 1 0 ${centerCircleRadius * 2} 0 `;
    path += `a ${centerCircleRadius} ${centerCircleRadius} 0 1 0 -${centerCircleRadius * 2} 0 `;
  }

  // Draw penalty arcs
  const penaltyMarkerX = originX + pitchWidth / 2;

  // Top penalty arc (facing towards the center of the field)
  const penaltyMarkerYTop = originY + 11; // 11 meters from goal line to penalty marker
  const penaltyBoxTopY = originY + penaltyBoxHeight;

  const arcStartTopX = penaltyMarkerX - Math.sqrt(penaltyArcRadius ** 2 - (penaltyBoxTopY - penaltyMarkerYTop) ** 2);
  const arcEndTopX = penaltyMarkerX + Math.sqrt(penaltyArcRadius ** 2 - (penaltyBoxTopY - penaltyMarkerYTop) ** 2);

  path += `M ${arcStartTopX} ${penaltyBoxTopY} `;
  path += `A ${penaltyArcRadius} ${penaltyArcRadius} 0 0 0 ${arcEndTopX} ${penaltyBoxTopY} `;

  if (!isHalfPitch) {
    // Bottom penalty arc (facing towards the center of the field)
    const penaltyMarkerYBottom = originY + pitchHeight - 11;
    const penaltyBoxBottomY = originY + pitchHeight - penaltyBoxHeight;

    const arcStartBottomX =
      penaltyMarkerX - Math.sqrt(penaltyArcRadius ** 2 - (penaltyBoxBottomY - penaltyMarkerYBottom) ** 2);
    const arcEndBottomX =
      penaltyMarkerX + Math.sqrt(penaltyArcRadius ** 2 - (penaltyBoxBottomY - penaltyMarkerYBottom) ** 2);

    path += `M ${arcStartBottomX} ${penaltyBoxBottomY} `;
    path += `A ${penaltyArcRadius} ${penaltyArcRadius} 0 0 1 ${arcEndBottomX} ${penaltyBoxBottomY} `;
  }

  // Draw penalty markers
  let penaltyMarkerOnePath = '';
  penaltyMarkerOnePath += `M ${penaltyMarkerX + penaltyMarkerSize / 2} ${penaltyMarkerYTop - penaltyMarkerSize / 2} `;
  penaltyMarkerOnePath += `m -${penaltyMarkerSize} 0 `;
  penaltyMarkerOnePath += `a ${penaltyMarkerSize} ${penaltyMarkerSize} 0 1 0 ${penaltyMarkerSize * 2} 0 `;
  penaltyMarkerOnePath += `a ${penaltyMarkerSize} ${penaltyMarkerSize} 0 1 0 -${penaltyMarkerSize * 2} 0 `;
  penaltyMarkerOnePath += 'Z';

  let penaltyMarkerTwoPath = '';
  if (!isHalfPitch) {
    // Draw second penalty marker for full pitch
    const penaltyMarkerYBottom = originY + pitchHeight - 11;
    penaltyMarkerTwoPath += `M ${penaltyMarkerX + penaltyMarkerSize / 2} ${penaltyMarkerYBottom - penaltyMarkerSize / 2} `;
    penaltyMarkerTwoPath += `m -${penaltyMarkerSize} 0 `;
    penaltyMarkerTwoPath += `a ${penaltyMarkerSize} ${penaltyMarkerSize} 0 1 0 ${penaltyMarkerSize * 2} 0 `;
    penaltyMarkerTwoPath += `a ${penaltyMarkerSize} ${penaltyMarkerSize} 0 1 0 -${penaltyMarkerSize * 2} 0 `;
    penaltyMarkerTwoPath += 'Z';
  }

  // Draw the center marker
  let centerMarkerPath = '';
  if (isHalfPitch) {
    // Center marker at the bottom edge of the half pitch
    centerMarkerPath += `M ${penaltyMarkerX + penaltyMarkerSize / 2} ${originY + pitchHeight} `;
  } else {
    // Center marker at the center of the full pitch
    centerMarkerPath += `M ${penaltyMarkerX + penaltyMarkerSize / 2} ${originY + pitchHeight / 2} `;
  }
  centerMarkerPath += `m -${centerDotRadius} 0 `;
  centerMarkerPath += `a ${centerDotRadius} ${centerDotRadius} 0 1 0 ${centerDotRadius * 2} 0 `;
  centerMarkerPath += `a ${centerDotRadius} ${centerDotRadius} 0 1 0 -${centerDotRadius * 2} 0 `;
  centerMarkerPath += `Z`;

  // Draw rounded corner lines for corner kicks
  path += `M ${originX} ${originY + cornerRadius} `;
  path += `A ${cornerRadius} ${cornerRadius} 0 0 0 ${originX + cornerRadius} ${originY} `;
  path += `M ${originX + pitchWidth} ${originY + cornerRadius} `;
  path += `A ${cornerRadius} ${cornerRadius} 0 0 1 ${originX + pitchWidth - cornerRadius} ${originY} `;

  if (!isHalfPitch) {
    // Draw bottom corners for full pitch
    path += `M ${originX} ${originY + pitchHeight - cornerRadius} `;
    path += `A ${cornerRadius} ${cornerRadius} 0 0 1 ${originX + cornerRadius} ${originY + pitchHeight} `;
    path += `M ${originX + pitchWidth} ${originY + pitchHeight - cornerRadius} `;
    path += `A ${cornerRadius} ${cornerRadius} 0 0 0 ${originX + pitchWidth - cornerRadius} ${originY + pitchHeight}`;
  }

  return {
    outline: path,
    centerMarker: centerMarkerPath,
    penaltyMarkerOne: penaltyMarkerOnePath,
    penaltyMarkerTwo: penaltyMarkerTwoPath,
  };
}

export function interpolateColor(color1: string, color2: string, value: number, maxValue: number) {
  // Convert hex colors to RGB
  const r1 = parseInt(color1.substring(1, 3), 16);
  const g1 = parseInt(color1.substring(3, 5), 16);
  const b1 = parseInt(color1.substring(5, 7), 16);

  const r2 = parseInt(color2.substring(1, 3), 16);
  const g2 = parseInt(color2.substring(3, 5), 16);
  const b2 = parseInt(color2.substring(5, 7), 16);

  // Interpolate RGB components
  const r = Math.round(r1 + (r2 - r1) * (value / maxValue));
  const g = Math.round(g1 + (g2 - g1) * (value / maxValue));
  const b = Math.round(b1 + (b2 - b1) * (value / maxValue));

  // Convert interpolated RGB back to hex
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
}

export type ZoneDisplayData = {
  zone: number;
  row: number;
  column: number;
  color: string;
  value: number;
};

export const getZonesDisplayData = (playerZones: Stat[], total: number, columns: number, rows: number) => {
  const zoneDisplayData = [];
  const playerZonesGroupedByZone = new Map<string, number>();

  let highestZoneTotal = 0;

  for (const pz of playerZones) {
    const zone = getPlayerZoneInfo(pz.label.toString()).zone;
    const currentTotal = (playerZonesGroupedByZone.get(zone) ?? 0) + pz.value;
    playerZonesGroupedByZone.set(zone, currentTotal);
    highestZoneTotal = Math.max(highestZoneTotal, currentTotal);
  }

  const maxPercentage = total !== 0 ? Math.round((highestZoneTotal / total) * 100) : 100;

  for (let col = 0; col < columns; col++) {
    for (let row = 0; row < rows; row++) {
      const zone = SoccerPitch.getZoneFromRowAndCol(row, col).toString();
      const value = playerZonesGroupedByZone.get(zone) ?? 0;

      let percentage = total !== 0 ? (value / total) * 100.0 : 0;
      percentage = percentage < 1 && percentage !== 0 ? 0.5 : Math.round(percentage);

      const color = interpolateColor('#232A31', '#636C75', percentage, maxPercentage);
      zoneDisplayData.push({ zone, row, column: col, color, value: percentage });
    }
  }

  return zoneDisplayData;
};

export function getColumnWidths(numberOfColumns: number, columnPercentages: number[], width: number) {
  const columnWidths = [];
  for (let col = 0; col < numberOfColumns; col++) {
    columnWidths.push(columnPercentages[col] * width);
  }
  return columnWidths;
}

export interface BorderSummaryDisplayItem {
  type: 'rect' | 'text' | 'line' | 'path';
}

export interface BorderSummaryRect extends BorderSummaryDisplayItem {
  x: number;
  y: number;
  width: number;
  height: number;
  numberOfEvents: number;
  zones: string[];
  qaId: string;
}

export interface BorderSummaryText extends BorderSummaryDisplayItem {
  x: number;
  y: number;
  transform?: string;
  value: string;
  selected: boolean;
}

export interface BorderSummaryLine extends BorderSummaryDisplayItem {
  x1: number;
  x2: number;
  y1: number;
  y2: number;
}

export interface BorderSummaryPath extends BorderSummaryDisplayItem {
  d: string;
}

export interface BorderSummaryDisplayData {
  rects: BorderSummaryRect[];
  text: BorderSummaryLine[];
  lines: BorderSummaryLine[];
  paths: BorderSummaryPath[];
}

export function getBorderSummaryDisplayData(
  stats: Stat[],
  selectedZones: string[],
  qaIdPrefix?: string
): (BorderSummaryLine | BorderSummaryPath | BorderSummaryRect | BorderSummaryText)[] {
  enum SoccerPitchDivision {
    Third = 'third',
    Half = 'half',
  }

  const SoccerPitchThirds = ['Attacking Third Percent', 'Middle Third Percent', 'Defensive Third Percent'];
  const SoccerPitchHalves = ['Attacking Half Percent', 'Defensive Half Percent'];

  const {
    origin,
    pitchDimensions,
    numZoneColumns,
    numZoneRows,
    pitchColumnPercentages,
    pitchSidesBorderPadding,
    pitchTopBorderPadding,
  } = SoccerPitch;

  const totalNumberOfEvents = stats.reduce((accum, s) => accum + (s?.value ?? 0), 0) ?? 0;
  const zoneHeight = pitchDimensions.pitchHeight / numZoneRows;
  const columnWidths = getColumnWidths(numZoneColumns, pitchColumnPercentages, pitchDimensions.pitchWidth);

  const calculateEventCounts = (zones: string[]) =>
    zones.reduce((sum, zone) => {
      const zoneData = stats.filter((s) => getPlayerZoneInfo(String(s.label)).zone === zone);
      return sum + zoneData.reduce((acc, s) => acc + (s?.value ?? 0), 0);
    }, 0);

  const createSummaryElements = (type: SoccerPitchDivision) => {
    const elements: (BorderSummaryLine | BorderSummaryPath | BorderSummaryRect | BorderSummaryText)[] = [];
    const textOffset = type === SoccerPitchDivision.Third ? 1.3 : 1.67;
    const textX =
      type === SoccerPitchDivision.Third
        ? origin[0] + pitchSidesBorderPadding / 2 + textOffset
        : origin[0] + pitchSidesBorderPadding * textOffset + pitchDimensions.pitchWidth;
    let numberOfEvents = 0;
    let selectableZones: string[] = [];

    for (let row = 0; row < numZoneRows; row++) {
      const zones = Array.from({ length: numZoneColumns }, (_, col) =>
        SoccerPitch.getZoneFromRowAndCol(row, col).toString()
      );
      const zonesWithData = zones.filter((zone) =>
        stats.some((playerZone) => playerZone.value !== 0 && getPlayerZoneInfo(String(playerZone.label)).zone === zone)
      );
      const eventCount = calculateEventCounts(zonesWithData);
      if (eventCount !== 0) {
        selectableZones.push(...zonesWithData);
      }
      numberOfEvents += eventCount;

      if (
        row % (type === SoccerPitchDivision.Third ? 2 : numZoneRows / 2) ===
        (type === SoccerPitchDivision.Third ? 1 : numZoneRows / 2 - 1)
      ) {
        const textY =
          origin[1] +
          pitchTopBorderPadding +
          (type === SoccerPitchDivision.Third
            ? row * zoneHeight
            : pitchDimensions.pitchHeight * (row === numZoneRows / 2 - 1 ? 0.25 : 0.75));
        const selected = selectableZones.length > 0 && selectableZones.every((z) => selectedZones.includes(z));

        const zonesForClick = [...selectableZones];
        elements.push({
          type: 'rect',
          x:
            type === SoccerPitchDivision.Third
              ? origin[0]
              : origin[0] + pitchSidesBorderPadding + pitchDimensions.pitchWidth,
          y: pitchTopBorderPadding + (row - (type === SoccerPitchDivision.Third ? 1 : 2)) * zoneHeight,
          width: pitchSidesBorderPadding,
          height: zoneHeight * (type === SoccerPitchDivision.Third ? 2 : 3),
          numberOfEvents,
          zones: zonesForClick,
          qaId: `${qaIdPrefix}${type === SoccerPitchDivision.Third ? SoccerPitchThirds[Math.floor(row / Math.ceil(numZoneRows / 3))] : SoccerPitchHalves[Math.floor(row / (numZoneRows / 2))]}`,
        });

        elements.push({
          type: 'text',
          x: textX,
          y: textY,
          transform: `rotate(-90 ${textX} ${textY})`,
          value: `${totalNumberOfEvents > 0 ? Math.round((numberOfEvents / totalNumberOfEvents) * 100) : 0}%`,
          selected,
        });

        if (row > 1 && type === SoccerPitchDivision.Third) {
          elements.push({
            type: 'line',
            x1: origin[0] + pitchSidesBorderPadding / 3.1,
            x2: origin[0] + pitchSidesBorderPadding * 0.68,
            y1: pitchTopBorderPadding + (row - 1) * zoneHeight,
            y2: pitchTopBorderPadding + (row - 1) * zoneHeight,
          });
        }
        numberOfEvents = 0;
        selectableZones = [];
      }
    }
    return elements;
  };

  const createColumnSummary = () => {
    const elements: (BorderSummaryLine | BorderSummaryPath | BorderSummaryRect | BorderSummaryText)[] = [];
    columnWidths.forEach((width, col) => {
      const cumulativeWidth = columnWidths.slice(0, col + 1).reduce((acc, w) => acc + w, 0);
      const zones = Array.from({ length: numZoneRows }, (_, row) =>
        SoccerPitch.getZoneFromRowAndCol(row, col).toString()
      );
      const zonesWithData = zones.filter((zone) =>
        stats.some((playerZone) => playerZone.value !== 0 && getPlayerZoneInfo(String(playerZone.label)).zone === zone)
      );
      const columnTotal = calculateEventCounts(zonesWithData);
      const selected = zonesWithData.length > 0 && zonesWithData.every((z) => selectedZones.includes(z));

      if (col !== numZoneColumns - 1) {
        const lineX = pitchSidesBorderPadding + cumulativeWidth;
        elements.push({
          type: 'line',
          x1: lineX,
          x2: lineX,
          y1: origin[1] + pitchTopBorderPadding / 2.7,
          y2: origin[1] + pitchTopBorderPadding * 0.6,
        });
      }

      elements.push(
        {
          type: 'rect',
          x: origin[0] + pitchSidesBorderPadding + cumulativeWidth - width,
          y: origin[1],
          width: width,
          height: pitchTopBorderPadding,
          numberOfEvents: columnTotal,
          zones: zonesWithData,
          qaId: `${qaIdPrefix}Column ${col} Percent`,
        },
        {
          type: 'text',
          x: origin[0] + pitchSidesBorderPadding + cumulativeWidth - width / 2,
          y: origin[1] + pitchTopBorderPadding / 2 + 1.3,
          selected,
          value: `${totalNumberOfEvents > 0 ? Math.round((columnTotal / totalNumberOfEvents) * 100) : 0}%`,
        }
      );
    });
    return elements;
  };

  const createAttackingDirectionArrow = (): BorderSummaryPath => {
    const offset = 1.5;
    const x = origin[0] + pitchSidesBorderPadding * offset + pitchDimensions.pitchWidth;

    // Draw the arrow
    let path = '';
    path += `M ${x} ${origin[1] + pitchTopBorderPadding + pitchDimensions.pitchHeight / 2 + zoneHeight / 2}`;
    path += `L ${x} ${origin[1] + pitchTopBorderPadding + pitchDimensions.pitchHeight / 2 - zoneHeight / 2}`;
    path += `l ${-offset} ${offset}`;
    path += `M ${x} ${origin[1] + pitchTopBorderPadding + pitchDimensions.pitchHeight / 2 - zoneHeight / 2}`;
    path += `l ${offset} ${offset}`;
    return { type: 'path', d: path };
  };

  return [
    ...createSummaryElements(SoccerPitchDivision.Third),
    ...createColumnSummary(),
    ...createSummaryElements(SoccerPitchDivision.Half),
    createAttackingDirectionArrow(),
  ];
}

export function getShotChartColumnPercentages(
  stats: Stat[],
  selectedZones: string[],
  qaIdPrefix?: string
): (BorderSummaryLine | BorderSummaryPath | BorderSummaryRect | BorderSummaryText)[] {
  const {
    origin,
    pitchDimensions,
    numZoneColumns,
    pitchColumnPercentages,
    pitchSidesBorderPadding,
    pitchTopBorderPadding,
  } = SoccerPitch;

  // Filter out secondary player stats (assists) so that we only calculate percentages for shots
  const primaryShotStats = stats.filter((stat) => {
    const { playerType } = parseLocationString(String(stat.label));
    return playerType === 'p';
  });

  const totalNumberOfEvents = primaryShotStats.reduce((accum, s) => accum + (s?.value ?? 0), 0) ?? 0;

  const columnWidths = getColumnWidths(numZoneColumns, pitchColumnPercentages, pitchDimensions.pitchWidth);

  const normalizeCoordinatesForHalfPitch = (x: number, y: number) => {
    const normalizedCoordinates = { x: x, y: -y };
    // If shot is in the "other half" of the pitch (y > 0), clamp it to the midline
    if (normalizedCoordinates.y > 0) {
      return { x: normalizedCoordinates.x, y: 0 };
    }
    return normalizedCoordinates;
  };

  const createColumnSummary = () => {
    const elements: (BorderSummaryLine | BorderSummaryPath | BorderSummaryRect | BorderSummaryText)[] = [];

    const shotColumnMap = new Map<string, number>();

    // Calculate where each column ends (boundaries) to determine which column a shot belongs to
    const columnBoundaries = columnWidths.reduce((boundaries, width) => {
      const previousBoundary = boundaries.length > 0 ? boundaries[boundaries.length - 1] : 0;
      boundaries.push(previousBoundary + (width || 0));
      return boundaries;
    }, [] as number[]);

    const totalWidth = columnBoundaries[columnBoundaries.length - 1] || 1;

    primaryShotStats.forEach((stat) => {
      const { x, y } = parseLocationString(String(stat.label));
      const normalizedCoordinates = normalizeCoordinatesForHalfPitch(x, y);

      // Convert normalized X coordinate to a position within the total width
      // X coordinates range from -1 to 1, we need to map this to a position within the total width
      const normalizedX = (normalizedCoordinates.x + 1) / 2; // Convert from [-1,1] to [0,1]
      const positionInWidth = normalizedX * totalWidth;

      // Find which column this position falls into
      let columnIndex = 0;
      for (let i = 0; i < columnBoundaries.length; i++) {
        if (positionInWidth <= columnBoundaries[i]) {
          columnIndex = i;
          break;
        }
      }

      const finalColumnIndex = Math.max(0, Math.min(numZoneColumns - 1, columnIndex));

      shotColumnMap.set(String(stat.label), finalColumnIndex);
    });

    // Calculate the percentages for each column
    const columnData = columnWidths
      .map((width, col) => {
        if (width === undefined || width <= 0) {
          return null;
        }

        // Find all shots that belong to this column
        const shotsInColumn = primaryShotStats.filter((stat) => {
          const shotColumn = shotColumnMap.get(String(stat.label));
          return shotColumn === col;
        });

        const columnTotal = shotsInColumn.reduce((sum, stat) => sum + (stat.value ?? 0), 0);
        const exactPercentage = totalNumberOfEvents > 0 ? (columnTotal / totalNumberOfEvents) * 100 : 0;
        return { width, col, zonesWithData: [], columnTotal, exactPercentage };
      })
      .filter((data) => data !== null && data !== undefined);

    const percentages = columnData.map((data) => {
      const wholeNumber = Math.floor(data.exactPercentage) ?? 0;
      const remainder = data.exactPercentage - wholeNumber;
      return { ...data, wholeNumber, remainder };
    });

    const percentagesSum = percentages.reduce((sum, p) => sum + p?.wholeNumber, 0);
    const needToRoundUp = 100 - percentagesSum;

    // Sort by remainder and round up the largest remainders to ensure percentages sum to 100%
    const columnsWithData = percentages.filter((p) => p.columnTotal > 0);
    columnsWithData.sort((a, b) => b.remainder - a.remainder);

    // Round up if needToRoundUp is positive and we have columns with data to round up
    if (needToRoundUp > 0 && columnsWithData.length > 0) {
      for (let i = 0; i < Math.min(needToRoundUp, columnsWithData.length); i++) {
        columnsWithData[i].wholeNumber++;
      }
    }

    percentages.forEach(({ width, col, zonesWithData, columnTotal, wholeNumber }) => {
      const cumulativeWidth = columnWidths.slice(0, col + 1).reduce((acc, w) => acc + (w || 0), 0);
      const selected = zonesWithData.length > 0 && zonesWithData.every((z) => selectedZones.includes(z));

      if (col !== numZoneColumns - 1) {
        const lineX = pitchSidesBorderPadding + cumulativeWidth;
        elements.push({
          type: 'line',
          x1: lineX,
          x2: lineX,
          y1: origin[1] + pitchTopBorderPadding / 2.7,
          y2: origin[1] + pitchTopBorderPadding * 0.6,
        });
      }

      elements.push(
        {
          type: 'rect',
          x: origin[0] + pitchSidesBorderPadding + cumulativeWidth - width,
          y: origin[1],
          width: width,
          height: pitchTopBorderPadding,
          numberOfEvents: columnTotal,
          zones: zonesWithData,
          qaId: `${qaIdPrefix}Column ${col} Percent`,
        },
        {
          type: 'text',
          x: origin[0] + pitchSidesBorderPadding + cumulativeWidth - width / 2,
          y: origin[1] + pitchTopBorderPadding / 2 + 1.3,
          selected,
          value: `${wholeNumber}%`,
        }
      );
    });
    return elements;
  };

  return createColumnSummary();
}

// Data is in the format -1 to 1 for x and y we need to convert to pixel space
const viewBoxPadding = 8;
const topLeftPoint = { x: viewBoxPadding + 0.5, y: viewBoxPadding + 4 };

export function getSoccerPitchSvgPointFromDataPoint(dataPoint: { x: number; y: number }) {
  const x = (dataPoint.x + 1) * (SoccerPitch.pitchDimensions.pitchWidth / 2) + topLeftPoint.x;
  const y = (dataPoint.y + 1) * (SoccerPitch.pitchDimensions.pitchHeight / 2) + topLeftPoint.y;
  return { x, y };
}

export function getSoccerPitchDrawableLine(
  label: string,
  teamContext?: string
): {
  startPoint: { x: number; y: number };
  endPoint?: { x: number; y: number };
  success: boolean;
} {
  const { x, y, success, endX, endY, endingTeam, playerType, momentType } = parseLocationString(label);
  const drawableEndX =
    (teamContext && endingTeam !== teamContext) || (momentType === SoccerMomentTypes.Shot && playerType === 'p')
      ? null
      : endX;
  const drawableEndY =
    (teamContext && endingTeam !== teamContext) || (momentType === SoccerMomentTypes.Shot && playerType === 'p')
      ? null
      : endY;
  const startPoint = { x, y };

  const endPoint =
    drawableEndX && drawableEndY
      ? {
          x: success ? drawableEndX : -drawableEndX,
          y: success ? drawableEndY : -drawableEndY,
        }
      : undefined;

  if (!endPoint) {
    return {
      startPoint,
      success,
    };
  }

  return {
    startPoint: playerType === 's' && momentType !== SoccerMomentTypes.Shot ? endPoint : startPoint,
    endPoint: playerType === 's' && momentType !== SoccerMomentTypes.Shot ? startPoint : endPoint,
    success,
  };
}
