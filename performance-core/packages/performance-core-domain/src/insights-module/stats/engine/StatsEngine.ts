import type { TeamDisplay } from 'insights-module/domain/team';

import {
  BaseballTaggingMoment,
  BasketballTaggingMoment,
  HudlTaggingMoment,
  IceHockeyTaggingMoment,
  MomentDataType,
  SoccerTaggingMoment,
  SoftballTaggingMoment,
  SportClass,
  type VirtualClip,
  VolleyballTaggingMoment,
  WrestlingTaggingMoment,
} from '@hudl/hudl-domain-types';
import { format } from '@hudl/platform-i18n-adapter';

import { notEmpty } from '../../../library-module/utils/notEmpty';
import { type Filter, FilterMode, type RosterAthlete } from '../../domain';
import { ignoreOwnFilters } from '../../utils/cardFilteringUtils';
import { isKeyStats } from '../../utils/keyStatsUtils';
import { isScoutTeamStatKey } from '../../utils/urlParamsUtils';
import { isShotChart } from '../basketball/utils';
import { calculateAverage, type CalculateCardStatsParams, type FilterOptions } from '../calculateCardStats';
import { type CardStats, FilterUIType, type Stat, type StatDefinition, type StatKey } from '../domain/Stats';
import getAllStatDefinitions from '../getAllStatDefinitions';
import getStatDefinition from '../getStatDefinition';
import { defaultStatHasGroupKey, teamToDisplayName } from '../shared/utils';
import { isSoccerShotChart } from '../soccer/utils';
import { isLossByFilter, isPointsPerPeriod, isWinByFilter, isWinOrLossFilter } from '../wrestling/utils';
import type { ClipSet } from './builder/ComputedStats';
import { ComputedStatsFactory } from './builder/ComputedStatsFactory';
import type { DisplayedStats } from './builder/DisplayedStats';
import type { AggregateStatsWithClips, MomentOf, PropertiesOf, StatDefinitionsBase } from './builder/StatDefinitions';
import type { StatsClip, StatsClips } from './builder/StatsContent';
import { baseballStatsMap, createBaseballStats } from './sports/baseball/createBaseballStats';
import { basketballStatsMap, createBasketballStats } from './sports/basketball/createBasketballStats';
import { createIceHockeyStats, IceHockeyStatsMap } from './sports/iceHockey/createIceHockeyStats';
import { createBaseStats, DefaultStatsMap } from './sports/shared/createBaseStats';
import { createSoccerStats, SoccerStatsMap } from './sports/soccer/createSoccerStats';
import { createSoftballStats, softballStatsMap } from './sports/softball/createSoftballStats';
import { StatsSport } from './sports/StatsSport';
import { createVolleyballStats, volleyballStatsMap } from './sports/volleyball/createVolleyballStats';
import { createWrestlingStats, wrestlingStatsMap } from './sports/wrestling/createWrestlingStats';
import { assertNever } from './utility/assertNever';
import { isDefined } from './utility/isDefined';
import { mapGetOrSet } from './utility/mapGetOrSet';
import { intersectMany, unionMany } from './utility/set-functions';

const isStatisticFilter = (key: string) =>
  isKeyStats(key) ||
  isShotChart(key) ||
  isSoccerShotChart(key) ||
  isPointsPerPeriod(key) ||
  isLossByFilter(key) ||
  isWinByFilter(key) ||
  isWinOrLossFilter(key);

export interface GetFilteredClipsOptions {
  sport: SportClass;
  unfilteredClips: VirtualClip[];
  selectedFilters: Filter[];
  mappedFilters?: (selectedFilters: Filter[]) => Filter[];
  useTableCellFilter?: boolean;
}

export type FilterOption = {
  label: string;
  value: string;
  isDisabled?: boolean;
  clipIds?: string[];
};

export type OptionGroup = {
  label: string;
  subLabel?: string;
  options: FilterOption[];
  clipIds: string[];
};

export type FilterDefinition = {
  key: string;
  label: string | null;
  hideLabel?: boolean;
  type: FilterUIType;
  filterOptions: FilterOption[] | OptionGroup[];
  selectedOptions: FilterOption[];
};

export type FilterGroup = {
  label: string;
  key: string;
  order?: number;
  filters: FilterDefinition[];
};

export interface GetFacetedFiltersOptions {
  sport: SportClass;
  roster: Map<string, RosterAthlete>;
  allTeams?: Map<string, TeamDisplay>;
  filterOptions: FilterOptions;
}

export class StatsEngine<TStatDefinitions extends StatDefinitionsBase> {
  private factory: ComputedStatsFactory<TStatDefinitions>;

  private constructor(
    definitions: TStatDefinitions,
    private readonly castClips: (clips: VirtualClip[]) => StatsClips<MomentOf<TStatDefinitions>>,
    private readonly displayMap: DisplayedStats<typeof definitions>
  ) {
    this.factory = new ComputedStatsFactory(definitions);
  }

  getFilteredClips(options: GetFilteredClipsOptions): VirtualClip[] {
    // Get the set of clips that each stat or filterKey/value combo represents
    // Return the intersection of those sets of clips
    const computedStats = this.factory.getComputedStats({
      unfilteredClips: this.castClips(options.unfilteredClips),
    });
    // If provided a filter map, we can replace the names of filters that will be used to generate the filtered clip set.
    let mappedFilters = options.selectedFilters;
    if (options.mappedFilters) {
      mappedFilters = options.mappedFilters(options.selectedFilters);
    }

    const clipSetsForFilterValues = Array.from(mappedFilters, (filter) => {
      // Special case for 'table' filter - skip unless useTableCellFilter is enabled
      if (filter.key === 'table' && options.useTableCellFilter === false) {
        return undefined;
      }

      if (filter.key === 'table') {
        const aggregates = computedStats.aggregates;

        // Parse filter values in the form "rowKey-ColumnKey"
        const clipSets = filter.values
          .flat()
          .map((value) => {
            if (typeof value !== 'string') {
              return undefined;
            }

            // Parse the rowKey-ColumnKey format
            const parts = value.split('-');
            if (parts.length !== 2) {
              return undefined;
            }

            const [rowKey, columnKey] = parts;

            // Get the column from aggregates
            const columnData = aggregates[columnKey];
            if (!columnData) {
              return undefined;
            }

            // Get the aggregate data for the rowKey
            const aggregateData = columnData.get(rowKey);
            if (!aggregateData) {
              return undefined;
            }

            // Extract clips from the aggregate data
            if (aggregateData && typeof aggregateData === 'object' && 'clips' in aggregateData) {
              const enhancedValue = aggregateData as AggregateStatsWithClips<any>;
              return new Set(enhancedValue.clips);
            }

            return undefined;
          })
          .filter(isDefined);

        return unionMany(clipSets);
      }

      if (isStatisticFilter(filter.key)) {
        const filterValueSets = filter.values
          .flat()
          .map((value) => {
            if (typeof value !== 'string') {
              return undefined;
            }
            const statInfo = this.displayMap[value];
            if (!statInfo) {
              return undefined;
            }
            if (statInfo.type !== 'statistic') {
              // eslint-disable-next-line no-console
              console.warn(
                `Expecting filter value for filter key '${filter.key}' to be a statistic. Value='${value}' `
              );
              return undefined;
            }

            const filterSet = computedStats.filters[statInfo.filter];
            const setsToIntersect = [filterSet];

            // Each value in the key stats card is a statistic with a statDefinition.
            // If that stat definition has a filterMap, we need to intersect the filter values for each stat
            // with the mapped filter.
            const statDefinition = getStatDefinition(value, options.sport);

            if (statDefinition.mappedFilters) {
              const mappedSelectedFilters = statDefinition.mappedFilters(options.selectedFilters);

              // Split out the filter keys and filter values
              const mappedFilterKeys = mappedSelectedFilters.map((f) => f.key).filter(isDefined);
              const mappedFilterValues = new Set(mappedSelectedFilters.flatMap((f) => f.values));

              // Get all of the properties for the mapped filters
              const properties = mappedFilterKeys.map((key) => computedStats.properties[key]).filter(isDefined);

              // Only include properites present in the filter values
              const filteredProperties = properties.map(
                (map) => new Map([...map].filter(([key]) => mappedFilterValues.has(key)))
              );

              // Get clip sets and compute intersection
              // Union clipsets that come from the same filter
              const clipSets = filteredProperties
                .map((map) => Array.from(map.values()))
                .map(unionMany)
                .filter(isDefined);

              // Intersect the clipsets
              const intersectSet = clipSets.length > 0 ? intersectMany(clipSets) : undefined;

              if (intersectSet) {
                setsToIntersect.push(intersectSet);
              }
            }
            const intersectedFilterSet = intersectMany(setsToIntersect);

            return intersectedFilterSet;
          })
          .filter(isDefined);
        return unionMany(filterValueSets);
      }

      const propertyKey = filter.key;
      const propertyValues = computedStats.properties[propertyKey];

      if (!filter.values?.length) {
        return undefined;
      }

      const setForAllValuesForFilterKey = filter.values
        .map((v) => {
          return propertyValues?.get(v as PropertiesOf<TStatDefinitions>[string]); // TODO fix this type assertion
        })
        .filter(isDefined);
      return unionMany(setForAllValuesForFilterKey) ?? new Set();
    }).filter(isDefined);
    const finalClipSet = intersectMany(clipSetsForFilterValues);
    if (!finalClipSet) {
      return options.unfilteredClips;
    }
    // The final set of clips is ordered based on insertion order of its items, which varies depending on the various
    // unions/intersections performed. Our desired ordering is to match that of the unfiltered set.
    // We could use a sort algorithm here, but filtering will let us do this with O(n) complexity, which is lower than
    // we would usually get from a sorting algorithm.
    return options.unfilteredClips.filter((clip) => finalClipSet.has(clip));
  }

  // CO: lseper - actual filtering logic of clips based on filters
  getFacetedFilters(options: GetFacetedFiltersOptions): FilterGroup[] {
    // CO: lseper - allTeams could maybe be extended to include team name to display as filterGroup label?
    const { filterOptions, roster, allTeams } = options;
    const statDefinitions = getAllStatDefinitions(options.sport);
    const computedStats = this.factory.getComputedStats({
      unfilteredClips: this.castClips(filterOptions.unfilteredClips),
      filteredClips: this.castClips(filterOptions.unfilteredClips),
    });
    function getAvailableOptions(sd: StatDefinition, selectedFilters: Filter[]): Stat[] {
      const propertyValues = computedStats.properties[sd.key];
      const statValues = Array.from(
        propertyValues.entries(),
        ([valueString, clipSet]): Stat => ({
          label: valueString,
          value: sd.dataValueFromClips ? sd.dataValueFromClips(clipSet, valueString, selectedFilters) : clipSet.size,
          virtualClipIds: [],
          momentIds: [],
        })
      );

      if (sd.permissibleLabels) {
        sd.permissibleLabels.forEach((permissibleLabel) => {
          if (!propertyValues.has(permissibleLabel)) {
            statValues.push({
              label: permissibleLabel,
              value: 0,
              virtualClipIds: [],
              momentIds: [],
            });
          }
        });
      }

      if (!propertyValues) {
        return [];
      }
      return statValues;
    }

    function getSelectedOptions(key: string) {
      return filterOptions.filters.filter((filter) => filter.key === key).flatMap((filter) => filter.values);
    }

    function mergeFilters(filters: Filter[], newFilter: Filter): Filter[] {
      // If a filter with the same key already exists, merge the values
      const existingFilterIndex = filters.findIndex((f) => f.key === newFilter.key);
      if (existingFilterIndex !== -1) {
        const existingFilter = filters[existingFilterIndex];
        const newFilters = [
          ...filters.slice(0, existingFilterIndex),
          {
            ...existingFilter,
            values: newFilter.values,
          },
          ...filters.slice(existingFilterIndex + 1),
        ];
        return newFilters;
      }
      const newFilters = [...filters, newFilter];
      return newFilters;
    }

    const statDefinitionsWithFilterConfig = statDefinitions.values().filter((sd) => {
      return sd.filterConfig !== undefined;
    });

    const statDefinitionsByGroupKey = new Map<string, StatDefinition[]>();
    statDefinitionsWithFilterConfig.forEach((sd) => {
      const groupKey = sd.filterConfig?.group?.key;
      if (groupKey) {
        if (!statDefinitionsByGroupKey.has(groupKey)) {
          statDefinitionsByGroupKey.set(groupKey, []);
        }
        statDefinitionsByGroupKey.get(groupKey)?.push(sd);
      }
    });

    const statDefinitionsByGroup = Array.from(statDefinitionsByGroupKey.entries()).map(([groupKey, sd]) => {
      const filterGroup = sd[0].filterConfig?.group;
      return {
        key: groupKey,
        label: filterGroup?.i18nKey ? format(filterGroup.i18nKey) : filterGroup?.label || '',
        order: filterGroup?.order,
        statDefinitions: sd,
      };
    });
    return statDefinitionsByGroup
      .sort((a, b) => (a.order || 1) - (b.order || 1))
      .map((group) => {
        return {
          label: group.label,
          key: group.key,
          filters: group.statDefinitions.map((sd) => {
            const optionGroups = sd.getGroups
              ? sd.getGroups(getAvailableOptions(sd, filterOptions.filters))
              : undefined;
            return {
              key: sd.key,
              label: sd.filterConfig?.hideLabel ? null : sd.friendlyName,
              type: sd.filterConfig?.type ? sd.filterConfig.type : FilterUIType.MultiSelect,
              filterOptions: optionGroups
                ? [...optionGroups].sort(sd.sortGroups).map((groupKey) => {
                    const availableOptions = getAvailableOptions(sd, filterOptions.filters)
                      .filter((stat) => {
                        return defaultStatHasGroupKey(stat, groupKey);
                      })
                      .sort(sd.sortFunc)
                      .sort(sd.rosterAwareSortFunc ? sd.rosterAwareSortFunc(roster) : () => 0)
                      .map((option): FilterOption => {
                        const clipsMatching = this.getFilteredClips({
                          sport: options.sport,
                          unfilteredClips: filterOptions.unfilteredClips,
                          selectedFilters: mergeFilters(filterOptions.filters, {
                            key: sd.key,
                            values: [option.label],
                            mode: FilterMode.Default,
                          }),
                          useTableCellFilter: true,
                        });
                        const isDisabled = clipsMatching.length === 0 ? true : false;

                        return {
                          label:
                            sd.statLabelToFriendlyName(option.label, false, roster, undefined, undefined, allTeams) ||
                            (option.label as string),
                          value: option.label as string,
                          clipIds: clipsMatching.map((c) => c.id),
                          isDisabled,
                        };
                      });

                    return {
                      label:
                        sd.groupLabelToFriendlyName?.(groupKey) ??
                        teamToDisplayName(groupKey, false, roster, undefined, undefined, allTeams),
                      subLabel: sd.groupLabelToSubLabel?.(groupKey),
                      options: availableOptions,
                      clipIds: Array.from(new Set(availableOptions.flatMap((o) => o.clipIds).filter(notEmpty))),
                    };
                  })
                : getAvailableOptions(sd, filterOptions.filters)
                    .sort(sd.rosterAwareSortFunc ? sd.rosterAwareSortFunc(roster) : (a, b) => b.value - a.value)
                    .sort(sd.sortFunc)
                    .map((option) => {
                      const clipsMatching = this.getFilteredClips({
                        sport: options.sport,
                        unfilteredClips: filterOptions.unfilteredClips,
                        selectedFilters: mergeFilters(filterOptions.filters, {
                          key: sd.key,
                          values: [option.label],
                          mode: FilterMode.Default,
                        }),
                        useTableCellFilter: false,
                      });
                      const isDisabled = clipsMatching.length === 0 ? true : false;

                      return {
                        label:
                          sd.statLabelToFriendlyName(option.label, false, roster, undefined, undefined, allTeams) ||
                          (option.label as string),
                        value: option.label as string,
                        clipIds: clipsMatching.map((c) => c.id),
                        isDisabled,
                      };
                    }),
              selectedOptions: getSelectedOptions(sd.key).map((option) => ({
                label:
                  sd.statLabelToFriendlyName(option, false, roster, undefined, undefined, allTeams) ||
                  (option as string),
                value: option as string,
              })),
            };
          }),
        };
      });
  }

  getStatsForCard(options: CalculateCardStatsParams): CardStats {
    let latestAverage = undefined;

    const { cardDefinition, filterOptions, cardStatDefinitions, hasScoutVideo, subject, includeMomentAndClipIds } =
      options;

    const statsMapEntries = cardDefinition.statDefinitions.map((statDefinitionKey): [StatKey, Stat[]] => {
      // TODO: In the future (likely: after we add support for American Football),
      // we should bake this logic into the stat calculation framework
      // and avoid the need for card-specific code.
      //
      // For now, the KeyStats card type can be supplied a config to choose whether or not to
      // take its own current filters into account while performing stat calculation.
      let selectedFilters = filterOptions.filters;
      if (!cardDefinition?.keyStatsConfig?.useOwnFilters) {
        // TODO: Improve caching logic to support the exclusion of a card's "own filters" while calculating stats.
        // https://hudl-jira.atlassian.net/browse/CBUPT-16178
        // This assignment will always yield a new instance of selectedFilters, breaking the caching within.
        // This filtered array could be cached using a WeakMap and a pattern similar to what we're doing in
        // ComputedStatsFactory
        // https://github.com/hudl/hudl-frontends/blob/0dc2a61d9ea824a2ea0e6390ebb1790e0a211167/packages/insights-module/src/stats/engine/builder/ComputedStatsFactory.ts#L61-L83
        if (
          isShotChart(cardDefinition.key) ||
          isKeyStats(cardDefinition.key) ||
          isSoccerShotChart(cardDefinition.key)
        ) {
          // Shot Chart and key stats cards needs to be handled separately since this is a unique case where one card represents multiple stats.
          // Therefore to support multi-selection without updating remaining zones/stats, we need to exclude the cardKey from the set of filters.
          selectedFilters = filterOptions.filters.filter((filter) => cardDefinition.key !== filter.key);
        } else if (ignoreOwnFilters(cardDefinition.key)) {
          // Ignore filters applied in the card when calculating the card's own stats
          selectedFilters = selectedFilters.filter(
            (filter) => !cardStatDefinitions?.map((sd) => sd.key).includes(filter.key)
          );
        } else {
          selectedFilters = filterOptions.filters.filter((filter) => statDefinitionKey !== filter.key);
        }
      }

      const cardStatDefinition = cardStatDefinitions?.find(
        (statDefinition) => statDefinition.key === statDefinitionKey
      );

      const filteredClips = this.getFilteredClips({
        sport: options.sport,
        unfilteredClips: filterOptions.unfilteredClips,
        selectedFilters,
        mappedFilters: cardStatDefinition?.mappedFilters,
        useTableCellFilter: false,
      });

      // TODO cache this call
      const computedStats = this.factory.getComputedStats({
        unfilteredClips: this.castClips(filterOptions.unfilteredClips),
        filteredClips: cardDefinition.useUnfilteredClipsOnly ? undefined : this.castClips(filteredClips),
      });
      // This might need to be a different type for just the exposed values?
      // const key = statDefinitionKey as keyof TPropertyTypes;
      const statInfo = this.displayMap[statDefinitionKey];

      if (!statInfo) {
        // eslint-disable-next-line no-console
        console.error(`Requested stats for unknown key: ${statDefinitionKey}`);
        return [statDefinitionKey, []];
      }
      const type = statInfo.type;
      let dataValueFromClips: ((clips: ClipSet, label: string, selectedFilters: Filter[]) => number) | undefined;
      switch (type) {
        case 'property':
          const propertyValues = computedStats.properties[statInfo.propertyKey];
          if (cardStatDefinitions) {
            if (cardStatDefinition) {
              dataValueFromClips = cardStatDefinition.dataValueFromClips;
            }
          }
          let statValues = Array.from(
            propertyValues.entries(),
            ([valueString, clipSet]): Stat => ({
              label: valueString,
              value: dataValueFromClips ? dataValueFromClips(clipSet, valueString, selectedFilters) : clipSet.size,
              virtualClipIds: includeMomentAndClipIds
                ? Array.from(clipSet).map((clip) => (clip as VirtualClip).id)
                : [],
              momentIds: includeMomentAndClipIds
                ? Array.from(clipSet).flatMap((clip) => (clip as VirtualClip).moments.map((moment) => moment.id))
                : [],
            })
          );

          if (cardStatDefinitions) {
            if (cardStatDefinition) {
              const { permissibleLabels } = cardStatDefinition;
              let permissibleLabelsToUse = permissibleLabels;
              if (cardStatDefinition.dataType === MomentDataType.Boolean) {
                permissibleLabelsToUse = [true, false];
              }
              permissibleLabelsToUse?.forEach((permissibleLabel) => {
                if (!propertyValues.has(permissibleLabel)) {
                  statValues.push({
                    label: permissibleLabel,
                    value: 0,
                    virtualClipIds: includeMomentAndClipIds
                      ? Array.from(propertyValues.get(permissibleLabel) ?? []).map((clip) => (clip as VirtualClip).id)
                      : [],
                    momentIds: includeMomentAndClipIds
                      ? Array.from(propertyValues.get(permissibleLabel) ?? []).flatMap((clip) =>
                          (clip as VirtualClip).moments.map((moment) => moment.id)
                        )
                      : [],
                  });
                }
              });
              statValues.sort((a, b) => {
                if (permissibleLabelsToUse?.length) {
                  return permissibleLabelsToUse.indexOf(a.label) - permissibleLabelsToUse.indexOf(b.label);
                }
                return b.value - a.value;
              });

              if (cardStatDefinition.momentPropertyForAverageCalculation) {
                const statForTeam = isScoutTeamStatKey(cardDefinition.key) ? '1' : '2';
                const sport = options.sport;
                latestAverage = calculateAverage(
                  filteredClips,
                  cardStatDefinition.momentPropertyForAverageCalculation,
                  undefined,
                  undefined,
                  cardStatDefinition.momentPropertyToGroupBy,
                  statForTeam,
                  sport
                );
              }
            }

            if (
              cardStatDefinition?.hideStat &&
              cardStatDefinition?.hideStat(filteredClips, undefined, hasScoutVideo, subject)
            ) {
              statValues.forEach((statValue) => {
                statValue.value = 0;
              });
            }
          }

          // This technically _could_ be re-sorting the list from above, but as noted that might not be working. And this would only
          // be for explicitly-sorted values.
          if (statInfo.sortFunc) {
            statValues = statInfo.sortFunc(statValues);
          }

          return [statDefinitionKey, statValues];

        case 'statistic':
          let statValue: number = computedStats.statistics[statInfo.statisticKey];
          const displayAsPercent = statInfo.format === 'percent';
          if (displayAsPercent) {
            statValue *= 100;
            // Could round other stats if needed, but so far should just be percentages.
            statValue = Math.round(statValue);
          }

          let shouldHideStat = false;
          if (cardStatDefinitions) {
            if (cardStatDefinition) {
              const { hideStat } = cardStatDefinition;
              dataValueFromClips = cardStatDefinition.dataValueFromClips;
              shouldHideStat =
                (hideStat && hideStat(filterOptions.unfilteredClips, statValue, hasScoutVideo, subject)) ?? false;
            }
          }

          // Retrieve all stat values from the list of secondary keys, defaulted to undefined.
          // TODO: Ideally we'll want each stat to have it's own representation instead of adding it as an optional property
          // https://hudl-jira.atlassian.net/browse/CBUPT-20037
          let secondaryValues: number[] | undefined = undefined;
          if (statInfo.secondaryStatisticKeys) {
            secondaryValues = statInfo.secondaryStatisticKeys.map((key) => computedStats.statistics[key]);
          }
          return [
            statDefinitionKey,
            [
              {
                // The key to use in filtering
                label: statDefinitionKey,
                value: dataValueFromClips
                  ? dataValueFromClips(new Set(filteredClips), statDefinitionKey, selectedFilters)
                  : statValue,
                virtualClipIds: [],
                momentIds: [],
                displayAsPercent,
                secondaryValues,
                hidden: shouldHideStat,
              },
            ],
          ];
        case 'aggregate':
          const aggregateStats = computedStats.aggregates[statInfo.aggregateKey];
          const stats = [...aggregateStats].map(([label, value]) => {
            // Handle both old format (number) and new format (object with count and clips)
            if (typeof value === 'number') {
              return {
                label,
                value,
                virtualClipIds: [],
                momentIds: [],
              };
            } else if (value && typeof value === 'object' && 'count' in value && 'clips' in value) {
              // Enhanced format with clips tracking
              const enhancedValue = value as AggregateStatsWithClips<any>;
              return {
                label,
                value: enhancedValue.count,
                virtualClipIds: enhancedValue.clips.map((clip: any) => (clip as VirtualClip).id),
                momentIds: enhancedValue.clips.flatMap((clip: any) =>
                  (clip as VirtualClip).moments.map((moment: any) => moment.id)
                ),
              };
            } else {
              // Fallback for unexpected format
              return {
                label,
                value: 0,
                virtualClipIds: [],
                momentIds: [],
              };
            }
          });

          return [statDefinitionKey, stats];
        default:
          return assertNever(type);
      }
    });

    const stats = new Map<StatKey, Stat[]>(statsMapEntries);
    let hasData = false;
    if (stats.size) {
      hasData = Array.from(stats.values()).some((stat) =>
        stat.some((prop) => prop.value || prop.secondaryValues?.some((v) => v > 0))
      );
    }

    const cardStats: CardStats = {
      key: cardDefinition.key,
      stats,
      hasNoData: !hasData,
      overallAverage: cardDefinition.statDefinitions.length === 1 ? latestAverage : undefined,
    };

    return cardStats;
  }

  private static engineCache = new Map<StatsSport, StatsEngine<any>>();

  static create(sport?: StatsSport): StatsEngine<any> {
    return mapGetOrSet(this.engineCache, sport, () => this.createInternal(sport));
  }

  private static createInternal(sport?: StatsSport): StatsEngine<any> {
    switch (sport) {
      case StatsSport.Basketball:
        return new StatsEngine(
          createBasketballStats().definitions,
          // FUTURE: clean this up and remove the type assertion
          (unfilteredClips) =>
            unfilteredClips as StatsClip<HudlTaggingMoment>[] as StatsClip<BasketballTaggingMoment>[],
          basketballStatsMap
        );
      case StatsSport.Baseball:
        return new StatsEngine(
          createBaseballStats().definitions,
          // FUTURE: clean this up and remove the type assertion
          (unfilteredClips) => unfilteredClips as StatsClip<HudlTaggingMoment>[] as StatsClip<BaseballTaggingMoment>[],
          baseballStatsMap
        );
      case StatsSport.Softball:
        return new StatsEngine(
          createSoftballStats().definitions,
          // FUTURE: clean this up and remove the type assertion
          (unfilteredClips) => unfilteredClips as StatsClip<HudlTaggingMoment>[] as StatsClip<SoftballTaggingMoment>[],
          softballStatsMap
        );
      case StatsSport.Wrestling:
        return new StatsEngine(
          createWrestlingStats().definitions,
          (unfilteredClips) => unfilteredClips as StatsClip<HudlTaggingMoment>[] as StatsClip<WrestlingTaggingMoment>[],
          wrestlingStatsMap
        );
      case StatsSport.Soccer:
        return new StatsEngine(
          createSoccerStats().definitions,
          (unfilteredClips) => unfilteredClips as StatsClip<HudlTaggingMoment>[] as StatsClip<SoccerTaggingMoment>[],
          SoccerStatsMap
        );
      case StatsSport.IceHockey:
        return new StatsEngine(
          createIceHockeyStats().definitions,
          (unfilteredClips) => unfilteredClips as StatsClip<HudlTaggingMoment>[] as StatsClip<IceHockeyTaggingMoment>[],
          { ...DefaultStatsMap, ...IceHockeyStatsMap }
        );
      case StatsSport.Volleyball:
        return new StatsEngine(
          createVolleyballStats().definitions,
          (unfilteredClips) =>
            unfilteredClips as StatsClip<HudlTaggingMoment>[] as StatsClip<VolleyballTaggingMoment>[],
          volleyballStatsMap
        );
      default:
        return new StatsEngine(
          createBaseStats().definitions,
          (unfilteredClips) => unfilteredClips as StatsClip<HudlTaggingMoment>[],
          DefaultStatsMap
        );
    }
  }
}
