import type { DataChipAvatar, DataChipItem, DataChipSegmentInfo, MomentDataChipContext } from 'data-chip/dataChipTypes';
import { notEmpty } from 'library-module/utils/notEmpty';
import { formatAthleteNameAndJerseyDisplay, getAthleteDisplay } from 'timeline-module/utils/formatAthleteDisplay';

import {
  createSoccerDataChipLabels,
  formatSoccerAdditionalLabels,
  getSecondaryPlayer,
  type HudlTaggingMoment,
  SoccerMomentTypes,
  SoccerTaggingMoment,
  Sport,
} from '@hudl/hudl-domain-types';
import { format } from '@hudl/platform-i18n-adapter';

import {
  ResultSuccessMapping,
  SoccerGoalkeeper,
  SuccessStatuses,
} from '../../../../../../../shared/domain-types/dist/moments/hudl-tagging/soccer/Constants';
import type { RosterAthlete } from '../../../insights-module/domain';

export const getSoccerDataChips = (moments: HudlTaggingMoment[], context: MomentDataChipContext): DataChipItem[] => {
  return moments.map((moment) => getSoccerDataChip(moment, context));
};

const getSecondaryPlayerDisplay = (
  secondaryPlayer: string | null,
  roster: Map<string, RosterAthlete>
): string | null => {
  if (!secondaryPlayer) return null;
  const athlete = roster.get(secondaryPlayer);

  if (!athlete) {
    if (!isNaN(Number(secondaryPlayer))) {
      return `#${secondaryPlayer}`; // if secondaryPlayer is a number, it is likely a jersey number
    }
    if (secondaryPlayer === SoccerGoalkeeper) {
      return format('domainTypes.moment.soccer.v1.jersey.goalkeeper');
    }
    return `${secondaryPlayer}`; // if secondaryPlayer is not a number, it is likely a word (e.g. "Unknown")
  }

  if (athlete.jersey) {
    return `#${athlete.jersey}`;
  }
  if (athlete.firstName || athlete.lastName) {
    // if the athlete has a first or last name, format it
    return formatAthleteNameAndJerseyDisplay(athlete);
  }
  return null;
};

// determine the avatar for the secondary player based on the moment type and result
const getIsSecondaryAthleteOnSameTeam = (soccerMoment: SoccerTaggingMoment): boolean => {
  // fouls are a special case where the secondary player is always on the other team
  if (soccerMoment.type === SoccerMomentTypes.Foul) {
    return false;
  }

  const result = soccerMoment.result;
  if (!result) {
    return true; // if there is no result, we cannot determine the team, so default to true
  }
  return ResultSuccessMapping[result] === SuccessStatuses.Successful;
};

export const getSoccerDataChip = (
  moment: HudlTaggingMoment,
  { teamInContext, opponentInContext, athletes }: MomentDataChipContext
): DataChipItem => {
  const roster = new Map(athletes?.map((athlete) => [athlete.participantId, athlete]) ?? []);
  const primaryTeam = moment.team === '1' ? teamInContext : opponentInContext;
  const secondaryTeam = moment.team === '1' ? opponentInContext : teamInContext;

  const soccerMoment = moment as SoccerTaggingMoment;
  const athleteInfo = getAthleteDisplay(soccerMoment, roster, { displayUnknowns: true });

  const chips: DataChipSegmentInfo[] = [];
  // get the secondary player
  const secondaryPlayer = getSecondaryPlayer(soccerMoment);
  const secondaryAthleteDisplay = getSecondaryPlayerDisplay(secondaryPlayer, roster);
  // generate the labels for the data chip
  const labels = createSoccerDataChipLabels(soccerMoment);
  // determine if the secondary avatar is the same team as the primary chip
  const sameTeam = getIsSecondaryAthleteOnSameTeam(soccerMoment);
  let secondaryChip: DataChipSegmentInfo | undefined;
  // secondary chip avatar
  let avatar: DataChipAvatar | undefined;

  if (!sameTeam) {
    avatar = {
      imageUrl: secondaryTeam?.imageUrl,
      primaryColor: secondaryTeam?.primaryColor,
      sport: Sport.Soccer,
    };
  }

  if (labels[1] && secondaryAthleteDisplay) {
    secondaryChip = {
      text: labels[1], // if text is empty, it will not render the secondary chip
      additionalText: [secondaryAthleteDisplay],
      avatar,
    };
  }

  // additional text for the primary chip
  const additionalText = [athleteInfo].filter(notEmpty);

  // if we do not have a secondary chip, we want to show the result on the first chip
  if (!secondaryChip) {
    const momentResult = formatSoccerAdditionalLabels(soccerMoment);
    if (momentResult) {
      additionalText.unshift(...momentResult); // add the result to the beginning of the additional text
    }
  }

  chips.push({
    text: labels[0] ?? '',
    additionalText: additionalText,
    avatar: {
      imageUrl: primaryTeam?.imageUrl,
      primaryColor: primaryTeam?.primaryColor,
      sport: Sport.Soccer,
    },
  });

  secondaryChip && chips.push(secondaryChip);

  if (chips.length === 1) {
    return {
      id: moment.id,
      ...chips[0],
    };
  }

  return {
    id: moment.id,
    chips,
  };
};
