import type { TeamDisplay } from 'insights-module/domain/team';
import { notEmpty } from 'library-module/utils/notEmpty';
import type { TimelineConfig, TimelineTrackDataItem } from 'timeline-module/timelineTypes';

import { type VirtualClip, WrestlingConstants, WrestlingTaggingMoment } from '@hudl/hudl-domain-types';

import type { TimelineTrackData } from '../../timelineTypes';
import { defaultTeamOneColor, defaultTeamTwoColor, getFirstMoment, isValidNonSegmentClip } from '../../utils/helpers';
import { formatAthlete, formatMomentToReadableText } from './helpers';

export const wrestlingTimelineconfig: TimelineConfig = {
  defaultZoomIndex: 6,
  initialSelectionZoomPercentage: 0.5,
  zoomSnapPoints: [
    { visibleTimePercentage: 1, label: 'FT' },
    { visibleTimeMs: 3600000, label: '1h' },
    { visibleTimeMs: 1800000, label: '30m' },
    { visibleTimeMs: 1200000, label: '20m' },
    { visibleTimeMs: 600000, label: '10m' },
    { visibleTimeMs: 300000, label: '5m' },
    { visibleTimeMs: 120000, label: '2m' },
    { visibleTimeMs: 60000, label: '1m' },
    { visibleTimeMs: 30000, label: '30s' },
    { visibleTimeMs: 10000, label: '10s' },
    { visibleTimeMs: 5000, label: '5s' },
  ],
  minimumRangeSelectionMs: 1000,
  createTrackData: ({ filteredClips, teamInContext, opponentTeamsInContext }) => {
    const scoutTeamIsGreen =
      filteredClips?.find((c) => c.teamInfo)?.teamInfo?.greenTeamId === teamInContext?.teamId ? true : false;
    const teamOneData = getTrackDataForTeam(
      filteredClips,
      scoutTeamIsGreen ? WrestlingConstants.WrestlingTeam.Green : WrestlingConstants.WrestlingTeam.Red,
      teamInContext,
      defaultTeamOneColor
    );
    const teamTwoData = getTrackDataForTeam(
      filteredClips,
      !scoutTeamIsGreen ? WrestlingConstants.WrestlingTeam.Green : WrestlingConstants.WrestlingTeam.Red,
      teamInContext,
      defaultTeamTwoColor
    );
    const tracks: TimelineTrackData[] = [];
    if (teamOneData && teamOneData.length > 0) {
      tracks.push({
        id: 'scout',
        title: teamInContext?.schoolAbbreviation ?? 'SCOUT',
        expanded: true,
        selected: false,
        data: teamOneData || [],
        children: [],
      });
    }
    if (teamTwoData && teamTwoData.length > 0) {
      tracks.push({
        id: 'OPP',
        title:
          opponentTeamsInContext?.values().next().value?.schoolAbbreviation ??
          teamInContext?.schoolAbbreviation.concat(' Opponent') ??
          'OPP',
        expanded: true,
        selected: false,
        data: teamTwoData || [],
        children: [],
      });
    }
    return tracks;
  },
};

function getTrackDataForTeam(
  filteredClips: VirtualClip[] | undefined,
  wrestlingTeam: string,
  teamInContext: TeamDisplay | undefined,
  color: string
): TimelineTrackDataItem[] | undefined {
  return filteredClips
    ?.filter((clip) => {
      return (
        isValidNonSegmentClip(clip) &&
        getMomentAnkletColor(getFirstMoment(clip) as WrestlingTaggingMoment) === wrestlingTeam
      );
    })
    .map((clip): TimelineTrackDataItem => {
      const teamInContextIsRed = teamInContext?.teamId ? teamInContext.teamId === clip?.teamInfo?.redTeamId : false;
      const moment = getFirstMoment(clip) as WrestlingTaggingMoment;
      const primaryLabelTextItems = [formatMomentToReadableText(moment, teamInContextIsRed)];
      let player = null;

      if (moment.stoppageType == 'endOfPeriod' || moment.stoppageType == 'other') {
        player = null;
      } else if (getMomentAnkletColor(moment) == 'red') {
        player = moment.redName;
      } else {
        player = moment.greenName;
      }
      const score = moment.redScore + '–' + moment.greenScore;
      const wrestler = player && player.trim() !== '' ? formatAthlete(player) : undefined;
      const period = moment.wrestlingPeriod != null ? 'Period ' + moment.wrestlingPeriod.toString() : undefined;
      const weightClass = moment.weightClass && moment.weightClass.trim() !== '' ? moment.weightClass : undefined;
      const secondaryLabelTextItems = [score, wrestler, period, weightClass].filter(notEmpty);
      return {
        id: clip.id,
        startTimeMs: clip.startTimeMs,
        endTimeMs: clip.endTimeMs,
        color: color,
        primaryLabelTextItems: primaryLabelTextItems,
        secondaryLabelTextItems: secondaryLabelTextItems,
      };
    });
}

const getMomentAnkletColor = (moment: WrestlingTaggingMoment) => {
  let anklet: string | null = null;
  switch (moment.type) {
    case WrestlingConstants.MomentTypes.Point:
      anklet = moment.getFirstTagValue(WrestlingConstants.TagConstants.PointFor);
      break;
    case WrestlingConstants.MomentTypes.Penalty:
      anklet = moment.getFirstTagValue(WrestlingConstants.TagConstants.PenaltyAgainst);
      break;
    case WrestlingConstants.MomentTypes.Stoppage:
      if (
        moment.getFirstTagValue(WrestlingConstants.TagConstants.StoppageType) ===
        WrestlingConstants.StoppageType.EndOfMatch
      ) {
        anklet = moment.getFirstTagValue(WrestlingConstants.TagConstants.MatchWinner);
      } else {
        anklet = moment.getFirstTagValue(WrestlingConstants.TagConstants.StoppageTeam);
      }
      break;
  }
  return anklet ?? WrestlingConstants.WrestlingTeam.Green;
};
