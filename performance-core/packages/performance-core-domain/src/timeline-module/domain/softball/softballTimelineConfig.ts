import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'insights-module/domain';
import type { TimelineConfig, TimelineTrackDataItem } from 'timeline-module/timelineTypes';

import { BaseballSoftballTaggingMoment, SoftballTaggingMoment, type VirtualClip } from '@hudl/hudl-domain-types';
import { BaseballConstants, formatBaseballSoftballMomentTagValue } from '@hudl/hudl-domain-types';
import * as i18nAdapter from '@hudl/platform-i18n-adapter';

import { notEmpty } from '../../../library-module/utils/notEmpty';
import type { TimelineTrackData } from '../../timelineTypes';
import { defaultTeamOneColor, defaultTeamTwoColor, getFirstMoment } from '../../utils/helpers';

const { TagConstants, InningHalf } = BaseballConstants;
const { format } = i18nAdapter;

export const softballTimelineConfig: TimelineConfig = {
  defaultZoomIndex: 6,
  initialSelectionZoomPercentage: 0.5,
  zoomSnapPoints: [
    { visibleTimePercentage: 1, label: 'FT' },
    { visibleTimeMs: 3600000, label: '1h' },
    { visibleTimeMs: 1800000, label: '30m' },
    { visibleTimeMs: 1200000, label: '20m' },
    { visibleTimeMs: 600000, label: '10m' },
    { visibleTimeMs: 300000, label: '5m' },
    { visibleTimeMs: 120000, label: '2m' },
    { visibleTimeMs: 60000, label: '1m' },
    { visibleTimeMs: 30000, label: '30s' },
    { visibleTimeMs: 10000, label: '10s' },
    { visibleTimeMs: 5000, label: '5s' },
  ],
  minimumRangeSelectionMs: 1000,
  createTrackData: ({ filteredClips, teamInContext, opponentTeamsInContext, roster, colors }) => {
    const teamOneColor = colors?.teamOne ? colors.teamOne.toLowerCase() : defaultTeamOneColor;
    const teamTwoColor = colors?.teamTwo ? colors?.teamTwo.toLowerCase() : defaultTeamTwoColor;

    const teamOneData = getTrackDataForTeam(filteredClips, '1', teamOneColor, roster);
    const teamTwoData = getTrackDataForTeam(filteredClips, '2', teamTwoColor, roster);
    const tracks: TimelineTrackData[] = [];
    if (teamOneData && teamOneData.length > 0) {
      tracks.push({
        id: 'scout',
        title: teamInContext?.schoolAbbreviation ?? 'SCOUT',
        expanded: true,
        selected: false,
        data: teamOneData || [],
        children: [],
      });
    }
    if (teamTwoData && teamTwoData.length > 0) {
      tracks.push({
        id: 'OPP',
        title:
          opponentTeamsInContext?.values().next().value?.schoolAbbreviation ??
          teamInContext?.schoolAbbreviation.concat(' Opponent') ??
          'OPP',
        expanded: true,
        selected: false,
        data: teamTwoData || [],
        children: [],
      });
    }
    return tracks;
  },
};

function getTrackDataForTeam(
  filteredClips: VirtualClip[] | undefined,
  team: string,
  color: string,
  roster: Map<string, RosterAthlete> | undefined
): TimelineTrackDataItem[] | undefined {
  return filteredClips
    ?.filter(
      (clip) => getFirstMoment(clip) && (getFirstMoment(clip) as SoftballTaggingMoment).battingPlayerTeam === team
    )
    .map((clip): TimelineTrackDataItem => {
      const moment = getFirstMoment(clip) as BaseballSoftballTaggingMoment;

      const {
        pitchResult,
        pitchingPlayerUserId,
        pitchingPlayerJersey,
        battingPlayerUserId,
        battingPlayerJersey,
        inningHalf,
        inningCount,
        occupiedBases,
        ballCount,
        strikeCount,
        outCount,
        scoreTeamOne,
        scoreTeamTwo,
      } = moment;

      const pitchResultLabel = formatBaseballSoftballMomentTagValue(TagConstants.PitchResult, pitchResult ?? '');

      const pitcherLabel = getPitcherBatterLabel(pitchingPlayerUserId, pitchingPlayerJersey, roster, true);
      const batterLabel = getPitcherBatterLabel(battingPlayerUserId, battingPlayerJersey, roster, false);
      const inningLabel = getInningLabel(inningHalf, inningCount);
      const occupiedBasesLabel = getOccupiedBasesLabel(occupiedBases);
      const countLabel = getCountLabel(ballCount, strikeCount);
      const outLabel = getOutLabel(outCount);
      const scoreLabel = getScoreLabel(scoreTeamOne, scoreTeamTwo);

      const secondaryLabelTextItems = [
        pitcherLabel,
        batterLabel,
        inningLabel,
        occupiedBasesLabel,
        countLabel,
        outLabel,
        scoreLabel,
      ].filter(notEmpty);

      return {
        id: clip.id,
        startTimeMs: clip.startTimeMs,
        endTimeMs: clip.endTimeMs,
        color,
        primaryLabelTextItems: [pitchResultLabel ?? ''],
        secondaryLabelTextItems,
      };
    });
}

const getPitcherBatterLabel = (
  userId: string | null,
  jerseyNumber: string | null,
  roster: Map<string, RosterAthlete> | undefined,
  isPitcher: boolean
): string | undefined => {
  const preLabel = isPitcher ? 'P:' : 'B:';
  if (notEmpty(userId) && roster) {
    const athlete = roster.get(userId ?? '');
    if (athlete) {
      return `${preLabel} ${formatAthlete(athlete)}`;
    }
  }

  if (notEmpty(jerseyNumber) && jerseyNumber !== '') {
    return `${preLabel} #${jerseyNumber}`;
  }
  return;
};

const formatAthlete = (athlete: RosterAthlete): string => {
  const jersey = notEmpty(athlete?.jersey) ? `#${athlete.jersey}` : null;
  const firstInitial = athlete.firstName ? `${athlete.firstName[0]}.` : undefined;

  return [jersey, firstInitial, athlete.lastName].filter(notEmpty).join(' ');
};

const getInningLabel = (inningHalf: string | null, inningCount: number | null): string | undefined => {
  if (notEmpty(inningHalf) && notEmpty(inningCount)) {
    if (inningHalf === InningHalf.Top) {
      return `▲ ${inningCount}`;
    }
    return `▼ ${inningCount}`;
  }
};

const getOccupiedBasesLabel = (occupiedBases: string | null): string | undefined => {
  const i18nKey = `module.timeline.baseballSoftball.occupiedBases.${occupiedBases}`;
  return format(i18nKey) ?? undefined;
};

const getCountLabel = (ballCount: number | null, strikeCount: number | null): string | undefined => {
  if (notEmpty(ballCount) && notEmpty(strikeCount)) {
    return `${ballCount}–${strikeCount}`;
  }
};

const getOutLabel = (outCount: number | null): string | undefined => {
  if (notEmpty(outCount)) {
    return format('module.timeline.baseballSoftball.outs', { outs: outCount });
  }
};

const getScoreLabel = (scoreTeamOne: number | null, scoreTeamTwo: number | null): string | undefined => {
  if (notEmpty(scoreTeamOne) && notEmpty(scoreTeamTwo)) {
    return `R: ${scoreTeamOne}–${scoreTeamTwo}`;
  }
};
