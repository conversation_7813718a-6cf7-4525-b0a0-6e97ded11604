import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'insights-module/domain';
import type { TimelineConfig, TimelineTrackData, TimelineTrackDataItem } from 'timeline-module/timelineTypes';

import type { VirtualClip } from '@hudl/hudl-domain-types';
import { formatBasketballMomentLabel } from '@hudl/hudl-domain-types';
import type { BasketballTaggingMoment } from '@hudl/hudl-domain-types';

import { defaultTeamOneColor, defaultTeamTwoColor, getClipTeam, getFirstMoment } from '../../utils/helpers';
import { getPlayResult } from './helpers';
import { getAthleteDisplay } from './helpers';

export const basketballTimelineConfig: TimelineConfig = {
  defaultZoomIndex: 6,
  initialSelectionZoomPercentage: 0.5,
  zoomSnapPoints: [
    { visibleTimePercentage: 1, label: 'FT' },
    { visibleTimeMs: 3600000, label: '1h' },
    { visibleTimeMs: 1800000, label: '30m' },
    { visibleTimeMs: 1200000, label: '20m' },
    { visibleTimeMs: 600000, label: '10m' },
    { visibleTimeMs: 300000, label: '5m' },
    { visibleTimeMs: 120000, label: '2m' },
    { visibleTimeMs: 60000, label: '1m' },
    { visibleTimeMs: 30000, label: '30s' },
    { visibleTimeMs: 10000, label: '10s' },
    { visibleTimeMs: 5000, label: '5s' },
  ],
  minimumRangeSelectionMs: 1000,
  createTrackData: ({ filteredClips, teamInContext, opponentTeamsInContext, roster, colors }) => {
    const teamOneColor = colors?.teamOne ? colors.teamOne.toLowerCase() : defaultTeamOneColor;
    const teamTwoColor = colors?.teamTwo ? colors?.teamTwo.toLowerCase() : defaultTeamTwoColor;

    const teamOneData = getTrackDataForTeam(filteredClips, '1', teamOneColor, roster);
    const teamTwoData = getTrackDataForTeam(filteredClips, '2', teamTwoColor, roster);
    const tracks: TimelineTrackData[] = [];

    if (teamOneData && teamOneData.length > 0) {
      tracks.push({
        id: 'scout',
        title: teamInContext?.schoolAbbreviation ?? 'SCOUT',
        expanded: true,
        selected: false,
        data: teamOneData || [],
        children: [],
      });
    }
    if (teamTwoData && teamTwoData.length > 0) {
      tracks.push({
        id: 'OPP',
        title:
          opponentTeamsInContext?.values().next().value?.schoolAbbreviation ??
          teamInContext?.schoolAbbreviation.concat(' Opponent') ??
          'OPP',
        expanded: true,
        selected: false,
        data: teamTwoData || [],
        children: [],
      });
    }
    return tracks;
  },
};

function getTrackDataForTeam(
  filteredClips: VirtualClip[] | undefined,
  team: string,
  color: string,
  roster: Map<string, RosterAthlete> | undefined
): TimelineTrackDataItem[] | undefined {
  return filteredClips
    ?.filter((clip) => getFirstMoment(clip) && getClipTeam(clip) === team)
    .map((clip): TimelineTrackDataItem => {
      const moments = clip.moments as BasketballTaggingMoment[];
      const primaryLabelTextItems: string[] = [];
      const playShotMoment = moments?.find((m) => m?.result);
      const { playResultMoment } = getPlayResult(moments, playShotMoment);
      let { playResultDisplay } = getPlayResult(moments, playShotMoment);
      const playShotDisplay = playShotMoment
        ? (formatBasketballMomentLabel(playShotMoment)
            ?.replace(/{([^}]*)}|#/g, '')
            .trim() || '') + (playShotMoment.player && roster ? ', ' + getAthleteDisplay(playShotMoment, roster) : '')
        : null;
      playShotDisplay !== null && primaryLabelTextItems.push(playShotDisplay);
      playResultDisplay = playResultMoment
        ? playResultDisplay +
          (playResultMoment.player && roster ? ', ' + getAthleteDisplay(playResultMoment, roster) : '')
        : null;
      playResultDisplay !== null && primaryLabelTextItems.push(playResultDisplay);
      const secondaryLabelText = [
        `${clip.scoreInfo?.atEndOfClip.team1.toString()}–${clip.scoreInfo?.atEndOfClip.team2.toString()} • Q${clip.periodInfo?.number}`,
      ];
      return {
        id: clip.id,
        startTimeMs: clip.startTimeMs,
        endTimeMs: clip.endTimeMs,
        color: color,
        primaryLabelTextItems: primaryLabelTextItems,
        secondaryLabelTextItems: secondaryLabelText,
      };
    });
}
