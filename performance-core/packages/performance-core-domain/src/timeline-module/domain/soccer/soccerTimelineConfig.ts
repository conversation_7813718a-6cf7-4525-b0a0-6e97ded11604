import type { TeamDisplay } from 'index';
import type { <PERSON><PERSON><PERSON><PERSON>thlet<PERSON> } from 'insights-module/domain';
import {
  type TimelineConfig,
  type TimelineTrackDataItem,
  TimelineTrackItemsDisplayMode,
} from 'timeline-module/timelineTypes';

import {
  createSoccerMomentLabels,
  formatSoccerAthlete,
  SoccerTaggingMoment,
  type VirtualClip,
} from '@hudl/hudl-domain-types';
import { format } from '@hudl/platform-i18n-adapter';

import type { TimelineTrackData } from '../../timelineTypes';
import {
  defaultTeamOneColor,
  defaultTeamTwoColor,
  findClipIndexById,
  getFirstMoment,
  getLastMoment,
} from '../../utils/helpers';

export const soccerTimelineConfig: TimelineConfig = {
  defaultZoomIndex: 6,
  initialSelectionZoomPercentage: 0.5,
  zoomSnapPoints: [
    { visibleTimePercentage: 1, label: 'FT' },
    { visibleTimeMs: 3600000, label: '1h' },
    { visibleTimeMs: 1800000, label: '30m' },
    { visibleTimeMs: 1200000, label: '20m' },
    { visibleTimeMs: 600000, label: '10m' },
    { visibleTimeMs: 300000, label: '5m' },
    { visibleTimeMs: 120000, label: '2m' },
    { visibleTimeMs: 60000, label: '1m' },
    { visibleTimeMs: 30000, label: '30s' },
    { visibleTimeMs: 10000, label: '10s' },
    { visibleTimeMs: 5000, label: '5s' },
  ],
  minimumRangeSelectionMs: 1000,
  createTrackData: ({
    filteredClips,
    teamInContext,
    opponentTeamsInContext,
    displayMode,
    roster,
    activeClipIndex,
    selectedClips,
    isAISession,
    colors,
  }) => {
    let teamOneData: TimelineTrackDataItem[] = [];
    let teamTwoData: TimelineTrackDataItem[] = [];

    isAISession = isAISession || false;

    const teamOneColor = colors?.teamOne ? colors.teamOne.toLowerCase() : defaultTeamOneColor;
    const teamTwoColor = colors?.teamTwo ? colors?.teamTwo.toLowerCase() : defaultTeamTwoColor;

    if (displayMode === TimelineTrackItemsDisplayMode.Default || displayMode === TimelineTrackItemsDisplayMode.Moment) {
      teamOneData = getMomentTrackDataForTeam(
        filteredClips ?? [],
        '1',
        teamOneColor,
        roster,
        activeClipIndex,
        selectedClips
      );
      teamTwoData = getMomentTrackDataForTeam(
        filteredClips ?? [],
        '2',
        teamTwoColor,
        roster,
        activeClipIndex,
        selectedClips
      );
    } else if (displayMode === TimelineTrackItemsDisplayMode.Sequence) {
      teamOneData = getSequenceTrackDataForTeam(filteredClips, '1', teamOneColor, activeClipIndex, selectedClips);
      teamTwoData = getSequenceTrackDataForTeam(filteredClips, '2', teamTwoColor, activeClipIndex, selectedClips);
    }

    const tracks: TimelineTrackData[] = [];
    if (teamOneData && teamOneData.length > 0) {
      tracks.push({
        id: 'scout',
        title: getTeamOneTrackTitle(teamInContext, isAISession),
        expanded: true,
        selected: false,
        data: teamOneData || [],
        children: [],
      });
    }
    if (teamTwoData && teamTwoData.length > 0) {
      tracks.push({
        id: 'OPP',
        title: getTeamTwoTrackTitle(opponentTeamsInContext, teamInContext, isAISession),
        expanded: true,
        selected: false,
        data: teamTwoData || [],
        children: [],
      });
    }
    return tracks;
  },
};

function getTeamTwoTrackTitle(
  opponentTeamsInContext: Map<string, TeamDisplay> | undefined,
  teamInContext: TeamDisplay | undefined,
  isAISession: boolean = false
): string {
  if (isAISession) {
    return format('module.timeline.soccer.team2');
  }
  return (
    opponentTeamsInContext?.values().next().value?.schoolAbbreviation ??
    teamInContext?.schoolAbbreviation.concat(' Opponent') ??
    'OPP'
  );
}

function getTeamOneTrackTitle(teamInContext: TeamDisplay | undefined, isAISession: boolean = false): string {
  if (isAISession) {
    return format('module.timeline.soccer.team1');
  }
  return teamInContext?.schoolAbbreviation ?? 'SCOUT';
}

function getClipTeamInPossession(clip: VirtualClip) {
  const firstMoment = getFirstMoment(clip) as SoccerTaggingMoment;
  return firstMoment.sequenceTeamInPossession;
}

function getSequenceTrackDataForTeam(
  filteredClips: VirtualClip[] | undefined,
  team: string,
  color: string,
  activeClipIndex?: number,
  selectedClips?: string[]
): TimelineTrackDataItem[] {
  const deduplicatedFilteredClips = deduplicateClips(filteredClips || []);
  return deduplicatedFilteredClips
    ?.filter((clip) => getFirstMoment(clip) && getClipTeamInPossession(clip) === team)
    .map((clip): TimelineTrackDataItem => {
      const firstMoment = getFirstMoment(clip) as SoccerTaggingMoment;
      const lastMoment = getLastMoment(clip) as SoccerTaggingMoment;
      const primaryLabelTextItems = [firstMoment?.sequenceStartEvent].filter((l) => l !== undefined && l !== null);
      const secondaryLabelTextItems = [
        firstMoment?.sequenceEndEvent,
        firstMoment?.sequenceStartingThird && firstMoment?.sequenceEndingThird
          ? `${firstMoment.sequenceStartingThird} to ${firstMoment.sequenceEndingThird}`
          : undefined,
        firstMoment?.sequencePassCount
          ? `${firstMoment.sequencePassCount} ${firstMoment.sequencePassCount === '1' ? format('domainTypes.moment.soccer.v1.pass') : format('domainTypes.moment.soccer.v1.passes')}`
          : undefined,
      ].filter((l) => l !== undefined && l !== null);

      const clipIndex = findClipIndexById(deduplicatedFilteredClips, clip.id);
      const universalClipId = buildUniversalClipId(clip);
      return {
        id: clip.id,
        startTimeMs: firstMoment.sequenceStartTime,
        endTimeMs: lastMoment.sequenceEndTime,
        highlighted: selectedClips && selectedClips.includes(universalClipId),
        color: color,
        primaryLabelTextItems,
        secondaryLabelTextItems,
        active: activeClipIndex === clipIndex,
      };
    });
}

function getMomentTrackDataForTeam(
  filteredClips: VirtualClip[],
  team: string,
  color: string,
  roster?: Map<string, RosterAthlete>,
  activeClipIndex?: number,
  selectedClips?: string[]
): TimelineTrackDataItem[] {
  return filteredClips
    .filter((clip) => {
      const firstMoment = getFirstMoment(clip) as SoccerTaggingMoment;
      if (!firstMoment) {
        return false;
      }
      return firstMoment.team === team || (firstMoment.team === null && firstMoment.sequenceTeamInPossession === team);
    })
    .map((clip): TimelineTrackDataItem => {
      const firstMoment = getFirstMoment(clip) as SoccerTaggingMoment;
      const labels = createSoccerMomentLabels(firstMoment);
      const playerLabels = [
        formatSoccerAthlete(firstMoment.primaryPlayer, roster),
        formatSoccerAthlete(firstMoment.secondaryPlayer, roster),
      ];
      const primaryLabels = labels.map((label, index) => {
        return label + playerLabels[index];
      });

      const sequenceLabel = firstMoment.sequence ? `Seq ${firstMoment.sequence}` : '';

      const secondaryLabels = [firstMoment.thirdLocation, sequenceLabel].filter(
        (l) => l !== undefined && l !== '' && l !== null
      );

      const clipIndexFromFilteredClips = findClipIndexById(filteredClips, clip.id);
      const universalClipId = buildUniversalClipId(clip);
      return {
        id: clip.id,
        startTimeMs: clip.startTimeMs,
        endTimeMs: clip.endTimeMs,
        color,
        highlighted: selectedClips && selectedClips.includes(universalClipId),
        primaryLabelTextItems: primaryLabels,
        secondaryLabelTextItems: secondaryLabels,
        active: activeClipIndex !== undefined && activeClipIndex === clipIndexFromFilteredClips,
      };
    });
}

// Deduplicates clips based on the value of the first moments sequence property
// Should only run on tagged games.
function deduplicateClips(clips: VirtualClip[]): VirtualClip[] {
  const seen: Set<string> = new Set();
  return clips.filter((clip) => {
    const firstMoment = getFirstMoment(clip) as SoccerTaggingMoment;
    if (!firstMoment || !firstMoment.sequence) {
      return false;
    }
    if (seen.has(firstMoment.sequence)) {
      return false;
    }
    seen.add(firstMoment.sequence);
    return true;
  });
}

// Returns the video id and start and end time of a clip
function buildUniversalClipId(clip: VirtualClip) {
  return clip.id.split('_')[1];
}
