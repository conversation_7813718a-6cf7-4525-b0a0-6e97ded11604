import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'insights-module/domain';

import { Sport, SportClass, type VirtualClip } from '@hudl/hudl-domain-types';
import { format } from '@hudl/platform-i18n-adapter';

import { basketballTimelineConfig } from './domain/basketball/basketballTimelineConfig';
import { defaultTimelineForMomentBasedSportsConfig } from './domain/defaultForMomentBasedSports/defaultTimelineForMomentBasedSports';
import { iceHockeyTimelineConfig } from './domain/icehockey/iceHockeyTimelineConfig';
import { soccerTimelineConfig } from './domain/soccer/soccerTimelineConfig';
import { softballTimelineConfig } from './domain/softball/softballTimelineConfig';
import { volleyballTimelineConfig } from './domain/volleyball/volleyballTimelineConfig';
import { wrestlingTimelineconfig } from './domain/wrestling/wrestlingTimelineConfig';
import type { TimelineConfig, TimelineTrackData, TimelineTrackDataItem } from './timelineTypes';
import { findClipIndexById, getClipTeam, getFirstMoment, isValidNonSegmentClip } from './utils/helpers';
import { defaultTeamOneColor, defaultTeamTwoColor } from './utils/helpers';

export interface TeamColors {
  backgroundColor: string;
  borderColor: string;
  textColor: string;
}

export const TIMES = {
  h1: 3600000,
  m30: 1800000,
  m20: 1200000,
  m15: 900000,
  m10: 600000,
  m5: 300000,
  m3: 180000,
  m2: 120000,
  m1: 60000,
  s30: 30000,
  s20: 20000,
  s15: 15000,
  s10: 10000,
  s6: 6000,
  s5: 5000,
  s3: 3000,
  s2: 2000,
  s1: 1000,
};

export const defaultTimelineConfig: TimelineConfig = {
  zoomSnapPoints: [
    { visibleTimePercentage: 1, label: 'FT' },
    { visibleTimeMs: TIMES.h1, label: '1 Hour' },
    { visibleTimeMs: TIMES.m30, label: '30 Minutes' },
    { visibleTimeMs: TIMES.m20, label: '20 Minutes' },
    { visibleTimeMs: TIMES.m10, label: '10 Minutes' },
    { visibleTimeMs: TIMES.m5, label: '5 Minutes' },
    { visibleTimeMs: TIMES.m2, label: '2 Minutes' },
    { visibleTimeMs: TIMES.m1, label: '1 Minutes' },
    { visibleTimeMs: TIMES.s30, label: '30 Seconds' },
    { visibleTimeMs: TIMES.s10, label: '10 Seconds' },
    { visibleTimeMs: TIMES.s5, label: '5 Seconds' },
  ],
  defaultZoomIndex: 6,
  initialSelectionZoomPercentage: 0.5, // 50% of the timeline will be taken up by the selection on initial load
  getIntervalSize: ({ visibleLength }) => {
    const idealInterval = visibleLength / 5.0;
    const actualIntervals = [
      TIMES.s1,
      TIMES.s2,
      TIMES.s3,
      TIMES.s5,
      TIMES.s10,
      TIMES.s15,
      TIMES.s20,
      TIMES.s30,
      TIMES.m1,
      TIMES.m2,
      TIMES.m3,
      TIMES.m5,
      TIMES.m10,
      TIMES.m15,
      TIMES.m20,
      TIMES.m30,
    ];
    const INTERVAL_BUFFER = 1; // 1ms buffer to prevent float jitter

    // Return the interval that is closest to the ideal interval
    return actualIntervals.reduce((prev, curr) =>
      Math.abs(curr - idealInterval) < Math.abs(prev - idealInterval) - INTERVAL_BUFFER ? curr : prev
    );
  },
  minimumRangeSelectionMs: 1000,
  createTrackData: ({
    filteredClips,
    teamInContext,
    sport,
    gender,
    colors,
    opponentTeamsInContext,
    activeClipIndex,
    roster,
  }) => {
    const teamOneData = getTrackDataForTeam(
      filteredClips,
      '1',
      sport,
      gender,
      colors?.teamOne ?? defaultTeamOneColor,
      true,
      activeClipIndex,
      roster
    );
    const teamTwoData = getTrackDataForTeam(
      filteredClips,
      '2',
      sport,
      gender,
      colors?.teamTwo ?? defaultTeamTwoColor,
      false,
      activeClipIndex,
      roster
    );

    const tracks: TimelineTrackData[] = [];
    if (teamOneData && teamOneData.length > 0) {
      tracks.push({
        id: 'scout',
        title: teamInContext?.schoolAbbreviation ?? 'SCOUT',
        expanded: true,
        selected: false,
        data: teamOneData || [],
        children: [],
      });
    }
    if (teamTwoData && teamTwoData.length > 0) {
      tracks.push({
        id: 'OPP',
        title:
          opponentTeamsInContext?.values().next().value?.schoolAbbreviation ??
          teamInContext?.schoolAbbreviation.concat(' Opponent') ??
          'OPP',
        expanded: true,
        selected: false,
        data: teamTwoData || [],
        children: [],
      });
    }
    return tracks;
  },
};

function getTrackDataForTeam(
  filteredClips: VirtualClip[] | undefined,
  team: string,
  sport: SportClass | undefined,
  gender: string | undefined,
  color: string,
  includeMomentWithoutTeam: boolean,
  activeClipIndex?: number,
  roster?: Map<string, RosterAthlete>
): TimelineTrackDataItem[] | undefined {
  return filteredClips
    ?.filter((clip) => {
      const firstMoment = getFirstMoment(clip);
      if (!firstMoment) return false;
      const teamFromClip = getClipTeam(clip);
      return isValidNonSegmentClip(clip) && (teamFromClip === team || (!teamFromClip && includeMomentWithoutTeam));
    })
    .map((clip): TimelineTrackDataItem => {
      const clipIndex = findClipIndexById(filteredClips, clip.id);
      const moment = getFirstMoment(clip);
      const i18nLabel = moment.getI18nLabel(roster?.values(), sport, {
        g11nJerseyFormat: (jerseyNumber: string) => format('tagging.player.jerseyNumber', { jerseyNumber }),
      });

      return {
        id: clip.id,
        startTimeMs: clip.startTimeMs,
        endTimeMs: clip.endTimeMs,
        color,
        active: activeClipIndex === clipIndex,
        primaryLabelTextItems: [
          format(i18nLabel.i18nKey, Object.assign({ gender, playerList: '' }, i18nLabel.i18nParams)),
        ],
      };
    });
}

// Merge the default timeline config with the sport specific timeline config
export const getTimelineConfig = (sport: SportClass): TimelineConfig => {
  switch (sport) {
    case Sport.Soccer:
      return { ...defaultTimelineConfig, ...soccerTimelineConfig };
    case Sport.Softball:
    case Sport.Baseball:
      return { ...defaultTimelineConfig, ...softballTimelineConfig };
    case Sport.Wrestling:
      return { ...defaultTimelineConfig, ...wrestlingTimelineconfig };
    case Sport.Basketball:
      return { ...defaultTimelineConfig, ...basketballTimelineConfig };
    case Sport.IceHockey:
      return { ...defaultTimelineConfig, ...iceHockeyTimelineConfig };
    case Sport.Lacrosse:
      return { ...defaultTimelineConfig, ...defaultTimelineForMomentBasedSportsConfig };
    case Sport.Volleyball:
      return { ...defaultTimelineConfig, ...volleyballTimelineConfig };
    default:
      return defaultTimelineConfig;
  }
};
