import type { RosterAthlete } from 'insights-module/domain';

import type { HudlTaggingMoment } from '@hudl/hudl-domain-types';
import { format } from '@hudl/platform-i18n-adapter';

export const formatAthleteNameAndJerseyDisplay = (athlete: <PERSON><PERSON><PERSON><PERSON>thlete | undefined): string | null => {
  if (!athlete) return null;
  const jersey = athlete.jersey ? `#${athlete.jersey}` : '';
  const { firstName, lastName } = athlete;
  const name = [firstName ? `${firstName[0]}.` : null, lastName].filter(Boolean).join(' ');
  return [jersey, name].filter(Boolean).join(' ') || null;
};

const formatAthleteJersey = (moment: HudlTaggingMoment, roster?: Map<string, RosterAthlete>): string | null => {
  const playerUserId = moment.player?.userId;
  const playerJersey = moment.player?.jersey;

  const athlete = roster?.get(playerUserId ?? '');
  if (athlete?.jersey || playerJersey) {
    return `#${playerJersey ?? athlete?.jersey}`;
  }
  return null;
};

const formatAthleteName = (moment: HudlTaggingMoment, roster?: Map<string, RosterAthlete>): string | null => {
  const playerUserId = moment?.player?.userId;
  if (!playerUserId) return null;

  const athlete = roster?.get(playerUserId ?? '');
  return athlete
    ? [athlete.firstName ? `${athlete.firstName[0]}.` : null, athlete.lastName].filter(Boolean).join(' ') || null
    : null;
};

type GetAthleteDisplayOptions = {
  displayUnknowns?: boolean;
};

export const getAthleteDisplay = (
  moment: HudlTaggingMoment,
  roster?: Map<string, RosterAthlete>,
  options?: GetAthleteDisplayOptions
): string | null => {
  if (!moment) return '';
  if (moment.player?.unknown && options?.displayUnknowns) {
    return format('domainTypes.moment.property.player.unknownLabel');
  }

  const athleteName = formatAthleteName(moment, roster);
  const jerseyNumber = formatAthleteJersey(moment, roster);

  const formattedName = [jerseyNumber, athleteName].filter(Boolean).join(' ');
  return formattedName || null;
};
