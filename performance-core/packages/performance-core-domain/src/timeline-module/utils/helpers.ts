import type { TaggedColors } from 'timeline-module/timelineTypes';

import { TagConstants, type VirtualClip } from '@hudl/hudl-domain-types';

const TEAM_TWO = '2';
const defaultTeamOneColor = 'TeamOneDefaultColor';
const defaultTeamTwoColor = 'TeamTwoDefaultColor';

function getFirstMoment(clip: VirtualClip) {
  return clip.moments[0] || undefined;
}

function getLastMoment(clip: VirtualClip) {
  return clip.moments[clip.moments.length - 1] || undefined;
}

function getClipTeam(clip: VirtualClip) {
  const firstMoment = getFirstMoment(clip);
  return firstMoment?.team;
}

function findClipIndexById(filteredClips: VirtualClip[], clipId: string) {
  return filteredClips.findIndex((clip) => clip.id === clipId);
}

const isValidNonSegmentClip = (clip: VirtualClip) => {
  const moment = getFirstMoment(clip);
  return moment && moment.type && moment.type !== TagConstants.Segment;
};

const getColorsForTeam = (colors: TaggedColors | undefined, team: string | null): string => {
  const teamOneColor = colors?.teamOne ? colors.teamOne.toLowerCase() : defaultTeamOneColor;
  const teamTwoColor = colors?.teamTwo ? colors?.teamTwo.toLowerCase() : defaultTeamTwoColor;
  return team === TEAM_TWO ? teamTwoColor : teamOneColor;
};

export {
  getFirstMoment,
  getLastMoment,
  getClipTeam,
  findClipIndexById,
  isValidNonSegmentClip,
  getColorsForTeam,
  defaultTeamOneColor,
  defaultTeamTwoColor,
};
