{"name": "org-programs-management", "version": "0.1.0", "private": false, "contributors": [{"name": "Supernova", "url": "https://hudl.slack.com/archives/C033JR0V0D7", "channel": "#squad-supernova"}], "scripts": {"antioch": "echo Antioch has not been enabled for this app. To enable update the antioch script to tsx ../../../internal/scripts/src/antioch-runner.ts --port 8100 -p 3", "build": "vite build", "build-storybook": "storybook build --stats-json", "build:analyze": "webpack-bundle-analyzer build/stats.json", "build:verify": "node ../../../internal/scripts/verify-bundle-stats.js", "chromatic": "chromatic --exit-once-uploaded --storybook-build-dir storybook-static --storybook-base-dir org-programs/apps/management --project-token $ORG_PROGRAMS_MANAGEMENT_CHROMATIC_PROJECT_TOKEN", "chromatic:skip": "chromatic --skip --project-token $ORG_PROGRAMS_MANAGEMENT_CHROMATIC_PROJECT_TOKEN", "clean": "rimraf build node_modules/.cache storybook-static", "dev": "vite-node ../../../internal/scripts/src/copy-dev-config.ts && concurrently \"pnpm run types:watch\" \"vite\"", "download-screenshots": "tsx ../../../internal/scripts/src/upload-screenshots.ts --download --app management", "generate-gql": "graphql-codegen --config codegen.ts", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "nuke": "pnpm run clean && rimraf node_modules", "playwright": "tsx ../../../internal/scripts/src/playwright-runner.ts --port 8100", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "preview": "vite preview", "release": "release-vite-app", "storybook": "storybook dev -p 9022", "test": "vitest", "test:ci": "vitest --coverage --silent", "test:nowatch": "vitest --watch=false", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src", "upload-screenshots": "tsx ../../../internal/scripts/src/upload-screenshots.ts --app management", "vr-playwright": "echo VR Playwright has not been enabled for this app. To enable update the playwright script to: tsx ../../../packages/scripts/src/playwright-runner.ts --config ./playwright/vr-playwright.config.ts"}, "dependencies": {"@apollo/client": "3.12.11", "@formatjs/intl": "2.10.1", "@hudl/analytics": "workspace:*", "@hudl/frontends-environment": "workspace:*", "@hudl/frontends-logging": "workspace:*", "@hudl/uniform-web": "workspace:*", "@hudl/uniform-web-actions-legacy": "npm:@hudl/uniform-web-actions@5.64.0", "@hudl/uniform-web-button-legacy": "npm:@hudl/uniform-web-button@5.54.0", "@hudl/uniform-web-dialogs-legacy": "npm:@hudl/uniform-web-dialogs@5.68.0", "@hudl/uniform-web-forms-legacy": "npm:@hudl/uniform-web-forms@5.60.0", "@hudl/uniform-web-notifications-legacy": "npm:@hudl/uniform-web-notifications@5.66.0", "@local/webnav": "workspace:*", "export-to-csv": "1.4.0", "frontends-i18n": "workspace:*", "frontends-preload": "workspace:*", "frontends-shared": "workspace:*", "jwt-decode": "3.1.2", "onboarding": "workspace:*", "react-intl": "6.6.4", "react-router-dom": "6.22.3", "ticketing-shared": "workspace:*"}, "devDependencies": {"@graphql-codegen/add": "5.0.3", "@graphql-codegen/cli": "5.0.3", "@graphql-codegen/typescript": "4.0.6", "@graphql-codegen/typescript-operations": "4.2.0", "@graphql-codegen/typescript-react-apollo": "4.3.0", "@graphql-codegen/typescript-resolvers": "4.0.6", "@hudl/eslint-config": "workspace:*", "@hudl/playwright-config": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "@vitest/coverage-v8": "3.2.4", "config": "workspace:*", "eslint": "8.45.0", "graphql-codegen-typescript-mock-data": "3.8.0", "jsdom": "24.0.0", "playwright-shared": "workspace:*", "upper-case": "2.0.0", "vite": "5.4.7"}}