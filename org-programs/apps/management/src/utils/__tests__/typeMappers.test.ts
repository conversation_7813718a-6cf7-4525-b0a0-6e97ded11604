import { Grade } from '../../enums/enums';
import {
  FeeResponsibility,
  Grade as GraphqlGrade,
  ProgramRegistrationStatus,
  ProgramState,
  ProgramVisibility,
  RegistrationGender,
  WebOrgProgramsManagementBulkUpsertProgramRegistrationsR1Mutation,
  WebOrgProgramsManagementQueryProgramByIdR1Query,
} from '../../graphql/graphqlTypes';
import { ProgramFormData } from '../../types/programTypes';
import {
  mapGenderInput,
  mapGradesInput,
  toProgram,
  toProgramFormData,
  toRegistrationFromUpsertedRegistration,
} from '../typeMappers';

describe('toProgramFormData', () => {
  it('returns formatted program data when data is provided', () => {
    const data: WebOrgProgramsManagementQueryProgramByIdR1Query = {
      school: {
        organizationProgram: {
          id: '1',
          orgId: 'org1',
          state: ProgramState.DRAFT,
          title: 'Program Title',
          type: { id: 'type1', name: 'Type 1' },
          description: 'Description',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          timeZoneIdentifier: 'America/New_York',
          visibility: ProgramVisibility.PUBLIC,
          feeResponsibility: FeeResponsibility.ORGANIZATION,
          registrationsForProgram: [],
        },
        organizationProgramTypes: [{ id: 'type1', orgId: 'org1', name: 'Type 1', description: 'Type 1 Description' }],
      },
    };

    const result = toProgramFormData(data);

    expect(result).toEqual({
      id: '1',
      title: 'Program Title',
      orgId: 'org1',
      hasChanged: false,
      programState: ProgramState.DRAFT,
      type: 'type1',
      description: 'Description',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
      programVisibility: 'PUBLIC',
      feeResponsibility: 'ORGANIZATION',
      orgProgramTypes: [{ id: 'type1', orgId: 'org1', name: 'Type 1', description: 'Type 1 Description' }],
      registrations: [],
      typeName: 'Type 1',
      state: ProgramState.DRAFT,
      visibility: ProgramVisibility.PUBLIC,
      timeZoneIdentifier: 'America/New_York',
    });
  });

  it('returns empty object when data is undefined', () => {
    const result = toProgramFormData(undefined);

    expect(result).toEqual({ hasChanged: false, registrations: [] });
  });

  it('returns empty strings for missing fields', () => {
    const data: WebOrgProgramsManagementQueryProgramByIdR1Query = {
      school: {
        organizationProgram: {
          id: '1',
          orgId: 'org1',
          title: '',
          state: ProgramState.DRAFT,
          type: { id: '', name: '' },
          description: '',
          startDate: '',
          endDate: '',
          timeZoneIdentifier: '',
          visibility: ProgramVisibility.UNSET,
          feeResponsibility: FeeResponsibility.UNSET,
          registrationsForProgram: [],
        },
        organizationProgramTypes: [],
      },
    };

    const result = toProgramFormData(data);

    expect(result).toEqual({
      id: '1',
      title: '',
      type: '',
      description: '',
      hasChanged: false,
      programState: ProgramState.DRAFT,
      orgId: 'org1',
      startDate: undefined,
      endDate: undefined,
      programVisibility: 'UNSET',
      feeResponsibility: 'UNSET',
      orgProgramTypes: [],
      registrations: [],
      typeName: '',
      state: ProgramState.DRAFT,
      visibility: ProgramVisibility.UNSET,
      timeZoneIdentifier: '',
    });
  });

  it('handles missing organizationProgram field', () => {
    const data: WebOrgProgramsManagementQueryProgramByIdR1Query = {
      school: {
        organizationProgramTypes: [],
      },
    };

    const result = toProgramFormData(data);

    expect(result).toEqual({
      id: undefined,
      orgId: '',
      hasChanged: false,
      title: '',
      type: '',
      description: '',
      startDate: undefined,
      endDate: undefined,
      programVisibility: undefined,
      feeResponsibility: undefined,
      orgProgramTypes: [],
      registrations: [],
      typeName: '',
      programState: undefined,
      state: undefined,
      visibility: undefined,
      timeZoneIdentifier: undefined,
    });
  });
});

describe('toRegistrationFromUpsertedRegistration', () => {
  it('correctly maps upserted registration to Registration object', () => {
    const upsertedRegistration: WebOrgProgramsManagementBulkUpsertProgramRegistrationsR1Mutation['bulkUpsertProgramRegistrations'][number] =
      {
        id: '123',
        programId: 'program-456',
        orgId: 'org-789',
        title: 'Summer Basketball Camp',
        priceInBaseDenomination: 15000,
        currencyCode: 'USD',
        description: 'Basketball training camp for high schoolers',
        startDate: '2023-06-15',
        endDate: '2023-07-30',
        timeZoneIdentifier: 'America/New_York',
        maxCapacity: 50,
        isWaitlistEnabled: true,
        eligibility: {
          birthDateFrom: '2005-01-01',
          birthDateTo: '2008-12-31',
          gender: RegistrationGender.MALE,
          grades: [GraphqlGrade.NINTH, GraphqlGrade.TENTH, GraphqlGrade.ELEVENTH, GraphqlGrade.TWELFTH],
        },
        status: ProgramRegistrationStatus.OPEN,
      };

    const result = toRegistrationFromUpsertedRegistration(upsertedRegistration);

    expect(result).toEqual({
      id: '123',
      programId: 'program-456',
      orgId: 'org-789',
      title: 'Summer Basketball Camp',
      priceInBaseDenomination: 15000,
      currencyCode: 'USD',
      description: 'Basketball training camp for high schoolers',
      startDate: '2023-06-15',
      endDate: '2023-07-30',
      timeZoneIdentifier: 'America/New_York',
      maxCapacity: 50,
      isWaitlistEnabled: true,
      eligibility: {
        birthdateFrom: '2005-01-01',
        birthdateTo: '2008-12-31',
        gender: 'Boys',
        grades: [Grade.Ninth, Grade.Tenth, Grade.Eleventh, Grade.Twelfth],
      },
      hasChanged: false,
      isDeleted: false,
      isDeletable: true,
      status: ProgramRegistrationStatus.OPEN,
    });
  });
});

describe('mapGenderInput', () => {
  it('maps internal GenderType values to GraphQL API RegistrationGender values', () => {
    // Test all possible input values
    expect(mapGenderInput('Boys')).toBe(RegistrationGender.MALE);
    expect(mapGenderInput('Girls')).toBe(RegistrationGender.FEMALE);
    expect(mapGenderInput('Mixed')).toBe(RegistrationGender.ANY);

    // Test undefined input
    expect(mapGenderInput(undefined)).toBe(undefined);
  });
});

describe('mapGradesInput', () => {
  it('maps internal Grade enum values to GraphQL API GraphqlGrade enum values', () => {
    // Test a subset of grade mappings
    expect(mapGradesInput([Grade.Kindergarten, Grade.First, Grade.Twelfth])).toEqual([
      GraphqlGrade.KINDERGARTEN,
      GraphqlGrade.FIRST,
      GraphqlGrade.TWELFTH,
    ]);

    // Test with empty array
    expect(mapGradesInput([])).toEqual([]);

    // Test with all grades
    const allGrades = [
      Grade.PreK,
      Grade.Kindergarten,
      Grade.First,
      Grade.Second,
      Grade.Third,
      Grade.Fourth,
      Grade.Fifth,
      Grade.Sixth,
      Grade.Seventh,
      Grade.Eighth,
      Grade.Ninth,
      Grade.Tenth,
      Grade.Eleventh,
      Grade.Twelfth,
    ];

    const expectedGraphqlGrades = [
      GraphqlGrade.PRE_K,
      GraphqlGrade.KINDERGARTEN,
      GraphqlGrade.FIRST,
      GraphqlGrade.SECOND,
      GraphqlGrade.THIRD,
      GraphqlGrade.FOURTH,
      GraphqlGrade.FIFTH,
      GraphqlGrade.SIXTH,
      GraphqlGrade.SEVENTH,
      GraphqlGrade.EIGHTH,
      GraphqlGrade.NINTH,
      GraphqlGrade.TENTH,
      GraphqlGrade.ELEVENTH,
      GraphqlGrade.TWELFTH,
    ];

    expect(mapGradesInput(allGrades)).toEqual(expectedGraphqlGrades);
  });
});

describe('toProgram', () => {
  it('should map complete ProgramFormData to Program with all fields', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      programState: ProgramState.PUBLISHED,
      title: 'Summer Soccer Camp',
      type: 'soccer-camp',
      typeName: 'Soccer Camp',
      description: 'A fun summer soccer camp for kids',
      startDate: '2024-06-01',
      endDate: '2024-08-31',
      visibility: ProgramVisibility.PUBLIC,
      feeResponsibility: FeeResponsibility.CUSTOMER,
      timeZoneIdentifier: 'America/New_York',
      registrations: [],
      hasChanged: false,
    };

    const result = toProgram(programFormData);

    expect(result).toEqual({
      id: 'program-123',
      orgId: 'org-456',
      state: ProgramState.PUBLISHED,
      type: {
        id: 'soccer-camp',
        name: 'Soccer Camp',
      },
      title: 'Summer Soccer Camp',
      description: 'A fun summer soccer camp for kids',
      startDate: '2024-06-01',
      endDate: '2024-08-31',
      timeZoneIdentifier: 'America/New_York',
      visibility: ProgramVisibility.PUBLIC,
      feeResponsibility: FeeResponsibility.CUSTOMER,
    });
  });

  it('should map ProgramFormData with only type (no typeName) to Program without type', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      type: 'soccer-camp',
      // typeName is missing
      registrations: [],
    };

    const result = toProgram(programFormData);

    expect(result).toEqual({
      id: 'program-123',
      orgId: 'org-456',
      state: ProgramState.DRAFT,
      type: undefined, // Should be undefined when typeName is missing
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      timeZoneIdentifier: undefined,
      visibility: ProgramVisibility.UNSET,
      feeResponsibility: FeeResponsibility.UNSET,
    });
  });

  it('should map ProgramFormData with only typeName (no type) to Program without type', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      typeName: 'Soccer Camp',
      // type is missing
      registrations: [],
    };

    const result = toProgram(programFormData);

    expect(result).toEqual({
      id: 'program-123',
      orgId: 'org-456',
      state: ProgramState.DRAFT,
      type: undefined, // Should be undefined when type is missing
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      timeZoneIdentifier: undefined,
      visibility: ProgramVisibility.UNSET,
      feeResponsibility: FeeResponsibility.UNSET,
    });
  });

  it('should map ProgramFormData with both type and typeName to Program with type object', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      type: 'basketball-camp',
      typeName: 'Basketball Camp',
      registrations: [],
    };

    const result = toProgram(programFormData);

    expect(result.type).toEqual({
      id: 'basketball-camp',
      name: 'Basketball Camp',
    });
  });

  it('should use default values when optional fields are undefined', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      registrations: [],
    };

    const result = toProgram(programFormData);

    expect(result).toEqual({
      id: 'program-123',
      orgId: 'org-456',
      state: ProgramState.DRAFT, // Default value
      type: undefined,
      title: '', // Default value
      description: '', // Default value
      startDate: '', // Default value
      endDate: '', // Default value
      timeZoneIdentifier: undefined,
      visibility: ProgramVisibility.UNSET, // Default value
      feeResponsibility: FeeResponsibility.UNSET, // Default value
    });
  });

  it('should use default values when optional fields are empty strings', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      registrations: [],
    };

    const result = toProgram(programFormData);

    expect(result).toEqual({
      id: 'program-123',
      orgId: 'org-456',
      state: ProgramState.DRAFT,
      type: undefined,
      title: '', // Empty string is preserved
      description: '', // Empty string is preserved
      startDate: '', // Empty string is preserved
      endDate: '', // Empty string is preserved
      timeZoneIdentifier: undefined,
      visibility: ProgramVisibility.UNSET,
      feeResponsibility: FeeResponsibility.UNSET,
    });
  });

  it('should handle different ProgramState values', () => {
    const draftProgram: ProgramFormData = {
      id: 'draft-program',
      orgId: 'org-456',
      programState: ProgramState.DRAFT,
      registrations: [],
    };

    const publishedProgram: ProgramFormData = {
      id: 'published-program',
      orgId: 'org-456',
      programState: ProgramState.PUBLISHED,
      registrations: [],
    };

    const archivedProgram: ProgramFormData = {
      id: 'archived-program',
      orgId: 'org-456',
      programState: ProgramState.ARCHIVED,
      registrations: [],
    };

    expect(toProgram(draftProgram).state).toBe(ProgramState.DRAFT);
    expect(toProgram(publishedProgram).state).toBe(ProgramState.PUBLISHED);
    expect(toProgram(archivedProgram).state).toBe(ProgramState.ARCHIVED);
  });

  it('should handle different ProgramVisibility values', () => {
    const unsetVisibility: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      visibility: ProgramVisibility.UNSET,
      registrations: [],
    };

    const publicVisibility: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      visibility: ProgramVisibility.PUBLIC,
      registrations: [],
    };

    const privateVisibility: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      visibility: ProgramVisibility.PRIVATE,
      registrations: [],
    };

    expect(toProgram(unsetVisibility).visibility).toBe(ProgramVisibility.UNSET);
    expect(toProgram(publicVisibility).visibility).toBe(ProgramVisibility.PUBLIC);
    expect(toProgram(privateVisibility).visibility).toBe(ProgramVisibility.PRIVATE);
  });

  it('should handle different FeeResponsibility values', () => {
    const unsetResponsibility: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      feeResponsibility: FeeResponsibility.UNSET,
      registrations: [],
    };

    const customerResponsibility: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      feeResponsibility: FeeResponsibility.CUSTOMER,
      registrations: [],
    };

    const organizationResponsibility: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      feeResponsibility: FeeResponsibility.ORGANIZATION,
      registrations: [],
    };

    expect(toProgram(unsetResponsibility).feeResponsibility).toBe(FeeResponsibility.UNSET);
    expect(toProgram(customerResponsibility).feeResponsibility).toBe(FeeResponsibility.CUSTOMER);
    expect(toProgram(organizationResponsibility).feeResponsibility).toBe(FeeResponsibility.ORGANIZATION);
  });

  it('should preserve timeZoneIdentifier when provided', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      timeZoneIdentifier: 'America/Los_Angeles',
      registrations: [],
    };

    const result = toProgram(programFormData);

    expect(result.timeZoneIdentifier).toBe('America/Los_Angeles');
  });

  it('should set timeZoneIdentifier to undefined when not provided', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      registrations: [],
    };

    const result = toProgram(programFormData);

    expect(result.timeZoneIdentifier).toBeUndefined();
  });

  it('should handle minimal ProgramFormData with only required fields', () => {
    const programFormData: ProgramFormData = {
      registrations: [], // Only required field
    };

    const result = toProgram(programFormData);

    expect(result).toEqual({
      id: '',
      orgId: '',
      state: ProgramState.DRAFT,
      type: undefined,
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      timeZoneIdentifier: undefined,
      visibility: ProgramVisibility.UNSET,
      feeResponsibility: FeeResponsibility.UNSET,
    });
  });

  it('should handle ProgramFormData with all optional fields set to falsy values', () => {
    const programFormData: ProgramFormData = {
      id: '',
      orgId: '',
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      timeZoneIdentifier: '',
      registrations: [],
    };

    const result = toProgram(programFormData);

    expect(result).toEqual({
      id: '',
      orgId: '',
      state: ProgramState.DRAFT,
      type: undefined,
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      timeZoneIdentifier: '', // Empty string is preserved, not converted to undefined
      visibility: ProgramVisibility.UNSET,
      feeResponsibility: FeeResponsibility.UNSET,
    });
  });

  it('should not include ProgramFormData-specific fields in the result', () => {
    const programFormData: ProgramFormData = {
      id: 'program-123',
      orgId: 'org-456',
      registrations: [],
      hasChanged: true,
      sidePanel: { registration: undefined },
      orgProgramTypes: [],
    };

    const result = toProgram(programFormData);

    // Verify that ProgramFormData-specific fields are not included
    expect(result).not.toHaveProperty('registrations');
    expect(result).not.toHaveProperty('hasChanged');
    expect(result).not.toHaveProperty('sidePanel');
    expect(result).not.toHaveProperty('orgProgramTypes');
  });
});
