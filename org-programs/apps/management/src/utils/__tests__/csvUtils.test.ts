import { describe, expect, it } from 'vitest';

import { ProgramsOverviewData, RegistrationsData } from '../../components/DataTable/configuration/Configuration';
import { ProgramRegistrationStatus } from '../../graphql/graphqlTypes';
import { getCsvDataForProgramOverviewTab, getCsvDataForRegistrationsTab } from '../csvUtils';

describe('csvUtils', () => {
  describe('getCsvDataForProgramOverviewTab', () => {
    it('should transform ProgramsOverviewData to CSV format correctly', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: '<PERSON>',
          age: 25,
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-123',
            title: 'Summer Camp Registration',
          },
          paymentDate: new Date('2024-01-15'),
          totalPaid: {
            priceInBaseDenomination: 10000, // $100.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Paid',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        name: 'John Doe',
        age: 25,
        gender: 'Male',
        contact: '<EMAIL>',
        registration: 'Summer Camp Registration',
        paymentDate: expect.any(String) as string,
        totalPaid: '$100.00',
        outstanding: '$0.00',
        refunded: '$0.00',
        status: 'Paid',
      });
      // Verify the date is formatted correctly (should be a date string)
      expect(result[0].paymentDate).toMatch(/^\d{1,2}\/\d{1,2}\/\d{4}$/);
    });

    it('should handle multiple registrants', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Alice Johnson',
          age: 16,
          gender: 'Female',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-456',
            title: 'Basketball Camp',
          },
          paymentDate: new Date('2024-02-20'),
          totalPaid: {
            priceInBaseDenomination: 15000, // $150.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 5000, // $50.00
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Outstanding',
        },
        {
          name: 'Bob Smith',
          age: 17,
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-789',
            title: 'Soccer Training',
          },
          paymentDate: new Date('2024-03-10'),
          totalPaid: {
            priceInBaseDenomination: 20000, // $200.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 20000, // $200.00
            currencyCode: 'USD',
          },
          status: 'Refunded',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        name: 'Alice Johnson',
        age: 16,
        gender: 'Female',
        contact: '<EMAIL>',
        registration: 'Basketball Camp',
        paymentDate: expect.any(String) as string,
        totalPaid: '$150.00',
        outstanding: '$50.00',
        refunded: '$0.00',
        status: 'Outstanding',
      });
      expect(result[1]).toEqual({
        name: 'Bob Smith',
        age: 17,
        gender: 'Male',
        contact: '<EMAIL>',
        registration: 'Soccer Training',
        paymentDate: expect.any(String) as string,
        totalPaid: '$200.00',
        outstanding: '$0.00',
        refunded: '$200.00',
        status: 'Refunded',
      });
      // Verify the dates are formatted correctly
      expect(result[0].paymentDate).toMatch(/^\d{1,2}\/\d{1,2}\/\d{4}$/);
      expect(result[1].paymentDate).toMatch(/^\d{1,2}\/\d{1,2}\/\d{4}$/);
    });

    it('should handle different currencies correctly', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Eva Wilson',
          age: 18,
          gender: 'Female',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-eur',
            title: 'European Camp',
          },
          paymentDate: new Date('2024-04-05'),
          totalPaid: {
            priceInBaseDenomination: 8500, // €85.00
            currencyCode: 'EUR',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'EUR',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'EUR',
          },
          status: 'Paid',
        },
        {
          name: 'Carlos Rodriguez',
          age: 19,
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-jpy',
            title: 'Japanese Training',
          },
          paymentDate: new Date('2024-05-12'),
          totalPaid: {
            priceInBaseDenomination: 10000,
            currencyCode: 'JPY',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'JPY',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'JPY',
          },
          status: 'Paid',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(2);
      expect(result[0].totalPaid).toBe('€85.00');
      expect(result[0].outstanding).toBe('€0.00');
      expect(result[0].refunded).toBe('€0.00');
      expect(result[1].totalPaid).toBe('¥10,000');
      expect(result[1].outstanding).toBe('¥0');
      expect(result[1].refunded).toBe('¥0');
    });

    it('should handle missing payment date', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'David Brown',
          age: 20,
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-no-date',
            title: 'No Payment Date Camp',
          },
          paymentDate: undefined,
          totalPaid: {
            priceInBaseDenomination: 7500, // $75.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 7500, // $75.00
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Outstanding',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].paymentDate).toBe('');
    });

    it('should handle missing registration title', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Sarah Davis',
          age: 21,
          gender: 'Female',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-no-title',
            title: undefined,
          },
          paymentDate: new Date('2024-06-01'),
          totalPaid: {
            priceInBaseDenomination: 12000, // $120.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Paid',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].registration).toBe('');
    });

    it('should handle string age values', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Mike Johnson',
          age: '15-16', // String age range
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-string-age',
            title: 'Age Range Camp',
          },
          paymentDate: new Date('2024-07-15'),
          totalPaid: {
            priceInBaseDenomination: 9000, // $90.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Paid',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].age).toBe('15-16');
    });

    it('should handle zero amounts correctly', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Lisa Chen',
          age: 22,
          gender: 'Female',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-zero',
            title: 'Free Camp',
          },
          paymentDate: new Date('2024-08-20'),
          totalPaid: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Free',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].totalPaid).toBe('$0.00');
      expect(result[0].outstanding).toBe('$0.00');
      expect(result[0].refunded).toBe('$0.00');
      expect(result[0].status).toBe('Free');
    });

    it('should handle large amounts correctly', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Alex Thompson',
          age: 23,
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-large',
            title: 'Premium Camp',
          },
          paymentDate: new Date('2024-09-30'),
          totalPaid: {
            priceInBaseDenomination: 999999, // $9,999.99
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Paid',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].totalPaid).toBe('$9,999.99');
    });

    it('should handle empty array input', () => {
      const mockData: ProgramsOverviewData[] = [];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(0);
      expect(result).toEqual([]);
    });

    it('should handle all payment statuses', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Paid User',
          age: 24,
          gender: 'Female',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-paid',
            title: 'Paid Registration',
          },
          paymentDate: new Date('2024-10-01'),
          totalPaid: {
            priceInBaseDenomination: 10000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Paid',
        },
        {
          name: 'Outstanding User',
          age: 25,
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-outstanding',
            title: 'Outstanding Registration',
          },
          paymentDate: new Date('2024-10-02'),
          totalPaid: {
            priceInBaseDenomination: 5000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 5000,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Outstanding',
        },
        {
          name: 'Refunded User',
          age: 26,
          gender: 'Female',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-refunded',
            title: 'Refunded Registration',
          },
          paymentDate: new Date('2024-10-03'),
          totalPaid: {
            priceInBaseDenomination: 15000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 15000,
            currencyCode: 'USD',
          },
          status: 'Refunded',
        },
        {
          name: 'Free User',
          age: 27,
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-free',
            title: 'Free Registration',
          },
          paymentDate: new Date('2024-10-04'),
          totalPaid: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Free',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(4);
      expect(result[0].status).toBe('Paid');
      expect(result[1].status).toBe('Outstanding');
      expect(result[2].status).toBe('Refunded');
      expect(result[3].status).toBe('Free');
    });

    it('should handle different date formats consistently', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Date Test User',
          age: 28,
          gender: 'Female',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-date',
            title: 'Date Test Registration',
          },
          paymentDate: new Date('2024-12-25'), // Christmas
          totalPaid: {
            priceInBaseDenomination: 25000, // $250.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Paid',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].paymentDate).toMatch(/^\d{1,2}\/\d{1,2}\/\d{4}$/);
      // Verify it's a valid date string format
      expect(typeof result[0].paymentDate).toBe('string');
    });

    it('should handle edge case with undefined registration title', () => {
      const mockData: ProgramsOverviewData[] = [
        {
          name: 'Undefined Title User',
          age: 29,
          gender: 'Male',
          contact: '<EMAIL>',
          registration: {
            id: 'reg-undefined',
            title: undefined,
          },
          paymentDate: new Date('2024-11-15'),
          totalPaid: {
            priceInBaseDenomination: 8000, // $80.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          refunded: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          status: 'Paid',
        },
      ];

      const result = getCsvDataForProgramOverviewTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].registration).toBe('');
    });
  });

  describe('getCsvDataForRegistrationsTab', () => {
    it('should transform RegistrationsData to CSV format correctly', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-123',
            title: 'Summer Basketball Camp',
          },
          count: 25,
          totalCollected: {
            priceInBaseDenomination: 250000, // $2,500.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 50000, // $500.00
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 12000, // $120.00
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 25,
            denominator: 30,
          },
          isRegistrationOpen: true,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Summer Basketball Camp',
        count: 25,
        totalCollected: '$2,500.00',
        outstanding: '$500.00',
        listPrice: '$120.00',
        capacity: '25/30',
        registration: ProgramRegistrationStatus.OPEN,
      });
    });

    it('should handle multiple registrations', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-456',
            title: 'Soccer Training',
          },
          count: 15,
          totalCollected: {
            priceInBaseDenomination: 150000, // $1,500.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000, // $100.00
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 15,
            denominator: 20,
          },
          isRegistrationOpen: true,
        },
        {
          title: {
            id: 'reg-789',
            title: 'Tennis Camp',
          },
          count: 8,
          totalCollected: {
            priceInBaseDenomination: 80000, // $800.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 20000, // $200.00
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 15000, // $150.00
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 8,
            denominator: 12,
          },
          isRegistrationOpen: false,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        title: 'Soccer Training',
        count: 15,
        totalCollected: '$1,500.00',
        outstanding: '$0.00',
        listPrice: '$100.00',
        capacity: '15/20',
        registration: ProgramRegistrationStatus.OPEN,
      });
      expect(result[1]).toEqual({
        title: 'Tennis Camp',
        count: 8,
        totalCollected: '$800.00',
        outstanding: '$200.00',
        listPrice: '$150.00',
        capacity: '8/12',
        registration: ProgramRegistrationStatus.CLOSED,
      });
    });

    it('should handle different currencies correctly', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-eur',
            title: 'European Football Camp',
          },
          count: 20,
          totalCollected: {
            priceInBaseDenomination: 180000, // €1,800.00
            currencyCode: 'EUR',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'EUR',
          },
          listPrice: {
            priceInBaseDenomination: 9000, // €90.00
            currencyCode: 'EUR',
          },
          capacity: {
            numerator: 20,
            denominator: 25,
          },
          isRegistrationOpen: true,
        },
        {
          title: {
            id: 'reg-jpy',
            title: 'Japanese Training',
          },
          count: 12,
          totalCollected: {
            priceInBaseDenomination: 120000,
            currencyCode: 'JPY',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'JPY',
          },
          listPrice: {
            priceInBaseDenomination: 10000,
            currencyCode: 'JPY',
          },
          capacity: {
            numerator: 12,
            denominator: 15,
          },
          isRegistrationOpen: false,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(2);
      expect(result[0].totalCollected).toBe('€1,800.00');
      expect(result[0].outstanding).toBe('€0.00');
      expect(result[0].listPrice).toBe('€90.00');
      expect(result[1].totalCollected).toBe('¥120,000');
      expect(result[1].outstanding).toBe('¥0');
      expect(result[1].listPrice).toBe('¥10,000');
    });

    it('should handle missing registration title', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-no-title',
            title: undefined,
          },
          count: 10,
          totalCollected: {
            priceInBaseDenomination: 100000, // $1,000.00
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000, // $100.00
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 10,
            denominator: 15,
          },
          isRegistrationOpen: true,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('');
    });

    it('should handle zero amounts correctly', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-free',
            title: 'Free Camp',
          },
          count: 5,
          totalCollected: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 5,
            denominator: 10,
          },
          isRegistrationOpen: true,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].totalCollected).toBe('$0.00');
      expect(result[0].outstanding).toBe('$0.00');
      expect(result[0].listPrice).toBe('$0.00');
    });

    it('should handle large amounts correctly', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-premium',
            title: 'Premium Elite Camp',
          },
          count: 50,
          totalCollected: {
            priceInBaseDenomination: 9999999, // $99,999.99
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 100000, // $1,000.00
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 200000, // $2,000.00
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 50,
            denominator: 60,
          },
          isRegistrationOpen: false,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].totalCollected).toBe('$99,999.99');
      expect(result[0].outstanding).toBe('$1,000.00');
      expect(result[0].listPrice).toBe('$2,000.00');
    });

    it('should handle empty array input', () => {
      const mockData: RegistrationsData[] = [];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(0);
      expect(result).toEqual([]);
    });

    it('should handle registration status correctly', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-open',
            title: 'Open Registration',
          },
          count: 20,
          totalCollected: {
            priceInBaseDenomination: 200000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 20,
            denominator: 25,
          },
          isRegistrationOpen: true,
        },
        {
          title: {
            id: 'reg-closed',
            title: 'Closed Registration',
          },
          count: 15,
          totalCollected: {
            priceInBaseDenomination: 150000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 15,
            denominator: 20,
          },
          isRegistrationOpen: false,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(2);
      expect(result[0].registration).toBe(ProgramRegistrationStatus.OPEN);
      expect(result[1].registration).toBe(ProgramRegistrationStatus.CLOSED);
    });

    it('should handle capacity formatting correctly', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-capacity',
            title: 'Capacity Test',
          },
          count: 0,
          totalCollected: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 5000,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 0,
            denominator: 100,
          },
          isRegistrationOpen: true,
        },
        {
          title: {
            id: 'reg-full',
            title: 'Full Capacity',
          },
          count: 50,
          totalCollected: {
            priceInBaseDenomination: 500000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 50,
            denominator: 50,
          },
          isRegistrationOpen: false,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(2);
      expect(result[0].capacity).toBe('0/100');
      expect(result[1].capacity).toBe('50/50');
    });

    it('should handle edge case with undefined registration title', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-undefined',
            title: undefined,
          },
          count: 8,
          totalCollected: {
            priceInBaseDenomination: 80000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 8,
            denominator: 10,
          },
          isRegistrationOpen: true,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('');
    });

    it('should handle mixed registration statuses in same dataset', () => {
      const mockData: RegistrationsData[] = [
        {
          title: {
            id: 'reg-mixed-1',
            title: 'Mixed Status 1',
          },
          count: 12,
          totalCollected: {
            priceInBaseDenomination: 120000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 12,
            denominator: 15,
          },
          isRegistrationOpen: true,
        },
        {
          title: {
            id: 'reg-mixed-2',
            title: 'Mixed Status 2',
          },
          count: 18,
          totalCollected: {
            priceInBaseDenomination: 180000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 20000,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 18,
            denominator: 20,
          },
          isRegistrationOpen: false,
        },
        {
          title: {
            id: 'reg-mixed-3',
            title: 'Mixed Status 3',
          },
          count: 5,
          totalCollected: {
            priceInBaseDenomination: 50000,
            currencyCode: 'USD',
          },
          outstanding: {
            priceInBaseDenomination: 0,
            currencyCode: 'USD',
          },
          listPrice: {
            priceInBaseDenomination: 10000,
            currencyCode: 'USD',
          },
          capacity: {
            numerator: 5,
            denominator: 8,
          },
          isRegistrationOpen: true,
        },
      ];

      const result = getCsvDataForRegistrationsTab(mockData);

      expect(result).toHaveLength(3);
      expect(result[0].registration).toBe(ProgramRegistrationStatus.OPEN);
      expect(result[1].registration).toBe(ProgramRegistrationStatus.CLOSED);
      expect(result[2].registration).toBe(ProgramRegistrationStatus.OPEN);
    });
  });
});
