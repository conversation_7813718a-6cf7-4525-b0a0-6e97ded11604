import { ToastMessenger } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

const toastMessages: Record<string, { type: 'confirmation' | 'critical'; message: string; qaId: string }> = {
  'save-as-draft-success': {
    type: 'confirmation',
    message: 'org-programs.program-form.save-as-draft.success',
    qaId: 'save-as-draft-success-toast',
  },
  'save-as-draft-error': {
    type: 'critical',
    message: 'org-programs.program-form.save-as-draft.error',
    qaId: 'save-as-draft-error-toast',
  },
  'changes-saved': {
    type: 'confirmation',
    message: 'org-programs.program-form.changes-saved',
    qaId: 'changes-saved-toast',
  },
  'changes-saved-error': {
    type: 'critical',
    message: 'org-programs.program-form.changes-saved-error',
    qaId: 'changes-saved-error-toast',
  },
  'publish-success': {
    type: 'confirmation',
    message: 'org-programs.program-form.publish.success',
    qaId: 'publish-success-toast',
  },
  'publish-error': {
    type: 'critical',
    message: 'org-programs.program-form.publish.error',
    qaId: 'publish-error-toast',
  },
  'registration-deleted': {
    type: 'confirmation',
    message: 'org-programs.program-form.registration-deleted',
    qaId: 'registration-deleted-toast',
  },
  'download-error': {
    type: 'critical',
    message: 'org-programs.manage.download.error',
    qaId: 'download-error-toast',
  },
};

type ToastKey = keyof typeof toastMessages;
/**
 * Show a toast notification
 * @param key The toast type key
 * @param messageParams Optional parameters for the message
 */
export const showToast = (
  key: ToastKey,
  messageParams?: Record<string, string | undefined>,
  action?: { text: string; qaId: string; onPress: () => void }
): void => {
  const messageKey = toastMessages[key];
  const text = format(messageKey.message, messageParams);
  const { type, qaId } = messageKey;
  ToastMessenger.show({ text, type, qaId, action });
};

/**
 * Show a save operation toast based on form mode and success status
 * @param success Whether the operation was successful
 * @param formMode The current form mode
 * @param messageParams Optional parameters for the message
 */
export const showSaveToast = (
  success: boolean,
  formMode: string,
  messageParams?: Record<string, string | undefined>
): void => {
  const key = success
    ? formMode === 'create'
      ? 'save-as-draft-success'
      : 'changes-saved'
    : formMode === 'create'
      ? 'save-as-draft-error'
      : 'changes-saved-error';

  showToast(key, messageParams);
};

/**
 * Show a publish operation toast based on success status
 * @param success Whether the operation was successful
 */
export const showPublishToast = (success: boolean): void => {
  const key = success ? 'publish-success' : 'publish-error';
  showToast(key);
};

/**
 * Show a download error toast with retry action
 * @param onRetry Callback function to execute when retry is clicked
 */
export const showDownloadErrorToast = (onRetry: () => void): void => {
  showToast('download-error', undefined, {
    text: format('org-programs.retry'),
    qaId: 'download-error-toast-retry',
    onPress: onRetry,
  });
};
