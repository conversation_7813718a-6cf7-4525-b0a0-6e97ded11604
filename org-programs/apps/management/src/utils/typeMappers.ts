import { Grade } from '../enums/enums.ts';
import {
  FeeResponsibility,
  Grade as GraphqlGrade,
  ProgramRegistrationStatus,
  ProgramState,
  ProgramVisibility,
  RegistrationGender,
  UpsertOrganizationProgramInput,
  UpsertRegistrationInput,
  WebOrgProgramsManagementBulkUpsertProgramRegistrationsR1Mutation,
  WebOrgProgramsManagementDeleteOrganizationProgramRegistrationsR1MutationVariables,
  WebOrgProgramsManagementQueryProgramByIdR1Query,
} from '../graphql/graphqlTypes.tsx';
import { Program, ProgramFormData } from '../types/programTypes.ts';
import { GenderType, Registration, RegistrationStatus } from '../types/registration.ts';
import { formatApiDate } from './formatting.ts';

export const toUpsertMutationVariables = (program: Program): UpsertOrganizationProgramInput => {
  return {
    id: program.id,
    orgId: program.orgId,
    programTypeId: program.type?.id,
    description: program.description,
    timeZoneIdentifier: program.timeZoneIdentifier,
    startDate: program.startDate,
    title: program.title,
    endDate: program.endDate,
    visibility: program.visibility,
    state: program.state,
    feeResponsibility: program.feeResponsibility,
  };
};

export const mapGender = (gender: RegistrationGender): GenderType | undefined => {
  switch (gender) {
    case RegistrationGender.ANY:
      return 'Mixed';
    case RegistrationGender.FEMALE:
      return 'Girls';
    case RegistrationGender.MALE:
      return 'Boys';
    default:
      return undefined;
  }
};

export const mapRegistrationStatus = (status: ProgramRegistrationStatus): RegistrationStatus => {
  switch (status) {
    case 'OPEN':
      return RegistrationStatus.OPEN;
    case 'CLOSED':
      return RegistrationStatus.CLOSED;
    default:
      return RegistrationStatus.CLOSED;
  }
};

export const mapRegistrationStatusInput = (status: RegistrationStatus): ProgramRegistrationStatus | undefined => {
  switch (status) {
    case 'OPEN':
      return ProgramRegistrationStatus.OPEN;
    case 'CLOSED':
      return ProgramRegistrationStatus.CLOSED;
  }
};

export const mapGenderInput = (gender: GenderType | undefined): RegistrationGender | undefined => {
  switch (gender) {
    case 'Boys':
      return RegistrationGender.MALE;
    case 'Girls':
      return RegistrationGender.FEMALE;
    case 'Mixed':
      return RegistrationGender.ANY;
    default:
      return undefined;
  }
};

export const mapGrades = (grades: GraphqlGrade[]): Grade[] => {
  if (!grades || grades.length === 0) {
    return [];
  }

  const mappedGrades = grades.map((g) => {
    switch (g) {
      case GraphqlGrade.KINDERGARTEN:
        return Grade.Kindergarten;
      case GraphqlGrade.PRE_K:
        return Grade.PreK;
      case GraphqlGrade.FIRST:
        return Grade.First;
      case GraphqlGrade.SECOND:
        return Grade.Second;
      case GraphqlGrade.THIRD:
        return Grade.Third;
      case GraphqlGrade.FOURTH:
        return Grade.Fourth;
      case GraphqlGrade.FIFTH:
        return Grade.Fifth;
      case GraphqlGrade.SIXTH:
        return Grade.Sixth;
      case GraphqlGrade.SEVENTH:
        return Grade.Seventh;
      case GraphqlGrade.EIGHTH:
        return Grade.Eighth;
      case GraphqlGrade.NINTH:
        return Grade.Ninth;
      case GraphqlGrade.TENTH:
        return Grade.Tenth;
      case GraphqlGrade.ELEVENTH:
        return Grade.Eleventh;
      case GraphqlGrade.TWELFTH:
        return Grade.Twelfth;
    }
  });

  // Define sorting order for grades
  const gradeOrder: Record<Grade, number> = {
    [Grade.PreK]: 0,
    [Grade.Kindergarten]: 1,
    [Grade.First]: 2,
    [Grade.Second]: 3,
    [Grade.Third]: 4,
    [Grade.Fourth]: 5,
    [Grade.Fifth]: 6,
    [Grade.Sixth]: 7,
    [Grade.Seventh]: 8,
    [Grade.Eighth]: 9,
    [Grade.Ninth]: 10,
    [Grade.Tenth]: 11,
    [Grade.Eleventh]: 12,
    [Grade.Twelfth]: 13,
  };

  // Sort the grades based on the defined order
  return mappedGrades.sort((a, b) => gradeOrder[a] - gradeOrder[b]);
};

export const mapGradesInput = (grades: Grade[]): GraphqlGrade[] => {
  if (!grades || grades.length === 0) {
    return [];
  }

  return grades.map((g) => {
    switch (g) {
      case Grade.Kindergarten:
        return GraphqlGrade.KINDERGARTEN;
      case Grade.PreK:
        return GraphqlGrade.PRE_K;
      case Grade.First:
        return GraphqlGrade.FIRST;
      case Grade.Second:
        return GraphqlGrade.SECOND;
      case Grade.Third:
        return GraphqlGrade.THIRD;
      case Grade.Fourth:
        return GraphqlGrade.FOURTH;
      case Grade.Fifth:
        return GraphqlGrade.FIFTH;
      case Grade.Sixth:
        return GraphqlGrade.SIXTH;
      case Grade.Seventh:
        return GraphqlGrade.SEVENTH;
      case Grade.Eighth:
        return GraphqlGrade.EIGHTH;
      case Grade.Ninth:
        return GraphqlGrade.NINTH;
      case Grade.Tenth:
        return GraphqlGrade.TENTH;
      case Grade.Eleventh:
        return GraphqlGrade.ELEVENTH;
      case Grade.Twelfth:
        return GraphqlGrade.TWELFTH;
    }
  });
};

export const toProgram = (programFormData: ProgramFormData): Program => {
  return {
    id: programFormData.id ?? '',
    type:
      programFormData.type && programFormData.typeName
        ? { id: programFormData.type, name: programFormData.typeName }
        : undefined,
    orgId: programFormData.orgId ?? '',
    state: programFormData.programState ?? ProgramState.DRAFT,
    title: programFormData.title ?? '',
    description: programFormData.description ?? '',
    startDate: programFormData.startDate ?? '',
    endDate: programFormData.endDate ?? '',
    timeZoneIdentifier: programFormData.timeZoneIdentifier,
    visibility: programFormData.visibility ?? ProgramVisibility.UNSET,
    feeResponsibility: programFormData.feeResponsibility ?? FeeResponsibility.UNSET,
  };
};

export const toProgramFormData = (
  data: WebOrgProgramsManagementQueryProgramByIdR1Query | undefined
): ProgramFormData => {
  const schoolData = data?.school;
  return schoolData
    ? ({
        id: schoolData?.organizationProgram?.id,
        title: schoolData?.organizationProgram?.title || '',
        type: schoolData?.organizationProgram?.type?.id || '',
        typeName: schoolData?.organizationProgram?.type?.name || '',
        description: schoolData?.organizationProgram?.description || '',
        startDate: formatApiDate(schoolData?.organizationProgram?.startDate as string | undefined),
        endDate: formatApiDate(schoolData?.organizationProgram?.endDate as string | undefined),
        programVisibility: schoolData?.organizationProgram?.visibility,
        feeResponsibility: schoolData?.organizationProgram?.feeResponsibility,
        programState: schoolData?.organizationProgram?.state,
        orgId: schoolData.organizationProgram?.orgId || '',
        hasChanged: false,
        state: schoolData?.organizationProgram?.state,
        visibility: schoolData?.organizationProgram?.visibility,
        timeZoneIdentifier: schoolData?.organizationProgram?.timeZoneIdentifier,
        orgProgramTypes:
          schoolData.organizationProgramTypes.map((t) => {
            return {
              id: t.id,
              orgId: t.orgId,
              name: t.name,
              description: t.description,
            };
          }) || [],
        registrations:
          schoolData.organizationProgram?.registrationsForProgram.map((r) => {
            return {
              ...r,
              startDate: formatApiDate(r.startDate as string | undefined) ?? '',
              endDate: formatApiDate(r.endDate as string | undefined),
              eligibility: r.eligibility && {
                birthdateFrom: formatApiDate(r.eligibility.birthDateFrom as string | undefined),
                birthdateTo: formatApiDate(r.eligibility.birthDateTo as string | undefined),
                gender: mapGender(r.eligibility.gender),
                grades: mapGrades(r.eligibility.grades),
              },
              price: r.priceInBaseDenomination?.toString() ?? null,
              isFree: r.priceInBaseDenomination === 0,
              hasChanged: false,
              isDeleted: !!r.deletedAt,
              isDeletable: !!r.isDeletable,
              status: r.status ? mapRegistrationStatus(r.status) : RegistrationStatus.CLOSED,
            } as Registration;
          }) ?? [],
      } as ProgramFormData)
    : { hasChanged: false, registrations: [] };
};

export const toUpsertMutationVariablesFromForm = (formData: ProgramFormData): UpsertOrganizationProgramInput => {
  return {
    id: formData?.id,
    orgId: formData.orgId ?? '',
    state: formData.programState ?? ProgramState.DRAFT,
    title: formData.title,
    programTypeId: formData.type,
    description: formData.description,
    startDate: formData.startDate,
    endDate: formData.endDate,
    timeZoneIdentifier: Intl.DateTimeFormat().resolvedOptions().timeZone,
    visibility: formData.programVisibility ?? ProgramVisibility.UNSET,
    feeResponsibility: formData.feeResponsibility ?? FeeResponsibility.UNSET,
  };
};

export const toUpsertRegistrationMutationVariablesFromRegistration = (
  formData: ProgramFormData
): UpsertRegistrationInput[] =>
  formData.registrations
    ?.filter((registration) => registration.hasChanged || !registration.id || registration.isDeleted === false)
    .map((registration) => {
      return {
        id: registration?.id,
        // what do we do when a draft isn't saved yet
        programId: formData?.id ?? '',
        orgId: formData?.orgId ?? '',
        title: registration?.title ?? '',
        description: registration?.description ?? '',
        priceInBaseDenomination: registration?.priceInBaseDenomination ?? 0,
        currencyCode: registration?.currencyCode,
        startDate: registration?.startDate,
        timeZoneIdentifier: Intl.DateTimeFormat().resolvedOptions().timeZone,
        endDate: registration?.endDate,
        maxCapacity: registration?.maxCapacity,
        isWaitlistEnabled: registration?.isWaitlistEnabled,
        eligibility: registration?.eligibility && {
          birthDateFrom: registration?.eligibility?.birthdateFrom,
          birthDateTo: registration?.eligibility?.birthdateTo,
          gender: mapGenderInput(registration?.eligibility?.gender),
          grades: mapGradesInput(registration?.eligibility?.grades),
        },
        status: mapRegistrationStatusInput(registration?.status ?? RegistrationStatus.CLOSED),
      } as UpsertRegistrationInput;
    }) ?? [];

type UpsertRegistrationItem =
  WebOrgProgramsManagementBulkUpsertProgramRegistrationsR1Mutation['bulkUpsertProgramRegistrations'][number];
export const toRegistrationFromUpsertedRegistration = (registration: UpsertRegistrationItem): Registration => {
  return {
    ...registration, // TODO: Review for potential over-populating of properties not needed for Registration
    eligibility: registration.eligibility && {
      birthdateFrom: formatApiDate(registration.eligibility?.birthDateFrom as string | undefined) ?? '',
      birthdateTo: formatApiDate(registration.eligibility?.birthDateTo as string | undefined) ?? '',
      gender: mapGender(registration.eligibility.gender),
      grades: mapGrades(registration.eligibility.grades),
    },
    isWaitlistEnabled: registration.isWaitlistEnabled,
    hasChanged: false,
    isDeleted: false,
    isDeletable: true,
    status: mapRegistrationStatus(registration.status),
  };
};

export const toDeleteRegistrationMutationVariables = (
  registrationIds: string[]
): WebOrgProgramsManagementDeleteOrganizationProgramRegistrationsR1MutationVariables => {
  return {
    programRegistrationIds: registrationIds,
  };
};
