import { ProgramsOverviewData, RegistrationsData } from '../components/DataTable/configuration/Configuration';
import { ProgramRegistrationStatus } from '../graphql/graphqlTypes';
import { formatPrice } from './currencyUtils';

type ProgramOverviewTabCsvData = {
  name: string;
  age: number | string;
  gender: string;
  contact: string;
  registration: string;
  paymentDate: string;
  totalPaid: string;
  outstanding: string;
  refunded: string;
  status: string;
};

type RegistrationsTabCsvData = {
  title: string;
  count: number;
  totalCollected: string;
  outstanding: string;
  listPrice: string;
  capacity: string;
  registration: ProgramRegistrationStatus;
};

export const getCsvDataForProgramOverviewTab = (
  registrantsForDataTable: ProgramsOverviewData[]
): ProgramOverviewTabCsvData[] => {
  return registrantsForDataTable.map((registrant) => ({
    name: registrant.name,
    age: registrant.age,
    gender: registrant.gender,
    contact: registrant.contact,
    registration: registrant.registration.title ?? '',
    paymentDate: registrant.paymentDate?.toLocaleDateString() || '',
    totalPaid: formatPrice(registrant.totalPaid.priceInBaseDenomination, registrant.totalPaid.currencyCode),
    outstanding: formatPrice(registrant.outstanding.priceInBaseDenomination, registrant.outstanding.currencyCode),
    refunded: formatPrice(registrant.refunded.priceInBaseDenomination, registrant.refunded.currencyCode),
    status: registrant.status,
  }));
};

export const getCsvDataForRegistrationsTab = (
  registrantsForDataTable: RegistrationsData[]
): RegistrationsTabCsvData[] => {
  return registrantsForDataTable.map((registrant) => ({
    title: registrant.title.title ?? '',
    count: registrant.count,
    totalCollected: formatPrice(
      registrant.totalCollected.priceInBaseDenomination,
      registrant.totalCollected.currencyCode
    ),
    outstanding: formatPrice(registrant.outstanding.priceInBaseDenomination, registrant.outstanding.currencyCode),
    listPrice: formatPrice(registrant.listPrice.priceInBaseDenomination, registrant.listPrice.currencyCode),
    capacity: registrant.capacity.numerator + '/' + registrant.capacity.denominator,
    registration: registrant.isRegistrationOpen ? ProgramRegistrationStatus.OPEN : ProgramRegistrationStatus.CLOSED,
  }));
};
