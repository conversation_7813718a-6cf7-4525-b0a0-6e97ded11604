import { ApolloClient, useApolloClient } from '@apollo/client';

import { Program, ProgramFormData } from '../../types/programTypes';
import { toUpsertMutationVariables, toUpsertMutationVariablesFromForm } from '../../utils/typeMappers';
import {
  ProgramState,
  UpsertOrganizationProgramInput,
  useWebOrgProgramsManagementUpsertOrganizationProgramR1Mutation,
  WebOrgProgramsManagementUpsertOrganizationProgramR1Mutation,
} from '../graphqlTypes';

interface UseUpdateProgramStateResponse {
  updateProgramState: (
    state: ProgramState,
    program: Program | ProgramFormData,
    onCompleted?: (data: WebOrgProgramsManagementUpsertOrganizationProgramR1Mutation) => void,
    onError?: (error: Error) => void
  ) => void;
  updateProgramStateLoading: boolean;
  updateProgramStateError: Error | undefined;
  updatedProgram: any;
}

interface Props {
  refetch?: () => Promise<void>;
}

export const useUpdateProgramState = ({ refetch }: Props): UseUpdateProgramStateResponse => {
  const [upsertMutation, { loading, error, data }] = useWebOrgProgramsManagementUpsertOrganizationProgramR1Mutation();
  const client = useApolloClient();

  const updateProgramState = (
    state: ProgramState,
    program: Program | ProgramFormData,
    onCompleted?: (data: WebOrgProgramsManagementUpsertOrganizationProgramR1Mutation) => void,
    onError?: (error: Error) => void
  ): void => {
    let upsertInput: UpsertOrganizationProgramInput = isProgram(program)
      ? toUpsertMutationVariables(program)
      : toUpsertMutationVariablesFromForm(program);
    upsertInput.state = state;
    upsertMutation({
      variables: {
        input: upsertInput,
      },
      onCompleted: async (data) => {
        // Clear all program queries from cache
        await client.cache.evict({
          fieldName: 'programs',
        });
        await client.cache.gc();

        // Force refetch current view
        if (refetch) {
          await refetch();
        }

        onCompleted?.(data);
      },
      onError: onError,
    });
  };

  return {
    updateProgramState,
    updateProgramStateLoading: loading,
    updateProgramStateError: error,
    updatedProgram: data?.upsertOrganizationProgram,
  };
};

const isProgram = (value: Program | ProgramFormData): value is Program => {
  return (
    value !== null &&
    typeof value === 'object' &&
    // Program has required string properties
    typeof value.id === 'string' &&
    typeof value.orgId === 'string' &&
    // Program has required state and visibility properties
    'state' in value &&
    'visibility' in value &&
    'feeResponsibility' in value &&
    // ProgramFormData has registrations array, Program doesn't
    !('registrations' in value) &&
    // ProgramFormData has sidePanel property, Program doesn't
    !('sidePanel' in value) &&
    // ProgramFormData has hasChanged property, Program doesn't
    !('hasChanged' in value)
  );
};
