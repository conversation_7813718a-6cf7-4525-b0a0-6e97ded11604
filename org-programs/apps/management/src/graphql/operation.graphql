# queries

query Web_OrgProgramsManagement_QueryPrograms_r1($orgId: String!, $state: ProgramState, $includeDeleted: Boolean) {
  school(internalSchoolId: $orgId) {
    organizationPrograms(state: $state, includeDeleted: $includeDeleted) {
      id
      orgId
      state
      type {
        id
        name
      }
      title
      description
      startDate
      timeZoneIdentifier
      endDate
      visibility
      feeResponsibility
      registrationsForProgram(includeDeleted: $includeDeleted) {
        id
        programId
        orgId
        title
        priceInBaseDenomination
        currencyCode
        description
        startDate
        endDate
        timeZoneIdentifier
        maxCapacity
        isWaitlistEnabled
        deletedAt
        isDeletable
        eligibility {
          birthDateFrom
          birthDateTo
          gender
          grades
        }
        status
      }
    }
  }
}

query Web_OrgProgramsManagement_QueryProgramById_r1(
  $orgId: String!
  $organizationProgramId: String!
  $hasProgramId: Boolean!
) {
  school(internalSchoolId: $orgId) {
    organizationProgram(organizationProgramId: $organizationProgramId) @include(if: $hasProgramId) {
      id
      orgId
      state
      type {
        id
        name
      }
      title
      description
      startDate
      timeZoneIdentifier
      endDate
      visibility
      feeResponsibility
      registrationsForProgram {
        id
        programId
        orgId
        title
        priceInBaseDenomination
        currencyCode
        description
        startDate
        endDate
        timeZoneIdentifier
        maxCapacity
        isWaitlistEnabled
        deletedAt
        isDeletable
        eligibility {
          birthDateFrom
          birthDateTo
          gender
          grades
        }
        status
      }
    }
    organizationProgramTypes {
      id
      orgId
      name
      description
    }
  }
}

query Web_OrgProgramsManagement_OnboardingProgressForEntityByContext_r1($input: GetOnboardingProgressByContextInput!) {
  onboardingProgressForEntityByContext(input: $input) {
    id
    entityId
    entityType
    version
    participatingUserIds
    onboardingTemplateId
    startedAt
    completedAt
    completedBy
    onboardingStepProgresses {
      id
      onboardingStepDefinitionName
      entityId
      entityType
      version
      requirement
      startedAt
      completedAt
      completedBy
      status
      createdAt
      updatedAt
      deletedAt
    }
    status
    createdAt
    updatedAt
    deletedAt
  }
}

query Web_OrgProgramsManagement_PaymentPlatformStatusForOrganization_r1($organizationId: String!) {
    school(internalSchoolId: $organizationId) {
        paymentPlatformAccountStatus {
            chargesEnabled
            payoutsEnabled
            detailsSubmitted
            requirements {
                currentlyDue
                pastDue
            }
            updatedAt
        }
    }
}

query Web_OrgProgramsManagement_ProgramOverviewTabData_r1($schoolId: String!, $programId: String!) {
    school(internalSchoolId: $schoolId) {
        organizationProgram(organizationProgramId: $programId) {
            title
            registrationsForProgram {
                id
                title
                priceInBaseDenomination
                currencyCode
                registrants {
                    id
                    firstName
                    lastName
                    email
                    installments {
                        id
                        registrantId
                        amountInBaseDenomination
                        dueDate
                        status
                        transactions {
                          id
                          amountInBaseDenomination
                          status
                          createdAt
                        }
                    }
                }
            }
        }
    }
}


query Web_OrgProgramsManagement_RegistrationsData_r1($schoolId: String!, $programId: String!) {
    school(internalSchoolId: $schoolId) {
        organizationProgram(organizationProgramId: $programId) {
            title
            registrationsForProgram {
                id
                programId
                orgId
                title
                status
                priceInBaseDenomination
                currencyCode
                maxCapacity
                timeZoneIdentifier
                startDate
                eligibility {
                    birthDateFrom
                    birthDateTo
                    gender
                    grades
                }
                registrants{
                    id
                    installments{
                        transactions{
                            amountInBaseDenomination
                        }
                    }
                }
            }
        }
    }
}

# mutations

mutation Web_OrgProgramsManagement_UpdateOnboardingStepProgressStatus_r1($input: UpdateOnboardingStepProgressStatusInput!) {
  updateOnboardingStepProgressStatus(input: $input) {
    id
  }
}

mutation Web_OrgProgramsManagement_CompleteOnboardingProgress_r1($input: CompleteOnboardingProgressInput!) {
  completeOnboardingProgress(input: $input) {
    id
  }
}

mutation Web_OrgProgramsManagement_UpsertOrganizationProgram_r1($input: UpsertOrganizationProgramInput!) {
  upsertOrganizationProgram(input: $input) {
    id
    orgId
    state
    type {
      id
      name
    }
    title
    description
    startDate
    timeZoneIdentifier
    endDate
    visibility
    feeResponsibility
  }
}

mutation Web_OrgProgramsManagement_DeleteOrganizationProgramRegistrations_r1($programRegistrationIds: [ID!]!) {
  deleteProgramRegistrations(programRegistrationIds: $programRegistrationIds)
}

mutation Web_OrgProgramsManagement_DuplicateOrganizationProgram_r1($programId: ID!, $title: String!) {
  duplicateOrganizationProgram(programId: $programId, title: $title) {
    id
    orgId
    state
    type {
      id
      name
    }
    title
    description
    startDate
    timeZoneIdentifier
    endDate
    visibility
    feeResponsibility
  }
}

mutation Web_OrgProgramsManagement_BulkUpsertProgramRegistrations_r1($input: [UpsertRegistrationInput!]!) {
  bulkUpsertProgramRegistrations(input: $input) {
    id
    programId
    orgId
    title
    priceInBaseDenomination
    currencyCode
    description
    startDate
    timeZoneIdentifier
    endDate
    maxCapacity
    isWaitlistEnabled
    eligibility {
      birthDateFrom
      birthDateTo
      gender
      grades
    }
    status
  }
}
