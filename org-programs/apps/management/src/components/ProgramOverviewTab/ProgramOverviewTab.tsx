import { useCallback, useMemo, useState } from 'react';

import { download, generateCsv, mkConfig } from 'export-to-csv';

import { Button, Divider, IconDownload, SearchInput, Spinner, Text } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import { ProgramRegistration as GqlRegistration, Registrant } from '../../graphql/graphqlTypes';
import useProgramOverviewTabData from '../../hooks/useProgramOverviewTabData';
import { getCsvDataForProgramOverviewTab } from '../../utils/csvUtils';
import {
  getMostRecentPaymentDateFromGqlInstallments,
  getOutstandingAmountFromGqlTransactions,
  getPaymentStatusForDataTable,
  getRefundedAmountFromGqlTransactions,
  getTotalPaidAmountFromGqlTransactions,
} from '../../utils/manageProgramUtils';
import { showDownloadErrorToast } from '../../utils/toastUtils';
import {
  getConfigurationForTable,
  ProgramsOverviewData,
  TableConfiguration,
} from '../DataTable/configuration/Configuration';
import { getKeyForColumnHeader } from '../DataTable/configuration/ProgramsOverview';
import { DataTable } from '../DataTable/DataTable';
import NoSearchResults from '../NoSearchResults/NoSearchResults';
import RegistrationTotalsSection from '../RegistrationTotalsSection/RegistrationTotalsSection';

import styles from './ProgramOverviewTab.module.scss';

interface ProgramOverviewTabProps {
  schoolId: string;
  programId: string;
}

export default function ProgramOverviewTab({ schoolId, programId }: ProgramOverviewTabProps): React.JSX.Element {
  const { data, loading } = useProgramOverviewTabData(schoolId, programId);
  const [searchValue, setSearchValue] = useState('');
  const [downloading, setDownloading] = useState(false);

  const mapGqlRegistrationsToProgramOverviewDataTable = useCallback(
    (registration: GqlRegistration, shouldFilter: boolean): ProgramsOverviewData[] => {
      return registration.registrants
        .filter((registrant) => {
          if (shouldFilter) {
            // If searchValue is not null, only include registrants that match the search value
            if (!searchValue) return true;

            const fullName = `${registrant.firstName} ${registrant.lastName}`.toLowerCase();
            return fullName.includes(searchValue.toLowerCase());
          }

          return true;
        })
        .map((registrant) => ({
          id: registrant.id,
          name: `${registrant.firstName} ${registrant.lastName}`,
          age: '', // TODO: Add real age when we have the data
          gender: '', // TODO: Add real gender when we have the data
          contact: registrant.email,
          registration: {
            id: registration.id,
            title: registration.title,
          },
          paymentDate: getMostRecentPaymentDateFromGqlInstallments(
            typeof registration.createdAt === 'string' ? new Date(registration.createdAt) : new Date(),
            registrant.installments
          ),
          totalPaid: {
            currencyCode: registration.currencyCode,
            priceInBaseDenomination: getTotalPaidAmountFromGqlTransactions(
              registrant.installments.flatMap((installment) => installment.transactions)
            ),
          },
          outstanding: {
            currencyCode: registration.currencyCode,
            priceInBaseDenomination: getOutstandingAmountFromGqlTransactions(
              registration.priceInBaseDenomination,
              registrant.installments.flatMap((installment) => installment.transactions)
            ),
          },
          refunded: {
            currencyCode: registration.currencyCode,
            priceInBaseDenomination: getRefundedAmountFromGqlTransactions(
              registrant.installments.flatMap((installment) => installment.transactions)
            ),
          },
          status: getPaymentStatusForDataTable(
            registration.priceInBaseDenomination,
            getOutstandingAmountFromGqlTransactions(
              registration.priceInBaseDenomination,
              registrant.installments.flatMap((installment) => installment.transactions)
            )
          ),
          currencyCode: registration.currencyCode,
        }));
    },
    [searchValue]
  );

  const registrantsForDataTable = useMemo(() => {
    if (!loading) {
      const registrations = data?.school?.organizationProgram?.registrationsForProgram;

      if (!registrations) {
        return [];
      }

      const programOverviewData = registrations
        .map((registration) => mapGqlRegistrationsToProgramOverviewDataTable(registration as GqlRegistration, true))
        .flat();

      return programOverviewData;
    }

    return [];
  }, [
    loading,
    data?.school?.organizationProgram?.registrationsForProgram,
    mapGqlRegistrationsToProgramOverviewDataTable,
  ]);

  const allRegistrants = useMemo(() => {
    if (!loading && data?.school?.organizationProgram?.registrationsForProgram) {
      return data.school.organizationProgram.registrationsForProgram.flatMap(
        (registration) => (registration?.registrants || []) as Registrant[]
      );
    }
    return [];
  }, [loading, data?.school?.organizationProgram?.registrationsForProgram]);

  const currencyCode = useMemo(() => {
    if (!loading && data?.school?.organizationProgram?.registrationsForProgram) {
      return data.school.organizationProgram.registrationsForProgram.find((registration) => registration?.currencyCode)
        ?.currencyCode;
    }
    return undefined;
  }, [loading, data?.school?.organizationProgram?.registrationsForProgram]);

  const handleClearSearch = useCallback(() => {
    setSearchValue('');
  }, []);

  const handleDownload = useCallback(() => {
    setDownloading(true);
    try {
      const allProgramsOverviewData =
        data?.school?.organizationProgram?.registrationsForProgram
          .map((registration) => mapGqlRegistrationsToProgramOverviewDataTable(registration as GqlRegistration, false))
          .flat() ?? [];

      const csvData = getCsvDataForProgramOverviewTab(allProgramsOverviewData);
      const configuration = getConfigurationForTable(TableConfiguration.ProgramsOverview);

      const csvConfig = mkConfig({
        columnHeaders: configuration.columnHeaders.map((header) => ({
          key: getKeyForColumnHeader(header as string),
          displayLabel: header as string,
        })),
        filename: `${data?.school?.organizationProgram?.title}-Overview-${new Date().toISOString()}`,
      });
      const csv = generateCsv(csvConfig)(csvData);

      download(csvConfig)(csv);
    } catch (error) {
      showDownloadErrorToast(handleDownload);
    } finally {
      setDownloading(false);
    }
  }, [
    data?.school?.organizationProgram?.registrationsForProgram,
    data?.school?.organizationProgram?.title,
    mapGqlRegistrationsToProgramOverviewDataTable,
  ]);

  if (loading) {
    return (
      <div className={styles.programOverviewTabContainer}>
        <Spinner />
      </div>
    );
  }

  return (
    <div className={styles.programOverviewTabContainer} data-qa-id="program-overview-tab-container">
      <RegistrationTotalsSection registrants={allRegistrants} currencyCode={currencyCode} />
      <Divider color="level0-accent" orientation="horizontal" />
      <div className={styles.programOverviewTabHeader} data-qa-id="program-overview-tab-header-container">
        <Text className={styles.programOverviewTabHeaderText}>{format('org-programs.manage.registrants')}</Text>
        <div
          className={styles.programOverviewTabHeaderActions}
          data-qa-id="program-overview-tab-header-actions-container"
        >
          <SearchInput
            qaId="program-overview-tab-search-input"
            value={searchValue}
            onChange={setSearchValue}
            formSize="small"
            placeholder={format('org-programs.manage.search-by-name')}
          />
          <Button
            size="small"
            status={downloading ? 'spinning' : undefined}
            icon={<IconDownload />}
            buttonType="subtle"
            onPress={handleDownload}
            data-qa-id="program-overview-tab-download-button"
          >
            {format('org-programs.manage.download')}
          </Button>
        </div>
      </div>
      <div
        className={styles.programOverviewTabDataTableContainer}
        data-qa-id="program-overview-tab-data-table-container"
      >
        {searchValue && registrantsForDataTable.length === 0 ? (
          <NoSearchResults searchTerm={searchValue} onClearSearch={handleClearSearch} />
        ) : (
          <DataTable
            configuration={TableConfiguration.ProgramsOverview}
            data={registrantsForDataTable}
            qaId="program-overview-tab-data-table"
            emptyStateHeading={format('org-programs.manage.no-registrants')}
            emptyStateSubheading={format('org-programs.manage.no-registrants-subtext')}
          />
        )}
      </div>
    </div>
  );
}
