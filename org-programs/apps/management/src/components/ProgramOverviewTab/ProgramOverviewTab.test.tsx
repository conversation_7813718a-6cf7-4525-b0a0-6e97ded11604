/* eslint-disable @typescript-eslint/no-unsafe-return -- Mock functions return any type for testing */

/* eslint-disable @typescript-eslint/no-explicit-any -- Test data uses any to bypass strict GraphQL typing */
import React from 'react';

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { InstallmentStatus, TransactionStatus } from '../../graphql/graphqlTypes';
import useProgramOverviewTabData from '../../hooks/useProgramOverviewTabData';
import {
  getMostRecentPaymentDateFromGqlInstallments,
  getOutstandingAmountFromGqlTransactions,
  getPaymentStatusForDataTable,
  getRefundedAmountFromGqlTransactions,
  getTotalPaidAmountFromGqlTransactions,
} from '../../utils/manageProgramUtils';
import { TableConfiguration } from '../DataTable/configuration/Configuration';
import { DataTable } from '../DataTable/DataTable';
import ProgramOverviewTab from './ProgramOverviewTab';

// Mock dependencies
vi.mock('@hudl/uniform-web', () => ({
  Button: vi.fn(({ children, onPress, ...props }) => (
    <button onClick={onPress} {...props}>
      {children}
    </button>
  )),
  IconDownload: vi.fn(() => <span data-qa-id="icon-download">Download Icon</span>),
  SearchInput: vi.fn(({ value, onChange, placeholder, ...props }) => (
    <input
      data-testid="search-input"
      data-qa-id="program-overview-tab-search-input"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      {...props}
    />
  )),
  Spinner: vi.fn(() => <div data-qa-id="spinner">Loading...</div>),
  Text: vi.fn(({ children, className, ...props }) => (
    <span className={className} {...props}>
      {children}
    </span>
  )),
  Title: vi.fn(({ children, className, ...props }) => (
    <h2 className={className} {...props}>
      {children}
    </h2>
  )),
  Lead: vi.fn(({ children, className, ...props }) => (
    <p className={className} {...props}>
      {children}
    </p>
  )),
  Divider: vi.fn(() => <div></div>),
}));

vi.mock('frontends-i18n', () => ({
  format: vi.fn((key: string, params?: { searchValue?: string }) => {
    const translations: Record<string, string> = {
      'org-programs.manage.search-by-name': 'Search by name',
      'org-programs.manage.download': 'Download',
      'org-programs.manage.no-results-found': `No results found for "${params?.searchValue}"`,
      'org-programs.manage.no-results-found-subtext': 'Try adjusting your search terms',
      'org-programs.manage.clear-search': 'Clear search',
      'org-programs.manage.no-registrants': 'No registrants',
      'org-programs.manage.no-registrants-subtext': 'No registrants found for this program',
      'org-programs.manage.registrants': 'Registrants',
    };
    return translations[key] || key;
  }),
}));

vi.mock('../../hooks/useProgramOverviewTabData');
vi.mock('../DataTable/DataTable');
vi.mock('../../utils/manageProgramUtils');

// Mock the utility functions
const mockGetMostRecentPaymentDateFromGqlInstallments = vi.mocked(getMostRecentPaymentDateFromGqlInstallments);
const mockGetOutstandingAmountFromGqlTransactions = vi.mocked(getOutstandingAmountFromGqlTransactions);
const mockGetPaymentStatusForDataTable = vi.mocked(getPaymentStatusForDataTable);
const mockGetRefundedAmountFromGqlTransactions = vi.mocked(getRefundedAmountFromGqlTransactions);
const mockGetTotalPaidAmountFromGqlTransactions = vi.mocked(getTotalPaidAmountFromGqlTransactions);

// Mock the hook
const mockUseProgramOverviewTabData = vi.mocked(useProgramOverviewTabData);

// Mock the DataTable component
const MockDataTable = vi.mocked(DataTable);

describe('ProgramOverviewTab', () => {
  const defaultProps = {
    schoolId: 'school-123',
    programId: 'program-456',
  };

  const mockRegistrationData = {
    school: {
      organizationProgram: {
        registrationsForProgram: [
          {
            __typename: 'ProgramRegistration',
            id: 'registration-1',
            title: 'Summer Camp 2024',
            currencyCode: 'USD',
            priceInBaseDenomination: 50000, // $500.00
            registrants: [
              {
                __typename: 'Registrant',
                id: 'registrant-1',
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                installments: [
                  {
                    __typename: 'Installment',
                    id: 'installment-1',
                    installmentPlanId: 'plan-1',
                    installmentNumber: 1,
                    registrantId: 'registrant-1',
                    amountInBaseDenomination: 25000,
                    dueDate: '2024-01-20T10:00:00Z',
                    status: InstallmentStatus.PAID,
                    transactions: [
                      {
                        __typename: 'Transaction',
                        id: 'transaction-1',
                        status: TransactionStatus.COMPLETED,
                        amountInBaseDenomination: 25000,
                        createdAt: '2024-01-20T10:00:00Z',
                      },
                    ],
                  },
                ],
              },
              {
                __typename: 'Registrant',
                id: 'registrant-2',
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
                installments: [
                  {
                    __typename: 'Installment',
                    id: 'installment-2',
                    installmentPlanId: 'plan-2',
                    installmentNumber: 1,
                    registrantId: 'registrant-2',
                    amountInBaseDenomination: 50000,
                    dueDate: '2024-01-25T10:00:00Z',
                    status: InstallmentStatus.PAID,
                    transactions: [
                      {
                        __typename: 'Transaction',
                        id: 'transaction-2',
                        status: TransactionStatus.COMPLETED,
                        amountInBaseDenomination: 50000,
                        createdAt: '2024-01-25T10:00:00Z',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    },
  } as any; // Use any to bypass strict typing for test data

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock implementations
    mockGetMostRecentPaymentDateFromGqlInstallments.mockReturnValue(new Date('2024-01-25T10:00:00Z'));
    mockGetOutstandingAmountFromGqlTransactions.mockReturnValue(0);
    mockGetPaymentStatusForDataTable.mockReturnValue('Paid');
    mockGetRefundedAmountFromGqlTransactions.mockReturnValue(0);
    mockGetTotalPaidAmountFromGqlTransactions.mockReturnValue(50000);

    MockDataTable.mockImplementation(({ data, configuration, qaId, emptyStateHeading, emptyStateSubheading }) => (
      <div data-qa-id={qaId}>
        <div data-qa-id="table-configuration">{configuration}</div>
        <div data-qa-id="table-data-count">{data?.length || 0}</div>
        <div data-qa-id="empty-state-heading">{emptyStateHeading}</div>
        <div data-qa-id="empty-state-subheading">{emptyStateSubheading}</div>
      </div>
    ));
  });

  describe('Loading State', () => {
    it('shows spinner when data is loading', () => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: undefined,
        loading: true,
        error: undefined,
      });

      render(<ProgramOverviewTab {...defaultProps} />);

      expect(screen.getByTestId('spinner')).toBeInTheDocument();
    });
  });

  describe('Data Rendering', () => {
    beforeEach(() => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('renders registrants table with correct data', () => {
      render(<ProgramOverviewTab {...defaultProps} />);

      expect(screen.getByText('Registrants')).toBeInTheDocument();
      expect(screen.getByTestId('program-overview-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('table-configuration')).toHaveTextContent(TableConfiguration.ProgramsOverview);
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('2');
    });

    it('renders search input and download button', () => {
      render(<ProgramOverviewTab {...defaultProps} />);

      expect(screen.getByTestId('program-overview-tab-search-input')).toBeInTheDocument();
      expect(screen.getByTestId('program-overview-tab-search-input')).toHaveAttribute('placeholder', 'Search by name');
      expect(screen.getByText('Download')).toBeInTheDocument();
    });

    it('calls utility functions with correct parameters', () => {
      render(<ProgramOverviewTab {...defaultProps} />);

      expect(mockGetMostRecentPaymentDateFromGqlInstallments).toHaveBeenCalled();
      expect(mockGetOutstandingAmountFromGqlTransactions).toHaveBeenCalled();
      expect(mockGetPaymentStatusForDataTable).toHaveBeenCalled();
      expect(mockGetRefundedAmountFromGqlTransactions).toHaveBeenCalled();
      expect(mockGetTotalPaidAmountFromGqlTransactions).toHaveBeenCalled();
    });

    it('passes correct props to DataTable', () => {
      render(<ProgramOverviewTab {...defaultProps} />);

      expect(MockDataTable).toHaveBeenCalledWith(
        expect.objectContaining({
          configuration: TableConfiguration.ProgramsOverview,
          data: expect.arrayContaining([
            expect.objectContaining({
              id: 'registrant-1',
              name: 'John Doe',
              contact: '<EMAIL>',
              registration: {
                id: 'registration-1',
                title: 'Summer Camp 2024',
              },
              currencyCode: 'USD',
            }),
            expect.objectContaining({
              id: 'registrant-2',
              name: 'Jane Smith',
              contact: '<EMAIL>',
              registration: {
                id: 'registration-1',
                title: 'Summer Camp 2024',
              },
              currencyCode: 'USD',
            }),
          ]),
          qaId: 'program-overview-tab-data-table',
          emptyStateHeading: 'No registrants',
          emptyStateSubheading: 'No registrants found for this program',
        }),
        expect.anything()
      );
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('filters registrants by name when search value is entered', async () => {
      const user = userEvent.setup();
      render(<ProgramOverviewTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-overview-tab-search-input');
      await user.type(searchInput, 'John');

      // Should only show John Doe
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('1');
    });

    it('shows no results when search has no matches', async () => {
      const user = userEvent.setup();
      render(<ProgramOverviewTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-overview-tab-search-input');
      await user.type(searchInput, 'NonExistent');

      expect(screen.getByText('No results found for "NonExistent"')).toBeInTheDocument();
      expect(screen.getByText('Try adjusting your search terms')).toBeInTheDocument();
      expect(screen.getByText('Clear search')).toBeInTheDocument();
    });

    it('clears search when clear search button is clicked', async () => {
      const user = userEvent.setup();
      render(<ProgramOverviewTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-overview-tab-search-input');
      await user.type(searchInput, 'asdfasdfasdf');

      // Click clear search button
      const clearButton = screen.getByText('Clear search');
      await user.click(clearButton);

      // Verify search is cleared and all results are shown
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('2');
      expect(searchInput).toHaveValue('');
    });

    it('performs case-insensitive search', async () => {
      const user = userEvent.setup();
      render(<ProgramOverviewTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-overview-tab-search-input');
      await user.type(searchInput, 'jane');

      // Should find Jane Smith despite lowercase search
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('1');
    });
  });

  describe('Empty States', () => {
    it('shows empty state when no registrations exist', () => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: {
          school: {
            organizationProgram: {
              title: 'Test Program',
              registrationsForProgram: [],
            },
          },
        },
        loading: false,
        error: undefined,
      });

      render(<ProgramOverviewTab {...defaultProps} />);

      expect(screen.getByTestId('program-overview-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('empty-state-heading')).toHaveTextContent('No registrants');
      expect(screen.getByTestId('empty-state-subheading')).toHaveTextContent('No registrants found for this program');
    });

    it('shows empty state when registrations exist but have no registrants', () => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: {
          school: {
            organizationProgram: {
              registrationsForProgram: [
                {
                  __typename: 'ProgramRegistration',
                  id: 'registration-1',
                  title: 'Empty Registration',
                  currencyCode: 'USD',
                  priceInBaseDenomination: 50000,
                  registrants: [],
                },
              ],
            },
          },
        } as any,
        loading: false,
        error: undefined,
      });

      render(<ProgramOverviewTab {...defaultProps} />);

      expect(screen.getByTestId('program-overview-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('empty-state-heading')).toHaveTextContent('No registrants');
      expect(screen.getByTestId('empty-state-subheading')).toHaveTextContent('No registrants found for this program');
    });
  });

  describe('Error Handling', () => {
    it('handles undefined data gracefully', () => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: undefined,
        loading: false,
        error: undefined,
      });

      render(<ProgramOverviewTab {...defaultProps} />);

      expect(screen.getByTestId('program-overview-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('0');
    });

    it('handles null registrations gracefully', () => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: {
          school: {
            organizationProgram: {
              registrationsForProgram: null,
            },
          },
        } as any,
        loading: false,
        error: undefined,
      });

      render(<ProgramOverviewTab {...defaultProps} />);

      expect(screen.getByTestId('program-overview-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('0');
    });
  });

  describe('Data Transformation', () => {
    beforeEach(() => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('correctly transforms registration data to table format', () => {
      render(<ProgramOverviewTab {...defaultProps} />);

      const dataTableCall = MockDataTable.mock.calls[0][0];
      const transformedData = dataTableCall.data;

      expect(transformedData).toHaveLength(2);
      expect(transformedData[0]).toMatchObject({
        id: 'registrant-1',
        name: 'John Doe',
        age: '',
        gender: '',
        contact: '<EMAIL>',
        registration: {
          id: 'registration-1',
          title: 'Summer Camp 2024',
        },
        currencyCode: 'USD',
      });
    });

    it('filters out registrants that do not match search criteria', async () => {
      const user = userEvent.setup();
      render(<ProgramOverviewTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-overview-tab-search-input');
      await user.type(searchInput, 'Doe');

      const dataTableCall = MockDataTable.mock.calls[1][0]; // Second call after search
      const filteredData = dataTableCall.data;

      expect(filteredData).toHaveLength(1);
      expect((filteredData[0] as any).name).toBe('John Doe');
    });
  });

  describe('Component Structure', () => {
    beforeEach(() => {
      mockUseProgramOverviewTabData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('renders header with correct structure', () => {
      render(<ProgramOverviewTab {...defaultProps} />);

      expect(screen.getByText('Registrants')).toBeInTheDocument();
      expect(screen.getByTestId('program-overview-tab-data-table')).toBeInTheDocument();
      expect(screen.getByText('Download')).toBeInTheDocument();
    });
  });
});

/* eslint-enable @typescript-eslint/no-unsafe-return -- End of test file */
/* eslint-enable @typescript-eslint/no-explicit-any -- End of test file */
