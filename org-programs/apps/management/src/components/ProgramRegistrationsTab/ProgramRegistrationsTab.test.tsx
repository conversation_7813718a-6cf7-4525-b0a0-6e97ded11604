/* eslint-disable @typescript-eslint/no-unsafe-return -- Mock functions return any type for testing */

/* eslint-disable @typescript-eslint/no-explicit-any -- Test data uses any to bypass strict GraphQL typing */
import React from 'react';

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { ProgramRegistrationStatus } from '../../graphql/graphqlTypes';
import useRegistrationsData from '../../hooks/useRegistrationsData';
import { TableConfiguration } from '../DataTable/configuration/Configuration';
import { DataTable } from '../DataTable/DataTable';
import NoSearchResults from '../NoSearchResults/NoSearchResults';
import ProgramRegistrationsTab from './ProgramRegistrationsTab';

// Mock dependencies
vi.mock('@hudl/uniform-web', () => ({
  Button: vi.fn(({ children, onPress, onClick, ...props }) => (
    <button onClick={onPress || onClick} {...props}>
      {children}
    </button>
  )),
  IconDownload: vi.fn(() => <span data-qa-id="icon-download">Download Icon</span>),
  SearchInput: vi.fn(({ value, onChange, onDismissClick, placeholder, ...props }) => (
    <div>
      <input
        data-testid="search-input"
        data-qa-id="program-registrations-tab-search-input"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        {...props}
      />
      {value && (
        <button onClick={onDismissClick} data-testid="dismiss-search" data-qa-id="dismiss-search">
          Clear
        </button>
      )}
    </div>
  )),
}));

vi.mock('frontends-i18n', () => ({
  format: vi.fn((key: string) => {
    const translations: Record<string, string> = {
      'org-programs.manage.search-by-title': 'Search by title',
      'org-programs.manage.download': 'Download',
      'org-programs.manage.no-results-found': 'No results found for "{searchTerm}"',
      'org-programs.manage.no-results-found-subtext': 'Check your spelling or try another search',
      'org-programs.manage.clear-search': 'Clear Search',
      'org-programs.manage.no-registrations': 'No registrations',
      'org-programs.manage.no-registrations-subtext': 'No registrations found for this program',
      'org-programs.manage.registrations': 'Registrations',
    };
    return translations[key] || key;
  }),
}));

vi.mock('../../hooks/useRegistrationsData');
vi.mock('../DataTable/DataTable');
vi.mock('../NoSearchResults/NoSearchResults');

// Mock the hook
const mockUseRegistrationsData = vi.mocked(useRegistrationsData);

// Mock the DataTable component
const MockDataTable = vi.mocked(DataTable);

// Mock the NoSearchResults component
const MockNoSearchResults = vi.mocked(NoSearchResults);

// Mock the mutation hook
const mockUpdateRegistrationStatus = vi.fn();
vi.mock('../../graphql/graphqlTypes', async () => {
  const actual = await vi.importActual('../../graphql/graphqlTypes');
  return {
    ...actual,
    useWebOrgProgramsManagementBulkUpsertProgramRegistrationsR1Mutation: () => [
      mockUpdateRegistrationStatus,
      { loading: false, error: null },
    ],
  };
});

describe('ProgramRegistrationsTab', () => {
  const defaultProps = {
    schoolId: 'school-123',
    programId: 'program-456',
  };

  const mockRegistrationData = {
    school: {
      organizationProgram: {
        title: 'Program Title',
        registrationsForProgram: [
          {
            __typename: 'ProgramRegistration',
            id: 'registration-1',
            programId: 'program-456',
            orgId: 'org-123',
            title: 'Summer Camp 2024',
            currencyCode: 'USD',
            priceInBaseDenomination: 50000, // $500.00
            startDate: '2024-06-01T00:00:00Z',
            timeZoneIdentifier: 'America/New_York',
            maxCapacity: 100,
            status: ProgramRegistrationStatus.OPEN,
            eligibility: {
              birthDateFrom: '2010-01-01T00:00:00Z',
              birthDateTo: '2015-12-31T23:59:59Z',
              gender: 'MALE',
              grades: ['FIRST', 'SECOND', 'THIRD'],
            },
            registrants: [
              {
                __typename: 'Registrant',
                id: 'registrant-1',
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                installments: [
                  {
                    __typename: 'Installment',
                    id: 'installment-1',
                    installmentPlanId: 'plan-1',
                    installmentNumber: 1,
                    registrantId: 'registrant-1',
                    amountInBaseDenomination: 25000,
                    dueDate: '2024-01-20T10:00:00Z',
                    status: 'PAID',
                    transactions: [
                      {
                        __typename: 'Transaction',
                        id: 'transaction-1',
                        status: 'COMPLETED',
                        amountInBaseDenomination: 25000,
                        createdAt: '2024-01-20T10:00:00Z',
                      },
                    ],
                  },
                ],
              },
              {
                __typename: 'Registrant',
                id: 'registrant-2',
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
                installments: [
                  {
                    __typename: 'Installment',
                    id: 'installment-2',
                    installmentPlanId: 'plan-2',
                    installmentNumber: 1,
                    registrantId: 'registrant-2',
                    amountInBaseDenomination: 50000,
                    dueDate: '2024-01-25T10:00:00Z',
                    status: 'PAID',
                    transactions: [
                      {
                        __typename: 'Transaction',
                        id: 'transaction-2',
                        status: 'COMPLETED',
                        amountInBaseDenomination: 50000,
                        createdAt: '2024-01-25T10:00:00Z',
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            __typename: 'ProgramRegistration',
            id: 'registration-2',
            programId: 'program-456',
            orgId: 'org-123',
            title: 'Winter Training 2024',
            currencyCode: 'USD',
            priceInBaseDenomination: 75000, // $750.00
            startDate: '2024-12-01T00:00:00Z',
            timeZoneIdentifier: 'America/New_York',
            maxCapacity: 50,
            status: ProgramRegistrationStatus.CLOSED,
            eligibility: {
              birthDateFrom: '2012-01-01T00:00:00Z',
              birthDateTo: '2018-12-31T23:59:59Z',
              gender: 'FEMALE',
              grades: ['FOURTH', 'FIFTH', 'SIXTH'],
            },
            registrants: [
              {
                __typename: 'Registrant',
                id: 'registrant-3',
                firstName: 'Bob',
                lastName: 'Johnson',
                email: '<EMAIL>',
                installments: [
                  {
                    __typename: 'Installment',
                    id: 'installment-3',
                    installmentPlanId: 'plan-3',
                    installmentNumber: 1,
                    registrantId: 'registrant-3',
                    amountInBaseDenomination: 75000,
                    dueDate: '2024-11-20T10:00:00Z',
                    status: 'PAID',
                    transactions: [
                      {
                        __typename: 'Transaction',
                        id: 'transaction-3',
                        status: 'COMPLETED',
                        amountInBaseDenomination: 75000,
                        createdAt: '2024-11-20T10:00:00Z',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    },
  } as any; // Use any to bypass strict typing for test data

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock implementations
    MockDataTable.mockImplementation(({ data, configuration, isLoading }) => (
      <div data-qa-id="program-registrations-tab-data-table">
        <div data-qa-id="table-configuration">{configuration}</div>
        <div data-qa-id="table-data-count">{data?.length || 0}</div>
        <div data-qa-id="table-loading">{isLoading ? 'Loading' : 'Not Loading'}</div>
      </div>
    ));

    MockNoSearchResults.mockImplementation(({ searchTerm, onClearSearch }) => (
      <div data-qa-id="no-search-results">
        <div data-qa-id="search-term">{searchTerm}</div>
        <button onClick={onClearSearch} data-testid="clear-search-results" data-qa-id="clear-search-results">
          Clear Search
        </button>
      </div>
    ));
  });

  describe('Loading State', () => {
    it('shows loading state when data is loading', () => {
      mockUseRegistrationsData.mockReturnValue({
        data: undefined,
        loading: true,
        error: undefined,
      });

      render(<ProgramRegistrationsTab {...defaultProps} />);

      expect(screen.getByTestId('table-loading')).toHaveTextContent('Loading');
    });
  });

  describe('Data Rendering', () => {
    beforeEach(() => {
      mockUseRegistrationsData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('renders registrations table with correct data', () => {
      render(<ProgramRegistrationsTab {...defaultProps} />);

      expect(screen.getByTestId('program-registrations-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('table-configuration')).toHaveTextContent(TableConfiguration.Registrations);
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('2');
    });

    it('renders search input and download button', () => {
      render(<ProgramRegistrationsTab {...defaultProps} />);

      expect(screen.getByTestId('program-registrations-tab-search-input')).toBeInTheDocument();
      expect(screen.getByTestId('program-registrations-tab-search-input')).toHaveAttribute(
        'placeholder',
        'Search by title'
      );
      expect(screen.getByText('Download')).toBeInTheDocument();
    });

    it('passes correct props to DataTable', () => {
      render(<ProgramRegistrationsTab {...defaultProps} />);

      expect(MockDataTable).toHaveBeenCalledWith(
        expect.objectContaining({
          configuration: TableConfiguration.Registrations,
          data: expect.arrayContaining([
            expect.objectContaining({
              title: {
                id: 'registration-1',
                title: 'Summer Camp 2024',
                priceInBaseDenomination: 50000,
                currencyCode: 'USD',
              },
              count: 2,
              totalCollected: {
                priceInBaseDenomination: 75000, // 25000 + 50000
                currencyCode: 'USD',
              },
              outstanding: {
                priceInBaseDenomination: 25000, // (50000 * 2) - 75000
                currencyCode: 'USD',
              },
              listPrice: {
                priceInBaseDenomination: 50000,
                currencyCode: 'USD',
              },
              capacity: {
                numerator: 2,
                denominator: 100,
              },
              isRegistrationOpen: true,
            }),
            expect.objectContaining({
              title: {
                id: 'registration-2',
                title: 'Winter Training 2024',
                priceInBaseDenomination: 75000,
                currencyCode: 'USD',
              },
              count: 1,
              totalCollected: {
                priceInBaseDenomination: 75000,
                currencyCode: 'USD',
              },
              outstanding: {
                priceInBaseDenomination: 0, // (75000 * 1) - 75000
                currencyCode: 'USD',
              },
              listPrice: {
                priceInBaseDenomination: 75000,
                currencyCode: 'USD',
              },
              capacity: {
                numerator: 1,
                denominator: 50,
              },
              isRegistrationOpen: false,
            }),
          ]),
          isLoading: false,
        }),
        expect.anything()
      );
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      mockUseRegistrationsData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('filters registrations by title when search value is entered', async () => {
      const user = userEvent.setup();
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-registrations-tab-search-input');
      await user.type(searchInput, 'Summer');

      // Should only show Summer Camp 2024
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('1');
    });

    it('shows no results component when search has no matches', async () => {
      const user = userEvent.setup();
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-registrations-tab-search-input');
      await user.type(searchInput, 'NonExistent');

      expect(screen.getByTestId('no-search-results')).toBeInTheDocument();
      expect(screen.getByTestId('search-term')).toHaveTextContent('NonExistent');
    });

    it('clears search when dismiss button is clicked', async () => {
      const user = userEvent.setup();
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-registrations-tab-search-input');
      await user.type(searchInput, 'Summer');

      // Click dismiss button
      const dismissButton = screen.getByTestId('dismiss-search');
      await user.click(dismissButton);

      // Verify search is cleared and all results are shown
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('2');
      expect(searchInput).toHaveValue('');
    });

    it('clears search when clear search button in NoSearchResults is clicked', async () => {
      const user = userEvent.setup();
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-registrations-tab-search-input');
      await user.type(searchInput, 'NonExistent');

      // Click clear search button in NoSearchResults
      const clearButton = screen.getByTestId('clear-search-results');
      await user.click(clearButton);

      // Verify search is cleared and all results are shown
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('2');
      expect(searchInput).toHaveValue('');
    });

    it('performs case-insensitive search', async () => {
      const user = userEvent.setup();
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-registrations-tab-search-input');
      await user.type(searchInput, 'summer');

      // Should find Summer Camp 2024 despite lowercase search
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('1');
    });
  });

  describe('Registration Toggle Functionality', () => {
    beforeEach(() => {
      mockUseRegistrationsData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('calls updateRegistrationStatus with correct parameters when toggle is triggered', async () => {
      mockUpdateRegistrationStatus.mockResolvedValue({} as any);

      render(<ProgramRegistrationsTab {...defaultProps} />);

      // Get the onToggle function from the first registration
      const dataTableCall = MockDataTable.mock.calls[0][0];
      const firstRegistration = dataTableCall.data[0] as any;

      // Call the onToggle function
      await firstRegistration.onToggle(false);

      expect(mockUpdateRegistrationStatus).toHaveBeenCalledWith({
        variables: {
          input: [
            {
              id: 'registration-1',
              programId: 'program-456',
              orgId: 'org-123',
              title: 'Summer Camp 2024',
              priceInBaseDenomination: 50000,
              currencyCode: 'USD',
              maxCapacity: 100,
              eligibility: {
                birthDateFrom: '2010-01-01T00:00:00Z',
                birthDateTo: '2015-12-31T23:59:59Z',
                gender: 'MALE',
                grades: ['FIRST', 'SECOND', 'THIRD'],
              },
              startDate: '2024-06-01T00:00:00Z',
              timeZoneIdentifier: 'America/New_York',
              status: ProgramRegistrationStatus.CLOSED,
            },
          ],
        },
        refetchQueries: ['WebOrgProgramsManagementRegistrationsDataR1'],
      });
    });

    it('handles toggle to open status correctly', async () => {
      mockUpdateRegistrationStatus.mockResolvedValue({} as any);

      render(<ProgramRegistrationsTab {...defaultProps} />);

      // Get the onToggle function from the second registration (which is closed)
      const dataTableCall = MockDataTable.mock.calls[0][0];
      const secondRegistration = dataTableCall.data[1] as any;

      // Call the onToggle function to open
      await secondRegistration.onToggle(true);

      expect(mockUpdateRegistrationStatus).toHaveBeenCalledWith({
        variables: {
          input: [
            {
              id: 'registration-2',
              programId: 'program-456',
              orgId: 'org-123',
              title: 'Winter Training 2024',
              priceInBaseDenomination: 75000,
              currencyCode: 'USD',
              maxCapacity: 50,
              eligibility: {
                birthDateFrom: '2012-01-01T00:00:00Z',
                birthDateTo: '2018-12-31T23:59:59Z',
                gender: 'FEMALE',
                grades: ['FOURTH', 'FIFTH', 'SIXTH'],
              },
              startDate: '2024-12-01T00:00:00Z',
              timeZoneIdentifier: 'America/New_York',
              status: ProgramRegistrationStatus.OPEN,
            },
          ],
        },
        refetchQueries: ['WebOrgProgramsManagementRegistrationsDataR1'],
      });
    });

    it('handles update error gracefully', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockUpdateRegistrationStatus.mockRejectedValue(new Error('Update failed'));

      render(<ProgramRegistrationsTab {...defaultProps} />);

      // Get the onToggle function from the first registration
      const dataTableCall = MockDataTable.mock.calls[0][0];
      const firstRegistration = dataTableCall.data[0] as any;

      // Call the onToggle function
      await firstRegistration.onToggle(false);

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to update registration status:', expect.any(Error));

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Empty States', () => {
    it('shows empty state when no registrations exist', () => {
      mockUseRegistrationsData.mockReturnValue({
        data: {
          school: {
            organizationProgram: {
              title: 'Program Title',
              registrationsForProgram: [],
            },
          },
        },
        loading: false,
        error: undefined,
      });

      render(<ProgramRegistrationsTab {...defaultProps} />);

      expect(screen.getByTestId('program-registrations-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('0');
    });

    it('handles undefined data gracefully', () => {
      mockUseRegistrationsData.mockReturnValue({
        data: undefined,
        loading: false,
        error: undefined,
      });

      render(<ProgramRegistrationsTab {...defaultProps} />);

      expect(screen.getByTestId('program-registrations-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('0');
    });

    it('handles null registrations gracefully', () => {
      mockUseRegistrationsData.mockReturnValue({
        data: {
          school: {
            organizationProgram: {
              title: 'Program Title',
              registrationsForProgram: null,
            },
          },
        } as any,
        loading: false,
        error: undefined,
      });

      render(<ProgramRegistrationsTab {...defaultProps} />);

      expect(screen.getByTestId('program-registrations-tab-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('table-data-count')).toHaveTextContent('0');
    });
  });

  describe('Data Transformation', () => {
    beforeEach(() => {
      mockUseRegistrationsData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('correctly calculates total collected from positive transactions', () => {
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const dataTableCall = MockDataTable.mock.calls[0][0];
      const transformedData = dataTableCall.data as any[];

      // Summer Camp has 2 registrants with 25000 + 50000 = 75000 total collected
      expect(transformedData[0].totalCollected.priceInBaseDenomination).toBe(75000);

      // Winter Training has 1 registrant with 75000 total collected
      expect(transformedData[1].totalCollected.priceInBaseDenomination).toBe(75000);
    });

    it('correctly calculates outstanding amounts', () => {
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const dataTableCall = MockDataTable.mock.calls[0][0];
      const transformedData = dataTableCall.data as any[];

      // Summer Camp: (50000 * 2) - 75000 = 25000 outstanding
      expect(transformedData[0].outstanding.priceInBaseDenomination).toBe(25000);

      // Winter Training: (75000 * 1) - 75000 = 0 outstanding
      expect(transformedData[1].outstanding.priceInBaseDenomination).toBe(0);
    });

    it('correctly sets registration status', () => {
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const dataTableCall = MockDataTable.mock.calls[0][0];
      const transformedData = dataTableCall.data as any[];

      expect(transformedData[0].isRegistrationOpen).toBe(true); // Summer Camp is OPEN
      expect(transformedData[1].isRegistrationOpen).toBe(false); // Winter Training is CLOSED
    });

    it('filters out registrations that do not match search criteria', async () => {
      const user = userEvent.setup();
      render(<ProgramRegistrationsTab {...defaultProps} />);

      const searchInput = screen.getByTestId('program-registrations-tab-search-input');
      await user.type(searchInput, 'Winter');

      const dataTableCall = MockDataTable.mock.calls[1][0]; // Second call after search
      const filteredData = dataTableCall.data as any[];

      expect(filteredData).toHaveLength(1);
      expect(filteredData[0].title.title).toBe('Winter Training 2024');
    });
  });

  describe('Component Structure', () => {
    beforeEach(() => {
      mockUseRegistrationsData.mockReturnValue({
        data: mockRegistrationData,
        loading: false,
        error: undefined,
      });
    });

    it('renders with correct structure', () => {
      render(<ProgramRegistrationsTab {...defaultProps} />);

      expect(screen.getByTestId('program-registrations-tab-search-input')).toBeInTheDocument();
      expect(screen.getByTestId('program-registrations-tab-data-table')).toBeInTheDocument();
      expect(screen.getByText('Download')).toBeInTheDocument();
    });

    it('handles registration with no title gracefully', () => {
      const dataWithNoTitle = {
        school: {
          organizationProgram: {
            title: 'Program Title',
            registrationsForProgram: [
              {
                ...mockRegistrationData.school.organizationProgram.registrationsForProgram[0],
                title: null,
              },
            ],
          },
        },
      };

      mockUseRegistrationsData.mockReturnValue({
        data: dataWithNoTitle,
        loading: false,
        error: undefined,
      });

      render(<ProgramRegistrationsTab {...defaultProps} />);

      const dataTableCall = MockDataTable.mock.calls[0][0];
      const transformedData = dataTableCall.data as any[];

      expect(transformedData[0].title.title).toBe('Untitled Registration');
    });

    it('handles registration with no currency code gracefully', () => {
      const dataWithNoCurrency = {
        school: {
          organizationProgram: {
            title: 'Program Title',
            registrationsForProgram: [
              {
                ...mockRegistrationData.school.organizationProgram.registrationsForProgram[0],
                currencyCode: null,
              },
            ],
          },
        },
      };

      mockUseRegistrationsData.mockReturnValue({
        data: dataWithNoCurrency,
        loading: false,
        error: undefined,
      });

      render(<ProgramRegistrationsTab {...defaultProps} />);

      const dataTableCall = MockDataTable.mock.calls[0][0];
      const transformedData = dataTableCall.data as any[];

      expect(transformedData[0].title.currencyCode).toBe('USD');
      expect(transformedData[0].totalCollected.currencyCode).toBe('USD');
      expect(transformedData[0].outstanding.currencyCode).toBe('USD');
      expect(transformedData[0].listPrice.currencyCode).toBe('USD');
    });

    it('handles registration with no max capacity gracefully', () => {
      const dataWithNoCapacity = {
        school: {
          organizationProgram: {
            title: 'Program Title',
            registrationsForProgram: [
              {
                ...mockRegistrationData.school.organizationProgram.registrationsForProgram[0],
                maxCapacity: null,
              },
            ],
          },
        },
      };

      mockUseRegistrationsData.mockReturnValue({
        data: dataWithNoCapacity,
        loading: false,
        error: undefined,
      });

      render(<ProgramRegistrationsTab {...defaultProps} />);

      const dataTableCall = MockDataTable.mock.calls[0][0];
      const transformedData = dataTableCall.data as any[];

      expect(transformedData[0].capacity.denominator).toBe(0);
    });

    it('sorts registrations by title to maintain consistent order', () => {
      // Create test data with registrations in non-alphabetical order
      const unsortedData = {
        school: {
          organizationProgram: {
            title: 'Program Title',
            registrationsForProgram: [
              {
                ...mockRegistrationData.school.organizationProgram.registrationsForProgram[1], // Winter Training
                title: 'Winter Training 2024',
                id: 'registration-2',
              },
              {
                ...mockRegistrationData.school.organizationProgram.registrationsForProgram[0], // Summer Camp
                title: 'Summer Camp 2024',
                id: 'registration-1',
              },
              {
                ...mockRegistrationData.school.organizationProgram.registrationsForProgram[0], // Copy of Summer Camp
                title: 'Summer Camp 2024',
                id: 'registration-3',
              },
            ],
          },
        },
      };

      mockUseRegistrationsData.mockReturnValue({
        data: unsortedData,
        loading: false,
        error: undefined,
      });

      render(<ProgramRegistrationsTab {...defaultProps} />);

      const dataTableCall = MockDataTable.mock.calls[0][0];
      const transformedData = dataTableCall.data as any[];

      // Should be sorted by title, then by ID for stability
      expect(transformedData[0].title.title).toBe('Summer Camp 2024');
      expect(transformedData[0].title.id).toBe('registration-1');
      expect(transformedData[1].title.title).toBe('Summer Camp 2024');
      expect(transformedData[1].title.id).toBe('registration-3');
      expect(transformedData[2].title.title).toBe('Winter Training 2024');
      expect(transformedData[2].title.id).toBe('registration-2');
    });
  });
});

/* eslint-enable @typescript-eslint/no-unsafe-return -- End of test file */
/* eslint-enable @typescript-eslint/no-explicit-any -- End of test file */
