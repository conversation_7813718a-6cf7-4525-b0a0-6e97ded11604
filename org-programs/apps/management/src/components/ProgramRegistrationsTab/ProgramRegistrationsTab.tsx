import { useCallback, useMemo, useState } from 'react';

import { download, generateCsv, mkConfig } from 'export-to-csv';

import { Button, IconDownload, SearchInput } from '@hudl/uniform-web';

import {
  Grade,
  ProgramRegistration,
  ProgramRegistrationStatus,
  RegistrationGender,
  useWebOrgProgramsManagementBulkUpsertProgramRegistrationsR1Mutation,
} from '../../graphql/graphqlTypes';
import useRegistrationsData from '../../hooks/useRegistrationsData';
import { getCsvDataForRegistrationsTab } from '../../utils/csvUtils';
import {
  getOutstandingAmountFromMultipleRegistrantsGqlTransactions,
  getTotalCollectedFromMultipleRegistrantsGqlTransactions,
} from '../../utils/manageProgramUtils';
import { showDownloadErrorToast } from '../../utils/toastUtils';
import {
  getConfigurationForTable,
  RegistrationsData,
  TableConfiguration,
} from '../DataTable/configuration/Configuration';
import { getKeyForColumnHeader } from '../DataTable/configuration/Registrations';
import { DataTable } from '../DataTable/DataTable';
import NoSearchResults from '../NoSearchResults/NoSearchResults';

import styles from './ProgramRegistrationsTab.module.scss';

interface ProgramRegistrationsTabProps {
  schoolId: string;
  programId: string;
}

export default function ProgramRegistrationsTab({
  schoolId,
  programId,
}: ProgramRegistrationsTabProps): React.JSX.Element {
  const { data, loading } = useRegistrationsData(schoolId, programId);
  const [searchTerm, setSearchTerm] = useState('');
  const [updateRegistrationStatus] = useWebOrgProgramsManagementBulkUpsertProgramRegistrationsR1Mutation();
  const [downloading, setDownloading] = useState(false);

  const handleRegistrationToggle = useCallback(
    async (registration: ProgramRegistration, newStatus: boolean) => {
      try {
        const status = newStatus ? ProgramRegistrationStatus.OPEN : ProgramRegistrationStatus.CLOSED;

        await updateRegistrationStatus({
          variables: {
            input: [
              {
                id: registration.id,
                programId: registration.programId,
                orgId: registration.orgId,
                title: registration.title,
                priceInBaseDenomination: registration.priceInBaseDenomination,
                currencyCode: registration.currencyCode,
                maxCapacity: registration.maxCapacity,
                ...(registration.eligibility && {
                  eligibility: {
                    birthDateFrom: registration.eligibility.birthDateFrom as string | undefined,
                    birthDateTo: registration.eligibility.birthDateTo as string | undefined,
                    gender: registration.eligibility.gender as RegistrationGender | undefined,
                    grades: registration.eligibility.grades as Grade[] | undefined,
                  },
                }),
                startDate: registration.startDate as string,
                timeZoneIdentifier: registration.timeZoneIdentifier,
                status: status,
              },
            ],
          },
          refetchQueries: ['WebOrgProgramsManagementRegistrationsDataR1'],
        });
      } catch (updateError) {
        console.error('Failed to update registration status:', updateError);
      }
    },
    [updateRegistrationStatus]
  );

  const mapGqlRegistrationsForDataTable = useCallback(
    (registrations: ProgramRegistration[]): RegistrationsData[] => {
      return registrations.map((registration: ProgramRegistration) => {
        const count = registration.registrants.length;
        const outstanding = getOutstandingAmountFromMultipleRegistrantsGqlTransactions(
          registration.registrants,
          registration.priceInBaseDenomination
        );

        return {
          title: {
            id: registration.id,
            title: registration.title || 'Untitled Registration',
            priceInBaseDenomination: registration.priceInBaseDenomination,
            currencyCode: registration.currencyCode || 'USD',
          },
          count,
          totalCollected: {
            priceInBaseDenomination: getTotalCollectedFromMultipleRegistrantsGqlTransactions(registration.registrants),
            currencyCode: registration.currencyCode || 'USD',
          },
          outstanding: {
            priceInBaseDenomination: outstanding,
            currencyCode: registration.currencyCode || 'USD',
          },
          listPrice: {
            priceInBaseDenomination: registration.priceInBaseDenomination,
            currencyCode: registration.currencyCode || 'USD',
          },
          capacity: {
            numerator: count,
            denominator: registration.maxCapacity || 0,
          },
          isRegistrationOpen: registration.status === ProgramRegistrationStatus.OPEN,
          onToggle: (newStatus: boolean) => handleRegistrationToggle(registration, newStatus),
        };
      });
    },
    [handleRegistrationToggle]
  );

  const registrationsForDataTable = useMemo(() => {
    const registrations = data?.school?.organizationProgram?.registrationsForProgram ?? [];
    const mappedRegistrations = mapGqlRegistrationsForDataTable(registrations as ProgramRegistration[]);

    // Sort registrations to maintain consistent order and prevent reordering when toggling
    return mappedRegistrations.sort((a, b) => {
      const titleComparison = (a.title.title ?? '').localeCompare(b.title.title ?? '', undefined, {
        numeric: true,
        sensitivity: 'base',
      });

      // If titles are equal, sort by ID
      if (titleComparison === 0) {
        return (a.title.id ?? '').localeCompare(b.title.id ?? '');
      }

      return titleComparison;
    });
  }, [data, mapGqlRegistrationsForDataTable]);

  // Filter registrations based on search term
  const filteredRegistrations = useMemo(() => {
    if (!searchTerm.trim()) {
      return registrationsForDataTable;
    }

    return registrationsForDataTable.filter((registration) =>
      registration.title.title?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [registrationsForDataTable, searchTerm]);

  const handleSearchChange = useCallback((value: string): void => {
    setSearchTerm(value);
  }, []);

  const handleDismissClick = useCallback((): void => {
    setSearchTerm('');
  }, []);

  const showNoResults = searchTerm.trim() && filteredRegistrations.length === 0;

  const handleDownload = useCallback(() => {
    setDownloading(true);
    try {
      const registrations = data?.school?.organizationProgram?.registrationsForProgram ?? [];
      const mappedRegistrations = mapGqlRegistrationsForDataTable(registrations as ProgramRegistration[]);

      const csvData = getCsvDataForRegistrationsTab(mappedRegistrations);
      const configuration = getConfigurationForTable(TableConfiguration.Registrations);

      const csvConfig = mkConfig({
        columnHeaders: configuration.columnHeaders.map((header) => ({
          key: getKeyForColumnHeader(header as string),
          displayLabel: header as string,
        })),
        filename: `${data?.school?.organizationProgram?.title}-Registrations-${new Date().toISOString()}`,
      });
      const csv = generateCsv(csvConfig)(csvData);
      download(csvConfig)(csv);
    } catch (error) {
      console.error('Failed to download CSV:', error);
      showDownloadErrorToast(handleDownload);
    } finally {
      setDownloading(false);
    }
  }, [data?.school?.organizationProgram?.registrationsForProgram, mapGqlRegistrationsForDataTable]);

  return (
    <div className={styles.programRegistrationsTabContainer} data-qa-id="program-registrations-tab-container">
      <div className={styles.programRegistrationsTabHeader} data-qa-id="program-registrations-tab-header">
        <div
          className={styles.programRegistrationsTabHeaderActions}
          data-qa-id="program-registrations-tab-header-actions-container"
        >
          <SearchInput
            formSize="small"
            placeholder="Search by title"
            value={searchTerm}
            onChange={handleSearchChange}
            onDismissClick={handleDismissClick}
            qaId="program-registrations-tab-search-input"
          />
          <Button
            size="small"
            status={downloading ? 'spinning' : undefined}
            icon={<IconDownload />}
            buttonType="subtle"
            onPress={handleDownload}
            qaId="program-registrations-tab-download-button"
          >
            Download
          </Button>
        </div>
      </div>
      {showNoResults ? (
        <NoSearchResults
          searchTerm={searchTerm}
          onClearSearch={handleDismissClick}
          qaId="program-registrations-tab-no-results"
        />
      ) : (
        <DataTable
          configuration={TableConfiguration.Registrations}
          data={filteredRegistrations}
          isLoading={loading}
          qaId="program-registrations-tab-data-table"
        />
      )}
    </div>
  );
}
