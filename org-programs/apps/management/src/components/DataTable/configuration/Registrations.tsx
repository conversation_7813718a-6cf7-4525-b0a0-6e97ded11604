import React from 'react';

import { Rows as UniformDataRows } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import { Toggle } from '../../Toggle/Toggle.tsx';
import { CurrencyLabel } from '../components/CurrencyLabel.tsx';
import { RegistrationLink } from '../components/RegistrationLink.tsx';
import { Configuration, RegistrationsData } from './Configuration.tsx';

export const getKeyForColumnHeader = (header: string): string => {
  switch (header) {
    case format('org-programs.data-table.title'):
      return 'title';
    case format('org-programs.data-table.count'):
      return 'count';
    case format('org-programs.data-table.total-collected'):
      return 'totalCollected';
    case format('org-programs.data-table.outstanding'):
      return 'outstanding';
    case format('org-programs.data-table.list-price'):
      return 'listPrice';
    case format('org-programs.data-table.capacity'):
      return 'capacity';
    case format('org-programs.data-table.registration'):
      return 'registration';
    default:
      return '';
  }
};

class RegistrationsConfiguration implements Configuration<RegistrationsData[]> {
  private createToggleHandler = (row: RegistrationsData) => {
    return () => {
      row.onToggle?.(!row.isRegistrationOpen);
    };
  };

  public dataToRows = (data: RegistrationsData[]): UniformDataRows => {
    return data.map((row, index) => {
      const rowData: (string | { value: string | number; element: React.JSX.Element })[] = [
        {
          value: row.title.title || row.title.id || `Registration ${index}`,
          element: <RegistrationLink registration={row.title} />,
        },
        row.count.toString(),
        {
          value: row.totalCollected.priceInBaseDenomination,
          element: <CurrencyLabel currency={row.totalCollected} qaId={`total-collected-${index}`} />,
        },
        {
          value: row.outstanding.priceInBaseDenomination,
          element: <CurrencyLabel currency={row.outstanding} qaId={`outstanding-${index}`} />,
        },
        {
          value: row.listPrice.priceInBaseDenomination,
          element: <CurrencyLabel currency={row.listPrice} qaId={`list-price-${index}`} />,
        },
        {
          value: `${row.capacity?.numerator ?? ''} / ${row.capacity?.denominator ?? ''}`,
          element: (
            <span data-qa-id={`capacity-${index}`}>
              {row.capacity?.numerator ?? ''} / {row.capacity?.denominator ?? ''}
            </span>
          ),
        },
        {
          value: 'Registration',
          element: (
            <Toggle
              checked={row.isRegistrationOpen}
              onChange={this.createToggleHandler(row)}
              qaId={`data-registration-toggle-${index}`}
            />
          ),
        },
      ];

      return {
        id: 'data-row-' + index,
        data: rowData,
      };
    });
  };

  public columnContentTypes = ['element', 'numeric', 'numeric', 'numeric', 'numeric', 'element', 'element'] as (
    | 'element'
    | 'numeric'
    | 'selectmark'
  )[];

  public get columnHeaders(): string[] {
    return [
      format('org-programs.data-table.title'),
      format('org-programs.data-table.count'),
      format('org-programs.data-table.total-collected'),
      format('org-programs.data-table.outstanding'),
      format('org-programs.data-table.list-price'),
      format('org-programs.data-table.capacity'),
      format('org-programs.data-table.registration'),
    ];
  }

  public columnShowSortArrows = [false, false, false, false, false, false, false];
}

export default new RegistrationsConfiguration();
