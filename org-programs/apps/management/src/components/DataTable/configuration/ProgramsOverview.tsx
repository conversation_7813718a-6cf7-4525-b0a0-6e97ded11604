import React from 'react';

import { Rows as UniformDataRows } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import { CurrencyLabel } from '../components/CurrencyLabel.tsx';
import { DateLabel } from '../components/DateLabel.tsx';
import { RegistrationLink } from '../components/RegistrationLink.tsx';
import { StatusLabel } from '../components/StatusLabel.tsx';
import { Configuration, ProgramsOverviewData } from './Configuration.tsx';

export const getKeyForColumnHeader = (header: string): string => {
  switch (header) {
    case format('org-programs.data-table.athlete-name'):
      return 'name';
    case format('org-programs.data-table.age'):
      return 'age';
    case format('org-programs.data-table.gender'):
      return 'gender';
    case format('org-programs.data-table.primary-contact'):
      return 'contact';
    case format('org-programs.data-table.registration'):
      return 'registration';
    case format('org-programs.data-table.payment-date'):
      return 'paymentDate';
    case format('org-programs.data-table.total-paid'):
      return 'totalPaid';
    case format('org-programs.data-table.outstanding'):
      return 'outstanding';
    case format('org-programs.data-table.refunded'):
      return 'refunded';
    case format('org-programs.data-table.status'):
      return 'status';
    default:
      return '';
  }
};

class ProgramsOverviewConfiguration implements Configuration<ProgramsOverviewData[]> {
  public dataToRows = (data: ProgramsOverviewData[]): UniformDataRows => {
    return data.map((row, index) => {
      const rowData: (string | { value: string | number; element: React.JSX.Element })[] = [
        row.name,
        { value: row.age.toString(), element: <span>{row.age}</span> },
        row.gender,
        row.contact,
        {
          value: row.registration.title || row.registration.id || `Registration ${index}`,
          element: <RegistrationLink registration={row.registration} />,
        },
        {
          value: row.paymentDate ? row.paymentDate.toISOString() : '',
          element: row.paymentDate ? <DateLabel date={row.paymentDate} qaId={`date-${index}`} /> : <span />,
        },
        {
          value: row.totalPaid.priceInBaseDenomination,
          element: <CurrencyLabel currency={row.totalPaid} qaId={`total-paid-${index}`} />,
        },
        {
          value: row.outstanding.priceInBaseDenomination,
          element: <CurrencyLabel currency={row.outstanding} qaId={`outstanding-${index}`} />,
        },
        {
          value: row.refunded.priceInBaseDenomination,
          element: <CurrencyLabel currency={row.refunded} qaId={`refunded-${index}`} />,
        },
        { value: row.status, element: <StatusLabel status={row.status} qaId={`status-${index}`} /> },
      ];

      return {
        id: 'data-row-' + index,
        data: rowData,
      };
    });
  };

  public columnContentTypes = [
    'text',
    'element',
    'text',
    'text',
    'element',
    'element',
    'element',
    'element',
    'element',
    'element',
  ] as ('text' | 'element' | 'numeric' | 'selectmark')[];

  public get columnHeaders(): string[] {
    return [
      format('org-programs.data-table.athlete-name'),
      format('org-programs.data-table.age'),
      format('org-programs.data-table.gender'),
      format('org-programs.data-table.primary-contact'),
      format('org-programs.data-table.registration'),
      format('org-programs.data-table.payment-date'),
      format('org-programs.data-table.total-paid'),
      format('org-programs.data-table.outstanding'),
      format('org-programs.data-table.refunded'),
      format('org-programs.data-table.status'),
    ];
  }

  public columnShowSortArrows = [true, true, true, true, true, true, true, true, true, true];
}

export default new ProgramsOverviewConfiguration();
