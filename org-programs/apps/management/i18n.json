{"version": 1.1, "pre-load-external-sets": ["ticketing-shared"], "sets": {"org-programs": {"keys": ["org-programs.add", "org-programs.archive", "org-programs.back", "org-programs.boys", "org-programs.boys", "org-programs.cancel", "org-programs.character-limit-help-text", "org-programs.continue", "org-programs.copy-of", "org-programs.data-table.athlete-name", "org-programs.data-table.age", "org-programs.data-table.gender", "org-programs.data-table.athlete-contact", "org-programs.data-table.registration", "org-programs.data-table.payment-date", "org-programs.data-table.amount", "org-programs.data-table.outstanding", "org-programs.data-table.refunded", "org-programs.data-table.status", "org-programs.data-table.title", "org-programs.data-table.count", "org-programs.data-table.total-collected", "org-programs.data-table.list-price", "org-programs.data-table.capacity", "org-programs.data-table.status.paid", "org-programs.data-table.status.outstanding", "org-programs.data-table.status.refunded", "org-programs.data-table.status.unknown", "org-programs.data-table.status.free", "org-programs.data-table.total-paid", "org-programs.data-table.primary-contact", "org-programs.delete", "org-programs.discard-changes.description", "org-programs.discard-changes", "org-programs.duplicate-action-edit", "org-programs.duplicate-action", "org-programs.edit", "org-programs.empty-state.archived.subtitle-r2", "org-programs.empty-state.archived.title", "org-programs.empty-state.draft.subtitle-r2", "org-programs.empty-state.draft.title", "org-programs.empty-state.published.subtitle-r2", "org-programs.empty-state.published.title", "org-programs.girls", "org-programs.girls", "org-programs.landing-page.add-program-button", "org-programs.landing-page.complete-onboarding-button", "org-programs.landing-page.programs-link", "org-programs.manage.download", "org-programs.manage.clear-search", "org-programs.manage.search-by-name", "org-programs.manage.no-results-found", "org-programs.manage.no-results-found-subtext", "org-programs.manage.no-registrants", "org-programs.manage.no-registrants-subtext", "org-programs.manage.overview", "org-programs.manage.share", "org-programs.manage.download.error", "org-programs.manage.type.program", "org-programs.manage.type.registration", "org-programs.manage.share-program", "org-programs.manage.share-program.description", "org-programs.manage.share-program.copy-link", "org-programs.manage.share-program.copied", "org-programs.manage.open-registration", "org-programs.manage.open-registration.tooltip", "org-programs.manage.registrants", "org-programs.menu", "org-programs.mixed", "org-programs.mixed", "org-programs.move-to-draft", "org-programs.never-mind", "org-programs.onboarding.banner.description", "org-programs.onboarding.banner.get-started", "org-programs.onboarding.complete-error", "org-programs.onboarding.direct-deposit.complete.description-initial", "org-programs.onboarding.direct-deposit.complete.description", "org-programs.onboarding.direct-deposit.cta", "org-programs.onboarding.direct-deposit.current-status", "org-programs.onboarding.direct-deposit.incomplete.description-initial", "org-programs.onboarding.direct-deposit.incomplete.description", "org-programs.onboarding.direct-deposit.needs-review.description-initial", "org-programs.onboarding.direct-deposit.needs-review.description", "org-programs.onboarding.direct-deposit.review.description-initial", "org-programs.onboarding.direct-deposit.review.description", "org-programs.onboarding.direct-deposit.status.complete", "org-programs.onboarding.direct-deposit.status.incomplete", "org-programs.onboarding.direct-deposit.status.needs-review", "org-programs.onboarding.direct-deposit.status.pending", "org-programs.onboarding.direct-deposit.title", "org-programs.program-form.archive.confirm", "org-programs.program-form.archive.description", "org-programs.program-form.archive.error", "org-programs.program-form.archive.success", "org-programs.program-form.changes-saved-error", "org-programs.program-form.changes-saved", "org-programs.program-form.duplicate.error", "org-programs.program-form.invalid-date-range-message", "org-programs.program-form.invalid-price-message", "org-programs.program-form.move-to-draft.confirm", "org-programs.program-form.move-to-draft.description", "org-programs.program-form.move-to-draft.error", "org-programs.program-form.move-to-draft.success", "org-programs.program-form.problem-loading-page", "org-programs.program-form.program-details.character-limit-help-text", "org-programs.program-form.program-details.character-limit-help-text", "org-programs.program-form.program-details.details-step-title", "org-programs.program-form.program-details.feeResponsibility.description", "org-programs.program-form.program-details.feeResponsibility.title.fees-for-this-program", "org-programs.program-form.program-details.feeResponsibility.title.punctuation", "org-programs.program-form.program-details.feeResponsibility.title.who-do-you-want-to-cover", "org-programs.program-form.program-details.feeResponsibility.your-organization-description", "org-programs.program-form.program-details.feeResponsibility.your-organization-title", "org-programs.program-form.program-details.feeResponsibility.your-registrants-description", "org-programs.program-form.program-details.feeResponsibility.your-registrants-title", "org-programs.program-form.program-details.program-date-range-help-text", "org-programs.program-form.program-details.program-description-label", "org-programs.program-form.program-details.program-description-placeholder", "org-programs.program-form.program-details.program-end-date-label", "org-programs.program-form.program-details.program-start-date-label", "org-programs.program-form.program-details.program-title-label", "org-programs.program-form.program-details.program-title-placeholder", "org-programs.program-form.program-details.program-type-label", "org-programs.program-form.program-details.program-type-label-tryout", "org-programs.program-form.program-details.program-type-label-team-dues", "org-programs.program-form.program-details.program-type-label-camp", "org-programs.program-form.program-details.program-type-label-clinic", "org-programs.program-form.program-details.program-type-label-other", "org-programs.program-form.program-details.program-type-placeholder", "org-programs.program-form.program-details.questions-step-title", "org-programs.program-form.program-details.registrations-step-title", "org-programs.program-form.program-details.summary-step-title", "org-programs.program-form.program-details.visibility.description", "org-programs.program-form.program-details.visibility.private", "org-programs.program-form.program-details.visibility.public", "org-programs.program-form.program-details.visibility.title", "org-programs.program-form.publish.error", "org-programs.program-form.publish.success", "org-programs.program-form.registration-deleted", "org-programs.program-form.required-message", "org-programs.program-form.restore.confirm", "org-programs.program-form.restore.description", "org-programs.program-form.restore.error", "org-programs.program-form.restore.success", "org-programs.program-form.save-as-draft.error", "org-programs.program-form.save-as-draft.success", "org-programs.programs.collected", "org-programs.programs.registrants", "org-programs.programs.visibility", "org-programs.programs", "org-programs.publish", "org-programs.refresh", "org-programs.registration.add", "org-programs.registration.capacity.description-v2", "org-programs.registration.capacity.disable-section-alert", "org-programs.registration.capacity.enable-waitlist", "org-programs.registration.capacity.max-capacity-label", "org-programs.registration.capacity.title", "org-programs.registration.date-range-outside-of-program-warning", "org-programs.registration.description-placeholder", "org-programs.registration.description", "org-programs.registration.disable-section-alert", "org-programs.registration.disable-section-confirm", "org-programs.registration.edit", "org-programs.registration.eligibility.birthdate-from", "org-programs.registration.eligibility.birthdate-in-future-validation", "org-programs.registration.eligibility.birthdate-required-validation", "org-programs.registration.eligibility.birthdate-to", "org-programs.registration.eligibility.description-v2", "org-programs.registration.eligibility.gender", "org-programs.registration.eligibility.grade", "org-programs.registration.eligibility", "org-programs.registration.end-date", "org-programs.registration.instruction", "org-programs.registration.is-free", "org-programs.registration.list-price", "org-programs.registration.max-capacity", "org-programs.registration.optional", "org-programs.registration.payment-options.free", "org-programs.registration.payment-options.full-payment", "org-programs.registration.payment-options.title", "org-programs.registration.price", "org-programs.registration.registration-details.title", "org-programs.registration.start-date", "org-programs.registration.title-placeholder-text", "org-programs.registration.title", "org-programs.registration.waitlist", "org-programs.restore", "org-programs.review", "org-programs.save-as-draft", "org-programs.save-changes", "org-programs.save", "org-programs.stay-on-page", "org-programs.summary.description", "org-programs.summary.details", "org-programs.summary.edit", "org-programs.summary.end-date", "org-programs.summary.fee-responsibility", "org-programs.summary.player-questions", "org-programs.summary.program-questions", "org-programs.summary.questions", "org-programs.summary.registrations", "org-programs.summary.start-date", "org-programs.summary.title", "org-programs.summary.type", "org-programs.summary.visibility", "org-programs.tabs.archived", "org-programs.tabs.draft", "org-programs.tabs.onboarding-start", "org-programs.tabs.onboarding", "org-programs.tabs.published", "org-programs.toast.page-error", "org-programs.try-again", "org-programs.manage.download.error", "org-programs.retry"]}}, "base-language": {"org-programs.add": "Add", "org-programs.archive": "Archive", "org-programs.back": "Back", "org-programs.boys": "Boys", "org-programs.cancel": "Cancel", "org-programs.character-limit-help-text": "Character limit: {charactersUsed}/{maxLength}", "org-programs.continue": "Continue", "org-programs.copy-of": "Copy of", "org-programs.data-table.athlete-name": "Athlete Name", "org-programs.data-table.age": "Age", "org-programs.data-table.gender": "Gender", "org-programs.data-table.athlete-contact": "Athlete Contact", "org-programs.data-table.registration": "Registration", "org-programs.data-table.payment-date": "Pymt. Date", "org-programs.data-table.amount": "Amount", "org-programs.data-table.outstanding": "Outstanding", "org-programs.data-table.refunded": "Refunded", "org-programs.data-table.status": "Status", "org-programs.data-table.title": "Title", "org-programs.data-table.count": "Count", "org-programs.data-table.total-collected": "Total Collected", "org-programs.data-table.list-price": "List Price", "org-programs.data-table.capacity": "Capacity", "org-programs.data-table.status.paid": "Paid", "org-programs.data-table.status.outstanding": "Outstanding", "org-programs.data-table.status.refunded": "Refunded", "org-programs.data-table.status.unknown": "Unknown", "org-programs.data-table.status.free": "Free", "org-programs.data-table.total-paid": "Total Paid", "org-programs.data-table.primary-contact": "Primary Contact", "org-programs.delete": "Delete", "org-programs.discard-changes.description": "You have unsaved changes. If you leave now, your edits will be lost.", "org-programs.discard-changes": "Discard Changes", "org-programs.duplicate-action-edit": "Duplicate and Edit", "org-programs.duplicate-action": "Duplicate", "org-programs.edit": "Edit", "org-programs.empty-state.archived.subtitle-r2": "Archive a draft or published program to display it here.", "org-programs.empty-state.archived.title": "No archived programs", "org-programs.empty-state.draft.subtitle-r2": "Add a program as a draft to display it here.", "org-programs.empty-state.draft.title": "No draft programs", "org-programs.empty-state.published.subtitle-r2": "Add a program and it will appear here. Programs can include camps, clinics, tryouts, season dues and more.", "org-programs.empty-state.published.title": "No published programs", "org-programs.girls": "Girls", "org-programs.retry": "Retry", "org-programs.landing-page.add-program-button": "Add Program", "org-programs.landing-page.complete-onboarding-button": "Complete Onboarding", "org-programs.landing-page.programs-link": "Programs Link", "org-programs.manage.download": "Download", "org-programs.manage.clear-search": "Clear Search", "org-programs.manage.search-by-name": "Search by name", "org-programs.manage.no-results-found": "No results found for \"{searchValue}\"", "org-programs.manage.no-results-found-subtext": "Check your spelling or try another search.", "org-programs.manage.no-registrants": "No Registrants", "org-programs.manage.no-registrants-subtext": "Share your program to begin collecting registrants", "org-programs.manage.overview": "Overview", "org-programs.manage.share": "Share", "org-programs.manage.type.program": "Program", "org-programs.manage.type.registration": "Registration", "org-programs.manage.share-program": "Share Program", "org-programs.manage.share-program.description": "Copy link to share this program or post it on social media.", "org-programs.manage.share-program.copy-link": "Copy Link", "org-programs.manage.share-program.copied": "Copied!", "org-programs.manage.open-registration": "Open Registration", "org-programs.manage.open-registration.tooltip": "Open and close registrations for this program", "org-programs.manage.registrants": "Registrants", "org-programs.menu": "<PERSON><PERSON>", "org-programs.mixed": "Mixed", "org-programs.move-to-draft": "Move to Draft", "org-programs.never-mind": "Never Mind", "org-programs.onboarding.banner.description": "You need to complete onboarding before you can create programs and accept registrations.", "org-programs.onboarding.banner.get-started": "Get Started", "org-programs.onboarding.complete-error": "There was an error completing onboarding. Please try again.", "org-programs.onboarding.direct-deposit.complete.description-initial": "You're All Set!", "org-programs.onboarding.direct-deposit.complete.description": "Looks like you're already set up with <PERSON><PERSON> for direct deposit payments.", "org-programs.onboarding.direct-deposit.cta": "Go to Stripe", "org-programs.onboarding.direct-deposit.current-status": "Current Status", "org-programs.onboarding.direct-deposit.incomplete.description-initial": "Hudl uses Stripe to support direct deposit.", "org-programs.onboarding.direct-deposit.incomplete.description": "Complete the Stripe onboarding steps to finish setting up this payment method.", "org-programs.onboarding.direct-deposit.needs-review.description-initial": "Additional information needed by <PERSON><PERSON>.", "org-programs.onboarding.direct-deposit.needs-review.description": "<PERSON>e is requesting for additional information to continue reviewing your account. Go to <PERSON>e to complete request.", "org-programs.onboarding.direct-deposit.review.description-initial": "Your direct deposit information is under review by Stripe.", "org-programs.onboarding.direct-deposit.review.description": "You'll receive an email once the review is complete—it may take up to 24 hours. For questions about the status of your direct deposit account, contact <PERSON><PERSON> directly.", "org-programs.onboarding.direct-deposit.status.complete": "Complete", "org-programs.onboarding.direct-deposit.status.incomplete": "Incomplete", "org-programs.onboarding.direct-deposit.status.needs-review": "Needs Review", "org-programs.onboarding.direct-deposit.status.pending": "Pending", "org-programs.onboarding.direct-deposit.title": "Set Up Direct Deposit", "org-programs.program-form.archive.confirm": "Yes, Archive", "org-programs.program-form.archive.description": "Archiving this program will close registrations and make it inaccessible to participants until it is restored from the \"Archived\" section. Do you want to proceed?", "org-programs.program-form.archive.error": "We failed to archive this program.", "org-programs.program-form.archive.success": "{title} archived", "org-programs.program-form.changes-saved-error": "{title} failed to save", "org-programs.program-form.changes-saved": "{title} changes saved", "org-programs.program-form.duplicate.error": "Failed to duplicate program. Please try again.", "org-programs.program-form.invalid-date-range-message": "End date must be after the start date.", "org-programs.program-form.invalid-price-message": "Price must be lower than {restriction}.", "org-programs.program-form.move-to-draft.confirm": "Yes, Move to Draft", "org-programs.program-form.move-to-draft.description": "Moving this program to draft will close registrations and make it inaccessible to participants until it is republished. Do you want to proceed?", "org-programs.program-form.move-to-draft.error": "{title} failed to move to draft", "org-programs.program-form.move-to-draft.success": "{title} moved to draft", "org-programs.program-form.problem-loading-page": "There was a problem loading this page. Try refreshing the page, or try again later.", "org-programs.program-form.program-details.character-limit-help-text": "Character limit: {charactersUsed}/{maxLength}", "org-programs.program-form.program-details.details-step-title": "Program Details", "org-programs.program-form.program-details.feeResponsibility.description": "If you opt for registrants to pay the fees, they'll have an additional fee added to their total cost. If you opt for your organization to pay the fees, the cost of the fees will be deducted from the total cost.", "org-programs.program-form.program-details.feeResponsibility.title.fees-for-this-program": "fees for this program", "org-programs.program-form.program-details.feeResponsibility.title.punctuation": "?", "org-programs.program-form.program-details.feeResponsibility.title.who-do-you-want-to-cover": "Who do you want to cover the ", "org-programs.program-form.program-details.feeResponsibility.your-organization-description": "Ex. $1,000 registration, a registrant would pay $1,000 and you would receive $970", "org-programs.program-form.program-details.feeResponsibility.your-organization-title": "Your Organization", "org-programs.program-form.program-details.feeResponsibility.your-registrants-description": "Ex. $1,000 registration, a registrant would pay $1,030 and you would receive $1,000", "org-programs.program-form.program-details.feeResponsibility.your-registrants-title": "Your Registrants", "org-programs.program-form.program-details.program-date-range-help-text": "Define your program's start and end date. This is typically the total duration of your program.", "org-programs.program-form.program-details.program-description-label": "Description", "org-programs.program-form.program-details.program-type-label-tryout": "Tryout", "org-programs.program-form.program-details.program-type-label-team-dues": "Team Dues", "org-programs.program-form.program-details.program-type-label-camp": "Camp", "org-programs.program-form.program-details.program-type-label-clinic": "Clinic", "org-programs.program-form.program-details.program-type-label-other": "Other", "org-programs.program-form.program-details.program-description-placeholder": "Describe your program (e.g. occasion, venue, location).", "org-programs.program-form.program-details.program-end-date-label": "End Date", "org-programs.program-form.program-details.program-start-date-label": "Start Date", "org-programs.program-form.program-details.program-title-label": "Title", "org-programs.program-form.program-details.program-title-placeholder": "Program Title", "org-programs.program-form.program-details.program-type-label": "Type", "org-programs.program-form.program-details.program-type-placeholder": "Select Type", "org-programs.program-form.program-details.questions-step-title": "Questions", "org-programs.program-form.program-details.registrations-step-title": "Registrations", "org-programs.program-form.program-details.summary-step-title": "Summary", "org-programs.program-form.program-details.visibility.description": "Public programs are immediately available for purchase. Private programs remain hidden until they are either made public or shared via a direct link.", "org-programs.program-form.program-details.visibility.private": "Private", "org-programs.program-form.program-details.visibility.public": "Public", "org-programs.program-form.program-details.visibility.title": "Is this program public or private?", "org-programs.program-form.publish.error": "Failed to publish program", "org-programs.program-form.publish.success": "Program successfully published", "org-programs.program-form.registration-deleted": "{registration} deleted", "org-programs.program-form.required-message": "{field} is required.", "org-programs.program-form.restore.confirm": "Yes, Restore", "org-programs.program-form.restore.description": "Restoring this program will return it to your draft programs list. Do you want to proceed?", "org-programs.program-form.restore.error": "{title} failed to restore", "org-programs.program-form.restore.success": "{title} restored", "org-programs.program-form.save-as-draft.error": "An unexpected error occurred", "org-programs.program-form.save-as-draft.success": "Draft saved successfully", "org-programs.programs.collected": "Collected", "org-programs.programs.registrants": "Registrants", "org-programs.programs.visibility": "Visibility", "org-programs.programs": "Programs", "org-programs.publish": "Publish", "org-programs.refresh": "Refresh", "org-programs.registration.add": "Add Registration", "org-programs.registration.capacity.description-v2": "Limit how many participants can register. You can also enable a waitlist.", "org-programs.registration.capacity.enable-waitlist": "Enable waitlist", "org-programs.registration.capacity.max-capacity-label": "Max Capacity", "org-programs.registration.capacity.title": "Capacity", "org-programs.registration.date-range-outside-of-program-warning": "The dates you entered are outside the program's date range. Consider updating your registration dates or the program timeframe if needed.", "org-programs.registration.description-placeholder": "Enter a description for your registration", "org-programs.registration.description": "Description", "org-programs.registration.disable-section-alert": "Turning this off will remove your {section} settings. Do you want to continue?", "org-programs.registration.disable-section-confirm": "Turn Off", "org-programs.registration.edit": "Edit Registration", "org-programs.registration.eligibility.birthdate-from": "Birthdate From", "org-programs.registration.eligibility.birthdate-in-future-validation": "Birthdate can't be in the future.", "org-programs.registration.eligibility.birthdate-required-validation": "Please enter a from and to date.", "org-programs.registration.eligibility.birthdate-to": "Birthdate To", "org-programs.registration.eligibility.description-v2": "Limit who can register based on age, gender or grade.", "org-programs.registration.eligibility.gender": "Gender", "org-programs.registration.eligibility.grade": "Grade", "org-programs.registration.eligibility": "Eligibility", "org-programs.registration.end-date": "End Date", "org-programs.registration.instruction": "Add registrations for your program. Each can have its own price, timeframe and requirements.", "org-programs.registration.is-free": "This registration is FREE", "org-programs.registration.list-price": "List Price", "org-programs.registration.max-capacity": "Capacity", "org-programs.registration.optional": "(Optional)", "org-programs.registration.payment-options.free": "FREE", "org-programs.registration.payment-options.full-payment": "Full payment", "org-programs.registration.payment-options.title": "Payment Options", "org-programs.registration.price": "Price", "org-programs.registration.registration-details.title": "Registration Details", "org-programs.registration.start-date": "Start Date", "org-programs.registration.title-placeholder-text": "Registration title", "org-programs.registration.title": "Title", "org-programs.registration.waitlist": "Waitlist", "org-programs.restore": "Rest<PERSON>", "org-programs.review": "Review Program", "org-programs.save-as-draft": "Save as Draft", "org-programs.save-changes": "Save Changes", "org-programs.save": "Save", "org-programs.stay-on-page": "Stay on Page", "org-programs.summary.description": "Description", "org-programs.summary.details": "Details", "org-programs.summary.edit": "Edit {title}", "org-programs.summary.end-date": "End Date", "org-programs.summary.fee-responsibility": "Fee Responsibility", "org-programs.summary.player-questions": "Player Questions", "org-programs.summary.program-questions": "Program Questions", "org-programs.summary.questions": "Questions", "org-programs.summary.registrations": "Registrations", "org-programs.summary.start-date": "Start Date", "org-programs.summary.title": "Title", "org-programs.summary.type": "Type", "org-programs.summary.visibility": "Visibility", "org-programs.tabs.archived": "Archived", "org-programs.tabs.draft": "Draft", "org-programs.tabs.onboarding-start": "Get Started with Programs", "org-programs.tabs.onboarding": "Onboarding", "org-programs.tabs.published": "Published", "org-programs.toast.page-error": "There was a problem loading this page. Refresh the page, or try again later.", "org-programs.try-again": "Try Again", "org-programs.manage.download.error": "The registration data didn't download. Check your connection and try again."}}