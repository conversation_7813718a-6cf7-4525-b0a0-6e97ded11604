export type ScheduleEntryPublicSummary = {
  id: string;
  scheduleEntryId: string;
  timeUtc: string;
  opponentDetails: OpponentDetails;
  scheduleEntryLocation: ScheduleEntryLocation;
};

export type ScheduleEntryPublicSummaryConnection = {
  nodes: ScheduleEntryPublicSummary[];
  edges: ScheduleEntryPublicSummaryEdge[];
  pageInfo: PageInfo;
  totalCount: number;
};

export type ScheduleEntryPublicSummaryEdge = {
  node: ScheduleEntryPublicSummary;
  cursor: string;
};

export type GetScheduleEntryPublicSummariesInput = {
  first?: number;
  after?: string | null;
  before?: string | null;
  schoolIds?: string[];
  filterStartDate?: string;
  filterEndDate?: string;
  sortByAscending: boolean;
  sortType: ScheduleEntrySortType;
  includeHiddenTeams?: boolean;
  teamIds?: string[];
  location?: ScheduleEntryLocation;
};

export enum ScheduleEntrySortType {
  ScheduleEntryDate = 'SCHEDULE_ENTRY_DATE',
}

export enum ScheduleEntryLocation {
  Neutral = 0,
  Home = 1,
  Away = 2,
}

export type OpponentDetails = {
  name: string;
  teamId: string;
};

export type Team = {
  id: string;
  level: TeamLevel;
  gender: Gender;
  sport: string;
  name?: string | null;
};

export type GetOrganizationAdminRequestsByOrganizationIdInput = {
  after?: string | null;
  before?: string | null;
  first?: number;
  last?: number;
  organizationId: string;
  sortByAscending: boolean;
  sortType: OrganizationAdminSortType;
};

export type OrganizationAdminRequestConnection = {
  edges: OrganizationAdminRequestEdge[];
  nodes: OrganizationAdminRequest[];
  pageInfo: PageInfo;
  totalCount: number;
};

export type OrganizationAdminRequestEdge = {
  cursor: string;
  node: OrganizationAdminRequest;
};

export type OrganizationAdminRequest = {
  id: string;
  organizationId: string;
  requestedAt: string;
  requesterUserId: string;
  requests: RequestedOrganizationAdmin[];
};

export type RequestedOrganizationAdmin = {
  email: string;
  firstName: string;
  lastName: string;
};

export type PageInfo = {
  endCursor: string;
  hasNextPage: boolean;
};

export enum Gender {
  Coed = 'COED',
  Mens = 'MENS',
  Womens = 'WOMENS',
}

export enum TeamLevel {
  Freshman = 'FRESHMAN',
  JuniorVarsity = 'JUNIOR_VARSITY',
  Other = 'OTHER',
  OtherNonHs = 'OTHER_NON_HS',
  Sophomore = 'SOPHOMORE',
  Varsity = 'VARSITY',
}

export enum OrganizationAdminSortType {
  RequestedAt = 'REQUESTED_AT',
}

export enum ScheduleReferenceSubmissionSortType {
  UploadDate = 'UPLOAD_DATE',
}

export type GetScheduleReferenceSubmissionsByOrganizationIdInput = {
  organizationId: string;
  first?: number;
  last?: number;
  after?: string | null;
  before?: string | null;
  sortType: ScheduleReferenceSubmissionSortType;
  sortByAscending: boolean;
};

export type ScheduleReferenceFile = {
  uri: string;
  name: string;
  associatedTeamIds: string[];
};

export type ScheduleReferenceSubmission = {
  id: string;
  organizationId: string;
  notes: string;
  files: ScheduleReferenceFile[];
  submitterUserId: string;
  uploadDate: string;
};

export type ScheduleReferenceSubmissionEdge = {
  cursor: string;
  node: ScheduleReferenceSubmission;
};

export type TicketType = {
  id: string;
  name: string;
  organizationId: string;
  priceInCents: number;
  quantity?: number;
  updatedAt: string | undefined;
  createdAt: string | undefined;
};

export type TicketTypeReference = {
  ticketTypeId: string;
  priceOverride?: number;
  quantityOverride?: number;
  seatingChartCategoryIds?: string[];
};

export type TicketTypeWithOverrides = TicketType & {
  priceOverride?: number;
  quantityOverride?: number;
};

export type SelectOption = {
  label: string;
  value: string;
};

export type FormField = {
  id: string;
  label: string;
  fieldType: string;
  helpText?: string | null;
  isRequired: boolean;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type TicketTypeReferenceInput = {
  ticketTypeId: string;
  priceOverride?: number;
  quantityOverride?: number;
  seatingChartCategoryIds?: string[];
};

export type CreateOrUpdateTicketedEventForScheduleEntriesInput = {
  date: string;
  description?: string;
  eventStatus: string;
  feeStrategy: string;
  formFieldIds?: string[];
  gender?: string;
  name: string;
  organizationId: string;
  participatingTeamIds: string[];
  refundable: boolean;
  scheduleEntryIds?: string[];
  sport?: string;
  ticketTypeReferences?: TicketTypeReferenceInput[];
  timeZoneIdentifier: string;
  visibility: string;
};

export type TeamHeader = {
  internalId: string;
  name: string;
};

export type ResendTicketGroupEmailInput = {
  emailAddress?: string;
  ticketGroupId: string;
};

export type TicketedEventEdge = {
  cursor: string;
  node: TicketedEvent;
};

export type TicketedEvent = {
  createdAt: string;
  date: string;
  deletedAt?: string;
  description?: string;
  eventStatus: string;
  gender?: string;
  id: string;
  linkedEntries: LinkedEntry[];
  name: string;
  organizationId: string;
  participatingTeamIds: string[];
  refundable: boolean;
  seatingChartEventId?: string;
  sport?: string;
  ticketedEventAnalytics: TicketedEventAnalytics;
  ticketedEventAttendance: TicketedEventAttendance;
  ticketTypeReferences: TicketTypeReference[];
  ticketTypes: TicketType[];
  timeZoneIdentifier: string;
  visibility: string;
  updatedAt: string;
  venueConfigurationId?: string;
};

export type LinkedEntry = {
  id: string;
  type: LinkedEntryType;
};

export type TicketedEventAnalytics = {
  totalTicketCount: number;
  totalComplimentaryTicketCount: number;
  complimentaryTicketCountForTicketType: TicketCountForTicketType[];
  ticketCountForTicketType: TicketCountForTicketType[];
};

export type TicketCountForTicketType = {
  ticketTypeId: string;
  ticketCount: number;
};

export type TicketedEventAttendance = {
  totalRedeemedCount: number;
  redeemedCountForRedemptionType: RedeemedCountForRedemptionType[];
};

export type RedeemedCountForRedemptionType = {
  id: string;
  lineItemType: string;
  redeemedCount: number;
};

export type PassConnection = {
  edges: PassEdge[];
  nodes: Pass[];
  pageInfo: PageInfo;
  totalCount: number;
};

export type PassEdge = {
  cursor: string;
  node: Pass;
};

export type Pass = {
  id: string;
  createdAt: string;
  deletedAt?: string;
  email: string;
  firstName: string;
  lastName: string;
  updatedAt: string;
  passConfigId: string;
  sharedAt?: string;
  userId?: string;
  source: string;
  reservedSeats: ReservedSeat[];
};

export type ReservedSeat = {
  generalAdmissionArea?: string;
  row?: string;
  seat?: string;
  section?: string;
  table?: string;
  seatIdentifier: string;
};

export type PassConfigRenewalCampaign = {
  id: string;
  passConfigId: string;
  renewalStartDate: string;
  renewalEndDate: string;
  timeZoneIdentifier: string;
  renewers: PassConfigRenewer[];
};

export type PassConfigRenewer = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  selectableSeatIdentifiers: string[];
};

export type PassConfig = {
  createdAt: string;
  deletedAt?: string;
  description?: string;
  endDate: string;
  id: string;
  name: string;
  organizationId: string;
  priceInCents: number;
  purchaseExpirationDate?: string;
  quantityAvailable?: number;
  passConfigStatus: string;
  visibility: string;
  startDate: string;
  teamIds: string[];
  updatedAt: string;
  eventFilters: EventFilter[];
  ticketedEvents: TicketedEvent[];
  feeStrategy: string;
  formFieldIds: string[];
  formFields: FormField[];
  includedTicketedEventIds: string[];
  excludedTicketedEventIds: string[];
  renewalCampaign: PassConfigRenewalCampaign;
};

export type PassConfigConnection = {
  edges: PassConfigEdge[];
  nodes: PassConfig[];
  pageInfo: PageInfo;
  totalCount: number;
};

export type PassConfigEdge = {
  cursor: string;
  node: PassConfig;
};

export type GetPassConfigsByOrganizationIdInput = {
  after?: string;
  before?: string;
  first?: number;
  last?: number;
  organizationId: string;
  startDateMin?: string;
  startDateMax?: string;
  endDateMin?: string;
  endDateMax?: string;
  sortType: PassConfigSortType;
  sortByAscending: boolean;
  passConfigStatuses?: PassConfigStatus[];
  visibilities?: PassConfigVisibility[];
};

export enum PassConfigSortType {
  StartDate = 'START_DATE',
}

export enum PassConfigStatus {
  Draft = 'Draft',
  Active = 'Active',
}

export enum PassConfigVisibility {
  Public = 'Public',
  Private = 'Private',
  Renewal = 'Renewal',
  NotForSale = 'NotForSale',
}

export type SeatingChart = {
  reservedSeats: ReservedSeat[];
};

export type VenueConfiguration = {
  seatingChart: SeatingChart;
};

export type EventFilter = {
  seatsDotIoEnabledCategoryIds: string[];
  seatsDotIoPartialSeasonId?: string;
  teamId: string;
  venueConfigurationId?: string;
  venueConfiguration?: VenueConfiguration;
};

export type SearchPassesByTextValueInput = {
  after?: string;
  before?: string;
  first?: number;
  last?: number;
  email?: string;
  endDate?: string;
  startDate?: string;
  firstName?: string;
  lastName?: string;
  sortByAscending: boolean;
  sortType: PassSortType;
};

export enum PassSortType {
  StartDate = 'START_DATE',
}

export type SearchTicketGroupsByTextValueInput = {
  after?: string;
  before?: string;
  first?: number;
  last?: number;
  email?: string;
  endDate?: string;
  startDate?: string;
  firstName?: string;
  lastName?: string;
  sortByAscending: boolean;
  sortType: TicketGroupSortType;
};

export enum TicketGroupSortType {
  TicketGroupPurchaseDate = 'TICKET_GROUP_PURCHASE_DATE',
  TicketGroupPurchaserLastName = 'TICKET_GROUP_PURCHASER_LAST_NAME',
}

export type TicketGroupConnection = {
  edges: TicketGroupEdge[];
  nodes: TicketGroup[];
  pageInfo: PageInfo;
  totalCount: number;
};

export type TicketGroupEdge = {
  cursor: string;
  node: TicketGroup;
};

export type TicketGroup = {
  id: string;
  createdAt: string;
  deletedAt?: string;
  email?: string;
  firstName: string;
  lastName: string;
  updatedAt: string;
  ticketGroupReference: string;
  userId?: string;
  passes: Pass[];
  orderTotalInCents: number;
  tickets: Ticket[];
  source: string;
  itemCategories: string[];
  currency: string;
};

export type SearchTicketsByTextValueInput = {
  after?: string;
  before?: string;
  first?: number;
  last?: number;
  email?: string;
  endDate?: string;
  startDate?: string;
  firstName?: string;
  lastName?: string;
  sortByAscending: boolean;
  sortType: TicketSortType;
};

export enum TicketSortType {
  TicketDate = 'TICKET_DATE',
}

export type TicketConnection = {
  edges: TicketEdge[];
  nodes: Ticket[];
  pageInfo: PageInfo;
  totalCount: number;
};

export type TicketEdge = {
  cursor: string;
  node: Ticket;
};

export type Ticket = {
  createdAt: string;
  deletedAt?: string;
  email: string;
  firstName: string;
  id: string;
  qrCodeData: string;
  sharedAt?: string;
  lastName: string;
  passId: string;
  redeemedAt?: string;
  refundable: boolean;
  ticketedEventId: string;
  ticketStatus: string;
  ticketTypeId: string;
  updatedAt: string;
  userId?: string;
  source: string;
  reservedSeat?: ReservedSeat;
};

export enum EventStatus {
  Unknown = 'Unknown',
  Draft = 'Draft',
  Upcoming = 'Upcoming',
  Open = 'Open',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Archived = 'Archived',
  Rescheduled = 'Rescheduled',
  Cancelled = 'Cancelled',
}

export enum ItemType {
  PassConfig = 'Pass',
  TicketedEvent = 'Event',
}

export enum LinkedEntryType {
  Unknown = 'Unknown',
  HudlScheduleEntry = 'HudlScheduleEntry',
}

export type SearchParams = {
  lastName?: string;
  firstName?: string;
  email?: string;
  startDate?: string;
  endDate?: string;
};

export type SearchParamValue = {
  label: string;
  value: string;
};

export type School = {
  timeZone: string;
};

export type CreatePassConfigRenewalCampaignInput = {
  passConfigId: string;
  renewalStartDate: string;
  renewalEndDate: string;
  timeZoneIdentifier: string;
  postRenewalVisibility: PassConfigVisibility;
  renewers: PassConfigRenewerInput[];
};

export type PassConfigRenewerInput = {
  email: string;
  firstName: string;
  lastName: string;
  selectableSeatIdentifiers: string[];
};
