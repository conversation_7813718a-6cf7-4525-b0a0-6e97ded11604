import { getMarvelOrigin } from '@hudl/frontends-environment';

import { ItemType } from '../types/ticketingDefinitions';

export const getUserAdminUrl = (userId: string): string => {
  return `${getMarvelOrigin()}/user/${userId}`;
};

export const getSchoolAdminUrl = (organizationId: string): string => {
  return `${getMarvelOrigin()}/school/${organizationId}`;
};

export const getTicketingAdminHomeUrl = (organizationId: string): string => {
  return `/ticketing/${organizationId}`;
};

export const getTeamAdminUrl = (teamId: string): string => {
  return `${getMarvelOrigin()}/team/${teamId}`;
};

enum Environment {
  Thor = 'thorhudl',
  Prod = 'hudl',
}

type UrlParts = {
  domain?: string;
  branch?: string;
  environment: Environment;
};
const getUrlParts = (): UrlParts => {
  const { hostname } = window.location;
  const hostParts = hostname.replace('admin.', '').split('.');
  const domain = hostParts[hostParts.length - 2];
  const branch = hostParts[0];

  return {
    domain,
    branch,
    environment: domain === Environment.Prod.toString() ? Environment.Prod : Environment.Thor,
  };
};

const latestSuffix = '--latest';
const getLatestSuffixedBranchName = (branch: string): string => {
  return branch.endsWith(latestSuffix) ? branch : `${branch}${latestSuffix}`;
};

export const getOrderUrl = (referenceId: string, itemType: ItemType): string => {
  const { domain, branch } = getUrlParts();
  const type = itemType === ItemType.PassConfig ? 'passes' : 'tickets';
  if (domain === Environment.Prod) {
    return `https://fan.${domain}.com/${type}/${referenceId}`;
  }

  return `https://${getLatestSuffixedBranchName(branch ?? '')}.fan.${domain}.com/${type}/${referenceId}`;
};

export const getPassConfigRenewalUrl = (renewerId: string): string => {
  const { domain, branch, environment } = getUrlParts();
  if (environment === Environment.Prod) {
    return `https://fan.${domain}.com/passes/renew/${renewerId}`;
  }

  return `https://${getLatestSuffixedBranchName(branch ?? '')}.fan.${domain}.com/passes/renew/${renewerId}`;
};
