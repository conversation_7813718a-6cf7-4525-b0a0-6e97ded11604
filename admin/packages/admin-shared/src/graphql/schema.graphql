schema {
  query: Query
  mutation: Mutation
  subscription: Subscription
}

"Representation of a relationship between two entities."
interface IRelationship {
  "The GraphQL ID of the relationship."
  id: ID!
  "The primary entity of the relationship's Id."
  primaryEntityId: ID!
  "The related entity of the relationship's Id."
  relatedEntityId: ID!
  "The type of entity that PrimaryEntityId refers to."
  primaryEntityType: EntityType!
  "The type of entity that RelatedEntityId refers to."
  relatedEntityType: EntityType!
  "The type of relationship."
  relationshipType: RelationshipType!
  "The date and time this relationship was created."
  createdAt: DateTime
  "The date and time this relationship was last updated."
  updatedAt: DateTime
  "The date and time this relationship was deleted."
  deletedAt: DateTime
}

interface ISearchableMedia {
  title: String
  createdAt: DateTime!
  updatedAt: DateTime!
  isDeleted: Boolean!
}

interface ITrial {
  startDate: DateTime!
  endDate: DateTime!
  trialStatus: Int!
}

"Describes extended access for a specific team, product, and subscription level."
type AccessExtensionOutput {
  "The ID of the team with extended access."
  teamId: String
  "The product with extended access."
  product: String
  "The subscription level with extended access, may not be populated."
  level: String
  "The start date of the extended access period, typically the created date."
  startDate: DateTime
  "The end date of the extended access period."
  endDate: DateTime
}

"Representation of an ad campaign."
type AdCampaign {
  "The GraphQL id of the ad campaign."
  id: ID!
  "The name of the ad campaign."
  name: String!
  "The cachekey for the ad campaign."
  cacheKey: String!
  "The ad targeting key\/value associated with the ad campaign."
  targetingKeyValue: String!
  "The ad campaign type."
  campaignType: String!
  "Indicates whether the ad campaign is enabled or not"
  isEnabled: Boolean!
  "The creation date of the ad campaign."
  createdAt: DateTime!
  "The last update date of the ad campaign."
  updatedAt: DateTime!
}

type Address {
  "First street address line."
  line1: String
  "Second street address line. Optional."
  line2: String
  "City name."
  city: String
  "State, province, or region name."
  state: String
  "Country name."
  country: String
  "Zip or postal code."
  zipCode: String
}

type AdminCreateEmergencyCodePayload {
  "The client mutation identifier."
  clientMutationId: String
  "The emergency code for this License."
  emergencyCode: String
  "The date that the emergency code is valid for."
  date: DateTime!
}

type AdminCreateLicenseNotePayload {
  "The client mutation identifier."
  clientMutationId: String
  "The updated License with the new note."
  license: License
}

type AdminCreateLicensesPayload {
  "The client mutation identifier."
  clientMutationId: String
  "The list of created Licenses."
  licenses: [License]
}

type AdminDeleteLicensesPayload {
  "The client mutation identifier."
  clientMutationId: String
  "Says whether the deletion was successful."
  wasSuccessful: Boolean!
}

type AdminDeregisterLicensesPayload {
  "The client mutation identifier."
  clientMutationId: String
  "Says whether the deregistration was successful."
  wasSuccessful: Boolean!
}

type AdminEditLicenseSpecialStringPayload {
  "The client mutation identifier."
  clientMutationId: String
  "The updated License with the new note."
  license: License
}

type AdminGetLicensesPayload {
  "The fetched License objects."
  licenses: [License]
}

type AdminLicenseDatesPayload {
  "The client mutation identifier."
  clientMutationId: String
  "The list of updated Licenses."
  licenses: [License]
}

type AdminLicenseSearchPayload {
  "The client mutation identifier."
  clientMutationId: String
  "The found License."
  license: License
}

type AdminUpdateLicenseTeamIdPayload {
  "The client mutation identifier."
  clientMutationId: String
  "The updated License with the new team ID."
  license: License
}

type AuthZeroConnection {
  "The Connection id"
  id: String
  "The Connection name"
  name: String
  "The Connection display name"
  displayName: String
  "The Realms of the Connection"
  realms: [String]
  "The enabled clients for the Connection"
  enabledClients: [String]
  "The Connection strategy"
  strategy: String
  "The Connection domain"
  domain: String
  "The Connection tenant domain"
  tenantDomain: String
  "The Connection client id"
  clientId: String
  "Whether to set the email verified to true for this connection. Valid options: 'never_set_emails_as_verified' & 'always_set_emails_as_verified'. MS Entra (Azure AD) only."
  shouldTrustEmailVerifiedConnection: String
  "Whether to request access to the Extended Profile for this connection. MS Entra (Azure AD) only."
  extProfile: Boolean
  "The Domain Aliases used in Home Realm Discovery"
  domainAliases: [String]
  "The status of the Connection: Off, Test, or Production"
  status: ConnectionStatus
}

type AvailablePublishEventStreamInfo {
  includeRtmp: Boolean!
  includeScoreboard: Boolean
  isDefaultRestream: Boolean!
  isDefaultStream: Boolean!
  streamType: StreamType!
  cameraId: String
  name: String!
  isRequired: Boolean!
  internalMicVolume: Float
  installationId: String!
}

type BackgroundImage {
  "Returns true if contentServerId or path is defined"
  isValid: Boolean!
  "The image size"
  size: ImageSize!
  "The image url"
  url: String
  "The image secure url"
  secureUrl: String
  "The image content server id"
  contentServerId: String
  "The image path"
  path: String
  "The image width"
  width: Int
  "The image height"
  height: Int
}

type BillingLink {
  "The Hudl entity type the billing record is linked to"
  entitledEntityType: EntitledEntityType!
  "The billing record type the Hudl entity is linked to"
  billingRecordType: BillingRecordType!
  "The Hudl entity id the billing record is linked to"
  entitledEntityId: String
  "The billing record id the Hudl entity is linked to"
  billingRecordId: String
  "The Entitled Entity for the BillingLink"
  entitledEntity: EntitledEntity @cost(weight: "10")
}

"A connection to a list of items."
type BillingLinkConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [BillingLinkEdge!]
  "A flattened list of the nodes."
  nodes: [BillingLink]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [BillingLink!]
}

"An edge in a connection."
type BillingLinkEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: BillingLink
}

type BillingSubscription {
  "The Id of the subscription from the external billing source."
  externalId: String
  "The Access status of the subscription."
  status: String
  "The Product the subscription is for."
  product: String
  "The level of the Product the subscription is for."
  level: String
  "The start date of the subscription"
  startDate: DateTime!
  "The end date of the subscription"
  endDate: DateTime!
  "The BillingLink for the Subscription"
  billingLink: BillingLink @cost(weight: "10")
  "The Invoices for a Subscription"
  invoices("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): InvoiceConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
}

"A connection to a list of items."
type BillingSubscriptionConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [BillingSubscriptionEdge!]
  "A flattened list of the nodes."
  nodes: [BillingSubscription]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [BillingSubscription!]
}

"An edge in a connection."
type BillingSubscriptionEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: BillingSubscription
}

type Camera {
  "The GraphQL Camera Id of this camera."
  id: ID!
  "The internal camera Id of this camera."
  cameraId: String
  "The name of the camera."
  name: String
  "The position of the camera."
  position: Int!
  "The virticam name of the camera."
  virtcamName: String
}

type Carrier {
  "Carrier value of the enum"
  value: String
  "Description for the phone carrier"
  description: String
}

"An stream of chunks an inference run output by an inference run."
type ChunkStreamOutput {
  "ContentServerId for the S3 bucket"
  contentServerId: String
  "Key for the S3 file"
  key: String!
}

type CoachOutput {
  "The internal ID of the coach."
  internalID: String
  "The name of the coach."
  name: String
  "The external source Id of the coach."
  externalSourceId: String
  "The external source type this coach information came from."
  externalSourceType: ExternalSourceType!
  "Whether a coach is allowed to be contacted by users."
  canContact: Boolean!
  "The date and time this coach was last updated."
  updatedAt: DateTime!
  "The date and time this coach was deleted."
  deletedAt: DateTime
  "The coaches email address."
  coachEmail: String
  "The coaches phone number."
  coachPhone: String
}

type CollegeSearchResultItem {
  id: ID!
  "The internal Id of the college."
  internalId: String
  "The GSL categorization of the college."
  organizationCategorizations: [GSLOrganizationCategorization]
  "The name of the college."
  name: String
  "The address of the college."
  address: String
  "The city where the college is located."
  city: String
  "The state, province, or other identifier where the college is located."
  subdivision: String
  "The country where the college is located."
  country: String
  "The postal code of the college."
  postalCode: String
  "The geocoordinates of the college."
  coordinates: Geocoding
  "The distance information computed from provided coordinates."
  distance: Distance
  "The date and time this college record was created."
  createdAt: DateTime
  "The date and time this college record was last updated."
  updatedAt: DateTime
  "The date and time the college record was deleted."
  deletedAt: DateTime
  "The GSL Id of the college."
  gslId: String
  "The Inun Id of the college."
  inunId: String
  "The Ipeds code of the college."
  mainIpedsCode: String
  "The phone number of the college."
  phoneNumber: String
  "The area code of the college."
  areaCode: String
  "The student body type of the college."
  studentBody: StudentBodyType!
  "The athletic association types of the college."
  athleticAssociation: [AthleticAssociationType]
  "The list of degrees offered by the college."
  degrees: [Degree]
  "The average SAT score of freshman accepted to the college."
  satMean: Int!
  "The average ACT score of freshman accepted to the college."
  actMean: Int!
  "The entrance exams accepted for admissions to the college."
  entranceExams: [EntranceExamType]
  "The currency associated with the cost to attend the college."
  currencyCode: CurrencyCodeType!
  "Indicates whether or not electronic applications are accepted at the college."
  allowsElectronicApplication: Boolean!
  "Indicates how difficult it is to be accepted to this college."
  acceptanceDifficulty: AcceptanceDifficultyType!
  "The list of sports offered by the college."
  sports: [SportOutput]
  "Indicates if the college provides housing options."
  hasHousing: Boolean!
  "The number of men enrolled at the college."
  enrolledMen: Int!
  "The number of women enrolled at the college."
  enrolledWomen: Int!
  "The total number of students enrolled at the college."
  enrolled: Int!
  "The average tuition to attend the college."
  tuitionOverall: Float!
  "The tuition for in-state students to attend the college."
  tuitionState: Float!
  "The tuition for out-of-state students to attend the college."
  tuitionOutOfState: Float!
  "The cost for room and board at the college."
  roomAndBoard: Float!
  "The URLs for the colleges website."
  url: [Url]
  "The type of the college i.e. Public or Private."
  collegeType: CollegeType!
}

"A connection to a list of items."
type CollegeSearchResultItemSearchConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [CollegeSearchResultItemSearchEdge!]
  "A flattened list of the nodes."
  nodes: [CollegeSearchResultItem]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [CollegeSearchResultItem!]
}

"An edge in a connection."
type CollegeSearchResultItemSearchEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: CollegeSearchResultItem
}

type ConvertPlayToolsTrialResponse {
  "The subscription number of the new play tools subscription"
  subscriptionNumber: String
  "Subscription details for the conversion."
  playToolsSubscriptionDetails: SubscriptionSignupDetailsOutput
  "Invoice details for the conversion."
  playToolsInvoiceDetails: InvoiceSignupDetailsOutput
}

"Successful response for convert trial mutation"
type ConvertTrial {
  "Subscription number for the newly created subscription."
  subscriptionNumber: String
  "Subscription details for the conversion."
  subscriptionSignupDetails: SubscriptionSignupDetailsOutput
  "Subscription details for add-ons for the conversion."
  addOnSubscriptionsSignupDetails: [SubscriptionSignupDetailsOutput]
  "Invoice details for the conversion."
  invoiceSignupDetails: [InvoiceSignupDetailsOutput]
  "Team created for the new internal signup"
  team: Team! @cost(weight: "10")
}

"A custom form definition"
type CustomForm {
  "The unique identifier of the form"
  id: ID!
  "The root form ID for versioning"
  rootFormId: ID!
  "The owner entity ID"
  ownerId: ID!
  "The type of owner"
  ownerType: OwnerType!
  "SurveyJS form definition JSON"
  formJson: String!
  "The title extracted from form JSON"
  title: String!
  "The description extracted from form JSON"
  description: String
  "The number of questions in the form"
  questionCount: Int!
  "When the form was created"
  createdAt: DateTime!
  "When the form was last updated"
  updatedAt: DateTime!
  "When the form was deleted, if applicable"
  deletedAt: DateTime
  "Who created the form"
  createdBy: ID!
  "Who last updated the form"
  updatedBy: ID!
  "Who deleted the form, if applicable"
  deletedBy: ID
}

"Default ticketing settings for an organization"
type DefaultTicketingSettings {
  "The default payout method chosen by the organization"
  payoutMethod: String
  "The default ticket types associated with teams belonging to the organization"
  defaultTeamTicketTypes: [TeamTicketType!]
  "The default fee strategy for ticketing entities"
  defaultFeeStrategy: String
}

type Degree {
  "The Classification of Instructional Programs (CIP) Code of the degree"
  cipCode: String
  "The description of the degree"
  description: String
  "Indicates if the degree is offered as an associates degree"
  isAssociates: Boolean!
  "Indicates if the degree is offered as a bachelors degree"
  isBachelors: Boolean!
}

type DestinationAuthentication {
  "The GraphQL Id of the destination authentication."
  id: ID!
  "The internal id of the destination authentication."
  internalId: String
  "The restream destination platform of the destination authentication."
  restreamDestinationPlatform: RestreamDestinationPlatform!
  "The SchoolId of the destination authentication."
  schoolId: String
  "The name of the destination authentication."
  name: String
  "The url of the channel the event will be streamed on."
  channelUrl: String
}

type Distance {
  "The distance (meters) from the geocordinates"
  meters: Float
  "The coordinates for distance computation"
  coordinates: Geocoding
}

"Representation of an embedded organization program type."
type EmbeddedOrganizationProgramType {
  "The Id of the Organization Program Type."
  id: ID!
  "The name of the program type."
  name: String!
}

type ErrorCreatingHardwareOrder {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

type ErrorCreatingOrganization {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

type ErrorCreatingTeam {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

type ErrorCreatingUser {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

type ErrorCreatingZuoraData {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

type ErrorRollingBackZuoraData {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

type ErrorUpdatingHudlData {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

type ExchangeFixtureAttachment {
  "The Id of the attachment to the fixture"
  id: ID!
  "The hudl Id of the attachment"
  internalId: String @deprecated(reason: "Use Id instead")
  "The name of the file"
  filename: String
  "The URI of the file to download"
  downloadUri: String
  "Is the attachment marked as soft deleted"
  isDeleted: Boolean!
}

type ExchangeLeague {
  "The fixtures in the league"
  fixtures("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor seasonId: ID teamIds: [ID!] requiredAssets: ExchangeMatchRequiredAsset): LeagueFixtureConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "A specific fixture in the league"
  fixture(fixtureId: ID!): LeagueFixture @cost(weight: "10")
  "The Id of the league in the exchange"
  id: ID!
  "The name of the league in the exchange"
  leagueName: String
  "The playing teams in the league; i.e. the teams that appear in the fixtures of that league"
  teamIds: [String]
  "Teams added to the league as an admin team"
  adminTeamIds: [String]
  "The default season for the league"
  defaultSeason: ExchangeSeason
  "The seasons belonging to the league"
  seasons: [ExchangeSeason]
}

type ExchangeSeason {
  "The Id of the season."
  id: ID!
  "The year of the season."
  year: String
  "The playing teams in the season."
  teams: [ExchangeTeam]
}

type ExchangeTeam {
  "The Id of the team in the fixture"
  id: ID!
  "The hudl Id of the team."
  internalId: String @deprecated(reason: "Use Id instead")
  "The name of the team in the fixture"
  name: String
}

"Representation of a favorite relationship between a user and a favorited entity."
type FavoriteRelationship implements IRelationship {
  "The GraphQL ID of the relationship."
  id: ID!
  "The primary entity of the relationship's Id."
  primaryEntityId: ID!
  "The related entity of the relationship's Id."
  relatedEntityId: ID!
  "The type of entity that PrimaryEntityId refers to."
  primaryEntityType: EntityType!
  "The type of entity that RelatedEntityId refers to."
  relatedEntityType: EntityType!
  "The type of relationship."
  relationshipType: RelationshipType!
  "The date and time this relationship was created."
  createdAt: DateTime
  "The date and time this relationship was last updated."
  updatedAt: DateTime
  "The date and time this relationship was deleted."
  deletedAt: DateTime
}

type FeatureToggle {
  "The ID of the Feature Toggle"
  id: String
  "Whether or not the Feature Toggle is enabled"
  isEnabled: Boolean!
}

type FixtureStream {
  "The ID of the stream."
  id: ID!
  "The name of the stream."
  name: String
  "The (absolute) playback url of the current stream."
  playbackUrl: String
  "The duration of the stream."
  durationMs: Long!
  "The time the stream was created at."
  createdAt: DateTime!
  "The time the stream playable was updated at."
  playableUpdatedAt: DateTime!
  "The url of the preview thumbnail."
  thumbnailUrl: String
}

type FixtureSummary {
  "The internal TeamId for the fixture summary."
  teamId: String
  "The internal GameId for the fixture summary."
  gameId: String
  "The internal VideoId for the fixture summary."
  videoId: String
}

"A field on an event or pass to be filled out by the user."
type FormField {
  "The ID of the Form Field."
  id: ID!
  "The unique identifier for the Organization that the Form Field belongs to."
  organizationId: ID!
  "The label for the Form Field."
  label: String!
  "The help text for the Form Field."
  helpText: String
  "The type of the Form Field."
  fieldType: String!
  "If the Form Field is a required field."
  isRequired: Boolean!
  "The time the Form Field was created."
  createdAt: DateTime!
  "The time the Form Field was last updated."
  updatedAt: DateTime!
  "The time the Form Field was deleted, if it was deleted."
  deletedAt: DateTime
}

"A connection to a list of items."
type FormFieldConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [FormFieldEdge!]
  "A flattened list of the nodes."
  nodes: [FormField]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type FormFieldEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: FormField
}

"A field on an event or pass to be filled out by the user."
type FormFieldResponse {
  "The unique identifier for the Form Field this response is for."
  formFieldId: ID!
  "The response to the Form Field."
  response: [String!]!
  "The Form Field associated with this response"
  formField: FormField @cost(weight: "10")
}

"Representation of a form reference."
type FormRef {
  "The display order of the form reference."
  displayOrder: Int!
  "The form ID."
  formId: ID!
  "Whether the form reference is enabled."
  isEnabled: Boolean!
}

"A chronological collection of the same competition period played repeatedly."
type GSLCompetition {
  "The ID of the competition, e.g. \"R1NMQ29tcGV0aXRpb242M2Y1NDFhZmY3MjMxMTczMzRhZGQyYmM=\""
  id: ID!
  "Competition name, e.g. \"Ontario Women's Hockey League U22\""
  name: String
}

"A connection to a list of items."
type GSLCompetitionConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLCompetitionEdge!]
  "A flattened list of the nodes."
  nodes: [GSLCompetition]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [GSLCompetition!]
}

"An edge in a connection."
type GSLCompetitionEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLCompetition
}

"""
A series of fixtures played in a defined span of time for the purpose of deciding one overall winner.
This is commonly called "season" when referring to a competition period that takes place over multiple months.
"""
type GSLCompetitionPeriod {
  "The competition this period is a part of, e.g. \"Ontario Women's Hockey League U22\""
  competition: GSLCompetition @cost(weight: "10")
  "The ID of the competition period, e.g. \"R1NMQ29tcGV0aXRpb25QZXJpb2Q2NGVmNzVhZjk4NzRiZTE1YmVhMjQ0M2M=\""
  id: ID!
  "Competition id, e.g. \"R1NMQ29tcGV0aXRpb242M2Y1NDFhZmY3MjMxMTczMzRhZGQyYmM=\""
  competitionId: ID!
  "Competition period name, e.g. \"Ontario Women's Hockey League U22 - 2023-2024\""
  name: String
  "Competition period start date, e.g. \"2023-07-01T00:00:00.000Z\""
  startDate: DateTime
  "Competition period end date, e.g. \"2024-06-30T23:59:59.000Z\""
  endDate: DateTime
}

"""
A broad term for a sports contest where two or more athletes or teams compete with the goal of winning. A fixture can be a single event such as a basketball game or global football match, or a collection of smaller events such as a wrestling meet with multiple matches.

"Friendlies" are fixtures that are played between teams without the specific purpose of progressing within a given competition.
"""
type GSLFixture {
  "The Competition Period for the fixture, e.g. \"Ontario Women's Hockey League U22 - 2023-2024\""
  competitionPeriod: GSLCompetitionPeriod @cost(weight: "10")
  "The ID of the fixture, e.g. \"R1NMRml4dHVyZTY0ZmQwMzg1MTIzNDBiYzMyYjgzYjgwNw==\""
  id: ID!
  "Fixture name, e.g. \"Ridley College Tigers vs OHA Mavericks Tardiff U22\""
  name: String
  "Fixture date"
  date: DateTime! @deprecated(reason: "Use LocalDate instead. Date is timezone-unaware")
  "The date the fixture took place, using its local timezone"
  localDate: DateTime
  "The Competition Period ID for the fixture, e.g. \"R1NMQ29tcGV0aXRpb25QZXJpb2Q2NGVmNzVhZjk4NzRiZTE1YmVhMjQ0M2M=\""
  competitionPeriodId: ID!
  "Teams involved in the fixture"
  teams: [GSLFixtureTeam]
  "The top-line outcome that is determined at the end of a fixture."
  fixtureResult: GSLFixtureResult
  "The streams of the video."
  streams: [GSLMediaStreamSummary]
  "The media contexts for the fixture."
  mediaContextSummaries: [GSLMediaContextSummary]
  "When the fixture was last updated, e.g. \"2023-09-09T23:45:12.370Z\""
  updatedAt: DateTime!
}

"A connection to a list of items."
type GSLFixtureConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLFixtureEdge!]
  "A flattened list of the nodes."
  nodes: [GSLFixture]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [GSLFixture!]
}

"An edge in a connection."
type GSLFixtureEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLFixture
}

"The relative position of participating teams at the end of a fixture. This is used for scenarios where more than two teams are competing at a single event, such as a swim meet or wrestling meet."
type GSLFixtureRank {
  "Team Id"
  teamId: ID!
  "Rank"
  rank: Int
}

"The top-line outcome that is determined at the end of a fixture."
type GSLFixtureResult {
  "One or more numbers that reflect the scoring actions of a given team throughout a fixture. The score format is determined by the competition"
  scores: [GSLFixtureScore]
  "The relative position of participating teams at the end of a fixture. This is used for scenarios where more than two teams are competing at a single event, such as a swim meet or wrestling meet."
  ranking: [GSLFixtureRank]
}

"A subset of a competition roster that describes the specific individuals called for a given fixture."
type GSLFixtureRoster {
  "The ID of the roster"
  id: ID!
  "Team Id"
  teamId: ID!
  "Fixture Id"
  fixtureId: ID!
  "Team members"
  teamMembers: [GSLFixtureTeamMember]
}

"""
One or more numbers that reflect the scoring actions of a given team throughout a fixture. The score format is determined by the competition.
Scoring varies widely by sport, and there are even nuanced differences between competitions of the same sport. It's important to get the score format from the competition that the fixture takes place in. In general, there are two broad categories of score formats: "simple scoring" and "compound scoring."

## Simple Scoring

A scoring format where the fixture result is typically determined by a single number for each participating team — the standard score. Some competitions may also include additional numbers that occur under certain circumstances (such a penalty kicks in global football) as part of determining the result — the additional score.

NOTE: Scores that contribute to the standard score itself (such as the number of games won within a given set of tennis, or the number of goals scored in an ice hockey shootout) are stored as part of media context, not additional circumstantial scores within the fixture result.
Additional scores contribute directly to the fixture result in their own right and are not included as part of the standard score.

### Examples

* Global football: 3-3 (4-5 penalty kicks)
* Basketball: 113-110
* Tennis: 5 - 3
* Ice Hockey: 2 - 1

## Compound scoring

A scoring format where the result of a fixture is determined by evaluating multiple standard scores for each participating team, regardless of whether there are also additional scores possible within the given score format.

### Examples

* Cricket: 226/7 - 147
"""
type GSLFixtureScore {
  "The ID of the team that took part in this fixture, e.g. \"R1NMRml4dHVyZTY0ZmQwMzg1MTIzNDBiYzMyYjgzYjgwNw==\""
  teamId: ID!
  "The single score per team that may, on its own, determine the fixture result when compared with the opponent's final score. e.g. 2 - 1 (Global Football)"
  standardScore: Int
  "Scoring that happens under certain circumstances when the standard score is not enough to determine the fixture result. These scores are displayed separately from the standard scores in reporting the official fixture result. e.g. 4 - 5 penalty kicks (Global Football)"
  additionalScore: Int
}

"The team that took part in a particular fixture"
type GSLFixtureTeam {
  "The full team information"
  team: GSLTeam @cost(weight: "10")
  "The roster with all players of the team that took part in this fixture"
  roster: GSLFixtureRoster @cost(weight: "10")
  "The ID of the fixture, e.g. \"R1NMRml4dHVyZTY0ZmQwMzg1MTIzNDBiYzMyYjgzYjgwNw==\""
  fixtureId: ID!
  "The ID of the team, e.g. \"R1NMVGVhbTYzZjYzYzA1NWUzNTU5NjExYTg5NmZjMA==\""
  teamId: ID!
  "Team Qualifier: HOME or AWAY"
  qualifier: GSLFixtureTeamQualifier!
}

type GSLFixtureTeamMember {
  "Team member"
  teamMember: GSLTeamMember @cost(weight: "10")
  "The ID of the team member"
  id: ID!
  "Jersey number"
  jersey: String
}

"A single person who is involved in the broader sport ecosystem. An individual is connected to teams or other groups by a \"member\" object, such as \"team member.\""
type GSLIndividual {
  "The ID of the individual"
  id: ID!
  "The individual's commonly referred to name, e.g. \"Cristiano Ronaldo\""
  commonName: GSLIndividualName
  "The individual's legal name, e.g. \"Cristiano Ronaldo dos Santos Aveiro\""
  legalName: GSLIndividualName
  "The individual's nickname, e.g. \"CR7'"
  nickname: String
}

type GSLIndividualName {
  "The full name of the individual. No formatting guaranteed"
  fullName: String
  "The given name of the individual"
  givenName: String
  "The family name of the individual"
  familyName: String
  "The middle name(s) of the individual"
  middleNames: String
  "The individual's suffix, e.g. Jr\/Sr"
  suffix: String
}

type GSLMediaContextSummary {
  "The ID of the related media stream."
  mediaStreamId: ID
  "The tagging data for the media context."
  taggingDataSummaries: [GSLTaggingDataSummary]
}

"A connection to a list of items."
type GSLMediaContextSummaryConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLMediaContextSummaryEdge!]
  "A flattened list of the nodes."
  nodes: [GSLMediaContextSummary]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLMediaContextSummaryEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLMediaContextSummary
}

type GSLMediaStreamPreloadDataSummary {
  "The playback url for the preloadable quality."
  playbackUrl: String
  "The quality identifier for the media stream quality."
  quality: String @deprecated(reason: "Use QualityId instead.")
  "The qualityId for the media stream quality."
  qualityId: Int!
}

type GSLMediaStreamSummary {
  "The thumbnail for the stream"
  thumbnailUrl: String
  "The (absolute) playback url of the current stream."
  playbackUrl: String
  "The ID of the stream."
  id: ID!
  "The name of the stream. (aka Angle)"
  name: String
  "The date\/time the stream was last updated on Hudl"
  updatedAt: DateTime!
  "The status of the stream"
  status: String
  "If present, reason media is not accessible."
  noAccessReason: GSLNoAccessReason! @deprecated(reason: "Use GSLNoAccessReasons")
  "If present, GSL media is not accessible."
  gslNoAccessReasons: [GSLNoAccessReason!]!
  "The list of preloadable playables that are ready and available for the current stream."
  preloadData: [GSLMediaStreamPreloadDataSummary]
  "The duration of the stream"
  durationMs: Long!
}

"A connection to a list of items."
type GSLMediaStreamSummaryConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLMediaStreamSummaryEdge!]
  "A flattened list of the nodes."
  nodes: [GSLMediaStreamSummary]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLMediaStreamSummaryEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLMediaStreamSummary
}

"A chronological collection of the same competition period played repeatedly."
type GSLSearchableCompetition {
  "A connection to competition periods"
  competitionPeriods("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the elements in the list that come before the specified cursor." before: String): GSLSearchableCompetitionPeriodConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false)
  "A connection to season periods"
  seasonPeriods("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the elements in the list that come before the specified cursor." before: String): GSLSearchableSeasonPeriodConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false)
  "A connection to regional alignments"
  regionalAlignments("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the elements in the list that come before the specified cursor." before: String): GSLSearchableRegionalAlignmentConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false)
  "The ID of the competition, e.g. \"R1NMQ29tcGV0aXRpb242M2Y1NDFhZmY3MjMxMTczMzRhZGQyYmM=\""
  id: ID!
  "When the competition was last updated"
  updatedAt: DateTime!
  "When the competition was created"
  createdAt: DateTime!
  "When the competition was deleted"
  deletedAt: DateTime
  "Competition name, e.g. \"Ontario Women's Hockey League U22\""
  name: String
  "Sport of the competition"
  sport: GSLCompetitionSport!
  "Gender of the competition"
  gender: GSLTeamGender!
}

"A connection to a list of items."
type GSLSearchableCompetitionConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableCompetitionEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableCompetition]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableCompetitionEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableCompetition
}

"""
A series of fixtures played in a defined span of time for the purpose of deciding one overall winner.
This is commonly called "season" when referring to a competition period that takes place over multiple months.
"""
type GSLSearchableCompetitionPeriod {
  "The ID of the competition period, e.g. \"R1NMQ29tcGV0aXRpb25QZXJpb2Q2NGVmNzVhZjk4NzRiZTE1YmVhMjQ0M2M=\""
  id: ID!
  "Competition id, e.g. \"R1NMQ29tcGV0aXRpb242M2Y1NDFhZmY3MjMxMTczMzRhZGQyYmM=\""
  competitionId: ID!
  "Competition period name, e.g. \"Ontario Women's Hockey League U22 - 2023-2024\""
  name: String
  "Competition period start date, e.g. \"2023-07-01T00:00:00.000Z\""
  startDate: DateTime
  "Competition period end date, e.g. \"2024-06-30T23:59:59.000Z\""
  endDate: DateTime
  "Season period ID associated with the competition period"
  seasonPeriodId: ID!
  "Season period associated with the competition period"
  seasonPeriod: GSLSearchableSeasonPeriod
}

"A connection to a list of items."
type GSLSearchableCompetitionPeriodConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableCompetitionPeriodEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableCompetitionPeriod]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableCompetitionPeriodEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableCompetitionPeriod
}

"A chronological collection of the same fixtures period played repeatedly."
type GSLSearchableFixture {
  "Individuals included in the fixture"
  individuals("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the elements in the list that come before the specified cursor." before: String): GSLSearchableIndividualConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false)
  "Rosters included in the fixture"
  fixtureRosters("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the elements in the list that come before the specified cursor." before: String): GSLSearchableFixtureRosterConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false)
  "Teams included in the fixture"
  teams("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the elements in the list that come before the specified cursor." before: String): GSLSearchableFixtureTeamConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false)
  "MediaStreams included in the fixture"
  mediaStreams("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the elements in the list that come before the specified cursor." before: String): GSLMediaStreamSummaryConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false)
  "MediaContexts included in the fixture"
  mediaContexts("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the elements in the list that come before the specified cursor." before: String): GSLMediaContextSummaryConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false)
  "The ID of the fixture"
  id: ID!
  "Sport of the fixture"
  sport: GSLCompetitionSport!
  "When the fixture was last updated"
  updatedAt: DateTime!
  "When the fixture was created"
  createdAt: DateTime!
  "When the fixture was deleted"
  deletedAt: DateTime
  "Name of the competition"
  competitionName: String
  "Date of fixture"
  localDate: DateTime
  "Home team ID"
  homeTeamId: ID!
  "Home team"
  homeTeam: GSLSearchableFixtureTeam
  "Away team ID"
  awayTeamId: ID!
  "Away team"
  awayTeam: GSLSearchableFixtureTeam
  "Fixture state"
  fixtureStates: GSLFixtureState!
  "The top-line outcome that is determined at the end of a fixture."
  result: GSLSearchableFixtureResult
}

"A connection to a list of items."
type GSLSearchableFixtureConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableFixtureEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableFixture]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableFixtureEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableFixture
}

"The top-line outcome that is determined at the end of a fixture."
type GSLSearchableFixtureResult {
  "One or more numbers that reflect the scoring actions of a given team throughout a fixture. The score format is determined by the competition"
  scores: [GSLSearchableFixtureScore]
}

"Rosters included in the fixture"
type GSLSearchableFixtureRoster {
  "The ID of the individual"
  individualId: ID!
  "Jersey number"
  jersey: String
  "Team Id"
  teamId: ID!
  "Team Qualifier: HOME or AWAY"
  qualifier: GSLFixtureTeamQualifier!
}

"A connection to a list of items."
type GSLSearchableFixtureRosterConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableFixtureRosterEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableFixtureRoster]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableFixtureRosterEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableFixtureRoster
}

"""
One or more numbers that reflect the scoring actions of a given team throughout a fixture. The score format is determined by the competition.
Scoring varies widely by sport, and there are even nuanced differences between competitions of the same sport. It's important to get the score format from the competition that the fixture takes place in. In general, there are two broad categories of score formats: "simple scoring" and "compound scoring."

## Simple Scoring

A scoring format where the fixture result is typically determined by a single number for each participating team — the standard score. Some competitions may also include additional numbers that occur under certain circumstances (such a penalty kicks in global football) as part of determining the result — the additional score.

NOTE: Scores that contribute to the standard score itself (such as the number of games won within a given set of tennis, or the number of goals scored in an ice hockey shootout) are stored as part of media context, not additional circumstantial scores within the fixture result.
Additional scores contribute directly to the fixture result in their own right and are not included as part of the standard score.

### Examples

* Global football: 3-3 (4-5 penalty kicks)
* Basketball: 113-110
* Tennis: 5 - 3
* Ice Hockey: 2 - 1

## Compound scoring

A scoring format where the result of a fixture is determined by evaluating multiple standard scores for each participating team, regardless of whether there are also additional scores possible within the given score format.

### Examples

* Cricket: 226/7 - 147
"""
type GSLSearchableFixtureScore {
  "The ID of the team that took part in this fixture, e.g. \"R1NMRml4dHVyZTY0ZmQwMzg1MTIzNDBiYzMyYjgzYjgwNw==\""
  teamId: ID!
  "The single score per team that may, on its own, determine the fixture result when compared with the opponent's final score. e.g. 2 - 1 (Global Football)"
  standardScore: Int
  "Scoring that happens under certain circumstances when the standard score is not enough to determine the fixture result. These scores are displayed separately from the standard scores in reporting the official fixture result. e.g. 4 - 5 penalty kicks (Global Football)"
  additionalScore: Int
}

type GSLSearchableFixtureTeam {
  "The ID of the team"
  id: ID!
  "The name of the team"
  name: String
}

"A connection to a list of items."
type GSLSearchableFixtureTeamConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableFixtureTeamEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableFixtureTeam]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableFixtureTeamEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableFixtureTeam
}

type GSLSearchableIndividual {
  "The ID of the individual"
  id: ID!
  "The individual's commonly referred to name, e.g. \"Cristiano Ronaldo\""
  commonName: GSLSearchableIndividualName
}

"A connection to a list of items."
type GSLSearchableIndividualConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableIndividualEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableIndividual]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableIndividualEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableIndividual
}

type GSLSearchableIndividualName {
  "The full name of the individual. No formatting guaranteed"
  fullName: String
  "The given name of the individual"
  givenName: String
  "The family name of the individual"
  familyName: String
  "The middle name(s) of the individual"
  middleNames: String
  "The individual's suffix, e.g. Jr\/Sr"
  suffix: String
}

"""
A collection of teams grouped together (often based on geographical proximity) by a governing body to compete frequently against each other.
This is often called a “conference,” “league” or “region.”
"""
type GSLSearchableRegionalAlignment {
  "The ID of the regional alignment, e.g. \"R1NMU2VhcmNoYWJsZVJlZ2lvbmFsQWxpZ25tZW50NjQ1YmRhZDcwZTc2NmVlYTNjOTY5MDZh\""
  id: ID!
  "When the regional alignment was last updated"
  updatedAt: DateTime!
  "When the regional alignment was created"
  createdAt: DateTime!
  "When the regional alignment was deleted"
  deletedAt: DateTime
  "Regional alignment name, e.g. \"Big 12 Conference\""
  name: String
}

"A connection to a list of items."
type GSLSearchableRegionalAlignmentConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableRegionalAlignmentEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableRegionalAlignment]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableRegionalAlignmentEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableRegionalAlignment
}

type GSLSearchableSeasonPeriod {
  "The ID of the season period, e.g. \"R1NMU2VhcmNoYWJsZVNlYXNvblBlcmlvZDY0ZWY3NWFmOTg3NGJlMTViZWEyNDQzYw==\""
  id: ID!
  "Season period name, e.g. \"2023-2024\""
  name: String
  "The sport of the season period"
  sport: GSLCompetitionSport!
  "Season period start date, e.g. \"2023-07-01T00:00:00.000Z\""
  startDate: DateTime
  "Season period end date, e.g. \"2024-06-30T23:59:59.000Z\""
  endDate: DateTime
}

"A connection to a list of items."
type GSLSearchableSeasonPeriodConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableSeasonPeriodEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableSeasonPeriod]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableSeasonPeriodEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableSeasonPeriod
}

type GSLSearchableTeam {
  "The ID of the team"
  id: ID!
  "When the team was last updated"
  updatedAt: DateTime!
  "When the team was created"
  createdAt: DateTime!
  "When the team was deleted"
  deletedAt: DateTime
  "The name of the team"
  name: String
  "The sport of the team"
  sport: GSLCompetitionSport!
  "The gender of the team"
  gender: GSLTeamGender!
  "The region name of the team"
  regionName: String
  "The type of the team"
  teamType: GSLTeamType!
  "The team members"
  teamMembers: [GSLSearchableTeamMember!]!
  "The competitions the team participates in"
  competitions: [GSLCompetition!]!
}

"A connection to a list of items."
type GSLSearchableTeamConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GSLSearchableTeamEdge!]
  "A flattened list of the nodes."
  nodes: [GSLSearchableTeam]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GSLSearchableTeamEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: GSLSearchableTeam
}

type GSLSearchableTeamMember {
  "The preferred jersey number"
  preferredJersey: String
  "The individual"
  individual: GSLSearchableIndividual!
}

type GSLTaggingDataSummary {
  "The ID of the related breakdown."
  breakdownId: ID!
}

"A group of individuals training and\/or competing together on behalf of a given sport organization, including coaches, analysts, etc."
type GSLTeam {
  "The ID of the team, e.g. \"R1NMVGVhbTYzZjYzYzA1NWUzNTU5NjExYTg5NmZjMA==\""
  id: ID!
  "Team name, e.g. \"Ridley College Tigers\""
  name: String
  "Team abbreviation, e.g. \"RCT\""
  abbreviation: String
  "Competition Sport, e.g. \"ICE_HOCKEY\""
  sport: GSLCompetitionSport!
}

"An individual training and\/or competing with others on behalf of a sport organization. Athletes, coaches, scouts, etc. are all types of team members."
type GSLTeamMember {
  "The ID of the team member"
  id: ID!
  "Team member position"
  position: String
  "Individual characteristics"
  individual: GSLIndividual
}

type Geocoding {
  "The latitude of the address"
  latitude: Float!
  "The longitude of the address"
  longitude: Float!
}

type Group {
  "Member ids"
  memberIds("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): IDConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Group Video Activity"
  videoActivity: VideoActivity @cost(weight: "10")
  "Group Name"
  name: String
  "The graphql Id of the group."
  id: ID!
  "The hudl Id of the group."
  internalId: String
  "Group is system group"
  isSystemGroup: Boolean!
  "Group date created"
  dateCreated: DateTime!
}

"A connection to a list of items."
type GroupConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [GroupEdge!]
  "A flattened list of the nodes."
  nodes: [Group]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type GroupEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: Group
}

"Representation of a relationship between two entities."
type GuardianMetadata {
  "The id of the organization reference."
  organizationId: String!
  "The id of the user that updated the status of this metadata."
  approverUserId: String
  "The status the approving user set this metadata to."
  status: GuardianStatusType!
  "The date and time this metadata was last updated."
  updatedAt: DateTime
  "The list of teams Ids that the guardian has access to via their athlete."
  teamIds: [String]
}

"Representation of a guardian relationship between two users."
type GuardianRelationship implements IRelationship {
  "The list of metadata informing which organizations the guardian has access to and the status of that access."
  metadata: [GuardianMetadata]
  "Indicates the level of guardian access to the athlete and their teams."
  guardianshipLevel: GuardianshipLevel
  "The GraphQL ID of the relationship."
  id: ID!
  "The primary entity of the relationship's Id."
  primaryEntityId: ID!
  "The related entity of the relationship's Id."
  relatedEntityId: ID!
  "The type of entity that PrimaryEntityId refers to."
  primaryEntityType: EntityType!
  "The type of entity that RelatedEntityId refers to."
  relatedEntityType: EntityType!
  "The type of relationship."
  relationshipType: RelationshipType!
  "The date and time this relationship was created."
  createdAt: DateTime
  "The date and time this relationship was last updated."
  updatedAt: DateTime
  "The date and time this relationship was deleted."
  deletedAt: DateTime
}

type HudlTagsInfo {
  "Event Id to which the desired tagging session belongs"
  eventId: String!
  "User teams that can grant access to the desired tagging session"
  userTeamIds: [String]!
  "Fixture to which the tagging session is attached"
  fixtureId: String!
  "League to which the fixture belongs"
  leagueId: String!
}

"A connection to a list of items."
type IDConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [IDEdge!]
  "A flattened list of the nodes."
  nodes: [ID]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [ID!] @cost(weight: "10")
}

"An edge in a connection."
type IDEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: ID
}

"An inference run stores information about a run of an algorithm on video such as progress, timing, and metadata."
type InferenceRun {
  "InferenceRun Id"
  id: ID!
  "The internal Id of the inference run."
  internalId: String
  "Outputs of the inference run."
  outputs: [InferenceOutput]
  "The inference run status."
  status: InferenceRunStatus!
  "The inference run input dtos."
  inputs: [InferenceInput]
  "The inference run workflow template Id."
  workflowTemplateId: String
}

"An input associated with an inference run."
type InferenceS3FileWithTypeInput {
  "File type of the S3 file"
  s3FileType: S3FileType
  "Bucket for the S3 file"
  bucket: String
  "Key for the S3 file"
  key: String
}

"A slice in time of a video input for an inference run."
type InferenceVideoReferenceInput {
  "Related mediastream id"
  mediaStreamId: String
  "Related video id"
  videoId: String
  "Start time of the segment in seconds."
  startS: Float
  "Duration of the segment in seconds."
  durationS: Float
}

"An installation in Hudl"
type Installation {
  "The available publish event stream info for the installation."
  availableStreamInfo(teamId: String! publishEventType: PublishEventType!): [AvailablePublishEventStreamInfo!]! @cost(weight: "10")
  "The ID of the installation."
  id: ID!
  "The internal Id of the installation."
  internalId: String
  "The name of the installation."
  name: String
  "The device Id for the installation."
  deviceId: String
  "The latest status history received for the installation."
  latestStatus: InstallationStatusHistory
  "The setup status for the installation."
  setupStatus: [InstallationSetupStatus]
  "The school id associated with the installation."
  schoolId: String
  "The school name associated with the installation."
  schoolName: String
  "Additional schools that have access to record from the installation."
  secondarySchoolIds: [String]
  "Provider type for installation."
  providerType: InstallationProviderType!
  "Camera sensors on the installation."
  cameras: [Camera]
  "The date and time the installation was created."
  createdAt: DateTime!
  "Is installation test installation."
  isTest: Boolean!
  "The base uri for the installation"
  deviceBaseUri: String
  "List of teams ids to automatically create publish events from schedule entries.Does not include the teams associated with this installation's school, which will also have publish events created."
  scheduleSyncedTeamIds: [String]
  "Is installation deleted."
  isDeleted: Boolean!
  "Date and time the last log was received at."
  lastLogReceivedAt: DateTime
  "Installation model information for installation."
  installationModel: InstallationModel
  "The date and time the installation was positioned at."
  positionedAt: DateTime
  "Links to the originally shipped Focus Installation, which has been replaced by this installation."
  replacementForInstallationId: String
  "AOR uses this to mute a installations offline errors unless it has an upcoming recording in next 90 mins."
  ignoreOfflineEvents: Boolean!
  "Feature flags to turn on and off behavior for this installation."
  featureFlags: InstallationFeatureFlags!
}

type InstallationModel {
  "The GraphQL Id for the installation model."
  id: ID!
  "The internal Id for the installation model"
  internalId: String
  "The provider type for the installation model."
  providerType: InstallationProviderType!
  "The name for the installation model."
  name: String
  "The Description for the installation model."
  description: String
  "The series for the installation model."
  series: String
  "The lens for the installation model."
  lens: String
  "The sensor for the installation model."
  sensor: String
  "The hardware type for the installation model."
  hardwareType: Int!
}

type InstallationStatusHistory {
  "The GraphQL Id for the installation status history."
  id: ID!
  "The internal Id for the installation status history."
  internalId: String
  "The installation Id for the installation status history."
  installationId: String
  "The date and time the installation status history was created."
  createdAt: DateTime!
  "The Installation status for the installation status history."
  basicStatus: InstallationStatus!
  "The status codes for the installation status history."
  statusCodes: [StatusCodeDetails]
}

type InviteCode {
  "Team"
  team: TeamHeader @cost(weight: "10")
  "Is Family Members enabled for team"
  isFamilyMembersEnabled: Boolean! @cost(weight: "10")
  "Invite code Id"
  id: String
  "Team Id"
  teamId: String
  "Date created"
  dateCreated: DateTime!
  "Date expired"
  lastModifiedDate: DateTime!
  "Invite requests"
  inviteRequests: [InviteRequest]
  "Cell carriers"
  cellCarriers: [Carrier]
}

type InviteRequest {
  "User Id"
  userId: String
  "Date created"
  dateCreated: DateTime!
  "Jersey number"
  jersey: String
  "Graduation Year"
  graduationYear: String
  "Position"
  position: [String]
  "First Name"
  firstName: String
  "Last Name"
  lastName: String
  "Email"
  email: String
  "Role"
  role: Role!
  "Cell number."
  cellPhone: String
  "Cell carrier"
  cellCarrier: String
}

type Invoice {
  "User facing id of the invoice."
  userFacingId: String
  "The total amount of the invoice."
  amount: Decimal!
  "The remaining balance of the invoice."
  balance: Decimal!
  "The due date of the invoice."
  dueDate: DateTime!
  "The country of the invoice."
  country: String
  "The admin url path to the invoice."
  hudlSystemAdminInvoiceUrlPath: String
  "Subscriptions for invoice."
  subscriptions("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): BillingSubscriptionConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
}

"A connection to a list of items."
type InvoiceConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [InvoiceEdge!]
  "A flattened list of the nodes."
  nodes: [Invoice]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [Invoice!]
}

"An edge in a connection."
type InvoiceEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: Invoice
}

"Invoice details for the create internal signup add ons mutation."
type InvoiceSignupDetailsOutput {
  "The resulting invoice id for signup."
  invoiceId: String
  "The resulting invoice number for signup."
  invoiceNumber: String
}

"A slice in time of a video input for an inference run."
type JsonFileInput {
  "File type of the JSON file"
  jsonFileContents: JsonFileContents!
  "Bucket for the JSON file"
  bucket: String
  "Key for the JSON file"
  key: String
}

type LeagueFixture {
  "The Id of the fixture between two teams in the league season"
  id: ID!
  "The hudl Id of the league fixture."
  internalId: String @deprecated(reason: "Use Id instead")
  "The date on which the fixture is played"
  date: String!
  "The team playing at home in the fixture"
  homeTeam: ExchangeTeam
  "The team playing away in the fixture"
  awayTeam: ExchangeTeam
  "The videos associated with the fixture"
  videoIds: [ID] @deprecated(reason: "Use streams")
  "The data associated with the fixture"
  attachments: [ExchangeFixtureAttachment]
  "The information required to obtain a Hudl tagging session (aka Hudl tags) associated with the fixture"
  hudlTagsInfo: HudlTagsInfo
}

"A connection to a list of items."
type LeagueFixtureConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [LeagueFixtureEdge!]
  "A flattened list of the nodes."
  nodes: [LeagueFixture]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type LeagueFixtureEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: LeagueFixture
}

"Representation of a league pass config."
type LeaguePassConfig {
  "The GraphQL id of the pass config."
  id: ID!
  "The league associated with the league pass config."
  leagueEntityId: ID!
  "The status of the league pass config."
  leagueEntityType: String!
  "The name of the league pass config."
  name: String!
  "The description of the league pass config."
  description: String
  "The pass configs associated with this leage pass config"
  associatedPassConfigIds: [LeaguePassConfigAssociatedPassConfig!]
  "The event filters used to determine which events are valid for a pass config."
  teamFilters: [LeaguePassConfigTeamFilter!]!
  "The fee strategy to apply to the league pass config."
  feeStrategy: String!
  "The visibility of the league pass config."
  visibility: String!
  "Price of the league pass."
  priceInCents: Int!
  "The currency associated with the priceInCents of the league pass config."
  currency: String!
  "The status of the league pass config."
  leaguePassConfigStatus: String!
  "The start date the league pass config is valid for. (inclusive)"
  startDate: DateTime!
  "The end date the league pass config is valid for. (inclusive)"
  endDate: DateTime!
  "The game types that are excluded from the league pass config."
  excludedGameTypes: [String!]
  "Date the league pass config was created."
  createdAt: DateTime!
  "Date the league pass config was updated."
  updatedAt: DateTime!
  "Date the league pass config was deleted."
  deletedAt: DateTime
  "Associated schools that have child pass configs"
  associatedSchools: [School] @cost(weight: "10")
}

type LeaguePassConfigAssociatedPassConfig {
  organizationId: ID!
  passConfigId: ID!
}

"A connection to a list of items."
type LeaguePassConfigConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [LeaguePassConfigEdge!]
  "A flattened list of the nodes."
  nodes: [LeaguePassConfig]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [LeaguePassConfig!]
}

"An edge in a connection."
type LeaguePassConfigEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: LeaguePassConfig
}

"The filter used to determine which teams to include in the leage pass child configs."
type LeaguePassConfigTeamFilter {
  "The team filter gender"
  gender: String!
  "The team filter level"
  teamLevel: String!
  "The team filter sport"
  sport: String!
  "The team id of the event filter"
  excludedTeamIds: [String!]!
}

"Sportscode license."
type License {
  "The license identifier."
  id: ID!
  "The registration code of the license."
  token: String
  licenseNumber: Int
  licenseType: LicenseType!
  schoolId: String
  teamId: String
  "The registration code of the license."
  registrationCode: String
  "The registration status of the license."
  registrationStatus: RegistrationStatus
  version: Int
  userVersion: String
  machineIdentifier: String
  machineName: String
  osVersion: String
  userComment: String
  subscriptionStartDate: DateTime
  subscriptionEndDate: DateTime
  authenticationStartDate: DateTime
  authenticationEndDate: DateTime
  tokenPrevious: String
  specialString: String
  deletedDate: DateTime
  emergencyCodePublicHash: String
  licenseNotes: [LicenseNote]
  createdBy: String
  createdByEmailAddress: String
  createdDate: DateTime
}

type LicenseNote {
  creatorUserId: String
  createdAt: DateTime!
  noteText: String
}

"Representation of an entry linked to a ticketed event."
type LinkedEntry {
  "The ID of the linked entry."
  id: ID!
  "The type of the linked entry."
  type: String
}

type LivestreamSchedulingPermission {
  "The entity id (i.e. org or team id)."
  entityId: String
  "The entity has standard livestreaming access."
  hasStandardLivestreamingAccess: Boolean!
  "The entity has premium livestreaming access."
  hasPremiumLivestreamingAccess: Boolean!
}

"Representation of a Location."
type Location {
  "The address of the location."
  address: String
  "The City of the location."
  city: String
  "The Subdivision of the location."
  subdivision: String
  "The Country of the location."
  country: String
  "The PostalCode of the location."
  postalCode: String
  "The Latitude of the location."
  latitude: String
  "The Longitude of the location."
  longitude: String
  "The TimezoneIdentifier of the location."
  timezoneIdentifier: String
}

type MarketTaxonomy {
  "The classification of the organization."
  orgClassificationInt: Int!
  "The business unit of the organization."
  businessUnitInt: Int!
}

"A MaxPreps Team."
type MaxPrepsTeam {
  "MaxPreps School ID"
  maxPrepsSchoolId: String
  "Hudl Team ID"
  teamId: String
  "Longform School"
  school: String
  "School Name"
  schoolName: String
  "Team Level"
  teamLevel: String
  "Name of Sport"
  sport: String
  "Gender of Team"
  gender: String
  "Are Team Highlights Private?"
  areHighlightsPrivate: Boolean!
  "Team Mascot"
  mascot: String
  "Team Logo"
  logo: String
}

"A connection to a list of items."
type MediaSearchContentConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [MediaSearchContentEdge!]
  "A flattened list of the nodes."
  nodes: [MediaSearchContent]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [MediaSearchContent!]
}

"An edge in a connection."
type MediaSearchContentEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: MediaSearchContent
}

"A lightweight version of a MediaStream in Hudl as a result from our mediasearch index"
type MediaStreamItem implements ISearchableMedia {
  "The (absolute) playback url of the current mediastream."
  playbackUrl: String
  id: ID!
  "The date when the mediastream was uploaded"
  uploadedAt: DateTime!
  "The provider id linked to this mediastream"
  providerId: String
  "The status of the mediastream"
  status: String
  "The source title of the type"
  title: String!
  "The date when the type was created"
  createdAt: DateTime!
  "The date when the type was updated"
  updatedAt: DateTime!
  "Is this mediasearch deleted?"
  isDeleted: Boolean!
}

type Member {
  "Member groups"
  groups(includeFamilyMembers: Boolean "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): IDConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Member Video Activity"
  videoActivity: VideoActivity @cost(weight: "10")
  "The graphql Id of the member."
  id: ID!
  "The teamId that the member belongs to"
  teamId: ID!
  "The hudl Id of the member."
  internalId: String
  "Member's first name."
  firstName: String
  "Member's last name."
  lastName: String
  "Member's full name."
  fullName: String
  "Member's email."
  email: String
  "Member's phone number."
  cell: String
  "Member's phone carrier."
  cellCarrier: String
  "Member's status"
  status: UserStatus!
  "Member's role."
  role: Role!
  "Picture"
  picture: String @deprecated(reason: "Use `PictureUrl` instead.")
  "PictureUrl"
  pictureUrl: String
  "Position"
  position: [String]
  "Jersey"
  jersey: String
  "Last Login Date"
  lastLoginDate: DateTime
  "Member Graduation Year"
  graduationYear: Short
  "IsDisabled"
  isDisabled: Boolean!
  "Season Ids"
  seasonIds: [String]
  "Height"
  height: String
  "Weight"
  weight: Long
  "Participant Id"
  participantId: String
}

"A connection to a list of items."
type MemberConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [MemberEdge!]
  "A flattened list of the nodes."
  nodes: [Member]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type MemberEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: Member
}

type MessagingSettings {
  "The internal ID of the team."
  teamId: String
  "Whether or not SafeSport standards are being enforced for users on this team."
  safeSportEnforced: Boolean!
  "The date and time these messaging settings were created."
  createdAt: DateTime
  "The date and time these messaging settings were last updated at."
  updatedAt: DateTime
}

type Municipality {
  "The Id of the Municipality"
  id: ID!
  "The internal Id of the Municipality"
  internalId: String
  "The name of the Municipality"
  name: String
  "The list of school Ids associated with the Municipality"
  schoolIds: [String]
  "The abbreviation of the Municipality"
  abbreviation: String
  "The city of the Municipality"
  city: String
  "The ISO 3166-2 code representing the state or province of the Municipality"
  subdivisionIso: String
  "The ISO 3166-1 alpha-3 code representing the country of the Municipality"
  countryIso: String
  "The primary contact user Id of the Municipality"
  primaryContactUserId: String
  "The list of secondary contact user Ids of the Municipality"
  secondaryContactUserIds: [String]
  "The date and time the Municipality was created"
  createdAtUtc: DateTime!
  "The date and time the Municipality was last updated"
  updatedAtUtc: DateTime!
  "The date and time the Municipality was deleted. Null if not deleted"
  deletedAtUtc: DateTime
  "The user Id of the user who created the Municipality"
  createdByUserId: String
  "The user Id of the user who last updated the Municipality"
  updatedByUserId: String
  "Flag to indicate if the Municipality profile is hidden"
  isProfileHidden: Boolean!
  "Flag to indicate if the Municipality is hidden from search"
  isHiddenFromSearch: Boolean!
}

type Mutation {
  id: String
  "Creates a new ad campaign."
  createAdCampaign(createAdCampaignInput: CreateAdCampaignInput!): AdCampaign @cost(weight: "10")
  "Updates an existing ad campaign."
  updateAdCampaign(updateAdCampaignInput: UpdateAdCampaignInput!): AdCampaign @cost(weight: "10")
  "Deletes an ad campaign."
  deleteAdCampaign(deleteAdCampaignInput: DeleteAdCampaignInput!): AdCampaign @cost(weight: "10")
  "Create or update a team's recording default"
  upsertRecordingDefault(input: UpsertRecordingDefaultInput!): UpsertRecordingDefaultPayload @cost(weight: "10")
  "Create Licenses in Admin."
  createLicenses(input: AdminCreateLicensesInput!): AdminCreateLicensesPayload! @cost(weight: "10")
  "Search for a License in Admin."
  licenseSearch(input: AdminLicenseSearchInput!): AdminLicenseSearchPayload @cost(weight: "10")
  "Deregister a list of Licenses in Admin."
  deregisterLicenses(input: AdminDeregisterLicensesInput!): AdminDeregisterLicensesPayload @cost(weight: "10")
  "Delete a list of Licenses in Admin."
  deleteLicenses(input: AdminDeleteLicensesInput!): AdminDeleteLicensesPayload @cost(weight: "10")
  "Update a list of License dates in Admin."
  updateLicenseDates(input: AdminLicenseDatesInput!): AdminLicenseDatesPayload @cost(weight: "10")
  "Create a new License Note for a License in Admin."
  createLicenseNote(input: AdminCreateLicenseNoteInput!): AdminCreateLicenseNotePayload @cost(weight: "10")
  "Edit the Special String for a License."
  editLicenseSpecialString(input: AdminEditLicenseSpecialStringInput!): AdminEditLicenseSpecialStringPayload @cost(weight: "10")
  "Generate an Emergency Code for a License."
  createEmergencyCode(input: AdminCreateEmergencyCodeInput!): AdminCreateEmergencyCodePayload @cost(weight: "10")
  "Update a license's team ID."
  updateLicenseTeamId(input: AdminUpdateLicenseTeamIdInput!): AdminUpdateLicenseTeamIdPayload @cost(weight: "10")
  resubmitInferenceRun("Inference run id" inferenceRunId: String!): InferenceRun @cost(weight: "10")
  "Upsert organization settings for an organization."
  upsertOrganizationSettings(input: UpsertOrganizationSettingsInput!): School! @cost(weight: "10")
  "Method to refresh the account status of a payment platform account."
  refreshPaymentPlatformAccountStatus(organizationId: ID!): PaymentPlatformAccountStatus @cost(weight: "10")
  "Extend access for a team."
  extendAccessForTeam("Details about access to extend." input: ExtendAccessInput!): AccessExtensionOutput @cost(weight: "10")
  "Creates an internal signup."
  signUpNewTeam(input: InternalSignupInput!): SignUpNewTeamResponse @cost(weight: "10")
  "Creates an internal signup for add ons."
  signUpNewAddOns(input: InternalSignupAddOnsInput!): SignUpNewAddOnsResponse @cost(weight: "10")
  "Cancel an active trial."
  cancelTrial("Trial to be cancelled." input: CancelTrialInput!): Trial @cost(weight: "10")
  "Converts a trial into a fully-fledged subscription."
  convertTrial(input: ConvertTrialInput!): ConvertTrialResponse @cost(weight: "10")
  "Converts a play tools trial into a fully-fledged subscription."
  convertPlayToolsTrial(input: ConvertPlayToolsTrialInput!): ConvertPlayToolsTrialResponse @cost(weight: "10")
  "Cancel an active playtools trial."
  cancelPlayToolsTrial("Trial to be cancelled." input: CancelPlayToolsTrialInput!): PlayToolsTrial @cost(weight: "10")
  "Method to upsert a ticketing experiment."
  upsertTicketingExperiment(input: UpsertTicketingExperimentInput!): TicketingExperiment @cost(weight: "10")
  "Method to link any unlinked ticketing entities that have an existing user."
  linkUnlinkedTicketingEntitiesToUsers(input: LinkUnlinkedTicketingEntitiesToUserInput!): Boolean! @cost(weight: "10")
  "Method to create a new salesforce case for organizations opting-in to check payouts."
  createSalesforceCaseForCheckPayouts(salesforceCaseInput: CreateSalesforceCaseForCheckPayoutsInput!): Boolean! @cost(weight: "10")
  "Method to create a new seating chart."
  createSeatingChart(createSeatingChartInput: CreateSeatingChartInput!): SeatingChart @cost(weight: "10")
  "Shares ticketing entities by associating them with a new ticket group."
  shareTicketingEntities(shareTicketingEntitiesInput: ShareTicketingEntitiesInput!): ShareHistory! @cost(weight: "10")
  transferTicketingEntities(transferTicketingEntitiesInput: TransferTicketingEntitiesInput!): ShareHistory! @cost(weight: "10")
  "Soft deletes a ticketed event."
  softDeleteTicketedEventById(ticketedEventId: ID!): TicketedEvent @cost(weight: "10")
  "Creates or updates a ticketed event."
  createOrUpdateTicketedEventWithScheduleEntries(ticketedEventInput: CreateOrUpdateTicketedEventForScheduleEntriesInput!): TicketedEvent @cost(weight: "10")
  "Creates or updates ticketed events."
  createOrUpdateTicketedEventsWithScheduleEntries(ticketedEventInputs: [CreateOrUpdateTicketedEventForScheduleEntriesInput!]!): Boolean! @cost(weight: "10")
  "Set a list of events as completed."
  setEventsAsCompleted(ticketedEventIds: [ID!]!): [TicketedEvent]! @cost(weight: "10")
  "Method to resend an email for a Ticket Group. Can be resent to either the original email address or a newly provided one."
  resendTicketGroupEmail(resendTicketGroupEmailInput: ResendTicketGroupEmailInput!): Boolean! @cost(weight: "10")
  "Upsert the settings for a ticketing organization."
  upsertTicketingOrganizationSettings(input: UpsertTicketingOrganizationSettingsInput!): TicketingOrganizationSettings! @cost(weight: "10")
  "Method to create an organization admin request."
  createOrganizationAdminRequest(createOrganizationAdminRequestInput: CreateOrganizationAdminRequestInput!): OrganizationAdminRequest @cost(weight: "10")
  "Method to create a new renewal campaign."
  createPassConfigRenewalCampaign(createPassConfigRenewalCampaignInput: CreatePassConfigRenewalCampaignInput!): PassConfigRenewalCampaign @cost(weight: "10")
  addRenewersToCampaign(addPassConfigRenewersToCampaignInput: AddPassConfigRenewersToCampaignInput!): Boolean! @cost(weight: "10")
  removeRenewalCampaignChannels(passConfigId: ID!): Boolean! @cost(weight: "10")
  "Method to resend the pass config renewal campaign email for a renewer."
  resendPassConfigRenewalCampaignRenewerEmail(passConfigRenewerId: ID!): Boolean! @cost(weight: "10")
  "Creates a new ticket type."
  createTicketType(createTicketTypeInput: CreateTicketTypeInput!): TicketType @cost(weight: "10")
  "Creates new ticket types."
  createTicketTypes(createTicketTypeInputs: [CreateTicketTypeInput!]!): [TicketType!]! @cost(weight: "10")
  "Soft deletes a ticket type."
  softDeleteTicketTypeById(ticketTypeId: ID!): TicketType @cost(weight: "10")
  "Soft deletes a league pass config."
  softDeleteLeaguePassConfigById(leaguePassConfigId: ID!): LeaguePassConfig @cost(weight: "10")
  "Creates a league pass config."
  createLeaguePassConfig(leaguePassConfigInput: CreateLeaguePassConfigInput!): LeaguePassConfig @cost(weight: "10")
  "Updates a league pass config."
  updateLeaguePassConfig(leaguePassConfigInput: UpdateLeaguePassConfigInput!): LeaguePassConfig @cost(weight: "10")
  "Method to create a new form field."
  createFormField(createFormFieldInput: CreateFormFieldInput!): FormField @cost(weight: "10")
  "Updates an Auth0 connection"
  updateConnection(updateAuthZeroConnectionInput: UpdateAuthZeroConnectionInput!): AuthZeroConnection @cost(weight: "10")
  "Soft deletes a venue configuration."
  softDeleteVenueConfigurationById(venueConfigurationId: ID!): VenueConfiguration @cost(weight: "10")
  "Creates a venue configuration."
  createVenueConfiguration(input: CreateVenueConfigurationInput!): VenueConfiguration @cost(weight: "10")
  "Updates a venue configuration."
  updateVenueConfiguration(input: UpdateVenueConfigurationInput!): VenueConfiguration @cost(weight: "10")
  "Soft deletes a venue."
  softDeleteVenueById(venueId: ID!): Venue @cost(weight: "10")
  "Creates or updates a venue."
  upsertVenue(input: UpsertVenueInput!): Venue @cost(weight: "10")
  "Create onboarding template."
  createOnboardingTemplate(input: CreateOnboardingTemplateInput!): OnboardingTemplate! @cost(weight: "10")
  "Soft delete onboarding template by id."
  softDeleteOnboardingTemplateById(onboardingTemplateId: ID!): OnboardingTemplate! @cost(weight: "10")
  createOnboardingStepDefinitions(input: CreateStepDefinitionsInput!): [StepDefinition!]! @cost(weight: "10")
  softDeleteStepDefinitionById(stepDefinitionId: ID!): StepDefinition! @cost(weight: "10")
  "Delete a Stream Messaging Channel."
  deleteChannel(deleteChannelInput: DeleteChannelInput!): Boolean! @cost(weight: "10")
  "Create a new custom form"
  createForm("The form data" input: CreateCustomFormInput!): CustomForm! @cost(weight: "10")
}

type OnboardingStepTemplate {
  "The ID of the step definition associated with the onboarding step template."
  onboardingStepDefinitionId: ID!
  "The semantic version of the onboarding step template."
  version: String!
  "The requirement of the onboarding step template."
  requirement: String!
}

type OnboardingTemplate {
  "The GraphQL ID of the onboarding template."
  id: ID!
  "The context, or name, of the onboarding template."
  context: String!
  "The semantic version of the onboarding template."
  version: String!
  onboardingStepTemplates: [OnboardingStepTemplate!]!
  "The time the onboarding template was created."
  createdAt: DateTime!
  "The time the onboarding template was last updated."
  updatedAt: DateTime!
  "The time the onboarding template was deleted, if it was deleted."
  deletedAt: DateTime
}

"Opponent details for a schedule entry. Primarily used for fan and public facing pages."
type OpponentDetails {
  "The name of the opponent for the schedule entry."
  name: String
  "The GraphQL encoded teamId of the opponent for the schedule entry. Can be null for non-Hudl opponents."
  teamId: ID
  "The internal teamId of the opponent for the schedule entry."
  internalTeamId: String
  "The GraphQL encoded schoolId of the opponent for the schedule entry. Can be null for non-Hudl opponents."
  schoolId: ID
  "The internal schoolId of the opponent for the schedule entry."
  internalSchoolId: String
  "The primary color of the opponent for the schedule entry."
  primaryColor: String
  "The secondary color of the opponent for the schedule entry."
  secondaryColor: String
  "The abbreviation of the opponent for the schedule entry."
  abbreviation: String
  "The mascot name of the opponent for the schedule entry."
  mascot: String
  "The short name of the opponent for the schedule entry."
  shortName: String
  "The profile image uri of the opponent for the schedule entry."
  profileImageUri: String
  "The seasonId for the opponent for the schedule entry."
  seasonId: String
  "The season year for the opponent for the schedule entry."
  seasonYear: Short
}

type Organization {
  "The Id of organization"
  id: ID!
  "The internal Id of organization"
  internalId: String
  "Full name of organization"
  fullName: String
  "Abbreviation for the organization"
  abbreviation: String
  "Whether or not the organization is enabled."
  isEnabled: Boolean!
  "Address of the organization"
  address: Address
  "The market taxonomy information for the associated organization."
  marketTaxonomy: MarketTaxonomy!
  "Primary color for organization"
  primaryColor: String
  "Secondary color for organization"
  secondaryColor: String
  "AWS Region for the organization"
  awsRegion: String
  "Teams within the organization"
  teams("Filter teams to those with given account statuses. Defaults to returning all teams." accountStatuses: [AccountStatus!]): [Team] @cost(weight: "10")
}

"Representation of an organization admin request in hudl-ticketing."
type OrganizationAdminRequest {
  "The ID of the organization admin request."
  id: ID!
  "The organization id of the request"
  organizationId: ID!
  "The user id of the user requesting the organization admin."
  requesterUserId: String!
  "The requests for organization admins."
  requests: [RequestedOrganizationAdmin!]!
  "The date of the organization admin request."
  requestedAt: DateTime!
}

"A connection to a list of items."
type OrganizationAdminRequestConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [OrganizationAdminRequestEdge!]
  "A flattened list of the nodes."
  nodes: [OrganizationAdminRequest]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type OrganizationAdminRequestEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: OrganizationAdminRequest
}

"A summary of Organization information that is public."
type OrganizationHeader {
  "Organization name"
  organizationName: String
  "Organization classification type"
  orgClassificationId: Int!
  "GraphQL Encoded Organization Id"
  id: ID!
  "Organization abbreviation"
  orgAbbreviation: String
}

"Representation of an organization program."
type OrganizationProgram {
  "The Id of the Organization Program."
  id: ID!
  "The id of the organization reference."
  orgId: ID!
  "The current state of the program."
  state: ProgramState!
  "The type of the program."
  type: EmbeddedOrganizationProgramType
  "The title of the program."
  title: String
  "The description of the program."
  description: String
  "The start date of the program."
  startDate: DateTime
  "The timezone identifier for the program."
  timeZoneIdentifier: String
  "The end date of the program."
  endDate: DateTime
  "The visibility setting of the program."
  visibility: ProgramVisibility!
  "The fee responsibility setting of the program."
  feeResponsibility: FeeResponsibility!
  "When the program was created."
  createdAt: DateTime!
  "When the program was last updated."
  updatedAt: DateTime!
  "When the program was deleted, if applicable."
  deletedAt: DateTime
  "Who created the program."
  createdBy: ID!
  "Who last updated the program."
  updatedBy: ID!
  "Who deleted the program, if applicable."
  deletedBy: ID
  "List of form references associated with the program."
  formRefs: [FormRef!]!
  "Gets forms for a program with their complete custom form data."
  forms: [ProgramForm!]! @cost(weight: "10")
}

"Representation of an organization program type."
type OrganizationProgramType {
  "The Id of the Organization Program Type."
  id: ID!
  "The id of the organization reference."
  orgId: ID
  "The name of the program type."
  name: String!
  "The description of the program type."
  description: String
  "When the program type was created."
  createdAt: DateTime!
  "When the program type was last updated."
  updatedAt: DateTime!
  "When the program type was deleted, if applicable."
  deletedAt: DateTime
  "Who created the program type."
  createdBy: ID
  "Who last updated the program type."
  updatedBy: ID
  "Who deleted the program type, if applicable."
  deletedBy: ID
}

"Settings for an organization"
type OrganizationSettings {
  "Indicates if student data privacy is enabled for the organization"
  isStudentDataPrivacyOn: Boolean
  "Indicates if the organization profile is hidden"
  isOrgProfileHidden: Boolean
}

"Information about pagination in a connection."
type PageInfo {
  "Indicates whether more edges exist following the set defined by the clients arguments."
  hasNextPage: Boolean!
  "Indicates whether more edges exist prior the set defined by the clients arguments."
  hasPreviousPage: Boolean!
  "When paginating backwards, the cursor to continue."
  startCursor: Cursor
  "When paginating forwards, the cursor to continue."
  endCursor: Cursor
}

"Team representation for participating teams on a schedule entry."
type ParticipatingTeam {
  "The school's name."
  schoolName: String
  "The team's internal id."
  teamId: String
  "The team's name."
  teamName: String
  "The city the school is in."
  schoolCity: String
  "The state the school is in."
  schoolState: String
}

"Representation of a pass."
type Pass {
  "The GraphQL id of the pass."
  id: ID!
  "The identifier for the Pass Config this pass is for."
  passConfigId: ID!
  "The Hudl user id of the person associated with this pass, if one exists."
  userId: String
  "The first name of the person associated with this pass."
  firstName: String
  "The last name of the person associated with this pass."
  lastName: String
  "The email of the person associated with this pass."
  email: String
  "The QR code linking to this pass."
  qrCodeUrl: String @deprecated(reason: "This property will no longer return a value. Use the QRCodeData property instead.")
  "Date the pass was created."
  createdAt: DateTime!
  "Date the pass was updated."
  updatedAt: DateTime!
  "The reserved seats associated with the pass."
  reservedSeats: [ReservedSeat!]
  "Date the pass was deleted."
  deletedAt: DateTime
  "The source where the pass was purchased."
  source: String
  "Date the pass was shared."
  sharedAt: DateTime
  "The tickets created for this pass"
  tickets: [Ticket] @cost(weight: "10")
  "The QR Code data for this pass"
  qrCodeData: String @cost(weight: "10")
}

"Representation of a pass config."
type PassConfig {
  "The GraphQL id of the pass config."
  id: ID!
  "The name of the pass config."
  name: String
  "The description of the pass config."
  description: String
  "The organization associated with the pass config."
  organizationId: ID!
  "List of the team ids valid for this pass config."
  teamIds: [ID]
  "List of the ticketed event ids valid for this pass config."
  ticketedEventIds: [ID!]
  "The status of the pass config's TicketedEventIds list."
  ticketedEventIdsCalculationStatus: String
  "Price of the pass."
  priceInCents: Int!
  "The currency associated with the priceInCents of the pass config."
  currency: String
  "The status of the pass config."
  passConfigStatus: String
  "The visibility of the pass config."
  visibility: String
  "Number of passes available for purchase."
  quantityAvailable: Int
  "The start date the pass config is valid for. (inclusive)"
  startDate: DateTime
  "The end date the pass config is valid for. (inclusive)"
  endDate: DateTime
  "The date the pass config is no longer available for purchase. (inclusive)"
  purchaseExpirationDate: DateTime
  "The event filters used to determine which events are valid for a pass config."
  eventFilters: [PassConfigEventFilter!]
  "The IDs of the form fields associated with the pass config."
  formFieldIds: [ID!]
  "Date the ticketed event was created."
  createdAt: DateTime!
  "Date the ticketed event was updated."
  updatedAt: DateTime!
  "Date the ticketed event was deleted."
  deletedAt: DateTime
  "The fee strategy to apply to the pass config."
  feeStrategy: String
  "Events that are included in the pass config outside of the events applied via filters."
  includedTicketedEventIds: [ID!]
  "Events that are excluded from the pass config."
  excludedTicketedEventIds: [ID!]
  "The ID of the League Pass config associated with this pass config."
  leaguePassConfigId: ID
  "The game types that are excluded from the pass config."
  excludedGameTypes: [String!]
  "Selected teams for the pass config"
  teams: [TeamHeader] @cost(weight: "10")
  "The number of passes sold associated with a pass config"
  passCount: Int! @cost(weight: "10")
  "The ticketed events associated with a pass config"
  ticketedEvents: [TicketedEvent] @cost(weight: "10")
  "The form fields associated with the pass config"
  formFields: [FormField] @cost(weight: "10")
  "The renewal campaign associated with this pass config"
  renewalCampaign: PassConfigRenewalCampaign @cost(weight: "10")
  "The pricing summary associated with the pass config"
  passConfigPricingSummary: PassConfigPricingSummary @cost(weight: "10")
}

"A connection to a list of items."
type PassConfigConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [PassConfigEdge!]
  "A flattened list of the nodes."
  nodes: [PassConfig]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type PassConfigEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: PassConfig
}

"The event filter used to determine which events are valid for a pass config."
type PassConfigEventFilter {
  "The team id of the event filter"
  teamId: ID!
  "The venue configuration id of the event filter"
  venueConfigurationId: ID
  "The Seats.io partial season id associated with the events calculated by the event filter."
  seatsDotIoPartialSeasonId: String
  "List of category ids that should be enabled in context of the venue config."
  seatsDotIoEnabledCategoryIds: [String!]
  "The venue configuration associated with the pass config"
  venueConfiguration: VenueConfiguration @cost(weight: "10")
}

"Representation of a pass config pricing summary including information about the Hudl fees applied."
type PassConfigPricingSummary {
  "The ID of the pass config."
  passConfigId: ID!
  "The price of the pass config in cents"
  priceInCents: Int!
  "The Hudl Fee applied to the pass purchase in cents"
  hudlFeeInCents: Int!
  "The price of the pass config in cents including the Hudl Fee"
  priceInCentsWithHudlFee: Int!
  "Whether the Hudl fee should be displayed to the user."
  shouldShowFee: Boolean!
  "The currency associated with the priceInCents of the pass config."
  currency: String
}

"Representation of a Renewal Campaign for a Pass Config."
type PassConfigRenewalCampaign {
  "The ID of the Pass Config Renewal Campaign"
  id: ID!
  "The ID of the Pass Config associated with this campaign"
  passConfigId: ID!
  "The ID of the organization associated with this campaign"
  organizationId: ID!
  "The start date of the renewal campaign"
  renewalStartDate: DateTime!
  "The end date of the renewal campaign"
  renewalEndDate: DateTime!
  "The time zone associated with the renewal campaign"
  timeZoneIdentifier: String!
  "The visibility of the Pass Config after the renewal campaign ends"
  postRenewalVisibility: String!
  "The date and time when the campaign was created"
  createdAt: DateTime!
  "The date and time when the campaign was last updated"
  updatedAt: DateTime!
  "The date and time when the campaign was deleted"
  deletedAt: DateTime
  "The renewers associated with this renewal campaign"
  renewers: [PassConfigRenewer!] @cost(weight: "10")
  "The pass config associated with this renewal campaign"
  passConfig: PassConfig @cost(weight: "10")
}

"Representation of a Renewer that is a part of a Pass Config Renewal Campaign."
type PassConfigRenewer {
  "The ID of the Renewer record."
  id: ID!
  "The ID of the Pass Config Renewal Campaign this Renewer is associated with."
  renewalCampaignId: ID!
  "The first name of the Renewer."
  firstName: String!
  "The last name of the Renewer."
  lastName: String!
  "The email address of the Renewer."
  email: String!
  "The ID of the user associated with this Renewer."
  userId: String
  "The list of seat identifiers that the Renewer can select."
  selectableSeatIdentifiers: [String!]
  "The ID of the SeatsDotIo channel that is associated with this Renewer."
  seatsDotIoChannelId: String
  "The date and time when the renewer was created."
  createdAt: DateTime!
  "The date and time when the renewer was last updated."
  updatedAt: DateTime!
  "The date and time when the renewer was deleted."
  deletedAt: DateTime
  "The renewal campaign associated with this renewer"
  renewalCampaign: PassConfigRenewalCampaign @cost(weight: "10")
}

"A connection to a list of items."
type PassConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [PassEdge!]
  "A flattened list of the nodes."
  nodes: [Pass!]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type PassEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: Pass!
}

"The requirements for a payment platform account."
type PaymentPlatformAccountRequirements {
  "A list of requirements that are currently due for the payment platform account."
  currentlyDue: [String!]
  "A list of requirements that are past due for the payment platform account."
  pastDue: [String!]
}

"The status of an organization on the payment platform."
type PaymentPlatformAccountStatus {
  "Whether the payment platform account can create live charges."
  chargesEnabled: Boolean!
  "Whether the payment platform account can receive payouts."
  payoutsEnabled: Boolean!
  "Whether the payment platform account has submitted all required account creation details"
  detailsSubmitted: Boolean!
  "The requirements for the payment platform account."
  requirements: PaymentPlatformAccountRequirements
  "When the status was last updated."
  updatedAt: DateTime!
}

"The status of an organization on the payment platform."
type PaymentPlatformStatus {
  "The ID of the organization whose payment platform status is being queried."
  organizationId: String
  "Whether the organization needs to be onboarded to the payment platform."
  needsOnboarding: Boolean!
  "Whether the organization can log into the payment platform."
  canLogin: Boolean!
  "Whether the organization can update their account on the payment platform."
  canUpdateAccount: Boolean!
  "Whether the organization can create charges on the payment platform."
  chargesEnabled: Boolean!
  "Whether the organization can onboard to the payment platform."
  canOnboard: Boolean!
}

type PlayToolsTrial implements ITrial {
  "The start date of the trial's access."
  startDate: DateTime!
  "The end date of the trial's access."
  endDate: DateTime!
  "The trial record's status."
  trialStatus: Int!
  "Team associated with the trial"
  team: Team! @cost(weight: "10")
}

type Practice {
  "The name of the practice."
  name: String
  "The category Id of the practice."
  categoryId: String
  "The category type for the practice."
  categoryType: CategoryType!
}

"Representation of a program form with its custom form data."
type ProgramForm {
  "The display order of the form in the program."
  displayOrder: Int!
  "Whether the form is enabled in the program."
  isEnabled: Boolean!
  "The complete custom form data."
  form: CustomForm!
}

"A publish event in Hudl."
type PublishEvent {
  "The Id of the publish event."
  id: ID!
  "The internal Id of the publish event."
  internalId: String
  "Publish event name."
  publishEventName: String
  "Date and time the Publish event was created."
  createdAt: DateTime!
  "Date and time the Publish event was last updated."
  lastUpdatedAt: DateTime!
  "Date and time the publish event will start."
  startTime: DateTime!
  "Date and time the publish event will stop."
  stopTime: DateTime!
  "Primary Team Id of the publish event."
  teamId: String
  "GameId associated with the publish event."
  gameId: String
  "Publish event streams for Publish event."
  streams: [PublishEventStream]
  "Video Id associated with the publish event."
  videoId: String
  "Publish session Id for the Publish event."
  publishSessionId: String
  "Is the publish event deleted."
  isDeleted: Boolean!
  "Is the publish event a test publish event."
  isTest: Boolean!
  "The PublishEventType of the publish event."
  publishEventType: PublishEventType!
  "The Client Publish Id for the publish event."
  clientPublishId: String
  "The Restreams for the publish event."
  restreams: [Restream]
  "The Secondary Fixtures on the publish event."
  secondaryFixtures: [FixtureSummary]
  "The start time of the associated game or competition, tracked separately from the recording start\/stop times."
  eventTime: DateTime
  "The primary installation on the publish event."
  primaryInstallationId: String
}

"A publish event stream in Hudl."
type PublishEventStream {
  "The Installation Id for the publish event stream."
  installationId: String
  "The Media Stream Id of the publish event stream."
  mediaStreamId: String
  "The Camera Id of the publish event stream."
  cameraId: String
  "The Stream Type of the publish event stream."
  streamType: StreamType!
  "The status of the publish event stream."
  status: PublishEventStreamStatus!
  "The quality of the publish event stream."
  quality: MediaQuality @deprecated(reason: "Use QualityId instead.")
  "The qualityId of the publish event stream."
  qualityId: Int!
  "The os version of the installation."
  osVersion: String
  "If the scoreboard is included in the publish event stream."
  includeScoreboard: Boolean!
  "If RTMP is included in the publish event stream."
  includeRtmp: Boolean!
  "The playing surface Id for the publish event stream."
  playingSurfaceId: String
  "The internal microphone volume for the publish event stream."
  internalMicVolume: Float
  "The external microphone volume for the publish event stream."
  externalMicVolume: Float
}

"Search by fixtures"
type Query {
  "Fetch all ad campaigns."
  allAdCampaigns: [AdCampaign!]! @cost(weight: "10")
  "Fetch competitions"
  competitions(searchTermName: String sport: GSLCompetitionSport! "Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor): GSLCompetitionConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch competition periods"
  competitionPeriods(competitionId: ID!): [GSLCompetitionPeriod] @cost(weight: "10")
  "Fetch a specific fixture, e.g. \"Ridley College Tigers vs OHA Mavericks Tardiff U22\""
  fixture("Fixture ID, e.g. \"R1NMRml4dHVyZTY0ZmQwMzg1MTIzNDBiYzMyYjgzYjgwNw==\"" fixtureId: ID!): GSLFixture @cost(weight: "10")
  "Fetch fixtures"
  fixtures(competitionIds: [ID!] competitionPeriodIds: [ID!] startLocalDate: DateTime endLocalDate: DateTime hasMedia: Boolean "Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor): GSLFixtureConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Search by competitions"
  searchableCompetitions("String value to search competitions" searchTerm: String "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor "List of query filters to search competitions" query: [CompetitionSearchPredicateInput] "List of criteria to sort competitions" sortCriteria: [CompetitionSearchSortInput] "Include deleted competitions; by default, deleted competitions will NOT be returned" includeDeleted: Boolean): GSLSearchableCompetitionConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  searchableFixtures("String value to search fixtures" searchTerm: String "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor "List of query filters to search fixtures" query: [FixtureSearchPredicateInput] "List of criteria to sort fixtures" sortCriteria: [FixtureSearchSortInput] "Include deleted fixtures; by default, deleted fixtures will NOT be returned" includeDeleted: Boolean): GSLSearchableFixtureConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Search by Teams"
  searchableTeams("String value to search teams" searchTerm: String "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor "List of query filters to search teams" query: [TeamSearchPredicateInput] "List of criteria to sort teams" sortCriteria: [TeamSearchSortInput] "Include deleted teams; by default, deleted teams will NOT be returned" includeDeleted: Boolean): GSLSearchableTeamConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch publish event by ID"
  publishEvent("Publish event ID" publishEventId: String!): PublishEvent @cost(weight: "10")
  "Fetch Publish events by ID's"
  publishEvents("Publish event ID's" publishEventIds: [String!]!): [PublishEvent] @cost(weight: "10")
  "Fetch Publish events by IDs"
  publishEventsForVideos("Video IDs" videoIds: [String!]!): [PublishEvent] @cost(weight: "10")
  "Fetch available PublishEventStream info for the given inputs"
  availablePublishEventStreamInfo("Installation ID" installationId: String! "Team ID" teamId: String! "PublishEvent type" publishEventType: PublishEventType!): [AvailablePublishEventStreamInfo!]! @cost(weight: "10")
  "Fetch Installation by ID"
  installation("Installation ID" installationId: String!): Installation @cost(weight: "10")
  "Fetch Installations by InstallationIds"
  installationsByIds("InstallationIds" installationIds: [String!]!): [Installation] @cost(weight: "10")
  "Fetch Installations according to provided filters"
  installations(count: Int skip: Int "Fetch installations belonging to these schools" schoolIds: [String!]! "Fetch installations for these schedule synced teams" teamIds: [String!]! "Fetch installations configured for these sports" sports: [Sport!] "Perform a 'like' operation across the device ID, name, and school name properties on installations" searchString: String): [Installation] @cost(weight: "10")
  "Fetch Billing Links for entitled entity"
  subscriptionsForEntitledEntities("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor entitledEntityRefs: [EntitledEntityRefInput]): BillingSubscriptionConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch Billing Links for entitled entity"
  subscriptionsForEntitledEntity("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor entitledEntityType: EntitledEntityType! entitledEntityId: String): BillingSubscriptionConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch invoices for entities"
  invoicesForEntitledEntities("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor entitledEntityRefInputs: [EntitledEntityRefInput]): InvoiceConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch invoices for subscriptions"
  invoicesForSubscriptions("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor subscriptionIds: [String]): InvoiceConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch subscriptions for invoice"
  subscriptionsForInvoice("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor invoiceId: String): BillingSubscriptionConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch Billing Links for entitled entity"
  billingLinksForEntitledEntity("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor type: EntitledEntityType! id: String!): BillingLinkConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch Billing Links for entitled entities"
  billingLinksForEntitledEntities("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor entitledEntityRefs: [EntitledEntityRefInput]): BillingLinkConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Search for normalized colleges from College Library"
  searchableColleges(searchTermName: String "Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor "List of query filters to search the colleges by" query: [CollegeSearchPredicateInput] "Include deleted colleges; by default, deleted colleges will NOT be returned" includeDeleted: Boolean "List of criteria to sort the colleges" sortCriteria: [CollegeSearchSortInput]): CollegeSearchResultItemSearchConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Retrieve colleges from College Library"
  colleges("List of college IDs to retrieve" collegeIds: [ID!] "Include deleted colleges; by default, deleted colleges will NOT be returned" includeDeleted: Boolean): [CollegeSearchResultItem] @cost(weight: "10")
  "Lookup license by license ID."
  adminLicense("Only fetch leagues these teams have access to" licenseId: ID!): License @cost(weight: "10")
  "Lookup license by license IDs."
  adminLicenses("Only fetch this league" input: AdminGetLicensesInput!): AdminGetLicensesPayload @cost(weight: "10")
  "Lookup license by school ID."
  adminLicensesForSchool("Only fetch this league" schoolId: ID!): AdminGetLicensesPayload @cost(weight: "10")
  "Fetch leagues and details for the provided teamIds"
  exchangeLeagues("Only fetch leagues these teams have access to" teamIds: [String]): [ExchangeLeague] @cost(weight: "10")
  "Fetch league details and fixtures for the provided leagueId"
  exchangeLeague("Only fetch this league" leagueId: ID!): ExchangeLeague @cost(weight: "10")
  "Fetch selected media streams from the provided fixtureId"
  leagueFixtureStreams("Only fetch from this fixture" fixtureId: ID! "Filter by provided media stream Ids" fixtureStreamIds: [ID!]): [FixtureStream] @cost(weight: "10")
  "Get an inference run by id"
  inferenceRun("Inference run id" inferenceRunId: String!): InferenceRun @cost(weight: "10")
  "Get inference runs"
  inferenceRuns("Search parameters for inference runs" searchParams: GetInferenceRunsParamsInput!): [InferenceRun!] @cost(weight: "10")
  "Search for mediastreams within the MediaSearch."
  mediaSearch("Returns the first _n_ elements from the list." first: Int "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the elements in the list that come before the specified cursor." before: Cursor "List of query filters to search the mediasearch content by" query: [MediaSearchPredicateInput]): MediaSearchContentConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Retrieve an organization by an internal id."
  organization("Organization internal id." organizationId: String): Organization @cost(weight: "10")
  "Fetch the school data by Id"
  school("GraphQL schoolId" schoolId: ID "Internal schoolId" internalSchoolId: String): School @cost(weight: "10")
  "Fetch the schools data for a list of internal school Ids"
  schools("List of internal schoolIds" schoolIds: [String] "List of GraphQL schoolIds" graphQLSchoolIds: [ID!]): [School] @cost(weight: "10")
  "Fetch paginated schools by municipality Id."
  schoolsForMunicipality("Input used to fetch and paginate Schools." input: GetSchoolsForMunicipalityPaginatedInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): SchoolConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch schools by their corresponding teams on a Competition Period given a CompetitionPeriodId, with schools returned according to lexicographically paginated TeamIds."
  schoolsForCompetitionPeriod("Input used to fetch and paginate Schools." input: GetSchoolsForCompetitionPeriodByTeamIdsPaginatedInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): SchoolConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch schools by their corresponding teams on a Regional Alignment given a CompetitionPeriodId, with schools returned according to lexicographically paginated TeamIds."
  schoolsForRegionalAlignment("Input used to fetch and paginate Schools." input: GetSchoolsForRegionalAlignmentByTeamIdsPaginatedInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): SchoolConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch the schools data by given internal user Id"
  userOrganizations("Internal userId" internalUserId: String! "Include orgs and teams that are on hold and disabled, by default return ALL" includeDisabled: Boolean): [School] @cost(weight: "10")
  "Fetch the municipality data by Id"
  municipality("GraphQL municipalityId" municipalityId: ID "Internal municipalityId" internalMunicipalityId: String "If true, will include soft deleted municipalities" includeDeleted: Boolean! = false): Municipality @cost(weight: "10")
  "Fetch the Regional Alignment data by Id"
  regionalAlignment("GraphQL regionalAlignmentId" regionalAlignmentId: ID "Internal regionalAlignmentId" internalRegionalAlignmentId: String "If true, will include soft deleted regional alignments" includeDeleted: Boolean! = false): RegionalAlignment @cost(weight: "10")
  "Gets the payment platform status for an organization."
  paymentPlatformStatusForOrganization(organizationId: String!): PaymentPlatformStatus @cost(weight: "10")
  "Fetch schedule entry summaries by using filters. Summaries include only public information."
  scheduleEntryPublicSummaries("Input used to fetch and paginate schedule entry summaries" input: GetScheduleEntryPublicSummariesInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): ScheduleEntryPublicSummaryConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch a schedule entry summary by using a schedule entry ID."
  scheduleEntryPublicSummary("Input used to fetch a schedule entry summary" scheduleEntryId: ID!): ScheduleEntryPublicSummary @cost(weight: "10")
  "Fetch ticketable schedule entry summaries by using filters. These summaries do not have linked ticketed events."
  ticketableScheduleEntrySummaries("Input used to fetch and paginate ticketable schedule entry summaries" input: GetScheduleEntryPublicSummariesInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): ScheduleEntryPublicSummaryConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch schedule reference submission by organization id"
  scheduleReferenceSubmissionsByOrganizationId("Schedule Reference Submission input" input: GetScheduleReferenceSubmissionsByOrganizationIdInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): ScheduleReferenceSubmissionConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  trial("Trial Id." id: String!): Trial @cost(weight: "10")
  trialsForOrganization("Internal organization (school) id" organizationId: String! "A list of Trial Statuses to filter to" trialStatuses: [TrialStatus!]): [Trial] @cost(weight: "10")
  playToolsTrial("Internal Team Id." teamId: String! "A list of Trial Statuses to filter to" trialStatuses: [TrialStatus!] "Exclude PT Trials automatically created with a Hudl Trial" onlyStandaloneTrials: Boolean): [PlayToolsTrial] @cost(weight: "10")
  playToolsTrialsForOrganization("Internal organization (school) id" organizationId: String! "A list of Trial Statuses to filter to" trialStatuses: [TrialStatus!] "Exclude PT Trials automatically created with a Hudl Trial" onlyStandaloneTrials: Boolean): [PlayToolsTrial] @cost(weight: "10")
  "Fetch team headers using filters."
  teamHeadersForSchool("Input used to fetch team headers" input: GetTeamHeadersForSchoolInput!): [TeamHeader] @cost(weight: "10")
  "Fetch team headers based on a list of Ids. Maximum is 150"
  teamHeaders("Input used to fetch team headers" input: GetTeamHeadersByIdsInput!): [TeamHeader] @cost(weight: "10")
  "Fetch paginated team headers by competition period Id."
  teamHeadersForCompetitionPeriod("Input used to fetch and paginate team headers for a competition period" input: GetTeamHeadersForCompetitionPeriodPaginatedInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): TeamHeaderConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch the team header data by id"
  teamHeader("GraphQL teamId" teamId: ID "Internal teamId" internalTeamId: String): TeamHeader @cost(weight: "10")
  "Get team by id"
  team("Team id" id: String): Team @cost(weight: "10")
  "Get a list of my teams"
  myTeams("Show teams that have one of these membership roles" membershipRoles: [String] "Show teams that have all of these features" requiredFeatures: [String] "Include teams that are on hold and disabled, by default return ALL teams" includeDisabledTeams: Boolean): [Team] @cost(weight: "10")
  "Search for opponents that can have events scheduled against."
  scheduleTeamsSearch("The searching teams Id." searchingTeamId: String! "The query string." queryString: String!): [TeamSearchResult] @cost(weight: "10")
  "Checks if an organization has ticketing enabled via the config value in hudl-ticketing"
  organizationHasTicketingEnabled(organizationId: String!): Boolean! @cost(weight: "10")
  "Checks if an organization is a free ticketing only organization via the config value in hudl-ticketing"
  organizationIsFreeTicketingOnly(organizationId: String!): Boolean! @cost(weight: "10")
  "Returns whether or not an organization is using the post-transaction revenue sharing model via the config value in hudl-ticketing"
  organizationIsUsingRevenueSharing(organizationId: String!): Boolean! @cost(weight: "10")
  "Checks if paid ticketing is enabled globally via the config value in hudl-ticketing"
  paidTicketingEnabled: Boolean! @cost(weight: "10")
  "Fetch pass by GraphQL ID."
  passById("A pass's GraphQL ID" passId: ID!): Pass @cost(weight: "10")
  "Fetch pass configs for a given Organization ID."
  passConfigsByOrganizationId("The Organization ID associated with the pass configs." input: GetPassConfigsByOrganizationIdInput "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): PassConfigConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch pass config by GraphQL ID."
  passConfigById("A pass config's GraphQL ID" passConfigId: ID!): PassConfig @cost(weight: "10")
  "Fetch pass config renewer by GraphQL ID."
  passConfigRenewerById("A pass config renewer's GraphQL ID" renewerId: ID!): PassConfigRenewer @cost(weight: "10")
  "Fetch passes matching search parameters"
  searchPassesByTextValue("Input to retrieve passes based on search params, output is paginated" input: SearchPassesByTextValueInput "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): PassConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch ticket groups matching search parameters"
  searchTicketGroupsByTextValue("Input to retrieve ticket groups based on search params, output is paginated" input: SearchTicketGroupsByTextValueInput "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): TicketGroupConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch tickets matching search parameters"
  searchTicketsByTextValue("Input to retrieve tickets based on search params, output is paginated" input: SearchTicketsByTextValueInput "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): TicketConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Query whether an organization has accepted the current terms of service."
  organizationHasAcceptedTermsOfService(organizationId: String): Boolean! @cost(weight: "10")
  "Fetch ticketed event by GraphQL ID."
  ticketedEventById("A ticketed event's GraphQL ID" ticketedEventId: ID!): TicketedEvent @cost(weight: "10")
  "Fetch all ticketed events in a date range."
  ticketedEventsInDateRange("The date range associated with the ticketed events" input: GetAllTicketedEventsInDateRangeInput "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): TicketedEventConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch ticket group by ticket group reference (order number)."
  ticketGroupByTicketGroupReference("A ticket group's reference (order number)" ticketGroupReference: String): TicketGroup @cost(weight: "10")
  "Fetch a ticketing experiment by name."
  ticketingExperimentByName("A ticketing experiment's name" experimentName: String): TicketingExperiment @cost(weight: "10")
  "Checks if ticketing passes are enabled globally via the config value in hudl-ticketing"
  ticketingPassesEnabled: Boolean! @cost(weight: "10")
  "Fetch ticket by GraphQL ID."
  ticketById("A ticket's GraphQL ID" ticketId: ID!): Ticket @cost(weight: "10")
  "Fetch organization admin requests by organization id."
  organizationAdminRequestsByOrganizationId("Input to fetch organization admin requests by organization id." inputValue: GetOrganizationAdminRequestsByOrganizationIdInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): OrganizationAdminRequestConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch league pass config by GraphQL ID."
  leaguePassConfigById("A league pass config's GraphQL ID" leaguePassConfigId: ID!): LeaguePassConfig @cost(weight: "10")
  "Fetch league pass configs for a given league."
  leaguePassConfigsByLeague("The league associated with the league pass configs." input: GetLeaguePassConfigsByLeagueInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): LeaguePassConfigConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch discoverable league pass configs for a given league."
  discoverableLeaguePassConfigsByLeague("The league associated with the league pass configs." input: GetDiscoverableLeaguePassConfigsByLeagueInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): LeaguePassConfigConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch ticket types for a given Organization ID."
  ticketTypesByOrganizationId("The Organization ID associated with the ticket types." organizationId: ID!): [TicketType] @cost(weight: "10")
  "Fetch form fields by organization ID."
  formFieldsByOrganizationId("Input to fetch form fields by organization ID." inputValue: GetFormFieldsByOrganizationIdInput! "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): FormFieldConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Gets the user from the AuthContext. The param userId will be ignored"
  user("User Id." userId: String!): User @cost(weight: "10") @deprecated(reason: "Use me query instead")
  "Gets the user from the AuthContext"
  me: User @cost(weight: "10")
  "Get the User for an Email, null if nonexistent"
  userForEmail("Email" email: String!): User @cost(weight: "10")
  "Get a list of Users by their Emails, empty if nonexistent"
  usersForEmails("Emails" emails: [String!]!): [User]! @cost(weight: "10")
  "Get a User by internal id, null if nonexistent"
  userById("User internal Id." userId: String): User @cost(weight: "10")
  "Get Users by their internal IDs, with a limit of 200."
  usersByIds("A list of internal user IDs. Maximum of 200 allowed." ids: [String!]!): [User!]! @cost(weight: "10")
  connectionByName("Connection name" connectionName: String!): AuthZeroConnection @cost(weight: "10")
  "Creates a time-limited ticket for SSO connection setup. connectionName is needed to create a new connection, connectionId is needed to update an existing connection."
  ssoSelfServeTicketUrl("Connection name" connectionName: String "Connection ID" connectionId: String): String @cost(weight: "10")
  "Get a user's consent status."
  userConsentStatus("The user's email address." userEmail: String!): UserConsentStatus @cost(weight: "10")
  "Get the audit logs for a ConsentStatus."
  userConsentStatusLogs("The id of the ConsentStatus object." userConsentStatusId: String!): [UserConsentStatusLog]! @cost(weight: "10")
  "Fetch venues by GraphQL IDs."
  venuesByIds("A list of venue GraphQL IDs" input: GetVenuesByIdsInput): [Venue] @cost(weight: "10")
  "Fetch venue by GraphQL ID."
  venueById("A venue's GraphQL ID" venueId: ID!): Venue @cost(weight: "10")
  "Fetch venues for a given Organization ID."
  venuesByOrganizationId("The Organization ID associated with the venues." input: GetVenuesByOrganizationIdInput "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): VenueConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch venue configurations for a given venue ID."
  venueConfigurationsByVenueId("The venue ID associated with the venue configurations." input: GetVenueConfigurationsByVenueIdInput "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): VenueConfigurationConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Fetch a venue configuration by GraphQL ID."
  venueConfigurationById("A venue configuration's GraphQL ID" venueConfigurationId: ID!): VenueConfiguration @cost(weight: "10")
  "Fetch onboarding template by context. Returns the latest version."
  latestOnboardingTemplateByContext("The context, or name, of an onboarding template" context: String!): OnboardingTemplate! @cost(weight: "10")
  "Get filtered custom forms based on various criteria"
  filteredForms("Filter criteria for forms" input: GetFilteredFormsInput): [CustomForm!]! @cost(weight: "10")
  "Fetch guardian relationships for a team."
  guardianRelationshipsForTeam("Input to guardian relationships based on team ID" input: GetGuardianRelationshipsForTeamInput): [GuardianRelationship]! @cost(weight: "10")
  "Get an organization program by ID"
  organizationProgramById("The ID of the organization program" programId: ID! "Whether to include deleted programs" includeDeleted: Boolean! = false): OrganizationProgram @cost(weight: "10")
}

"Default angles for a team by installation"
type RecordingDefault {
  "The team id of the RecordingDefault"
  teamId: String!
  "The recording type of the RecordingDefault"
  recordingType: PublishEventType!
  "The primary installation id associated with the RecordingDefault"
  primaryInstallationId: String!
  "The default Focus streams by installation."
  streams: [RecordingDefaultStream!]!
}

"The default streams for a given installation"
type RecordingDefaultStream {
  "The installation id associated with the stream."
  installationId: String!
  "The camera id associated with the stream."
  cameraId: String
  "The type for the stream."
  streamType: StreamType!
}

type RecurrenceMetadata {
  "Id of the RecurringEvent."
  recurringEventId: ID
  "Date this event represents in the recurring series."
  recurrenceDate: DateTime
  "Day of the week this event recurs on."
  recurrenceByDay: Int
  "Day of the month this event recurs on."
  recurrenceByMonthDay: Int
  "Month of the year this event recurs on."
  recurrenceByMonth: Int
  "Indicates if this event is still attached to the recurring event."
  attachedToRecurringEvent: Boolean
  "Frequency for the series this event belongs to."
  frequencyType: FrequencyType!
  "Interval for the series this event belongs to."
  interval: Int!
  "Days of the week for the series this event belongs to."
  byDay: [Int!]!
  "Days of the month for the series this event belongs to."
  byMonthDay: [Int!]!
  "Days of the week for the series this event belongs to."
  byMonth: [Int!]!
  "Date the series begins."
  startDate: DateTime!
  "Date the series ends."
  until: DateTime!
  "If the associated series has detached events."
  seriesHasDetachedEvents: Boolean!
}

"Representation of tickets redeemed for a redemption type."
type RedeemedCountForRedemptionType {
  "The GraphQL Id of the redemption type."
  id: ID!
  "The type of redemption."
  lineItemType: String
  "The number of tickets redeemed by the redemption type."
  redeemedCount: Int!
}

type RegionalAlignment {
  "The graphQL Id of the Regional Alignment"
  id: ID!
  "The internal Id of the Regional Alignment"
  internalId: String
  "The name of the Regional Alignment"
  name: String
  "The abbreviation of the Regional Alignment"
  abbreviation: String
  "The ISO 3166-2 code representing the state or province of the Regional Alignment"
  subdivisionIso: String
  "The ISO 3166-1 alpha-3 code representing the country of the Regional Alignment"
  countryIso: String
  "The primary contact user Id of the Regional Alignment"
  primaryContactUserId: String
  "The list of secondary contact user Ids of the Regional Alignment"
  secondaryContactUserIds: [String]
  "The date and time the Regional Alignment was created"
  createdAtUtc: DateTime!
  "The date and time the Regional Alignment was last updated"
  updatedAtUtc: DateTime!
  "The date and time the Regional Alignment was deleted. Null if not deleted"
  deletedAtUtc: DateTime
  "The user Id of the user who created the Regional Alignment"
  createdByUserId: String
  "The user Id of the user who last updated the Regional Alignment"
  updatedByUserId: String
  "The list of team Ids associated with the Regional Alignment"
  teamIds: [String]
}

"Representation of a requested organization admin in hudl-ticketing."
type RequestedOrganizationAdmin {
  "The first name of the requested user to be added as an organization admin."
  firstName: String!
  "The last name of the requested user to be added as an organization admin."
  lastName: String!
  "The email of the requested user to be added as an organization admin."
  email: String!
}

"Representation of a Reserved Seat."
type ReservedSeat {
  "The full identifier of the reserved seat"
  seatIdentifier: String
  "The section of the reserved seat"
  section: String
  "The row of the reserved seat"
  row: String
  "The identifier of the reserved seat itself"
  seat: String
  "The identifier of the table associated with the reservation"
  table: String
  "The identifier of the general admission area associated with the reservation"
  generalAdmissionArea: String
}

type Restream {
  "The GraphQL Restream Id."
  id: ID!
  "The internal Id of the restream."
  internalId: String
  "The hudl-livestreaming restream Id of the restream."
  restreamId: String
  "The authentication Id of the restream."
  authenticationId: String
  "The destination platform of the restream."
  platformId: Int!
  "The last known status of the restream."
  lastKnownStatus: RestreamStatus!
  "The Media stream Id of the restream."
  mediaStreamId: String
  "The stream type of the restream."
  streamType: StreamType!
  "The camera Id of the restream."
  cameraId: String
  "The installation Id of the restream."
  installationId: String
}

"An output file with a bucket for an inference run."
type S3FileOutput {
  "Content Server for the S3 file"
  contentServerId: String
  "Key for the S3 file"
  key: String!
  "Bucket for the S3 file"
  bucket: String!
  "Output type"
  outputType: OutputType!
}

type ScheduleEntry {
  "The Id of the schedule entry."
  id: ID!
  "The internal Id of the schedule entry."
  internalId: String
  "The encoded Id of the schedule entry."
  eventId: ID!
  "The name of the opponent of this schedule entry."
  opponentName: String
  "The opponent team Id of this schedule entry."
  opponentTeamId: String
  "The schedule entry is scheduled as a home schedule entry."
  isHome: Boolean
  "The time the schedule entry is scheduled to be played."
  time: DateTime
  "The GameType of the schedule entry."
  gameType: GameType!
  "The score of the schedule entry."
  score1: Short
  "The score of the schedule entry."
  score2: Short
  "The outcome of the schedule entry for the home team."
  isWin: Boolean
  "Practices associated with this schedule entry."
  practices: [Practice]
  "The seasonId this schedule entry is associated with."
  seasonId: String
  "The Id of the team who created this schedule entry."
  teamId: String
  "The time in UTC the schedule entry is scheduled to be played."
  timeUtc: DateTime
  "The name of the host of this schedule entry."
  hostName: String
  "The team ID of the host of this schedule entry."
  hostTeamId: String
  "The event name of this schedule entry."
  eventName: String
  "The description of this schedule entry."
  eventDescription: String
  "The list of teams participating in the schedule entry."
  participatingTeams: [ParticipatingTeam]
  "The time the schedule entry is scheduled to end."
  endTime: DateTime
  "Data about the tournament this schedule entry belongs to (if applicable)."
  tournamentMetadata: TournamentMetadata
  "Data about the recurring event series this schedule entry belongs to (if applicable)."
  recurrenceMetadata: RecurrenceMetadata
}

"A summary for a schedule entry. Primarily used for fan and public facing pages."
type ScheduleEntryPublicSummary {
  "The Id of the schedule entry summary."
  id: ID!
  "The internal Id of the schedule entry summary."
  internalId: String
  "The GraphQL encoded Id of the schedule entry that this summary describes."
  scheduleEntryId: ID!
  "The numeric enum value of the location of the schedule entry."
  scheduleEntryLocation: Int!
  "The numeric enum value of the outcome of the schedule entry"
  scheduleEntryOutcome: Int!
  "The details of the opponent for the schedule entry."
  opponentDetails: OpponentDetails
  "The GraphQL encoded schoolId of the owning team for the schedule entry."
  schoolId: ID!
  "The GraphQL encoded teamId of the owning team for the schedule entry."
  teamId: ID!
  "The internal teamId of the owning team for the schedule entry."
  internalTeamId: String!
  "One of the team scores for a schedule entry."
  score1: Short
  "One of the team scores for a schedule entry."
  score2: Short
  "The GraphQL encoded seasonId for the schedule entry."
  seasonId: ID!
  "The internal seasonId for the schedule entry."
  internalSeasonId: String
  "The sport of owning team of the schedule entry."
  sport: Sport!
  "The numeric enum value of the sport of owning team of the schedule entry."
  sportId: Int!
  "The numeric enum value of the game type of the schedule entry."
  gameType: Int!
  "The numeric enum value of the gender of the owning team of the schedule entry."
  genderId: Int!
  "The UTC time that the schedule entry will start."
  timeUtc: DateTime!
  "The UTC time that the schedule entry is projected to end."
  projectedEndTime: DateTime
  "Data about the tournament this schedule entry belongs to (if applicable)."
  tournamentMetadata: TournamentMetadata
}

"A connection to a list of items."
type ScheduleEntryPublicSummaryConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [ScheduleEntryPublicSummaryEdge!]
  "A flattened list of the nodes."
  nodes: [ScheduleEntryPublicSummary]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type ScheduleEntryPublicSummaryEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: ScheduleEntryPublicSummary
}

type ScheduleEntrySuggestion {
  "The Id of the schedule entry suggestion."
  id: ID!
  "The internal id of the schedule entry suggestion."
  internalId: String
  "The internal team id of the schedule entry suggestion."
  teamId: String
  "The opponent team id of the schedule entry suggestion."
  opponentTeamId: String
  "If the schedule entry suggestion is a home suggestion. If the value is null, the schedule entry suggestion is for a neutral site."
  isHome: Boolean
  "The Date and Time in UTC of the schedule entry suggestion."
  timeUtc: DateTime
  "The schedule entry type of the schedule entry suggestion."
  scheduleEntryType: ScheduleEntryType!
  "The suggested match Id for the schedule entry suggestion, typically Game Id."
  scheduleEntrySuggestedMatchId: String!
}

type ScheduleReferenceFile {
  "URI of the file"
  uri: String!
  "The name of the file"
  name: String!
  "The team id associated with the file"
  associatedTeamIds: [ID!]
}

type ScheduleReferenceSubmission {
  "The Id for the schedule reference submission"
  id: ID!
  "This Id of the organization that the submission belongs to"
  organizationId: ID!
  "Notes for the schedule reference submission"
  notes: String
  "The file references for the submission"
  files: [ScheduleReferenceFile!]!
  "Date the submission was uploaded"
  uploadDate: DateTime!
}

"A connection to a list of items."
type ScheduleReferenceSubmissionConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [ScheduleReferenceSubmissionEdge!]
  "A flattened list of the nodes."
  nodes: [ScheduleReferenceSubmission]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type ScheduleReferenceSubmissionEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: ScheduleReferenceSubmission
}

type ScheduleSettings {
  "The id of the default livestream account to use when creating new schedule entries."
  defaultLivestreamAuthenticationId: String
}

type School {
  "The Id of the school"
  id: ID!
  "The internal Id of the school"
  internalId: String
  "Full name of school"
  fullName: String
  "First 20 characters of school name"
  shortName: String
  "Abbreviation of school name"
  abbreviation: String
  "If school is enabled or not"
  isEnabled: Boolean!
  "City where school is located"
  city: String
  "State where school is located"
  state: String
  "Zip code where school is located"
  zipCode: String
  "Country where school is located"
  country: String
  "The two-character ISO 3166-1 alpha-2 country code corresponding to this organization's country"
  countryIsoAlpha2: String
  "The three-character ISO 3166-1 alpha-3 country code corresponding to this organization's country"
  countryIsoAlpha3: String
  "School's primary color"
  primaryColor: String
  "School's secondary color"
  secondaryColor: String
  "School's first address line"
  addressLine1: String
  "School's second address line"
  addressLine2: String
  "If school is active or not"
  isActive: Boolean!
  "The profile image uri of the organization"
  profileImageUri: String
  "The banner image uri of the organization"
  bannerImageUri: String
  "The mascot of the organization"
  mascot: String
  "The time zone of the organization"
  timeZone: String
  "The settings of the organization"
  organizationSettings: OrganizationSettings
  "Allows league name to be seen and edited from edit profile page in hudl-profiles"
  leagueName: String
  "Maps to how an organization is classified"
  orgClassificationId: Int!
  "The teams for a school."
  teams("Include orgs and teams that are on hold and disabled, by default return ALL" includeDisabled: Boolean): [Team] @cost(weight: "10")
  "A boolean representing if the current user can edit the current school. This will always be false for unregistered users."
  canUserEditSchool: Boolean! @cost(weight: "10")
  "A boolean representing if the current user is an admin for an org."
  isAdmin: Boolean! @cost(weight: "10")
  "The default ticketing settings for an organization"
  defaultTicketingSettings: DefaultTicketingSettings! @cost(weight: "10")
  "Programs for an organization."
  organizationPrograms("The state of the programs to fetch. Optional" state: ProgramState "Whether to include deleted programs." includeDeleted: Boolean): [OrganizationProgram]! @cost(weight: "10")
  "Retrieve program types for an organization."
  organizationProgramTypes: [OrganizationProgramType!]! @cost(weight: "10")
  "Program for an organization by program Id."
  organizationProgram("Id for organization program." organizationProgramId: String! "Whether to include deleted program." includeDeleted: Boolean): OrganizationProgram @cost(weight: "10")
  "The ticketing payee information for an organization"
  ticketingPayeeInformation: TicketingPayeeInformation @cost(weight: "10")
  "The organization's payment platform account status."
  paymentPlatformAccountStatus: PaymentPlatformAccountStatus @cost(weight: "10")
}

"A connection to a list of items."
type SchoolConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [SchoolEdge!]
  "A flattened list of the nodes."
  nodes: [School]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type SchoolEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: School
}

type Season {
  "The GraphQL id of the season."
  seasonId: ID!
  "The internal id of the season."
  internalId: String
  "Season value maps to internal season id"
  value: String
  "Season description {SeasonYear} - {SeasonYear + 1}"
  description: String
  "Flag to verify if max preps was imported"
  isMaxPrepsImported: Boolean
  "The year that the season was started."
  year: Int!
  "The internal team Id of the team who owns this season"
  internalTeamId: String
  "The schedule entries associated with this season."
  scheduleEntries: [ScheduleEntry] @cost(weight: "10")
  scheduleEntrySuggestions: [ScheduleEntrySuggestion] @cost(weight: "10")
  roster: [Member] @cost(weight: "10")
  seasonRecord: SeasonRecord @cost(weight: "10")
}

type SeasonRecord {
  "The InternalId for the season"
  seasonId: String
  "Number of wins for the season"
  wins: Int!
  "Number of draws for the season"
  draws: Int!
  "Number of losses for the season"
  losses: Int!
  "Date of most recent modification"
  dateModified: DateTime!
}

"Representation of a Seating Chart."
type SeatingChart {
  "Internal unique identifier used to reference the seating chart. Consumers should use the internal Hudl ID over the external SeatingChartId to minimize direct interaction with provider-specific details"
  id: ID!
  "External ID used to reference the seating chart."
  seatingChartId: String
  "Provider of the referenced seating chart"
  seatingChartProvider: String
  "The workspace in which the seating chart is stored"
  workspace: String
  "The parent season ID for the seating chart in Seats.io. All events and partial seasons will be children of this season"
  seatsDotIoParentSeasonId: String
  "The event ID for the static display seating chart in Seats.io. This event will be used for displaying the seating chart preview in the UI, and will never have objects booked on it"
  seatsDotIoStaticDisplayEventId: String
  "Categories for a seats.io seating chart"
  seatsDotIoCategories: [SeatsDotIoCategory] @cost(weight: "10")
  "Reserved Seats for a seats.io seating chart"
  reservedSeats: [ReservedSeat] @cost(weight: "10")
}

"Category for a seats.io seating chart."
type SeatsDotIoCategory {
  "Identifier for the category"
  key: String
  "Label for the category."
  label: String
  "Category Color"
  color: String
  "Whether or not the category indicates wheelchair accessibility"
  accessible: Boolean
}

"Representation of a share history."
type ShareHistory {
  "The GraphQL id of the share history."
  id: ID!
  "The unique identifier for the original ticket group."
  originalTicketGroupId: ID!
  "The unique identifier for the new ticket group."
  newTicketGroupId: ID!
  "The list of entities shared"
  sharedEntities: [SharedEntity!]!
  "Date the share history was created."
  createdAt: DateTime!
  "Date the share history was updated."
  updatedAt: DateTime!
  "Date the share history was deleted."
  deletedAt: DateTime
}

"Representation of a shared ticketing entity."
type SharedEntity {
  "The ID of the shared entity."
  entityId: ID!
  "The type of the shared entity."
  entityType: String!
}

"Successful response for signUpNewAddOns mutation."
type SignUpNewAddOns {
  "Team ID for the new internal add ons signup."
  teamId: String
  "Organization ID for the new internal add ons signup."
  organizationId: String
  "Subscription Numbers for resulting Zuora Subscriptions"
  subscriptionNumbers: [String]
  "Ids for resulting Zuora Invoices"
  invoiceIds: [String]
  "Subscription details."
  subscriptionSignupDetails: [SubscriptionSignupDetailsOutput]
  "Invoice details for the signup."
  invoiceSignupDetails: [InvoiceSignupDetailsOutput]
}

"Successful response for signUpNewTeam mutation."
type SignUpNewTeam {
  "Team ID for the new internal signup."
  teamId: String
  "User ID for the new internal signup."
  userId: String
  "Organization ID for the new internal signup."
  organizationId: String
  "Subscription details for the base subscription."
  baseSubscriptionSignupDetails: SubscriptionSignupDetailsOutput
  "Subscription details for the add-on subscriptions."
  addOnSubscriptionsSignupDetails: [SubscriptionSignupDetailsOutput]
  "Ids for resulting Zuora Invoices"
  invoiceIds: [String]
  "Invoice details for the signup."
  invoiceSignupDetails: [InvoiceSignupDetailsOutput]
  "Team created for the new internal signup"
  team: Team! @cost(weight: "10")
  "User created for the new internal signup"
  user: User! @cost(weight: "10")
  "Organization created for the new internal signup"
  organization: Organization! @cost(weight: "10")
}

type SportOutput {
  name: String
  "The type of the sport"
  type: Sport!
  "The sport level of the mens team"
  mensLevel: SportLevelType!
  "The sport level of the womens team"
  womensLevel: SportLevelType!
  "The athletic conference for the mens team"
  mensConference: String
  "The athletic conference for the womens team"
  womensConference: String
  "The Mens coaches of the sport"
  mensCoaches: [CoachOutput]
  "The Womens coaches of the sport"
  womensCoaches: [CoachOutput]
  "Indicates if men's scholarships are available for the sport"
  scholarshipsMen: Boolean!
  "Indicates if women's scholarships are available for the sport"
  scholarshipsWomen: Boolean!
}

type StatusCodeDetails {
  "The Status Code for the status code details."
  statusCode: StatusCode!
  "The Status Code Type for the status code details."
  type: StatusCodeType!
  "The description of the status code details."
  description: String
  "The expected value of the status code details."
  expectedValue: String
  "The help text for the status code details."
  helpText: String
  "The help link for the status code details."
  helpLink: String
}

type StepDefinition {
  "The GraphQL ID of the step definition."
  id: ID!
  "The name of the step definition."
  name: String!
  "The description of the step definition. Not intended to be displayed to the user in any case, meant for internal notes to describe the purpose of the onboarding step."
  description: String!
  "The list of event actions to be associated with the step definition."
  eventActions: [String!]
  "The time the step definition was created."
  createdAt: DateTime!
  "The time the step definition was last updated."
  updatedAt: DateTime!
  "The time the step definition was deleted, if it was deleted."
  deletedAt: DateTime
}

type Subscription {
  id: String
}

"Subscription details for the create internal signup add ons mutation."
type SubscriptionSignupDetailsOutput {
  "The Universal Subscription Product requested for signup."
  requestedUniversalSubscriptionProduct: String
  "The resulting subscription number for signup."
  resultingSubscriptionNumber: String
  "The resulting subscription number for signup."
  resultingHardwareOrderIds: [String]
  "The resulting subscription id for signup."
  resultingSubscriptionId: String
}

type Team {
  "The User's roles on the team"
  role: Role!
  "Team groups"
  groups(includeFamilyMembers: Boolean "Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): GroupConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "One Group"
  group("Group Id" groupId: String): Group @cost(weight: "10")
  "Invite Code"
  inviteCode: InviteCode @cost(weight: "10")
  "Team members"
  members("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor "Group id to filter members" groupId: String "Only Disable members" disabledOnly: Boolean "Roles to include" roles: [String]): MemberConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Suggested Team members to remove"
  membersToRemove("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): MemberConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "One member, use member id or internal member id"
  member("Member id" memberId: String "Internal Member id" memberInternalId: String): Member @cost(weight: "10")
  "Team positions"
  positions: [String] @cost(weight: "10")
  "The graphql Id of the team."
  id: ID!
  "The hudl Id of the team."
  internalId: String
  "The year of the current season."
  currentSeasonYear: Short
  "The name of the team."
  name: String
  "The sport that the team plays."
  sport: Sport!
  "The Id of the sport that the team plays."
  sportId: Int!
  "The gender of the team."
  gender: Gender!
  "The Id of the gender of the team."
  genderId: Int!
  "The team level."
  level: TeamLevel!
  "The Id of the team level of the team."
  levelId: Int!
  "The team logo URL."
  logoUrl: String
  "The team primary color."
  primaryColor: String
  "The team secondary color."
  secondaryColor: String
  "Whether the team is a Hudl test team."
  isTestTeam: Boolean!
  "Team Members Additional Fields"
  membersAdditionalFields: [String]
  "Cell Carriers"
  cellCarriers: [Carrier]
  "Whether the team is on hold"
  isOnHold: Boolean!
  "Whether highlights are public, as determined by the team's privacy settings"
  areHighlightsPublic: Boolean!
  "Whether athletes can hear audio, as determined by the team's privacy settings"
  canAthletesHearAudio: Boolean!
  "The status of the team"
  teamStatus: AccountStatus!
  "Organization the team belongs to."
  organization: Organization
  "Installations that this team is schedule synced on."
  installations: [Installation] @cost(weight: "10")
  "Get Destination Authentications for Team by School."
  destinationAuthentications: [DestinationAuthentication] @cost(weight: "10")
  "Seasons for the team."
  seasons: [Season] @cost(weight: "10")
  "One Season for a team"
  season("GraphQL Season Id" seasonId: ID "Internal Season Id. If you provide a GraphQL Season Id, the GraphqL Id will be used by default." seasonInternalId: String): Season @cost(weight: "10")
  "Current Season for a team"
  currentSeason: Season @cost(weight: "10")
  "MaxPreps team info for the Hudl team."
  maxPrepsTeam: MaxPrepsTeam @cost(weight: "10")
  "Settings used for creating and updating schedule entries for a team"
  scheduleSettings: ScheduleSettings @cost(weight: "10")
  "Team Feature Toggles."
  featureToggles("If set, returns requested toggles, else returns all enabled toggles" toggleIds: [Long!]): [FeatureToggle] @cost(weight: "10")
  "Team Product Access."
  productAccess("If set, returns requested enabled product access level for the provided product, else returns empty list" product: String!): [String] @cost(weight: "10")
  "Team messaging settings."
  messagingSettings: MessagingSettings @cost(weight: "10")
  "Is the user a coach or admin for the team."
  isCoachOrAdmin: Boolean!
  "Guardian Relationships for the Hudl team."
  guardianRelationships: [GuardianRelationship] @cost(weight: "10")
  "Livestream scheduling access for the team"
  livestreamSchedulingPermission: LivestreamSchedulingPermission @cost(weight: "10")
  "BillingLinks for the Hudl team."
  billingLinks("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): BillingLinkConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Subscriptions for the Hudl team."
  subscriptions("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): BillingSubscriptionConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "Invoices for the Hudl team."
  invoices("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: Cursor "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: Cursor): InvoiceConnection @listSize(assumedSize: 0, slicingArguments: [ "first", "last" ], slicingArgumentDefaultValue: 10, sizedFields: [ "edges", "nodes" ], requireOneSlicingArgument: false) @cost(weight: "10")
  "The team's group storage limit in milliseconds."
  storageLimitMs: Long @cost(weight: "10")
}

"A summary of team information that is public."
type TeamHeader {
  "Is the user a coach or admin for the team."
  isCoachOrAdmin: Boolean!
  "Background images for team."
  backgroundImages: [BackgroundImage] @cost(weight: "10")
  "Positions"
  positions: [String] @cost(weight: "10")
  "GraphQL Encoded Team Id"
  id: ID!
  "Internal teamId"
  internalId: String
  "Team name"
  name: String
  "Team logo"
  logo: String
  organizationName: String @deprecated(reason: "Use OrganizationHeader.OrganizationName instead")
  "Team additional Fields"
  additionalFields: [String]
  "Current season year"
  currentSeasonYear: Int
  "Team Level"
  teamLevel: TeamLevel!
  "The primary color of the team."
  primaryColor: String
  "The secondary color of the team."
  secondaryColor: String
  "The sport of the team."
  sport: Sport!
  "The gender of the team."
  gender: Gender!
  "The summary of the team's organization."
  organizationHeader: OrganizationHeader
  "Indicates whether the team's profile is hidden."
  isTeamProfileHidden: Boolean!
  "Indicates whether the team is a test team."
  isTestTeam: Boolean!
}

"A connection to a list of items."
type TeamHeaderConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [TeamHeaderEdge!]
  "A flattened list of the nodes."
  nodes: [TeamHeader]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type TeamHeaderEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: TeamHeader
}

type TeamSearchResult {
  "The score of the team search result."
  score: Int!
  "The internal team id of the team search result."
  teamId: String
  "The team name of the team search result."
  teamName: String
  "If the team of the team search result is enabled."
  isTeamEnabled: Boolean!
  "The internal school Id of the team search result."
  schoolId: Long!
  "The school name of the team search result."
  schoolName: String
  "The shool city of the team search result."
  schoolCity: String
  "The school state of the team search result."
  schoolState: String
  "If the school of the team search result is enabled."
  isSchoolEnabled: Boolean!
  "If the team of the team search result has exchanges."
  hasExchanges: Boolean!
  "The sport Id of the team search result."
  sportId: Long!
  "The level of the team search result.s"
  level: String
  "The gender of the team search result."
  gender: String
}

type TeamTicketType {
  teamId: ID!
  ticketTypeIds: [ID!]!
}

"Representation of a ticket."
type Ticket {
  "The GraphQL id of the ticket."
  id: ID!
  "The unique identifier for the Ticketed Event this ticket is for."
  ticketedEventId: ID!
  "The Hudl user id of the person associated with this ticket, if one exists."
  userId: String
  "The first name of the person associated with this ticket."
  firstName: String
  "The last name of the person associated with this ticket."
  lastName: String
  "The email of the person associated with this ticket."
  email: String
  "The timestamp when this ticket was redeemed. Will be null if the ticket has not yet been redeemed."
  redeemedAt: DateTime
  "Whether or not the ticket is refundable. Taken from the related Ticketed Event."
  refundable: Boolean!
  "The status of the ticket."
  ticketStatus: String
  "The unique identifier for the Ticket Type associated with this ticket."
  ticketTypeId: ID
  "The url of the QR code associated with this ticket."
  qrCodeUrl: String @deprecated(reason: "This property will no longer return a value. Use the QRCodeData property instead.")
  "The unique identifier for the Pass associated with this ticket."
  passId: ID
  "The reserved seat associated with this ticket."
  reservedSeat: ReservedSeat
  "The payment type used to purchase this ticket."
  paymentType: String
  "Date the ticket was created."
  createdAt: DateTime!
  "Date the ticket was updated."
  updatedAt: DateTime!
  "Date the ticket was deleted."
  deletedAt: DateTime
  "Date the ticket was shared."
  sharedAt: DateTime
  "The source where the ticket was purchased."
  source: String
  "The QR Code data for this ticket"
  qrCodeData: String @cost(weight: "10")
}

"A connection to a list of items."
type TicketConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [TicketEdge!]
  "A flattened list of the nodes."
  nodes: [Ticket]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [Ticket!]
}

"Representation of a ticket count for a payment type."
type TicketCountByPaymentType {
  "The payment type used to purchase the tickets."
  paymentType: String!
  "The number of tickets sold for the payment type."
  ticketCount: Int!
}

"Representation of a ticket count for a ticket type."
type TicketCountForTicketType {
  "The GraphQL Id of the ticket type."
  ticketTypeId: ID
  "The number of tickets sold for the ticket type."
  ticketCount: Int!
  ticketCountByPaymentType: [TicketCountByPaymentType!]
}

"An edge in a connection."
type TicketEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: Ticket
}

"Representation of a ticket group."
type TicketGroup {
  "The hydrated tickets associated with this ticket group"
  tickets: [Ticket] @cost(weight: "10")
  "The hydrated passes associated with this ticket group"
  passes: [Pass] @cost(weight: "10")
  "The GraphQL id of the ticket group."
  id: ID!
  "The more readable but not necessarily unique reference\/identifier for this ticket group."
  ticketGroupReference: String
  "The Hudl user id of the person associated with this ticket group, if one exists"
  userId: String
  "The first name of the person associated with this ticket group"
  firstName: String
  "The last name of the person associated with this ticket group"
  lastName: String
  "The email of the person associated with this ticket group."
  email: String
  "The total cost of the ticket group in cents."
  orderTotalInCents: Int
  "The currency associated with the OrderTotalInCents of this ticket group."
  currency: String
  "The item categories associated with the ticket group."
  itemCategories: [String!]
  "The timestamp when this ticket group was created."
  createdAt: DateTime!
  "The timestamp when this ticket group was last updated."
  updatedAt: DateTime!
  "The timestamp when this ticket group was deleted. Will be null if the ticket is not deleted."
  deletedAt: DateTime
  "The source where the ticket group was purchased."
  source: String
  "The payment type used for this ticket group."
  paymentType: String
  "The responses to the form fields associated with this ticket group."
  formFieldResponses: [FormFieldResponse]
}

"A connection to a list of items."
type TicketGroupConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [TicketGroupEdge!]
  "A flattened list of the nodes."
  nodes: [TicketGroup]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [TicketGroup!]
}

"An edge in a connection."
type TicketGroupEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: TicketGroup
}

"Representation of a ticket type."
type TicketType {
  "The GraphQL id of the ticket type."
  id: ID!
  "The name of the ticket type."
  name: String
  "The price in cents of the ticket type."
  priceInCents: Int!
  "The quantity of the ticket type."
  quantity: Int
  "The organization associated with the ticket type."
  organizationId: String
  "The currency associated with the priceInCents of the ticket type."
  currency: String
  "Date the ticket type was created."
  createdAt: DateTime!
  "Date the ticket type was updated."
  updatedAt: DateTime!
  "Date the ticket type was deleted."
  deletedAt: DateTime
}

"Representation of a ticket type referenced by a ticketed event."
type TicketTypeReference {
  "The GraphQL id of the referenced ticket type."
  ticketTypeId: ID
  "The price override to the referenced ticket type."
  priceOverride: Int
  "The quantity override to the referenced ticket type."
  quantityOverride: Int
  "The seating chart category IDs for the referenced ticket type."
  seatingChartCategoryIds: [String]
}

"Representation of a ticketable event"
type TicketedEvent {
  "The hydrated ticket types associated with this ticketed event"
  ticketTypes: [TicketType] @cost(weight: "10")
  "The GraphQL id of the ticketed event."
  id: ID!
  "The name of the ticketed event."
  name: String
  "The description of the ticketed event."
  description: String
  "The organization associated with the ticketed event."
  organizationId: ID!
  "List of the team ids of teams participating in the ticketed event."
  participatingTeamIds: [ID]
  "The date\/time the ticketed event takes place."
  date: DateTime!
  "The time zone identifier for when the ticketed event takes place. Must be a valid IANA time zone identifier."
  timeZoneIdentifier: String
  "The sport being played at the ticketed event."
  sport: String
  "The gender of the teams participating in the ticketed event."
  gender: String
  "List of event entries linked to the ticketed event."
  linkedEntries: [LinkedEntry]
  "The ID of the venue the ticketed event is being held at."
  venueId: String @deprecated(reason: "Use VenueConfigurationId instead.")
  "The ID of the venue configuration the ticketed event is being held at."
  venueConfigurationId: ID
  "The status of the ticketed event."
  eventStatus: String
  "The visibility of the ticketed event."
  visibility: String
  "Is the ticketed event refundable."
  refundable: Boolean
  "Date the ticketed event was created."
  createdAt: DateTime!
  "Date the ticketed event was updated."
  updatedAt: DateTime!
  "Date the ticketed event was deleted."
  deletedAt: DateTime
  "Referenced ticket types for the event"
  ticketTypeReferences: [TicketTypeReference]
  "The fee strategy applied to the ticketed event."
  feeStrategy: String
  "The ID of the seating chart event associated with this ticketed event."
  seatingChartEventId: String
  "The IDs of the form fields associated with the ticketed event."
  formFieldIds: [ID!]
  "The number of tickets sold associated with a ticketed event"
  ticketCount: Int! @cost(weight: "10")
  "Analytics for the ticketed event"
  ticketedEventAnalytics: TicketedEventAnalytics @cost(weight: "10")
  "Attendance for the ticketed event"
  ticketedEventAttendance: TicketedEventAttendance @cost(weight: "10")
  "The form fields associated with the ticketed event"
  formFields: [FormField] @cost(weight: "10")
}

"Representation of ticketed event analytics."
type TicketedEventAnalytics {
  "The total number of tickets sold for the ticketed event, excluding complimentary tickets."
  totalTicketCount: Int!
  "The total number of complimentary tickets assigned for the ticketed event."
  totalComplimentaryTicketCount: Int!
  "The number of complimentary tickets assigned by ticket type."
  complimentaryTicketCountForTicketType: [TicketCountForTicketType]
  "The number of tickets sold by ticket type."
  ticketCountForTicketType: [TicketCountForTicketType]
  "The number of tickets sold by payment type."
  ticketCountByPaymentType: [TicketCountByPaymentType!]
}

"Representation of ticketed event attendance."
type TicketedEventAttendance {
  "The total number of tickets redeeemd for the ticketed event."
  totalRedeemedCount: Int!
  "The number of tickets redeemed by redemption type for the tickted event"
  redeemedCountForRedemptionType: [RedeemedCountForRedemptionType]
}

"A connection to a list of items."
type TicketedEventConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [TicketedEventEdge!]
  "A flattened list of the nodes."
  nodes: [TicketedEvent]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
}

"An edge in a connection."
type TicketedEventEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: TicketedEvent
}

"Representation of an experiment conducted in hudl-ticketing."
type TicketingExperiment {
  "The ID of the experiment."
  id: ID!
  "The name of the experiment."
  experimentName: String
  "The organization ids associated with the experiment."
  organizationIds: [String]
}

"Ticketing settings for an organization"
type TicketingOrganizationSettings {
  "The unique identifier for the organization settings"
  id: ID!
  "The unique identifier for the organization settings"
  organizationId: ID!
  "The default payout method chosen by the organization"
  payoutMethod: String
  "The default ticket types associated with teams belonging to the organization"
  defaultTeamTicketTypes: [TeamTicketType!]
  "The default fee strategy for ticketing entities"
  defaultFeeStrategy: String
}

"Payee information"
type TicketingPayeeInformation {
  "The payable status of the payee"
  payableStatus: String
  "The email of the payee"
  email: String
  "The payment method of the payee"
  paymentMethod: String
}

type TournamentMetadata {
  "Tournament ID from hudl-tournaments."
  tournamentId: ID
  "The OpponentDetailsDto for the Home Team."
  homeTeam: OpponentDetails
  "The OpponentDetailsDto for the Away Team."
  awayTeam: OpponentDetails
  "The Name of the Home Tournament Team"
  homeTeamName: String!
  "The Name of the Away Tournament Team"
  awayTeamName: String!
}

type Trial implements ITrial {
  "GraphQL id of the trial."
  id: ID!
  "Internal id of the trial. (Not an encoded GraphQL id.)"
  internalId: String!
  "The starting date of the trial's access."
  startDate: DateTime!
  "The end date of the trial's access."
  endDate: DateTime!
  "The trial record's status."
  trialStatus: Int!
  "Team associated with the trial"
  team: Team! @cost(weight: "10")
  "Initial users added to the trialing team as part of signup. Loaded via values in InternalInitialUserIds."
  initialUsers: [User] @cost(weight: "10")
}

type UnknownConversionError {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

type UnknownSignupError {
  "The error message."
  message: String
  "The error code."
  code: Int!
}

"Payload after create or update a base recording default for a team"
type UpsertRecordingDefaultPayload {
  clientMutationId: String
  "The recording default for a team"
  recordingDefault: RecordingDefault
}

type Url {
  "The web address of the URL"
  address: String
  "The type of website the address points to"
  urlCodeType: UrlCodeType!
}

"Representation of a Hudl user."
type User {
  "The Hudl ID of the user."
  id: String!
  "The Auth0 ID of the user."
  auth0Id: String
  "The firstname of the user."
  firstName: String
  "The lastname of the user."
  lastName: String
  "The e-mail address of the user."
  emailAddress: String
  "The username."
  username: String
  "The phone number of the user."
  phoneNumber: String
  "The user's mobile carrier."
  carrier: Int
  "The URL of a thumbnail of the user's profile picture."
  lowResolutionImageUrl: String
  "The URL of a high-resolution version of the user's profile picture."
  highResolutionImageUrl: String
  "Indication of whether the user prefers being addressed as 'Coach' or not."
  useCoachPrefix: Boolean!
  "Indication of whether the user is an administrator or coach on any (one, more or all) of the teams that he\/she is affiliated with."
  isCoachOrAdmin: Boolean!
  "Admin Feature Privileges"
  adminPrivileges: [Int!] @cost(weight: "10")
}

"Representation of a Hudl user consent status."
type UserConsentStatus {
  "The consent status object id."
  userConsentStatusId: String!
  "The consent subject's email address."
  subjectEmail: String!
  "The type of consent being provided."
  type: ConsentStatusType!
  "The consent provider's email address."
  providerEmail: String!
  "Whether the consent provider has been verified as an adult."
  providerVerified: Boolean!
  "The state of the consent gathering workflow."
  state: ConsentStatusState!
  "The time consent status was last updated."
  dateUpdated: DateTime
  "The time consent status was created."
  dateCreated: DateTime!
}

"Representation of a Hudl user consent status log."
type UserConsentStatusLog {
  "The consent status object id."
  userConsentStatusId: String!
  "The consent status field that was updated."
  updatedField: String!
  "The consent status field's new value."
  newValue: String!
  "The date that the consent status field was modified."
  dateModified: DateTime!
  "The IP address of the entity that modified the consent status field."
  ipAddress: String!
}

"Representation of a Venue."
type Venue {
  "The GraphQL ID of the venue."
  id: ID!
  "The internalID of the venue."
  internalId: String
  "List of Organizations IDs associated with this venue"
  organizationIds: [ID]
  "Name of the venue."
  name: String
  "Location of the venue."
  location: Location
  "Setting of the venue."
  setting: String
  "IDs for the associated Venue Configurations"
  venueConfigurationIds: [ID]
  "Date the venue was created."
  createdAt: DateTime!
  "Date the venue was updated."
  updatedAt: DateTime!
  "Date the venue was deleted."
  deletedAt: DateTime
  "A list of venue configurations associated with a venue. Because venue configurations will never reach the max page size set, we can return all of them and not worry about pagination."
  venueConfigurations: [VenueConfiguration] @cost(weight: "10")
}

"Representation of a Venue Configuration."
type VenueConfiguration {
  "The unique identifier for this venue configuration."
  id: ID!
  "List of Organizations IDs associated with this venue configuration"
  organizationIds: [ID]
  "Name of the venue configuration."
  name: String
  "Description of the venue configuration."
  description: String
  "Unique identifier representing the venue the venue configuration is associated with."
  venueId: ID!
  "Unique identifier representing a seating chart for the venue configuration."
  seatingChartId: ID
  "Date the venue configuration was created."
  createdAt: DateTime!
  "Date the venue configuration was updated."
  updatedAt: DateTime!
  "Date the venue configuration was deleted."
  deletedAt: DateTime
  "The seating chart associated with a venue configuration"
  seatingChart: SeatingChart @cost(weight: "10")
}

"A connection to a list of items."
type VenueConfigurationConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [VenueConfigurationEdge!]
  "A flattened list of the nodes."
  nodes: [VenueConfiguration]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [VenueConfiguration!]
}

"An edge in a connection."
type VenueConfigurationEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: VenueConfiguration
}

"A connection to a list of items."
type VenueConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [VenueEdge!]
  "A flattened list of the nodes."
  nodes: [Venue]
  "Identifies the total count of items in the connection."
  totalCount: Int! @cost(weight: "10")
  items: [Venue!]
}

"An edge in a connection."
type VenueEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: Venue
}

type VideoActivity {
  "Video Watched Today"
  videoWatchedTodayInSeconds: Long!
  "Video Watched Yesterday"
  videoWatchedYesterdayInSeconds: Long!
  "Video Watched This Week"
  videoWatchedThisWeekInSeconds: Long!
  "Video watched this week, formatted and with an empty indicator"
  formattedVideoWatchedThisWeek: String
}

"Output for convert trial mutation."
union ConvertTrialResponse = ConvertTrial | ErrorCreatingZuoraData | ErrorRollingBackZuoraData | ErrorUpdatingHudlData | UnknownConversionError

"Represents the Hudl entity entitled to access through the linked billing record."
union EntitledEntity = Team

"An input associated with an inference run."
union InferenceInput = InferenceVideoReferenceInput | JsonFileInput | InferenceS3FileWithTypeInput

"An output associated with an inference run."
union InferenceOutput = ChunkStreamOutput | S3FileOutput

"Represents a searchable resource in the mediasearch domain."
union MediaSearchContent = MediaStreamItem

"Output for create internal signup add ons mutation."
union SignUpNewAddOnsResponse = ErrorCreatingZuoraData | SignUpNewAddOns | ErrorCreatingHardwareOrder | UnknownSignupError

"Output for create internal signup mutation."
union SignUpNewTeamResponse = UnknownSignupError | SignUpNewTeam | ErrorCreatingOrganization | ErrorCreatingTeam | ErrorCreatingUser

input AccessProvisioningInput {
  "The type of access being provisioned (e.g., ZuoraBilled, Trial, InternalTestTeam)"
  type: String
}

"Input to add renewers to a pass config renewal campaign."
input AddPassConfigRenewersToCampaignInput {
  "The ID of the Renewal Campaign to add renewers to"
  renewalCampaignId: ID!
  "The list of renewers to add to this campaign"
  renewers: [PassConfigRenewerInput!]!
}

input AdminCreateEmergencyCodeInput {
  "The client mutation identifier."
  clientMutationId: String
  "The License ID."
  licenseId: ID!
}

input AdminCreateLicenseNoteInput {
  "The client mutation identifier."
  clientMutationId: String
  "The License ID."
  licenseId: ID!
  "The new note text."
  noteText: String!
}

input AdminCreateLicensesInput {
  "The client mutation identifier."
  clientMutationId: String
  "The School ID."
  schoolId: String
  "The max allowed product version."
  sportscodeVersion: String!
  "The subscription start date."
  subscriptionStartDate: String!
  "The subscription end date."
  subscriptionEndDate: String!
  "An initial Note for each License."
  note: String
  "The number of Elite licenses to be created."
  eliteLicensesCount: Int
  "The number of Elite Review licenses to be created."
  eliteReviewLicensesCount: Int
  "The number of Pro licenses to be created."
  proLicensesCount: Int
  "The number of Pro Review licenses to be created."
  proReviewLicensesCount: Int
  "The number of Player licenses to be created."
  playerLicensesCount: Int
  "The number of Gamebreaker+ licenses to be created."
  gamebreakerPlusLicensesCount: Int
  "The number of Studiocode licenses to be created."
  studiocodeLicensesCount: Int
  "The number of Wimu SPro licenses to be created."
  wimuSproLicensesCount: Int
  "The number of Wimu SVivo licenses to be created."
  wimuSvivoLicensesCount: Int
  "The number of GSL API tokens to be created."
  gslApiLicensesCount: Int
}

input AdminDeleteLicensesInput {
  "The client mutation identifier."
  clientMutationId: String
  "The Licenses to be deleted."
  licenseIds: [ID!]!
}

input AdminDeregisterLicensesInput {
  "The client mutation identifier."
  clientMutationId: String
  "The Licenses to be deregistered."
  licenseIds: [ID!]!
}

input AdminEditLicenseSpecialStringInput {
  "The client mutation identifier."
  clientMutationId: String
  "The License ID."
  licenseId: ID!
  "The new note text."
  specialString: String!
}

input AdminGetLicensesInput {
  "The client mutation identifier."
  clientMutationId: String
  "The Licenses to be fetched."
  licenseIds: [ID!]!
}

input AdminLicenseDatesInput {
  "The client mutation identifier."
  clientMutationId: String
  "The License IDs to be modified."
  licenseIds: [ID!]!
  "The new subscription start date."
  newSubscriptionStartDate: String
  "The new subscription end date"
  newSubscriptionEndDate: String
  "The note to be added"
  note: String
}

input AdminLicenseSearchInput {
  "The client mutation identifier."
  clientMutationId: String
  "The term used to search for Licenses."
  searchTerm: String
}

input AdminUpdateLicenseTeamIdInput {
  "The client mutation identifier."
  clientMutationId: String
  "The License ID."
  licenseId: ID!
  "The license's new team ID."
  teamId: String
}

input AuthorizationContextDtoInput {
  userId: String
  teams: [TeamAuthorizationContextDtoInput]
  isHudlAdminRequest: Boolean!
  isSystemAdmin: Boolean!
  backdooredUserId: String
  claims: [ClaimDtoInput]
}

input CancelPlayToolsTrialInput {
  "The Id of the team whose trial is being cancelled."
  teamId: String!
  "The reason why the trial is being cancelled."
  cancellationReason: String!
  "Any additional details why the trial is being cancelled."
  cancellationExplanation: String
}

input CancelTrialInput {
  "The graphql id of the trial to cancel."
  id: ID!
  "The reason why the trial is being cancelled."
  cancellationReason: String!
  "Any additional details why the trial is being cancelled."
  cancellationExplanation: String
}

input ClaimDtoInput {
  resources: [ResourceNameDtoInput]
  operation: String
}

"Predicate used in college search."
input CollegeSearchPredicateInput {
  "The field that this predicate applies to."
  field: CollegeSearchField!
  "Search operator used to evaluate this predicate."
  operator: CollegeSearchOperator!
  "Search values to be searched. OR will be applied between each item if more then one is provided."
  values: [String]
}

"Sort criteria used in college search."
input CollegeSearchSortInput {
  "The field that this criteria applies to."
  sortField: CollegeSearchSortField!
  "Sort direction used to evaluate this criteria."
  sortDirection: CollegeSearchSortDirection!
}

"Predicate used in search."
input CompetitionSearchPredicateInput {
  "The field that this predicate applies to."
  field: GSLCompetitionSearchField!
  "Search operator used to evaluate this predicate."
  operator: GSLSearchOperator!
  "Search values to be searched. OR will be applied between each item if more then one is provided."
  values: [String]
}

"Sort used in search."
input CompetitionSearchSortInput {
  "The field that this criteria applies to."
  sortField: GSLCompetitionSearchField
  "Sort direction used to evaluate this criteria."
  sortDirection: GSLSearchSortDirection!
}

"Representation of a play tools trial conversion input"
input ConvertPlayToolsTrialInput {
  "Id of the team being converted."
  teamId: String!
  "Date that the subscription will start."
  subscriptionStartDate: DateTime!
  "Promo code for the subscription."
  promoCode: String
  "Whether or not invoices should be immediately generated for this signup."
  shouldGenerateInvoices: Boolean
  "Whether or not the subscription auto-renews after its term."
  autoRenew: Boolean
}

"Representation of a conversion input"
input ConvertTrialInput {
  "Id of the trial being converted."
  trialId: String
  "Id of existing Hudl user who is the billing contact"
  existingBillingContactUserId: String
  "The billing contact for this organization."
  billingContact: ConvertTrialUserInput
  "Base subscription for this conversion."
  baseSubscription: ConvertTrialSubscriptionInput
  "A list of addon subscriptions for this conversion."
  addOnSubscriptions: [ConvertTrialSubscriptionInput]
  "The term start date for the subscriptions."
  termStartDate: DateTime
  "Whether or not invoices should be immediately generated for this signup."
  shouldGenerateInvoices: Boolean
  "Whether or not the subscriptions auto-renew after their term."
  autoRenew: Boolean
}

"Representation of an conversion subscription input"
input ConvertTrialSubscriptionInput {
  "Universal Subscription Product for the subscription"
  universalSubscriptionProduct: String
  "Promo Code for the subscription"
  promoCode: String
  "Whether this subscription should be invoiced separarely from any other subscriptions."
  invoiceSeparately: Boolean!
}

input ConvertTrialUserInput {
  "First Name of the user"
  firstName: String
  "Last Name of the user"
  lastName: String
  "The phone number for the user"
  phoneNumber: String
  "The email for the user"
  email: String!
}

"Input to create a new ad campaign."
input CreateAdCampaignInput {
  "The name of the ad campaign."
  name: String!
  "The cachekey for the ad campaign."
  cacheKey: String!
  "The ad targeting key\/value associated with the ad campaign."
  targetingKeyValue: String!
  "The ad campaign type."
  campaignType: String!
  "Indicates whether the ad campaign is enabled or not"
  isEnabled: Boolean!
}

"Input for creating a new custom form"
input CreateCustomFormInput {
  "The owner entity ID"
  ownerId: ID!
  "The type of owner"
  ownerType: OwnerType!
  "SurveyJS form definition JSON"
  formJson: String!
}

"Input to create a form field."
input CreateFormFieldInput {
  "The unique identifier for the Organization that the Form Field belongs to"
  organizationId: ID!
  "The label for the Form Field"
  label: String!
  "The help text for the Form Field"
  helpText: String
  "The type of the Form Field"
  fieldType: String!
  "If the Form Field is a required field"
  isRequired: Boolean!
}

"Representation of an input to create a league pass config."
input CreateLeaguePassConfigInput {
  "The name of the league pass config."
  name: String!
  "The description of the league pass config."
  description: String
  "The ID of the league associated with the league pass config."
  leagueEntityId: ID!
  "The type of the league associated with the league pass config."
  leagueEntityType: String!
  "A list of team filters that are associated with this league pass config."
  teamFilters: [LeaguePassConfigTeamFilterInput!]!
  "The fee strategy to apply to the league pass config."
  feeStrategy: String
  "The visibility of the league pass config"
  visibility: String!
  "The price of the passes."
  priceInCents: Int!
  "The status of the league pass config"
  leaguePassConfigStatus: String!
  "The start date the league pass config is valid for. (inclusive)"
  startDate: DateTime!
  "The end date the league pass config is valid for. (inclusive)"
  endDate: DateTime!
  "The game types that are excluded from the league pass config."
  excludedGameTypes: [String!]
}

"Input to create an onboarding template."
input CreateOnboardingTemplateInput {
  "The context, or name, of an onboarding template."
  context: String!
  "The semantic version of the onboarding template."
  version: String!
  "The list of onboarding step templates associated with the onboarding template."
  onboardingStepTemplates: [OnboardingStepTemplateInput!]!
}

"Input to create or update a ticketed event."
input CreateOrUpdateTicketedEventForScheduleEntriesInput {
  "The GraphQL ID for the ticketed event being upserted."
  ticketedEventId: ID
  "The name of the ticketed event."
  name: String!
  "The description of the ticketed event."
  description: String
  "The organization associated with the ticketed event."
  organizationId: ID!
  "List of the team ids of teams participating in the ticketed event."
  participatingTeamIds: [ID!]
  "The date\/time the ticketed event takes place."
  date: DateTime!
  "The time zone identifier for when the ticketed event takes place. Must be a valid IANA time zone identifier."
  timeZoneIdentifier: String!
  "The sport being played at the ticketed event."
  sport: String
  "The gender of the teams participating in the ticketed event."
  gender: String
  "List of hudl schedule entry IDs associated with the ticketed event."
  scheduleEntryIds: [ID!]
  "The ID of the venue in which the ticketed event is being held."
  venueId: String @deprecated(reason: "Use VenueConfigurationId instead.")
  "The ID of the venue configuration the ticketed event is being held at."
  venueConfigurationId: ID
  "The status of the ticketed event."
  eventStatus: String!
  "The visibility of the ticketed event."
  visibility: String!
  "Is the ticketed event refundable."
  refundable: Boolean!
  "Date the ticketed event was deleted."
  deletedAt: DateTime
  "Date the ticketed event was created."
  createdAt: DateTime
  "Ticket types referenced by the event"
  ticketTypeReferences: [TicketTypeReferenceInput]
  "The fee strategy to use for the ticketed event."
  feeStrategy: String
  "The seating chart event ID for the ticketed event."
  seatingChartEventId: String
  "The graphql ids of the form fields to associate with this pass config."
  formFieldIds: [ID!]
}

"Input to create an organization admin request."
input CreateOrganizationAdminRequestInput {
  "The unique identifier for the organization that the admin request is for."
  organizationId: ID!
  "The requests for organization admins."
  requests: [CreateRequestedOrganizationAdminInput!]!
}

"Input to create a pass config renewal campaign."
input CreatePassConfigRenewalCampaignInput {
  "The ID of the Pass Config associated with this campaign"
  passConfigId: ID!
  "The start date of the renewal campaign"
  renewalStartDate: DateTime!
  "The end date of the renewal campaign"
  renewalEndDate: DateTime!
  "The time zone to set the start and end dates in"
  timeZoneIdentifier: String!
  "The visibility of the Pass Config after the renewal campaign ends"
  postRenewalVisibility: String!
  "The list of renewers to add to this campaign"
  renewers: [PassConfigRenewerInput!]!
}

"Input to create a requested organization admin."
input CreateRequestedOrganizationAdminInput {
  "The first name of the requested user to be added as an organization admin."
  firstName: String!
  "The last name of the requested user to be added as an organization admin."
  lastName: String!
  "The email of the requested user to be added as an organization admin."
  email: String!
}

"Input to create a Salesforce case for when organizations opt-in to check payouts."
input CreateSalesforceCaseForCheckPayoutsInput {
  "The ID of the organization."
  organizationId: ID!
  "The first name for the Tipalti account holder."
  tipaltiAccountFirstName: String!
  "The last name for the Tipalti account holder."
  tipaltiAccountLastName: String!
  "The email for the Tipalti account holder."
  tipaltiAccountEmail: String!
}

"Representation of an input to create a seating chart"
input CreateSeatingChartInput {
  "The GraphQL ID of the Venue Configuration to create the chart for"
  venueConfigurationId: ID!
  "The name of the seating chart being created"
  seatingChartName: String!
  "If true, the created chart will have sections enabled (used for grouping seats together in larger venues)"
  sectionsEnabled: Boolean!
}

"Input to create onboarding step definitions."
input CreateStepDefinitionsInput {
  "The list of step definitions to create."
  stepDefinitions: [StepDefinitionInput!]!
}

"Representation of an input to create a ticket type."
input CreateTicketTypeInput {
  "The name of the ticket type."
  name: String!
  "The price in cents of the ticket type."
  priceInCents: Int!
  "The quantity associated with the ticket type."
  quantity: Int
  "The organization associated with the ticket type."
  organizationId: String!
}

"Representation of an input to create a venue configuration."
input CreateVenueConfigurationInput {
  "Name of the venue configuration."
  name: String!
  "List of Organizations IDs associated with this venue configuration"
  organizationIds: [ID!]
  "Description of the venue configuration."
  description: String
  "Unique identifier for the parent venue"
  venueId: ID!
  "Seating chart Id for the venue configuration"
  seatingChartId: String
}

"Input to delete an ad campaign."
input DeleteAdCampaignInput {
  "The GraphQL encoded ID of the ad campaign to delete."
  id: String!
}

"Representation of an input to delete a channel"
input DeleteChannelInput {
  "The Stream channel ID to delete"
  channelId: String!
  "The channel type for the given Stream channel ID"
  channelType: String!
}

input EntitledEntityRefInput {
  "The entitled entity type"
  type: EntitledEntityType!
  "The internal ID of the entity being referenced"
  id: String!
}

input ExtendAccessInput {
  "The ID of the team to be extended."
  teamId: ID!
  "The type of product to extend access for."
  product: String!
  "The subscription level to extend access for."
  level: String
  "The date to extend the team's access to."
  accessEndDate: DateTime!
  "An optional note on why this extension is happening."
  note: String
}

"Predicate used in fixture search."
input FixtureSearchPredicateInput {
  "The field that this predicate applies to."
  field: GSLFixtureSearchField!
  "Search operator used to evaluate this predicate."
  operator: GSLSearchOperator!
  "Search values to be searched. OR will be applied between each item if more then one is provided."
  values: [String]
}

"Sort used in search."
input FixtureSearchSortInput {
  "The field that this criteria applies to."
  sortField: GSLFixtureSearchField
  "Sort direction used to evaluate this criteria."
  sortDirection: GSLSearchSortDirection!
}

"Representation of an input to fetch all ticketed events in a date range."
input GetAllTicketedEventsInDateRangeInput {
  "Filters for Ticketed Events with a Date property after this date."
  startDate: DateTime!
  "Filters for Ticketed Events with a Date property before this date."
  endDate: DateTime
  "The organizations IDs to exclude from the query."
  excludedOrganizationIds: [ID!]
  "Number of ticketed events requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of ticketed events requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of ticketed events."
  after: Cursor
  "Value used to end a page of ticketed events."
  before: Cursor
}

"Representation of an input to fetch discoverable league pass configs linked to a league."
input GetDiscoverableLeaguePassConfigsByLeagueInput {
  "The ID of the league associated with the pass configs."
  leagueEntityId: ID!
  "The type of the league associated with the pass configs."
  leagueEntityType: String!
}

"Input for filtering custom forms"
input GetFilteredFormsInput {
  "Filter by specific form IDs"
  formIds: [ID!]
  "Filter by root form IDs (gets latest version)"
  rootFormIds: [ID!]
  "Filter by ownership"
  owners: [OwnerFilterInput!]
  "Include soft-deleted forms"
  includeDeleted: Boolean
}

"Input to retrieve form fields for an organization, paginated."
input GetFormFieldsByOrganizationIdInput {
  "The unique identifier for the Organization that the Form Field belongs to"
  organizationId: ID!
  "How to sort the form fields returned"
  sortType: String!
  "Determines whether to sort form fields ascending or descending based on the sort type."
  sortByAscending: Boolean!
  "Number of form fields requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of form fields requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of form fields."
  after: Cursor
  "Value used to end a page of form fields."
  before: Cursor
}

"Representation of an input to create a guardian relationship between two users."
input GetGuardianRelationshipsForTeamInput {
  "The ID of the team to get relationships for."
  teamId: ID!
  "Optional filter for status types on the guardian relationship metadata for the given teamId."
  status: GuardianStatusType
  "If true, will return results that have been deleted."
  includeDeleted: Boolean
}

input GetInferenceRunsParamsInput {
  "Filter for runs created after this datetime"
  createdAfter: DateTime
  "Filter for runs created before this datetime"
  createdBefore: DateTime
  "Filter for runs that ended after this datetime"
  endedAfter: DateTime
  "Filter for runs that ended before this datetime"
  endedBefore: DateTime
  "Number of items to skip for pagination"
  skip: Int!
  "Maximum number of items to return"
  limit: Int!
  "List of algorithm IDs to filter by"
  algorithms: [Int!]!
  "List of status IDs to filter by"
  statuses: [InferenceRunStatus!]!
  "Authorization context for the query"
  authorizationContext: AuthorizationContextDtoInput
  "Filter for runs that have finished or not"
  hasFinished: Boolean
  "Sort order flag to return newest results first"
  newestFirst: Boolean
  "Filter for runs with a specific workflow template ID"
  workflowTemplateId: String
}

"Representation of an input to fetch league pass configs linked to a league."
input GetLeaguePassConfigsByLeagueInput {
  "The ID of the league associated with the pass configs."
  leagueEntityId: ID!
  "The type of the league associated with the pass configs."
  leagueEntityType: String!
  "Number of league pass configs requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of league pass configs requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of league pass configs."
  after: Cursor
  "Value used to end a page of league pass configs."
  before: Cursor
  "Filters for pass configs with a StartDate property after this date."
  startDateMin: DateTime
  "Filters for pass configs with a StartDate property before this date."
  startDateMax: DateTime
  "Filters for pass configs with a EndDate property after this date."
  endDateMin: DateTime
  "Filters for pass configs with a EndDate property before this date."
  endDateMax: DateTime
  "Used to determine how to sort the resulting summaries."
  sortType: PassConfigSortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
  "Pass config statuses to filter by."
  leaguePassConfigStatuses: [String]
  "Pass config visibilities to filter by, such as public or private passes."
  visibilities: [String]
}

"Input to retrieve organization admin requests for an organization, paginated."
input GetOrganizationAdminRequestsByOrganizationIdInput {
  "The unique identifier for the Organization that the admin request is for"
  organizationId: ID!
  "How to sort the organization admin requests returned"
  sortType: OrganizationAdminSortType!
  "Determines whether to sort organization admin requests ascending or descending based on the sort type."
  sortByAscending: Boolean!
  "Number of organization admin requests requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of organization admin requests requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of organization admin requests."
  after: Cursor
  "Value used to end a page of organization admin requests."
  before: Cursor
}

"Representation of an input to fetch pass configs linked to an organization ID."
input GetPassConfigsByOrganizationIdInput {
  "The ID of the organization associated with the pass configs."
  organizationId: ID!
  "Number of pass configs requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of pass configs requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of pass configs."
  after: Cursor
  "Value used to end a page of pass configs."
  before: Cursor
  "Filters for pass configs with a StartDate property after this date."
  startDateMin: DateTime
  "Filters for pass configs with a StartDate property before this date."
  startDateMax: DateTime
  "Filters for pass configs with a EndDate property after this date."
  endDateMin: DateTime
  "Filters for pass configs with a EndDate property before this date."
  endDateMax: DateTime
  "Used to determine how to sort the resulting summaries."
  sortType: PassConfigSortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
  "Pass config statuses to filter by."
  passConfigStatuses: [String]
  "Pass config visibilities to filter by, such as public or private passes."
  visibilities: [String]
  "Pass config exclusions to filter by, such as filtering out passes associated with a league pass."
  exclusions: [String]
}

"Representation of an input to fetch schedule entry summaries."
input GetScheduleEntryPublicSummariesInput {
  "Filters schedule entry summaries by GraphQL encoded schoolId."
  schoolIds: [ID!]
  "Filters schedule entry summaries by GraphQL encoded teamIds."
  teamIds: [ID!]
  "Filters schedule entry summaries by internal teamIds. This will take precedence over TeamIds."
  internalTeamIds: [String!]
  "Filters schedule entry summaries by GraphQL encoded seasonIds."
  seasonIds: [ID!]
  "Filters schedule entry summaries by GraphQL encoded schedule entry Id."
  scheduleEntryIds: [ID!]
  "Filters schedule entry summaries by location."
  location: Int
  "Filters schedule entry summaries by outcome."
  outcome: Int
  "Filters schedule entry summaries by sport."
  sportId: Int
  "Filters schedule entry summaries by gender."
  genderId: Int
  "Number of summaries requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of summaries requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of summaries."
  after: Cursor
  "Value used to end a page of summaries."
  before: Cursor
  "Date used to start a filter time span."
  filterStartDate: DateTime
  "Date used to end a filter time span."
  filterEndDate: DateTime
  "Used to determine how to sort the resulting summaries."
  sortType: ScheduleEntrySortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
  "If true, will include in progress schedule entry summaries"
  includeInProgressScheduleEntrySummaries: Boolean
  "If true, will include schedule entry summaries of hidden teams"
  includeHiddenTeams: Boolean
}

"Representation of an input to fetch schedule reference submissions."
input GetScheduleReferenceSubmissionsByOrganizationIdInput {
  "Organization ID to fetch schedule reference submissions."
  organizationId: ID!
  "Number of submissions requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of submissions requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of submissions."
  after: Cursor
  "Value used to end a page of submissions."
  before: Cursor
  "Used to determine how to sort the schedule reference submissions."
  sortType: ScheduleReferenceSubmissionSortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
}

"Representation of an input to fetch schools for a competition period."
input GetSchoolsForCompetitionPeriodByTeamIdsPaginatedInput {
  "GraphQL encoded competition period Id to filter schools by."
  competitionPeriodId: ID!
  "Number of teams to fetch schools for. Note that the number of schools returned may be less than this value if there are duplicates. Maximum is 100"
  first: Int!
  "Team Id value used to start a page of schools."
  after: Cursor
}

"Representation of an input to fetch schools for a municipality."
input GetSchoolsForMunicipalityPaginatedInput {
  "GraphQL encoded municipalityId to filter schools by."
  municipalityId: ID!
  "Number of schools requested in a single page. Maximum is 100"
  first: Int!
  "Value used to start a page of schools."
  after: Cursor
}

"Representation of an input to fetch schools for a Regional Alignment."
input GetSchoolsForRegionalAlignmentByTeamIdsPaginatedInput {
  "GraphQL encoded regional alignment Id to filter schools by."
  regionalAlignmentId: ID!
  "Number of teams to fetch schools for. Note that the number of schools returned may be less than this value if there are duplicates. Maximum is 100"
  first: Int!
  "Team Id value used to start a page of schools."
  after: Cursor
}

"Representation of an input to fetch team headers."
input GetTeamHeadersByIdsInput {
  "Filters teams by teamIds"
  teamIds: [String]!
}

"Representation of an input to fetch teams for a competition period."
input GetTeamHeadersForCompetitionPeriodPaginatedInput {
  "GraphQL encoded competition period Id to filter teams by."
  competitionPeriodId: ID!
  "Number of teams requested in a single page. Maximum is 150."
  first: Int!
  "Value used to start a page of schools."
  after: Cursor
}

"Representation of an input to fetch team headers."
input GetTeamHeadersForSchoolInput {
  "Filters teams by schoolId."
  schoolId: ID!
  "Filters teams by team level."
  teamLevels: [TeamLevel!]
  "Filters teams by sport."
  sports: [Sport!]
  "If true, will include hidden teams"
  includeHiddenTeams: Boolean
}

"Representation of an input to fetch venue configurations linked to an venue ID."
input GetVenueConfigurationsByVenueIdInput {
  "The ID of the venue associated with the venue configuration."
  venueId: ID!
  "Used to determine how to sort the resulting venues."
  sortType: VenueConfigurationSortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
  "If true, will return results that have been deleted."
  includeDeleted: Boolean
  "Number of venue configurations requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of venue configurations requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of venue configurations."
  after: Cursor
  "Value used to end a page of venue configurations."
  before: Cursor
}

"Representation of an input to fetch venues by IDs."
input GetVenuesByIdsInput {
  "The IDs of the venues to retrieve."
  venueIds: [ID!]
}

"Representation of an input to fetch venues linked to an organization ID."
input GetVenuesByOrganizationIdInput {
  "The ID of the organization associated with the venue."
  organizationId: ID!
  "Number of venues requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of venues requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of venues."
  after: Cursor
  "Value used to end a page of venues."
  before: Cursor
  "Filters for venues with a Date property after this date."
  startDate: DateTime
  "Filters for venues with a Date property before this date."
  endDate: DateTime
  "Used to determine how to sort the resulting venue configurations."
  sortType: VenueSortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
  "If true, will return results that have been deleted."
  includeDeleted: Boolean
}

"Representation of a create internal signup for add ons input"
input InternalSignupAddOnsInput {
  "The existing team to create a new signup on"
  existingTeamId: String!
  "List of add-on subscriptions for this internal signup"
  addOnSubscriptions: [SignupSubscriptionInput]
  "The term start date for the subscriptions"
  termStartDate: DateTime
  "Whether or not invoices should be immediately generated for this signup."
  shouldGenerateInvoices: Boolean
  "The shipping address for any shipments"
  shippingAddress: SignupShippingAddressInput
  "Whether or not the subscriptions auto-renew after their term."
  autoRenew: Boolean
}

"Representation of a create internal signup input"
input InternalSignupInput {
  "Id of existing user who is the admin for this signup"
  existingUserId: String
  "New user information"
  user: SignupUserInput!
  "The existing organization to create a new signup on"
  existingOrganizationId: String
  "New organization information"
  newOrganization: SignupOrganizationInput
  "Whether or not this signup is for a Hudl trial."
  isTrial: Boolean!
  "Base subscription for this internal signup"
  baseSubscription: SignupSubscriptionInput
  "List of add-on subscriptions for this internal signup"
  addOnSubscriptions: [SignupSubscriptionInput]
  "A description of what type of access should be provisioned for the signup"
  accessProvisioning: AccessProvisioningInput
  "Information about the team signing up for the trial"
  team: SignupTeamInput!
  "The term start date for the subscriptions"
  termStartDate: DateTime
  "Whether or not invoices should be immediately generated for this signup. Not relevant for trial signups."
  shouldGenerateInvoices: Boolean
  "Whether or not the subscriptions auto-renew after their term."
  autoRenew: Boolean
  "The term length in months of the subscriptions created for this internal signup."
  termLengthMonths: Int
}

"The criteria for finding teams that should be included in a league pass."
input LeaguePassConfigTeamFilterInput {
  "The gender to filter teams by"
  gender: String!
  "The level to filter teams by"
  teamLevel: String!
  "The sport to filter teams by"
  sport: String!
  "The teams that meet the gender\/level\/sport criteria but should not be included in the pass."
  excludedTeamIds: [String!]
}

"Links any unlinked ticketing entities when an email has an existing hudl user record."
input LinkUnlinkedTicketingEntitiesToUserInput {
  "Number of tickets being requested, taken from the start of the Skip param. If both limit and skip are null then all distinct unlinked emails will try to be linked."
  limit: Int
  "Number of tickets being skipped, taken from the end of the total list. If both limit and skip are null then all distinct unlinked emails will try to be linked."
  skip: Int
}

"Representation of a location."
input LocationInput {
  "Address of the location."
  address: String
  "City of the location."
  city: String
  "Subdivision of the location."
  subdivision: String
  "Country of the location."
  country: String
  "Postal code of the location."
  postalCode: String
  "Latitude of the location."
  latitude: String
  "Longitude of the location."
  longitude: String
  "Timezone of the location."
  timezoneIdentifier: String
}

"Predicate used in mediasearch."
input MediaSearchPredicateInput {
  "The field that this predicate applies to."
  field: MediaSearchField!
  "Search operator used to evaluate this predicate."
  operator: MediaSearchOperator!
  "Search values to be searched. OR will be applied between each item if more then one is provided."
  values: [String!]!
}

"Input for an onboarding step template."
input OnboardingStepTemplateInput {
  "The unique identifier for the onboarding step definition associated with the onboarding step template."
  onboardingStepDefinitionId: ID!
  "The semantic version of the onboarding step template."
  version: String!
  "The requirement of the onboarding step template."
  requirement: String!
}

input OwnerDtoInput {
  userId: String
  teamId: String
}

"Filter for form ownership"
input OwnerFilterInput {
  "The owner entity ID"
  ownerId: ID!
  "The type of owner"
  ownerType: OwnerType!
}

"Input representing a pass config renewer for a renewal campaign."
input PassConfigRenewerInput {
  "The first name of the Renewer"
  firstName: String!
  "The last name of the Renewer"
  lastName: String!
  "The email address of the Renewer"
  email: String!
  "The list of seat identifiers that the Renewer can select"
  selectableSeatIdentifiers: [String!]!
}

"Input to resend a ticket group email."
input ResendTicketGroupEmailInput {
  "GraphQL encoded Id of the ticket group to resend an email for"
  ticketGroupId: ID!
  "Optional email address to send the email to. If not provided, the email address on the ticket group will be used."
  emailAddress: String
}

input ResourceNameDtoInput {
  id: String
  type: String
  owner: OwnerDtoInput
}

"Representation of an input to fetch passes based on search criteria."
input SearchPassesByTextValueInput {
  "The first name of the purchaser associated with the pass."
  firstName: String
  "The last name of the purchaser associated with the pass."
  lastName: String
  "The email of the purchaser associated with the pass."
  email: String
  "Number of passes requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of passes requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of passes."
  after: Cursor
  "Value used to end a page of passes."
  before: Cursor
  "Filters for passes with a Date property after this date."
  startDate: DateTime
  "Filters for passes with a Date property before this date."
  endDate: DateTime
  "Used to determine how to sort the resulting summaries."
  sortType: PassSortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
}

"Representation of an input to fetch ticket groups based on search criteria."
input SearchTicketGroupsByTextValueInput {
  "The first name of the purchaser associated with the ticket group."
  firstName: String
  "The last name of the purchaser associated with the ticket group."
  lastName: String
  "The email of the purchaser associated with the ticket group."
  email: String
  "Number of ticket groups requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of ticket groups requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of ticket groups."
  after: Cursor
  "Value used to end a page of ticket groups."
  before: Cursor
  "Filters for ticket groups with a Date property after this date."
  startDate: DateTime
  "Filters for ticket groups with a Date property before this date."
  endDate: DateTime
  "Used to determine how to sort the resulting summaries."
  sortType: TicketGroupSortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
}

"Representation of an input to fetch tickets based on search criteria."
input SearchTicketsByTextValueInput {
  "The first name of the purchaser associated with the ticket."
  firstName: String
  "The last name of the purchaser associated with the ticket."
  lastName: String
  "The email of the purchaser associated with the ticket."
  email: String
  "Number of tickets requested, taken from the start of the total list. Also used for paging."
  first: Int
  "Number of tickets requested, taken from the end of the total list. Also used for paging."
  last: Int
  "Value used to start a page of tickets."
  after: Cursor
  "Value used to end a page of tickets."
  before: Cursor
  "Filters for Tickets with a Date property after this date."
  startDate: DateTime
  "Filters for Tickets with a Date property before this date."
  endDate: DateTime
  "Used to determine how to sort the resulting summaries."
  sortType: TicketSortType!
  "If true, will sort results ascending instead of descending."
  sortByAscending: Boolean!
}

"Representation of an input to share ticketing entities."
input ShareTicketingEntitiesInput {
  "The id associated with the original ticket group."
  originalTicketGroupId: ID!
  "List of entities shared."
  sharedEntities: [SharedEntityInput!]!
}

"Representation of the shared entities in a ticket group."
input SharedEntityInput {
  "The ID of the shared entity."
  entityId: ID!
  "The type of the shared entity."
  entityType: String!
}

"Representation of a Address input."
input SignupOrganizationInput {
  "Name of the organization"
  organizationName: String
  "Name of the league"
  leagueName: String
  "First line of street address"
  address1: String
  "Second line of street address"
  address2: String
  "City for the address"
  city: String
  "State for the address"
  state: String
  "Zip\/postal code for the address"
  zip: String
  "Country for the address"
  country: String
  "Org Classification Int Value of the organization"
  orgClassificationId: Int!
}

"Representation of a Shipping Address input."
input SignupShippingAddressInput {
  "Name for the shipping address"
  fullName: String
  "Company name for the shipping address"
  companyName: String
  "Phone for the shipping address"
  phoneNumber: String
  "Email address for the shipping notifications"
  email: String
  "First line of street address"
  address1: String
  "Second line of street address"
  address2: String
  "Third line of street address"
  address3: String
  "City for the address"
  city: String
  "State for the address"
  state: String
  "Zip\/postal code for the address"
  zip: String
  "Country for the address"
  country: String
}

"Representation of an internal signup subscription input"
input SignupSubscriptionInput {
  "Universal Subscription Product for the subscription"
  universalSubscriptionProduct: String!
  "Promo Code for the subscription"
  promoCode: String
  "Whether this subscription should be invoiced separarely from any other subscriptions."
  invoiceSeparately: Boolean!
}

input SignupTeamInput {
  "The name of the team"
  teamName: String!
  "The gender of the team"
  gender: Int!
  "The level of the team"
  teamLevel: Int
  "The sport of the team"
  sportId: String!
  "Is the team a test team"
  isTest: Boolean!
}

input SignupUserInput {
  "First Name of the user"
  firstName: String
  "Last Name of the user"
  lastName: String
  "The phone number for the user"
  phoneNumber: String
  "The email for the user"
  email: String
  "The user id (if it exists)"
  userId: String
}

"Input for an onboarding step definition."
input StepDefinitionInput {
  "The name of the onboarding step definition."
  name: String!
  "The description of the step definition. Not intended to be displayed to the user in any case, meant for internal notes to describe the purpose of the onboarding step."
  description: String!
  "The list of event actions to be associated with the step definition."
  eventActions: [String!]
}

input StreamInput {
  "The installation id associated with the RecordingDefault"
  installationId: String!
  "The camera id associated with the stream."
  cameraId: String
  "The type for the stream."
  streamType: StreamType!
}

input TeamAuthorizationContextDtoInput {
  teamId: String
  groupIds: [String]
  roles: [Role!]
}

"Predicate used in search."
input TeamSearchPredicateInput {
  "The field that this predicate applies to."
  field: GSLTeamSearchField!
  "Search operator used to evaluate this predicate."
  operator: GSLSearchOperator!
  "Search values to be searched. OR will be applied between each item if more then one is provided."
  values: [String]
}

"Sort used in search."
input TeamSearchSortInput {
  "The field that this criteria applies to."
  sortField: GSLTeamSearchField
  "Sort direction used to evaluate this criteria."
  sortDirection: GSLSearchSortDirection!
}

input TeamTicketTypeInput {
  teamId: ID!
  ticketTypeIds: [ID!]!
}

"Representation of an input for a ticket type referenced by a ticketed event."
input TicketTypeReferenceInput {
  "The GraphQL ID of the referenced ticket type."
  ticketTypeId: ID
  "The price override to the referenced ticket type."
  priceOverride: Int
  "The quantity override to the referenced ticket type."
  quantityOverride: Int
  "The seating chart category IDs for the referenced ticket type."
  seatingChartCategoryIds: [String]
}

"Representation of an input to transfer ticketing entities."
input TransferTicketingEntitiesInput {
  "The id associated with the original ticket group."
  originalTicketGroupId: ID!
  "List of entities to transfer."
  entitiesToTransfer: [SharedEntityInput!]!
  "The first name of the user to transfer the entities to."
  recipientFirstName: String!
  "The last name of the user to transfer the entities to"
  recipientLastName: String!
  "The email of the user to transfer the entities to"
  recipientEmail: String!
}

"Input to update an existing ad campaign."
input UpdateAdCampaignInput {
  "The GraphQL encoded ID of the ad campaign to update."
  id: String!
  "The name of the ad campaign."
  name: String!
  "The cachekey for the ad campaign."
  cacheKey: String!
  "The ad targeting key\/value associated with the ad campaign."
  targetingKeyValue: String!
  "The ad campaign type."
  campaignType: String!
  "Indicates whether the ad campaign is enabled or not"
  isEnabled: Boolean!
}

"Representation of an input for Auth0 connection updates"
input UpdateAuthZeroConnectionInput {
  "The Connection id"
  id: String!
  "The Domain Aliases used in Home Realm Discovery"
  domainAliases: [String!]
  "The status of the Connection: Off, Test, or Production"
  status: ConnectionStatus
}

"Representation of an input to update a league pass config."
input UpdateLeaguePassConfigInput {
  "The GraphQL ID for the league pass config being updated."
  id: ID!
  "The name of the league pass config."
  name: String
  "The description of the league pass config."
  description: String
  "A list of team filters that are associated with this league pass config."
  teamFilters: [LeaguePassConfigTeamFilterInput!]
  "The fee strategy to apply to the league pass config."
  feeStrategy: String
  "The visibility of the league pass config"
  visibility: String
  "The price of the passes."
  priceInCents: Int
  "The status of the league pass config"
  leaguePassConfigStatus: String
  "The start date the league pass config is valid for. (inclusive)"
  startDate: DateTime
  "The end date the league pass config is valid for. (inclusive)"
  endDate: DateTime
  "The game types that are excluded from the league pass config."
  excludedGameTypes: [String!]
}

"Representation of an input to create a venue configuration."
input UpdateVenueConfigurationInput {
  "The GraphQL ID for the venue configuration being updated."
  id: ID!
  "Name of the venue configuration."
  name: String!
  "List of Organizations IDs associated with this venue configuration"
  organizationIds: [ID!]
  "Description of the venue configuration."
  description: String
  "Seating chart Id for the venue configuration"
  seatingChartId: String
}

"Representation of an input to upsert an organization settings object"
input UpsertOrganizationSettingsInput {
  "The unique identifier for the organization."
  organizationId: ID!
  "The time zone of the organization."
  timeZone: String
  "Indicates if student data privacy is enabled for the organization."
  isStudentDataPrivacyOn: Boolean
  "Indicates if the organization profile is hidden."
  isOrgProfileHidden: Boolean
}

"Details for upserting a recording default for team."
input UpsertRecordingDefaultInput {
  "The team id requesting to create or update a RecordingDefault"
  teamId: String!
  "The installation id associated with the RecordingDefault"
  primaryInstallationId: String!
  "The recording type associated with the RecordingDefault"
  recordingType: PublishEventType!
  "The default Focus streams for the given installation id"
  streams: [StreamInput!]!
}

"Representation of an input to create an experiment."
input UpsertTicketingExperimentInput {
  "The GraphQL ID for the experiment being upserted."
  experimentId: ID
  "The name of the experiment."
  experimentName: String!
  "List of the organization ids for the experiment."
  organizationIds: [String]
  "Date the experiment was deleted."
  deletedAt: DateTime
}

"Representation of an input to create or update ticketing organization settings."
input UpsertTicketingOrganizationSettingsInput {
  "The organization id for the associated settings."
  organizationId: ID!
  "The default fee strategy for the organization."
  defaultFeeStrategy: String
  "The default ticket types per team for the organization."
  defaultTeamTicketTypes: [TeamTicketTypeInput!]
  "The payout method for the organization."
  payoutMethod: String
}

"Representation of an input to create or update a venue."
input UpsertVenueInput {
  "The GraphQL ID for the venue being upserted."
  id: ID
  "Name of the venue."
  name: String!
  "List of Organizations IDs associated with this venue"
  organizationIds: [ID!]
  "Location of the venue."
  location: LocationInput
  "Setting of the venue"
  setting: String
  "IDs for the associated Venue Configurations."
  venueConfigurationIds: [ID!]
  "Date the venue was deleted."
  deletedAt: DateTime
  "Date the venue was created."
  createdAt: DateTime
}

enum AcceptanceDifficultyType {
  UNKNOWN
  MINIMALLY_DIFFICULT
  MODERATELY_DIFFICULT
  MOST_DIFFICULT
  NON_COMPETITIVE
  VERY_DIFFICULT
}

enum AccountStatus {
  GHOST
  CORRUPT
  ENABLED
  DISABLED
}

enum AthleticAssociationType {
  UNKNOWN
  NCAA
  NAIA
  NJCAA
  CIAU
  USCAA
  NCCAA
  CCCAA
  NWAC
  USACC
  MCLA
  IRA
  CSFL
  MSFL
  NCWA
  ACHA
}

enum BillingRecordType {
  "Default value - likely that new billing record types have been added that that GraphQL Gateway doesn't understand"
  UNKNOWN
  "Billing record is a rate plan in the secondary Zuora tenant"
  COMPETITIVE_ZUORA_RATE_PLAN
  "Billing record is a rate plan in the primary Zuora tenant"
  PRIMARY_ZUORA_RATE_PLAN
}

enum CategoryType {
  SEASON
  GAME
  CLINIC
  SPECIAL_PROJECT
  GAME_FOOTAGE
  PRACTICE
  OPPONENT_SCOUT
  OTHER_ITEMS
  END_OF_SEASON
  MISC
  FILM_EXCHANGE
  FILM_STUDY
  DRILLS
  DSV_ANYWHERE
  MOBILE_UPLOADS
}

enum CollegeSearchField {
  UNKNOWN
  "Values must be the state abbreviation."
  STATE
  "Values must be the major description."
  MAJOR
  "Values must exactly match the Hudl.SharedTypes.Enums Sport enum name or value."
  SPORT
  "Values must exactly match the hudl-collegeLibrary Gender enum name or value."
  GENDER
  "Values must exactly match the hudl-collegeLibrary SportLevelType enum name or value."
  DIVISION
  "Values must be the number of enrolled students."
  SIZE
  "Values are the Ids of a subset of colleges to perform the search on."
  COLLEGE_ID
  "Values must be double parseable strings for the minimum and\/or maximum distance range."
  DISTANCE
  "Values must be boolean parsable strings."
  SCHOLARSHIPS
  "Values are the source address for distance computation."
  ADDRESS
  "Values must be comma-separated double parseable geocoordinates for distance computation."
  GEO_COORDINATES
}

enum CollegeSearchOperator {
  UNKNOWN
  EQUALS
  GREATER_THAN
  LESS_THAN
  GREATER_THAN_OR_EQUAL
  LESS_THAN_OR_EQUAL
}

enum CollegeSearchSortDirection {
  UNKNOWN
  ASC
  DESC
}

enum CollegeSearchSortField {
  UNKNOWN
  ID
  NAME
  SIZE
  TUITION
  ACCEPTANCE_DIFFICULTY
  DISTANCE
  _SCORE
}

enum CollegeType {
  UNKNOWN
  PUBLIC
  PRIVATE
}

enum ConnectionStatus {
  OFF
  TEST
  PRODUCTION
}

enum ConsentStatusState {
  UNKNOWN
  PENDING
  APPROVED
  DENIED
  NOT_STARTED
  NOT_REQUIRED
}

enum ConsentStatusType {
  SDP
}

enum CurrencyCodeType {
  UNKNOWN
  USD
  EUR
  JPY
  GBP
  AUD
  CAD
  OTHER
}

enum EntitledEntityType {
  "Default value - likely that new entitled entity types have been added that that GraphQL Gateway doesn't understand"
  UNKNOWN
  "Product is sold to a Hudl Team (or the paying team in a program)"
  HUDL_TEAM
}

enum EntityType {
  USER
  ATHLETE
  TEAM
  ORGANIZATION
}

enum EntranceExamType {
  UNKNOWN
  ACT
  SAT
  OTHER
}

enum ExchangeMatchRequiredAsset {
  ALL
  VIDEO
}

enum ExternalSourceType {
  UNKNOWN
  COLLEGE_COACHES_ONLINE
}

enum FeeResponsibility {
  UNSET
  CUSTOMER
  ORGANIZATION
}

enum FrequencyType {
  NONE
  SECONDLY
  MINUTELY
  HOURLY
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}

enum GSLCompetitionSearchField {
  UNKNOWN
  ID
  CREATED_AT
  UPDATED_AT
  SPORT
  NAME
  GENDER
  REGIONAL_ALIGNMENT_IDS
  SEASON_PERIOD_IDS
  DELETED_AT
  _SCORE
}

enum GSLCompetitionSport {
  UNKNOWN
  FOOTBALL
  BASKETBALL
  WRESTLING
  VOLLEYBALL
  BASEBALL
  SOCCER
  LACROSSE
  GOLF
  GYMNASTICS
  SOFTBALL
  SWIMMING_AND_DIVING
  TRACK
  ICE_HOCKEY
  FIELD_HOCKEY
  WATER_POLO
  CHEER_AND_SPIRIT
  DANCE_AND_DRILL
  CRICKET
  CROSS_COUNTRY
  PERFORMING_ARTS
  RUGBY
  TENNIS
  AUSTRALIAN_RULES_FOOTBALL
  RUGBY_LEAGUE
  RUGBY_UNION
  NETBALL
  SURFING
  TENPIN_BOWLING
  BADMINTON
  CYCLING
  SAILING_AND_YACHTING
  FENCING
  HANDBALL
  SQUASH
  OTHER
  NO_SPORT
}

enum GSLFixtureSearchField {
  UNKNOWN
  ID
  CREATED_AT
  UPDATED_AT
  SPORT
  COMPETITION_PERIOD_ID
  COMPETITION_ID
  COMPETITION_NAME
  SEASON_PERIOD_ID
  SEASON_PERIOD_NAME
  SEASON_PERIOD_START_DATE
  SEASON_PERIOD_END_DATE
  INDIVIDUAL_ID
  TEAM_ID
  FIXTURE_STATE
  LOCAL_DATE
  MEDIA_STREAMS
  MEDIA_CONTEXTS
  HOME_TEAM_ID
  AWAY_TEAM_ID
  REGIONAL_ALIGNMENT_ID
  DELETED_AT
  _SCORE
}

enum GSLFixtureState {
  UNKNOWN
  COMPLETE
}

enum GSLFixtureTeamQualifier {
  HOME
  AWAY
}

enum GSLNoAccessReason {
  UNKNOWN
  MISSING_SUBSCRIPTION
  MISSING_VIDEO_RIGHTS
  ACCESS_DENIED @deprecated(reason: "This entry should not be used here")
}

enum GSLOrganizationCategorization {
  UNKNOWN
  COLLEGE
}

"Operator to use in the comparison"
enum GSLSearchOperator {
  UNKNOWN
  EQUALS
  GREATER_THAN
  LESS_THAN
  GREATER_THAN_OR_EQUAL
  LESS_THAN_OR_EQUAL
  CONTAINS
  EXISTS
  DOES_NOT_EXIST
  DOES_NOT_EQUAL
}

enum GSLSearchSortDirection {
  UNKNOWN
  ASCENDING
  DESCENDING
}

enum GSLTeamGender {
  UNKNOWN
  WOMEN
  MEN
  MIXED
}

enum GSLTeamSearchField {
  UNKNOWN
  ID
  CREATED_AT
  UPDATED_AT
  NAME
  SHORT_NAME
  ABBREVIATION
  SPORT
  GENDER
  TEAM_TYPE
  REGION_ID
  REGION_NAME
  REGIONAL_ALIGNMENT_IDS
  TEAM_MEMBER_IDS
  INDIVIDUAL_IDS
  COMPETITION_PERIOD_IDS
  COMPETITION_IDS
  SEASON_PERIOD_IDS
  DELETED_AT
  _SCORE
}

enum GSLTeamType {
  UNKNOWN
  NATIONAL
  OTHER
  YOUTH
}

enum GameType {
  REGULAR_SEASON
  POST_SEASON
  CAMP
  SCRIMMAGE
  TOURNAMENT
  COACH
  POST_SEASON_TOURNAMENT
}

enum Gender {
  MENS
  WOMENS
  COED
}

enum GuardianStatusType {
  DENIED
  PENDING
  APPROVED
}

enum GuardianshipLevel {
  ATHLETE_GUARDIAN_ACCESS
}

enum ImageSize {
  NORMAL
  THUMBNAIL
  RETINA
  FULL
}

enum InferenceRunStatus {
  RUNNING
  FAILED
  SUCCEEDED
  SUBMITTED
  RECEIVED
  CANCELLED
  REJECTED
  UNKNOWN
}

enum InstallationFeatureFlags {
  NONE
  ENABLE_RTMP_OUTPUT
  USE_NGINX_BROADCAST_SERVER
}

enum InstallationProviderType {
  UNKNOWN
  HUDL
  ATV
}

enum InstallationSetupStatus {
  UNKNOWN
  ORDERED
  DELIVERED
  INITIALIZED
  CAMERAS_CONFIGURED
  DISABLED
  INSTALLED
  MANUAL_CALIBRATION
  POSITIONED
  SCOREBOARD_CONFIGURED
}

enum InstallationStatus {
  UNKNOWN
  GREEN
  YELLOW
  RED
}

enum JsonFileContents {
  UNKNOWN
  MOMENT_LIST
  TEAM_ROSTERS
  CUTUP_URL_LIST
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_METADATA
  GAMEDATA_S3_LOCATIONS
  UNKNOWN_JSON
}

enum LicenseType {
  ELITE
  ELITE_REVIEW
  PRO
  PRO_REVIEW
  PLAYER
  GAMEBREAKER_PLUS
  STUDIOCODE
  WIMU_SPRO
  WIMU_SVIVO
  GSL_API_TOKEN
}

enum MediaQuality {
  UNKNOWN
  LD_H264_722_KB
  SD_H264_1100_KB
  HD_H264_3000_KB
  THD_H264_6500_KB
  UHD_H264_16000_KB
}

enum MediaSearchField {
  TITLE
  PROVIDER_ID
}

enum MediaSearchOperator {
  EQUALS
  GREATER_THAN
  LESS_THAN
  GREATER_THAN_OR_EQUAL
  LESS_THAN_OR_EQUAL
}

enum OrganizationAdminSortType {
  REQUESTED_AT
}

enum OutputType {
  IMAGE_DETECTIONS
  IMAGE_TRACKING
  PITCH_TRACKING
  JERSEY_RECOGNITION
  GAME_TIME_DETECTION
  SUBSTITUTIONS
  PRIMITIVES
  AMERICAN_FOOTBALL_CLIPPING
  AMERICAN_FOOTBALL_TAGGING
  BASKETBALL_ACTION_RECOGNITION
  BASKETBALL_FRAME_ORACLE
  INBOUNDS
  BASKETBALL_STOPPAGE_PREDICTION
  AMERICAN_FOOTBALL_FORMATIONS
  BASEBALL_SOFTBALL_CLIPPING
  BASEBALL_SOFTBALL_FRAME_ORACLE
  GLOBAL_FOOTBALL_BROADCAST_TRACKING
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_CALIBRATION
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_DETECTION
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_HOMOGRAPHY
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_PROJECTION
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_MOT
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_BALL_TRACKING
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_HOT
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_FINAL_CHUNK
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_METRICS
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_CONFIDENCE_SCORES
  EVENT_DETECTION
  GLOBAL_FOOTBALL_BROADCAST_TRACKING_S3_CACHE_OUTPUT
  UNKNOWN
}

"Type of form owner"
enum OwnerType {
  "Organization. Use 'School' as GraphQL prefix for IDs."
  ORGANIZATION
}

enum PassConfigSortType {
  UNKNOWN
  START_DATE
}

enum PassSortType {
  START_DATE
}

enum ProgramState {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum ProgramVisibility {
  UNSET
  PUBLIC
  PRIVATE
}

enum PublishEventStreamStatus {
  UNKNOWN
  UPLOADING
  UPLOADED
  ENCODING
  ENCODED
  AUDIO_MUXING
  AUDIO_MUXED
  ERROR
  SCHEDULED_ON_DEVICE
  MANUAL_UPLOAD_RETRY
  MANUAL_UPLOAD_REQUESTED
  SOURCE_UPLOADS_SAVED
  OFFLINE_DURING_RECORDING
  VIDEO_INTERRUPTED
  VIDEO_DURATION_ISSUE
}

enum PublishEventType {
  UNKNOWN
  GAME
  PRACTICE
}

enum RegistrationStatus {
  UNREGISTERED
  REGISTERED
}

enum RelationshipType {
  GUARDIAN
  FAVORITE
}

enum RestreamDestinationPlatform {
  UNKNOWN
  YOU_TUBE
  MOCK
  HUDL_TV
}

enum RestreamStatus {
  UNKNOWN
  UNINITIALIZED
  INITIALIZED
  STARTED
  STOPPED
}

enum Role {
  FAN @deprecated(reason: "Try to avoid using this. We don't store 'Fan' roles for user records, so prefer to use a nullable Role if 'no role' is a possibility.")
  PARTICIPANT
  COACH
  ADMINISTRATOR
  RECRUITER @deprecated(reason: "jond - This should be going away...at some point.  However, it needs to continue to be accounted for in code\/security checks")
  INSIDER @deprecated(reason: "caleb - This is going away for now. There are some Insider accounts still, but no more should be getting created.")
  TECHNIQUE
  NONE @deprecated(reason: "Try to avoid using this. We don't store 'None' roles for user records, so prefer to use a nullable Role if 'no role' is a possibility.")
}

enum S3FileType {
  VIRTCAM_CONFIG_FILE
  MOSAIC_MANIFEST_FILE
  UNKNOWN
}

enum ScheduleEntrySortType {
  SCHEDULE_ENTRY_DATE
}

enum ScheduleEntryType {
  REGULAR_SEASON
  POST_SEASON
  CAMP
  SCRIMMAGE
  TOURNAMENT
  COACH
  POST_SEASON_TOURNAMENT
}

enum ScheduleReferenceSubmissionSortType {
  UPLOAD_DATE
}

enum Sport {
  FOOTBALL
  BASKETBALL
  WRESTLING
  VOLLEYBALL
  BASEBALL
  SOCCER
  LACROSSE
  GOLF
  GYMNASTICS
  SOFTBALL
  SWIMMING_AND_DIVING
  TRACK
  ICE_HOCKEY
  FIELD_HOCKEY
  WATER_POLO
  CHEER_AND_SPIRIT
  DANCE_AND_DRILL
  CRICKET
  CROSS_COUNTRY
  PERFORMING_ARTS
  RUGBY
  TENNIS
  AUSTRALIAN_RULES_FOOTBALL
  RUGBY_LEAGUE
  RUGBY_UNION
  NETBALL
  SURFING
  TENPIN_BOWLING
  BADMINTON
  CYCLING
  SAILING_AND_YACHTING
  FENCING
  HANDBALL
  SQUASH
  OTHER
  NO_SPORT
  FOOTBALL_RECRUITING
  BASKETBALL_RECRUITING
  WRESTLING_RECRUITING
  VOLLEYBALL_RECRUITING
  BASEBALL_RECRUITING
  SOCCER_RECRUITING
  LACROSSE_RECRUITING
  GOLF_RECRUITING
  GYMNASTICS_RECRUITING
  SOFTBALL_RECRUITING
  SWIMMING_AND_DIVING_RECRUITING
  TRACK_RECRUITING
  ICE_HOCKEY_RECRUITING
  FIELD_HOCKEY_RECRUITING
  WATER_POLO_RECRUITING
  CHEER_AND_SPIRIT_RECRUITING
  DANCE_AND_DRILL_RECRUITING
  CRICKET_RECRUITING
  CROSS_COUNTRY_RECRUITING
  PERFORMING_ARTS_RECRUITING
  RUGBY_RECRUITING
  TENNIS_RECRUITING
  AUSTRALIAN_RULES_FOOTBALL_RECRUITING
  RUGBY_LEAGUE_RECRUITING
  RUGBY_UNION_RECRUITING
  NETBALL_RECRUITING
  SURFING_RECRUITING
  TENPIN_BOWLING_RECRUITING
  BADMINTON_RECRUITING
  CYCLING_RECRUITING
  SAILING_AND_YACHTING_RECRUITING
  FENCING_RECRUITING
  HANDBALL_RECRUITING
  SQUASH_RECRUITING
}

enum SportLevelType {
  UNKNOWN
  INTERCOLLEGIATE_SPORT_D1
  INTERCOLLEGIATE_SPORT_D2
  INTERCOLLEGIATE_SPORT_D3
  INTERCOLLEGIATE_FOOTBALL1_A
  INTERCOLLEGIATE_FOOTBALL1_AA
  INTERCOLLEGIATE_CLUB
  INTERCOLLEGIATE_SPORT
  NCAAD1
  NCAAD2
  NCAAD3
  NAIA
  NJCAAD1
  NJCAAD2
  NJCAAD3
  CIAU
  USCAAD1
  USCAAD2
  NCCAAD1
  NCCAAD2
  CCCAA
  NWAC
  USACC
  MCLAD1
  MCLAD2
  IRA_HEAVYWEIGHT
  IRA_LIGHTWEIGHT
  CSFL_NORTH
  CSFL_SOUTH
  MSFL
  NCWAD1
  NCWAD2
  ACHAD1
  ACHAD2
  ACHAD3
}

enum StatusCode {
  UNKNOWN
  UPLOAD_SPEED_BELOW_RECOMMENDED
  UPLOAD_SPEED_BELOW_MINIMUM
  UNABLE_TO_COMMUNICATE_WITH_HUDL
  UNABLE_TO_COMMUNICATE_WITH_AUTO_CAP_S3_UPLOADS
  UNABLE_TO_COMMUNICATE_WITH_IO_T
  UNABLE_TO_COMMUNICATE_WITH_DATAPLICITY
  UNABLE_TO_COMMUNICATE_WITH_DEBIAN_REPOS
  UNABLE_TO_CONNECT_WITH_IO_T
  UNABLE_TO_COMMUNICATE_WITH_AUTO_CAP_S3_UPDATES
  HARD_DRIVE_SPACE_BELOW_RECOMMENDED
  HARD_DRIVE_SPACE_BELOW_MINIMUM
  CAMERA_SENSOR_OFFLINE
  MEMORY_FAILURE
  SYSTEM_DISK_BELOW_RECOMMENDED
  SYSTEM_DISK_BELOW_MINIMUM
  HARD_DRIVE_FAILURE
  KOKO_UPLOADER_WORKER_NOT_RUNNING
  KOKO_STATUS_WORKER_NOT_RUNNING
  KOKO_IO_TNOT_RUNNING
  KOKO_VIRT_CAM_COMMUNICATOR_WORKER_NOT_RUNNING
  RABBIT_MQ_SERVER_NOT_RUNNING
  VIRT_CAM_NOT_RUNNING
  HUDL_UPDATER_NOT_RUNNING
  HUDL_LOG_UPLOADER_NOT_RUNNING
  KOKO_RECORDER_NOT_RUNNING
  KOKO_WATCHER_NOT_RUNNING
  BROADCAST_SERVER_NOT_RUNNING
  SSH_CLIENT_NOT_RUNNING
  UNABLE_TO_AUTHENTICATE_WITH_HUDL
}

enum StatusCodeType {
  UNKNOWN
  WARNING
  ERROR
}

enum StreamType {
  UNKNOWN
  RAW_VIDEO
  PANORAMIC
  TACTICAL
  EDGE_DATA
  PANORAMIC_DEBUG
  TACTICAL_DEBUG
  STATIC_CAMERA
  SCOREBOARD_VIDEO
  GAME_DATA
  RAW_BALL_DATA
  RAW_HISTOGRAM_DATA
  EXTRA_WIDE_TACTICAL
  MOSAIC_PLAYER_FEED
}

enum StudentBodyType {
  UNKNOWN
  COED
  ONLY_MEN
  ONLY_WOMEN
  PRIMARILY_MEN
  PRIMARILY_WOMEN
  UNDERGRAD_ONLY_MEN
  UNDERGRAD_ONLY_WOMEN
}

enum TeamLevel {
  VARSITY
  JUNIOR_VARSITY
  SOPHOMORE
  FRESHMAN
  OTHER
  OTHER_NON_HS
}

enum TicketGroupSortType {
  TICKET_GROUP_PURCHASE_DATE
  TICKET_GROUP_PURCHASER_LAST_NAME
}

enum TicketSortType {
  TICKET_DATE
}

enum TrialStatus {
  UNKNOWN
  ACTIVE
  CANCELED
  CONVERTED
  ON_HOLD
}

enum UrlCodeType {
  UNKNOWN
  APP
  URL
}

enum UserStatus {
  CREATED_WITHOUT_AN_EMAIL_OR_USERNAME
  HAS_NEVER_LOGGED_IN
  ACTIVE
  DISABLED
}

enum VenueConfigurationSortType {
  UNKNOWN
  NAME
  CREATED_AT
}

enum VenueSortType {
  UNKNOWN
  NAME
}

"The purpose of the `cost` directive is to define a `weight` for GraphQL types, fields, and arguments. Static analysis can use these weights when calculating the overall cost of a query or response."
directive @cost("The `weight` argument defines what value to add to the overall cost for every appearance, or possible appearance, of a type, field, argument, etc." weight: String!) on SCALAR | OBJECT | FIELD_DEFINITION | ARGUMENT_DEFINITION | ENUM | INPUT_FIELD_DEFINITION

"The purpose of the `@listSize` directive is to either inform the static analysis about the size of returned lists (if that information is statically available), or to point the analysis to where to find that information."
directive @listSize("The `assumedSize` argument can be used to statically define the maximum length of a list returned by a field." assumedSize: Int "The `slicingArguments` argument can be used to define which of the field's arguments with numeric type are slicing arguments, so that their value determines the size of the list returned by that field. It may specify a list of multiple slicing arguments." slicingArguments: [String!] "The `slicingArgumentDefaultValue` argument can be used to define a default value for a slicing argument, which is used if the argument is not present in a query." slicingArgumentDefaultValue: Int "The `sizedFields` argument can be used to define that the value of the `assumedSize` argument or of a slicing argument does not affect the size of a list returned by a field itself, but that of a list returned by one of its sub-fields." sizedFields: [String!] "The `requireOneSlicingArgument` argument can be used to inform the static analysis that it should expect that exactly one of the defined slicing arguments is present in a query. If that is not the case (i.e., if none or multiple slicing arguments are present), the static analysis may throw an error." requireOneSlicingArgument: Boolean! = true) on FIELD_DEFINITION

"The `@specifiedBy` directive is used within the type system definition language to provide a URL for specifying the behavior of custom scalar definitions."
directive @specifiedBy("The specifiedBy URL points to a human-readable specification. This field will only read a result for scalar types." url: String!) on SCALAR

"Represents a cursor"
scalar Cursor

"The `DateTimeType` scalar represents an ISO-8601 compliant date time type"
scalar DateTime @specifiedBy(url: "https:\/\/www.graphql-scalars.com\/date-time")

"The `Decimal` scalar type represents a decimal floating-point number."
scalar Decimal

"The `Long` scalar type represents non-fractional signed whole 64-bit numeric values. Long can represent values between -(2^63) and 2^63 - 1."
scalar Long

"The `Short` scalar type represents non-fractional signed whole 16-bit numeric values. Short can represent values between -(2^15) and 2^15 - 1."
scalar Short

"The `TimeSpan` scalar represents an ISO-8601 compliant duration type."
scalar TimeSpan

"The UnsignedShort scalar type represents a unsigned 16-bit numeric non-fractional value greater than or equal to 0."
scalar UnsignedShort