import { type Definitions<PERSON><PERSON><PERSON><PERSON>, type TagTypesFromApi } from '@reduxjs/toolkit/query';

import { api } from './api.generated';

type Definitions = DefinitionsFromApi<typeof api>;
type TagTypes = TagTypesFromApi<typeof api>;

/**
 * The enhanced API provides tags and endpoint definitions that get merged into the existing
 * definitions from api.generated.ts
 * https://redux-toolkit.js.org/rtk-query/api/created-api/code-splitting#enhanceendpoints
 *
 * Paginated requests can merge their responses into a single cache
 * https://redux-toolkit.js.org/rtk-query/api/createApi#merge
 *
 * Optimistic updates can update the playlist cache as soon as the request is sent
 * https://redux-toolkit.js.org/rtk-query/usage/manual-cache-updates#optimistic-updates
 *
 * Pessimistic updates can the playlist cache as soon as the request has been fulfilled
 * https://redux-toolkit.js.org/rtk-query/usage/manual-cache-updates#pessimistic-updates
 *
 * See the sync page for more details
 * https://sync.hudlnet.com/display/PF/An+RTK+Query+common+scenarios+guide
 */
export const enhancedApi: typeof api = api.enhanceEndpoints<TagTypes, Definitions>({
  endpoints: {},
});

export const {
  usePrefetch,
  useGetTicketableScheduleEntrySummariesR1Query,
  useCreateFormFieldR1Mutation,
  useCreateTicketTypeR1Mutation,
  useFormFieldsByOrganizationIdR1Query,
  useLazyFormFieldsByOrganizationIdR1Query,
  useAdminOrgIsFreeTicketingOnlyR1Query,
  useLazyAdminOrgIsFreeTicketingOnlyR1Query,
  useMyTeamsR1Query,
  useLazyMyTeamsR1Query,
  useMyUserR1Query,
  useLazyMyUserR1Query,
  useAdminOrgAdminRequestsByOrgIdR1Query,
  useLazyAdminOrgAdminRequestsByOrgIdR1Query,
  useAdminOrganizationR1Query,
  useLazyAdminOrganizationR1Query,
  useAdminPaymentPlatformStatusForOrganizationR1Query,
  useLazyAdminPaymentPlatformStatusForOrganizationR1Query,
  useGetScheduleEntrySummariesR1Query,
  useLazyGetScheduleEntrySummariesR1Query,
  useAdminGetSchoolR1Query,
  useLazyAdminGetSchoolR1Query,
  useAdminGetTeamR1Query,
  useLazyAdminGetTeamR1Query,
  useAdminTeamPageGetTeamR1Query,
  useAdminGetTeamExchangeLeaguesR1Query,
  useLazyAdminGetTeamExchangeLeaguesR1Query,
  useAdminGetTeamMembersR1Query,
  useLazyAdminGetTeamMembersR1Query,
  useAdminGetGuardianRelationshipsR1Query,
  useLazyAdminGetGuardianRelationshipsR1Query,
  useLazyAdminGetGuardianRelationshipsUsersR1Query,
  useLazyAdminGetTeamGroupsR1Query,
  useGetTicketTypesByOrganizationIdR1Query,
  useLazyGetTicketTypesByOrganizationIdR1Query,
  useLazyGetTicketableScheduleEntrySummariesR1Query,
  useAdminTeamHeadersForSchoolR1Query,
  useLazyAdminTeamHeadersForSchoolR1Query,
  useAdminOrgHasTicketingEnabledR1Query,
  useLazyAdminOrgHasTicketingEnabledR1Query,
  useAdminOrganizationsForUserR1Query,
  useLazyAdminOrganizationsForUserR1Query,
  useAdminUsersForEmailsR1Query,
  useLazyAdminUsersForEmailsR1Query,
  useAdminOrgIsUsingRevenueSharingR1Query,
  useLazyAdminOrgIsUsingRevenueSharingR1Query,
  useAdminGetSchoolWithTicketingSettingsR1Query,
  useLazyAdminVenueConfigurationsForOrganizationR1Query,
  useAdminGetSsoConnectionR1Query,
  useLazyAdminGetSsoConnectionR1Query,
  useLazyAdminVenuesForOrganizationR1Query,
  useAdminCreateSeatingChartR1Mutation,
  useAdminCreateVenueR1Mutation,
  useAdminCreateVenueConfigurationR1Mutation,
  useLazyAdminPassByIdR1Query,
  useLazyAdminPassConfigByIdR1Query,
  useLazyAdminSearchPassesByTextValueR1Query,
  useLazyAdminSearchTicketGroupsByTextValueR1Query,
  useLazyAdminSearchTicketsByTextValueR1Query,
  useLazyAdminGetTicketByIdR1Query,
  useLazyAdminTicketedEventByIdWithAnalyticsR1Query,
  useLazyAdminGetTicketGroupByTicketGroupReferenceR1Query,
  useAdminResendTicketGroupEmailR1Mutation,
  useAdminSetEventsAsCompletedR1Mutation,
  useAdminSoftDeleteTicketedEventByIdR1Mutation,
  useAdminGenerateSelfServeTicketQuery,
  useLazyAdminGenerateSelfServeTicketQuery,
  useAdminUpdateSsoConnectionMutation,
  useAdminMunicipalityByIdR1Query,
  useAdminRegionalAlignmentByIdR1Query,
  useLazyAdminMunicipalityByIdR1Query,
  useLazyAdminRegionalAlignmentByIdR1Query,
  useAdminLeaguePassConfigByIdR1Query,
  useLazyAdminLeaguePassConfigByIdR1Query,
  useLazyAdminWebGetDraftPassConfigsForRenewalR1Query,
  useLazyAdminWebGetReservedSeatsForPassConfigR1Query,
  useLazyAdminWebGetSchoolTimezoneR1Query,
  useAdminWebCreatePassConfigRenewalCampaignR1Mutation,
  useAdminUserByIdR1Query,
  useLazyAdminUserByIdR1Query,
  useAdminUsersByIdsR1Query,
  useLazyAdminUsersByIdsR1Query,
  useLazyAdminGetConsentStatusUserR1Query,
  useLazyAdminGetConsentStatusR1Query,
  useLazyAdminGetConsentStatusLogsR1Query,
  useAdminOrganizationSettingsR1Query,
  useLazyAdminOrganizationSettingsR1Query,
  useAdminUpsertOrganizationSettingsMutation,
} = enhancedApi;
