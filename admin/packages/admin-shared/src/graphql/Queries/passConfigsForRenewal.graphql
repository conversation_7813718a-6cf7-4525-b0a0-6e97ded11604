query AdminWebGetDraftPassConfigsForRenewalR1($input: GetPassConfigsByOrganizationIdInput!) {
  passConfigsByOrganizationId(input: $input) {
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
    }
    edges {
      cursor
      node {
        id
        name
        passConfigStatus
        visibility
        startDate
        endDate
        eventFilters {
          venueConfigurationId
        }
        renewalCampaign {
          id
          renewalStartDate
          renewalEndDate
          timeZoneIdentifier
          renewers {
            id
            firstName
            lastName
            email
            selectableSeatIdentifiers
          }
        }
      }
    }
  }
}
