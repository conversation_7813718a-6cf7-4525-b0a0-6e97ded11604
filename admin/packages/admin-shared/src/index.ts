import { enhancedApi } from './graphql/enhancedApi';

export { AdminPrivilege } from './constants/AdminPrivilege';

export const {
  reducer: apiReducer,
  reducerPath: apiReducerPath,
  middleware,
  injectEndpoints,
  useMyTeamsR1Query,
  useMyUserR1Query,
  useAdminGetTeamR1Query,
  useAdminTeamPageGetTeamR1Query,
  useAdminGetTeamMembersR1Query,
  useLazyAdminGetTeamMembersR1Query,
  useAdminGetGuardianRelationshipsR1Query,
  useLazyAdminGetGuardianRelationshipsR1Query,
  useLazyAdminGetGuardianRelationshipsUsersR1Query,
  useLazyAdminGetTeamGroupsR1Query,
  useAdminGetSchoolR1Query,
  useLazyAdminGetSchoolR1Query,
  useAdminGetTeamExchangeLeaguesR1Query,
  useAdminOrgHasTicketingEnabledR1Query,
  useAdminOrgIsFreeTicketingOnlyR1Query,
  useAdminOrgIsUsingRevenueSharingR1Query,
  useAdminPaymentPlatformStatusForOrganizationR1Query,
  useAdminOrganizationR1Query,
  useGetScheduleEntrySummariesR1Query,
  useLazyGetTicketableScheduleEntrySummariesR1Query,
  useLazyAdminOrgAdminRequestsByOrgIdR1Query,
  useAdminOrganizationsForUserR1Query,
  useAdminUsersForEmailsR1Query,
  useLazyAdminOrgScheduleReferenceSubmissionsR1Query,
  useAdminTeamHeadersR1Query,
  useCreateTicketTypeR1Mutation,
  useLazyAdminGetTeamR1Query,
  useGetTicketTypesByOrganizationIdR1Query,
  useCreateFormFieldR1Mutation,
  useFormFieldsByOrganizationIdR1Query,
  useAdminGetSchoolWithTicketingSettingsR1Query,
  useAdminTicketingCreateTicketedEventsR1Mutation,
  useLazyAdminVenueConfigurationsForOrganizationR1Query,
  useAdminGetSsoConnectionR1Query,
  useLazyAdminGetSsoConnectionR1Query,
  useLazyAdminVenuesForOrganizationR1Query,
  useAdminCreateSeatingChartR1Mutation,
  useAdminCreateVenueR1Mutation,
  useAdminCreateVenueConfigurationR1Mutation,
  useLazyAdminPassByIdR1Query,
  useLazyAdminPassConfigByIdR1Query,
  useLazyAdminSearchPassesByTextValueR1Query,
  useLazyAdminSearchTicketGroupsByTextValueR1Query,
  useLazyAdminSearchTicketsByTextValueR1Query,
  useLazyAdminGetTicketByIdR1Query,
  useLazyAdminTicketedEventByIdWithAnalyticsR1Query,
  useLazyAdminGetTicketGroupByTicketGroupReferenceR1Query,
  useAdminResendTicketGroupEmailR1Mutation,
  useAdminSetEventsAsCompletedR1Mutation,
  useAdminSoftDeleteTicketedEventByIdR1Mutation,
  useAdminGenerateSelfServeTicketQuery,
  useLazyAdminGenerateSelfServeTicketQuery,
  useAdminUpdateSsoConnectionMutation,
  useAdminMunicipalityByIdR1Query,
  useAdminRegionalAlignmentByIdR1Query,
  useLazyAdminMunicipalityByIdR1Query,
  useLazyAdminRegionalAlignmentByIdR1Query,
  useAdminLeaguePassConfigByIdR1Query,
  useLazyAdminLeaguePassConfigByIdR1Query,
  useLazyAdminWebGetDraftPassConfigsForRenewalR1Query,
  useLazyAdminWebGetReservedSeatsForPassConfigR1Query,
  useLazyAdminWebGetSchoolTimezoneR1Query,
  useAdminWebCreatePassConfigRenewalCampaignR1Mutation,
  useAdminGetSchoolsR1Query,
  useLazyAdminGetSchoolsR1Query,
  useAdminUserByIdR1Query,
  useLazyAdminUserByIdR1Query,
  useAdminUsersByIdsR1Query,
  useLazyAdminUsersByIdsR1Query,
  useAdminTeamHeadersForSchoolR1Query,
  useLazyAdminTeamHeadersForSchoolR1Query,
  useLazyAdminGetConsentStatusUserR1Query,
  useLazyAdminGetConsentStatusR1Query,
  useLazyAdminGetConsentStatusLogsR1Query,
  useAdminGetFormsForProgramR1Query,
  useAdminGetFilteredFormsR1Query,
  useLazyAdminGetFilteredFormsR1Query,
  useLazyAdminGetFormsForProgramR1Query,
  useLazyAdminOrgProgramsByOrganizationR1Query,
  useAdminOrganizationSettingsR1Query,
  useLazyAdminOrganizationSettingsR1Query,
  useAdminUpsertOrganizationSettingsMutation,
} = enhancedApi;

export { restEndpointHelper, RestMethod } from './rest/restEndpointHelper';

export { buildAdminUrl } from './utilities/buildAdminUrl';

export { encodeOrganizationId, decodeOrganizationId, decodeTeamId } from './utilities/encodingUtils';

export { getNowUTCString, getXYearsFromNowDateString } from './utilities/dateTimeUtils';

export { useOrgPageTitle } from './hooks/useOrgPageTitle';
export { useGetAllFormFieldsByOrganizationId } from './hooks/useGetAllFormFieldsByOrganizationId';

export { defineEnvConfigGroup, globalEnvConfig } from './config/index';

export {
  AccountStatus,
  type FeatureToggle,
  type GetScheduleEntryPublicSummariesInput,
  type ScheduleEntryPublicSummary,
  type Team,
  type LeaguePassConfig,
  type RegionalAlignment,
  type Municipality,
  type User,
  type UserConsentStatus,
  type ConsentStatusState,
  type ConsentStatusType,
  type UserConsentStatusLog,
  type ProgramForm,
  type CustomForm,
  type OrganizationProgram,
  type OwnerType,
  type AdminGetFilteredFormsR1Query,
  type AdminGetFormsForProgramR1Query,
  type AdminOrgProgramsByOrganizationR1Query,
} from './graphql/api.generated';

export { openUrlInNewTab, reloadPage } from './utilities/windowUtils';

export { LeagueEntityType } from './types/shared';
