import React, { type ReactNode, useCallback, useEffect, useState } from 'react';

import { useSearchParams } from 'react-router-dom';

import { getMarvelOrigin } from '@hudl/frontends-environment';
import { Button, Input, Link, Text, Title } from '@hudl/uniform-web';

import { ConsentStatusLogs } from '../../components/ConsentStatusLogs/ConsentStatusLogs';
import { useGetConsentStatus, useGetConsentStatusUser } from './hooks';

import styles from './App.module.css';

const buildMarvelAdminUrl = (relativeUrl: string): string => {
  return `${getMarvelOrigin()}${relativeUrl}`;
};

function AppComponent(): React.JSX.Element {
  // If we can, pull user consent status identifiers out of the URL params
  const [searchParams] = useSearchParams();
  const paramsUser = searchParams.get('user') || '';
  const paramsUserId = searchParams.get('userid') || '';
  const paramsId = searchParams.get('id') || '';
  const paramsEmail = searchParams.get('email') || '';

  // These are the main identifiers, from either URL params or specified by the Admin user.
  const [userId, setUserId] = useState<string>(paramsUser || paramsUserId || paramsId);
  const [userEmail, setUserEmail] = useState<string>(paramsEmail);

  // Input form values
  const [userIdInput, setUserIdInput] = useState<string>('');
  const [userEmailInput, setUserEmailInput] = useState<string>('');

  // API calls to get user and consent status
  const { user, isLoading: isLoadingUser } = useGetConsentStatusUser(userId, userEmail);
  const { consentStatus, isLoading: isLoadingConsentStatus } = useGetConsentStatus(userEmail);
  useEffect(() => {
    if (user?.emailAddress) {
      setUserEmail(user.emailAddress);
    }
  }, [user]);

  const saveUserIdFromForm = useCallback(() => {
    setUserId(userIdInput);
  }, [userIdInput]);
  const saveUserEmailFromForm = useCallback(() => {
    setUserEmail(userEmailInput);
  }, [userEmailInput]);

  const renderInputForm = (): ReactNode => {
    return (
      <div className={styles.formContainer}>
        <div className={styles.formRowContainer}>
          <Input placeholder="(user ID)" onChange={(value) => setUserIdInput(value)} />
          <Button onPress={saveUserIdFromForm}>Look Up User Id</Button>
        </div>
        <div className={styles.formRowContainer}>
          <Input placeholder="(user email)" onChange={(value) => setUserEmailInput(value)} />
          <Button onPress={saveUserEmailFromForm}>Look Up Email Address</Button>
        </div>
      </div>
    );
  };

  const renderConsentStatus = (): ReactNode => {
    if (isLoadingUser) {
      return <Text>Loading user...</Text>;
    }
    if (isLoadingConsentStatus) {
      return <Text>Loading consent status...</Text>;
    }
    if (!consentStatus) {
      return <Text>No consent status for user.</Text>;
    }

    const userIdComponent = userId ? (
      <Link type="article" href={buildMarvelAdminUrl(`/user/${userId}`)}>
        {userId}
      </Link>
    ) : (
      <Text className={styles.unknownUserIdComponent}>(unknown)</Text>
    );

    return (
      <div>
        <Text>User ID: {userIdComponent}</Text>
        <Text>Guardian email: {consentStatus.providerEmail}</Text>
        <Text>Athlete email: {consentStatus.subjectEmail}</Text>
        <Text>State: {consentStatus.state}</Text>
        <Text>Type: {consentStatus.type}</Text>
        <Text>Is Provider Verified: {consentStatus.providerVerified ? 'Yes' : 'No'}</Text>
        <Text>Created At: {consentStatus.dateCreated}</Text>
        <Text>Updated At: {consentStatus.dateUpdated}</Text>
        <ConsentStatusLogs userConsentStatusId={consentStatus?.userConsentStatusId} />
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <Title as="h3" className={styles.headline}>
        Consent Status
      </Title>
      {!userId && !userEmail ? renderInputForm() : renderConsentStatus()}
    </div>
  );
}

/**
 * App exports the component itself, a loader for getting data before rendering, and slices to inject into the Redux store.
 */
export const App = {
  Component: AppComponent,
  title: 'Example',
};
