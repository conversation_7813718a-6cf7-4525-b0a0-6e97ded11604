import React, { useCallback, useState } from 'react';

import { Button, DataTable, Spinner, Text } from '@hudl/uniform-web';

import { useGetConsentStatusLogs } from './hooks';

interface ConsentStatusLogsProps {
  userConsentStatusId: string;
}

const getUpdatedFieldLabel = (field: string): string => {
  switch (field) {
    case 's':
      return 'Status';
    case 'pv':
      return 'Provider Verified';
    case 'p':
      return 'Provider Email';
    default:
      return field;
  }
};

export function ConsentStatusLogs({ userConsentStatusId }: ConsentStatusLogsProps): React.JSX.Element {
  const [shouldLoadLogs, setShouldLoadLogs] = useState<boolean>(false);
  const { consentStatusLogs, isLoading: isLoadingConsentStatusLogs } = useGetConsentStatusLogs(
    shouldLoadLogs,
    userConsentStatusId
  );

  const onClickLoadLogs = useCallback(() => {
    setShouldLoadLogs(true);
  }, [setShouldLoadLogs]);

  if (isLoadingConsentStatusLogs) {
    return <Spinner />;
  }

  if (shouldLoadLogs && !isLoadingConsentStatusLogs && (!consentStatusLogs || !consentStatusLogs.length)) {
    return <Text>No consent status logs found.</Text>;
  }

  if (consentStatusLogs?.length) {
    return (
      <DataTable
        columnContentTypes={['text', 'text', 'text', 'text', 'text']}
        columnHeaders={['UserConsentStatusId', 'Updated Field', 'New Value Assigned', 'Date Modified', 'IP Address']}
        rows={consentStatusLogs.map((log) => ({
          id: log.userConsentStatusId,
          data: [
            log.userConsentStatusId,
            getUpdatedFieldLabel(log.updatedField),
            log.newValue,
            new Date(log.dateModified).toLocaleString(),
            log.ipAddress || '(no ip)',
          ],
        }))}
      />
    );
  }

  return (
    <div>
      <Text>Consent Status Logs</Text>
      <Button onPress={onClickLoadLogs}>Load Logs</Button>
    </div>
  );
}
