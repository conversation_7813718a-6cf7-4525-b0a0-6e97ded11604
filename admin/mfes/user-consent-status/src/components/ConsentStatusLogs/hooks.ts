import { useEffect, useState } from 'react';

import { useLazyAdminGetConsentStatusLogsR1Query } from '@local/admin-shared';

interface UserConsentStatusLog {
  userConsentStatusId: string;
  updatedField: string;
  newValue: string;
  dateModified: string | number | Date;
  ipAddress?: string;
}

interface UseGetConsentStatusLogsResponse {
  consentStatusLogs: UserConsentStatusLog[] | null;
  isLoading: boolean;
}
export const useGetConsentStatusLogs = (
  shouldLoadLogs: boolean,
  userConsentStatusId: string
): UseGetConsentStatusLogsResponse => {
  const [getAdminConsentStatusLogsFn, getAdminConsentStatusLogsFnStatus] = useLazyAdminGetConsentStatusLogsR1Query();
  const [consentStatusLogs, setConsentStatusLogs] = useState<UserConsentStatusLog[] | null>(null);

  useEffect(() => {
    if (!shouldLoadLogs || !userConsentStatusId) {
      return;
    }
    const fetchData = async (): Promise<void> => {
      const response = await getAdminConsentStatusLogsFn({ userConsentStatusId: userConsentStatusId });
      const mappedConsentStatusLogs = response.data?.userConsentStatusLogs
        ? response.data.userConsentStatusLogs
            .filter((log): log is NonNullable<typeof log> => log != null)
            .map((log) => ({
              userConsentStatusId: log.userConsentStatusId ?? '',
              updatedField: log.updatedField ?? '',
              newValue: log.newValue ?? '',
              dateModified:
                typeof log.dateModified === 'string' ||
                typeof log.dateModified === 'number' ||
                log.dateModified instanceof Date
                  ? log.dateModified
                  : '',
              ipAddress: log.ipAddress ?? '',
            }))
        : null;
      setConsentStatusLogs(mappedConsentStatusLogs);
    };
    fetchData().catch((e) => {
      console.log('Error fetching consent status logs. ' + e);
    });
  }, [getAdminConsentStatusLogsFn, shouldLoadLogs, userConsentStatusId]);

  return {
    consentStatusLogs: consentStatusLogs,
    isLoading: getAdminConsentStatusLogsFnStatus.isLoading,
  };
};
