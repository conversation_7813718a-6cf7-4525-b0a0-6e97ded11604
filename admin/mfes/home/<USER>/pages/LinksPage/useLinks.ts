import { useCallback, useMemo } from 'react';

import { getMarvelOrigin } from '@hudl/frontends-environment';
import { AdminPrivilege, useMyUserR1Query } from '@local/admin-shared';

import { useMarkCutupPublishedMutation } from '../../store/extendedApiSlice';

type Panel = Array<Section>;
export interface Section {
  title: string;
  links: Array<{
    hasAccess: boolean;
    title: string;
    href?: string;
    onClick?: () => void;
  }>;
}

function buildAdminUrl(relativeUrl: string): string {
  return `${getMarvelOrigin()}${relativeUrl}`;
}

export const useLinks = (): { isLoading: boolean; panels: Array<Panel> } => {
  const { isLoading, data } = useMyUserR1Query({});
  const [markCutupPublished] = useMarkCutupPublishedMutation();

  const adminPrivileges = useMemo(() => new Set(data?.me?.adminPrivileges ?? []), [data?.me?.adminPrivileges]);

  // Having these links make API calls is not good practice.
  // This kind of behavior should be on an admin page dedicated to Cutups, not here if possible
  const handleCutupPublish = useCallback(async () => {
    const cutupId = prompt('CutupId:');
    const isValidInput = cutupId !== null && cutupId.length && parseInt(cutupId, 10);

    if (cutupId === null || !isValidInput) {
      alert('Invalid cutupId. It should be an integer.');
      return;
    }

    const response = await markCutupPublished(cutupId);

    if ('data' in response && response.data) {
      alert('exchange away');
    } else {
      alert('failed');
    }
  }, [markCutupPublished]);

  const hasAdminPrivilege = useCallback(
    (adminPrivilege: number) => adminPrivileges.has(adminPrivilege),
    [adminPrivileges]
  );

  const panels = useMemo(() => {
    const leftPanel: Panel = [
      {
        title: 'Signup and Billing',
        links: [
          {
            hasAccess: true,
            href: buildAdminUrl('/admin/internalsignup/signup'),
            title: 'Internal Signup Tool',
          },
          {
            hasAccess: true,
            href: '/invoice-link-lookup',
            title: 'Invoice Link Lookup',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SearchSalesRepresentatives),
            href: buildAdminUrl('/admin/salesforce/territories'),
            title: 'Search Sales Representatives',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.CreateAccess),
            href: buildAdminUrl('/admin/provisioningorchestrator/search'),
            title: 'View Provisioning Operations',
          },
        ],
      },
      {
        title: 'Orders',
        links: [
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.DVD_ORDER_MANAGEMENT),
            href: buildAdminUrl('/admin/highlights/dvds/'),
            title: 'Highlight Order Management',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.HardwareInventoryView),
            href: buildAdminUrl('/admin/hardware/orders'),
            title: 'Hardware Orders Management',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.HardwareInventoryView),
            href: buildAdminUrl('/admin/hardware/requests'),
            title: 'Hardware Requests Management',
          },
        ],
      },
      {
        title: 'Hudl Focus',
        links: [
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.AutomaticCaptureDetails),
            href: buildAdminUrl(
              '/admin/automaticcapture/installations?providerType=1&testInstallations=false&filterPreset=3'
            ),
            title: 'View Cameras',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.AutomaticCaptureDetails),
            href: buildAdminUrl('/admin/automaticcapture/publish-events?providerType=1&testPublishEvents=false'),
            title: 'View Publish Events',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.AutomaticCaptureDetails),
            href: buildAdminUrl('/admin/automaticcapture/dashboard'),
            title: 'View Dashboard',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.AutomaticCaptureDetails),
            href: buildAdminUrl('/admin/automaticcapture/current-livestreams'),
            title: 'View Current Livestreams',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.AutomaticCaptureSupport),
            href: buildAdminUrl('/admin/automaticcapture/investigator'),
            title: 'Support Investigator',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.AutomaticCaptureQualityControl),
            href: buildAdminUrl('/admin/automaticcapture/quality-control'),
            title: 'Quality Control',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.AutomaticCaptureManagement),
            href: buildAdminUrl('/admin/automaticcapture/simulator'),
            title: 'Simulator',
          },
        ],
      },
      {
        title: 'Global Sports Library',
        links: [
          {
            hasAccess:
              hasAdminPrivilege(AdminPrivilege.GSLOperations) || hasAdminPrivilege(AdminPrivilege.GSLDeveloper),
            href: buildAdminUrl('/admin/gslconnectors/search'),
            title: 'Search',
          },
          {
            hasAccess:
              hasAdminPrivilege(AdminPrivilege.GSLOperations) || hasAdminPrivilege(AdminPrivilege.GSLDeveloper),
            href: buildAdminUrl('/admin/nautilus/entity-catalog'),
            title: 'Entity Catalog',
          },
          {
            hasAccess:
              hasAdminPrivilege(AdminPrivilege.GSLOperations) || hasAdminPrivilege(AdminPrivilege.GSLDeveloper),
            href: '/gsl/entity-matching',
            title: 'Entity Matching Tool',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.GSLDeveloper),
            href: buildAdminUrl('/admin/atlas/indices-manager'),
            title: 'Atlas Indices Manager',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.GSLDeveloper),
            href: buildAdminUrl('/admin/mediasearch/indices-manager'),
            title: 'MediaSearch Indices Manager',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.GSLDeveloper),
            href: buildAdminUrl('/admin/momentssearch/indices-manager'),
            title: 'MomentsSearch Indices Manager',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.GSLDeveloper),
            href: buildAdminUrl('/admin/gslaccess/competition-collections/upsert-collection'),
            title: 'Manage Competition Collections',
          },
          {
            hasAccess: true,
            href: buildAdminUrl('/admin/gslaccess/instat/user/search'),
            title: 'Manage InStat Users GSL Access',
          },
          {
            hasAccess:
              hasAdminPrivilege(AdminPrivilege.GSLOperations) || hasAdminPrivilege(AdminPrivilege.GSLDeveloper),
            href: '/gsl/access/search',
            title: 'Manage GSL permissions',
          },
        ],
      },
      {
        title: 'Fan Experience',
        links: [
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ManageFanExperience),
            href: '/leagues',
            title: 'Search and Manage Leagues',
          },
        ],
      },
    ];

    const middlePanel: Panel = [
      {
        title: 'System Management',
        links: [
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ANNOUNCEMENTS),
            href: buildAdminUrl('/system/announcements.aspx'),
            title: 'Announcements',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.FeedManualEntry),
            href: buildAdminUrl('/admin/feedproducer/hudl-accounts'),
            title: 'Publish To Hudl',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.FeedManualEntry),
            href: buildAdminUrl('/admin/feedproducer/content-series-metadata'),
            title: 'Content Series Metadata',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.FeedManagement),
            href: buildAdminUrl('/admin/feedproducer/feedmanagement/managepost'),
            title: 'Manage Feed Post',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.FeedManagement),
            href: buildAdminUrl('/admin/feedconsumer/cacheutility'),
            title: 'Feed Cache Utility',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.FeedManageFeaturedAccounts),
            href: buildAdminUrl('/admin/feedproducer/featured-accounts'),
            title: 'Manage Feed Featured Accounts',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.FeedAutoFollowManagement),
            href: buildAdminUrl('/admin/feedproducer/auto-follow-accounts'),
            title: 'Manage Feed Auto-Follow Accounts',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.CommunitySearchManagement),
            href: buildAdminUrl('/admin/communitysearch/manage'),
            title: 'Manage Community Search',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ManageAdminSearch),
            href: buildAdminUrl('/admin/adminsearch/indices-manager'),
            title: 'Manage Admin Search',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ViewLibraryElasticsearch),
            href: buildAdminUrl('/admin/library/elasticsearch'),
            title: 'Manage Library Elasticsearch',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ManageAssistQueueSearch),
            href: buildAdminUrl('/admin/assistqueuesearch/indices-manager'),
            title: 'Manage Assist Queue Search',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.RecommendationsManagement),
            href: buildAdminUrl('/admin/recommendations/overview'),
            title: 'Manage Recommendations',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ManageBannerAds),
            href: buildAdminUrl('/banner-ads'),
            title: 'Banner Ads',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SYS_CONFIGURATION),
            href: buildAdminUrl('/system/configuration.aspx'),
            title: 'Manage system configuration',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.BATCH_JOBS),
            href: buildAdminUrl('/batch-jobs'),
            title: 'Scheduled Jobs',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.MANAGE_CACHE),
            href: buildAdminUrl('/system/cache.aspx'),
            title: 'Cache Utility',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.NODE),
            href: buildAdminUrl('/system/node.aspx'),
            title: 'Node Dashboard',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.MANAGE_QUEUES),
            href: buildAdminUrl('/system/queues.aspx'),
            title: 'Manage Queues',
          },
          {
            hasAccess:
              hasAdminPrivilege(AdminPrivilege.ManageLeagueExchange) ||
              hasAdminPrivilege(AdminPrivilege.ViewLeagueExchange) ||
              hasAdminPrivilege(AdminPrivilege.ManagePoolExchange),
            href: buildAdminUrl('/admin/exchanges/pools'),
            title: 'Manage Exchange Pools',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.MANAGE_DVMS),
            href: buildAdminUrl('/admin/dvms'),
            title: 'Manage DVMS',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.MANAGE_DVMS_VIDEO_OFFSETS),
            href: buildAdminUrl('/admin/dvms/assets/manage-offsets'),
            title: 'Manage DVMS Video Offsets',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.MANAGE_APP_UPDATES),
            href: buildAdminUrl('/appupdate'),
            title: 'Manage Application Updates',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.MIGRATE_CURRENT_SEASON),
            href: buildAdminUrl('/migrate-season'),
            title: 'Migrate Teams to the new Season',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ManagePresentationDownloads),
            href: buildAdminUrl('/presentations'),
            title: 'Manage Presentation Downloads',
          },
          {
            hasAccess: true,
            href: buildAdminUrl('/admin/emails/manager'),
            title: 'Manage Emails',
          },
        ],
      },
      {
        title: 'Feature Toggles',
        links: [
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SET_FEATURE_PRIVILEGES),
            href: buildAdminUrl('/FeaturesList'),
            title: 'View List of All Team Feature Toggles',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SET_FEATURE_PRIVILEGES),
            href: buildAdminUrl('/admin/featuretoggles/bulkupdate/hudlteam'),
            title: 'Bulk Add/Remove Team Features',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SET_FEATURE_PRIVILEGES),
            href: buildAdminUrl('/admin/featuretoggles/bulkupdate/hudluser'),
            title: 'Bulk Add/Remove User Features',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SET_FEATURE_PRIVILEGES),
            href: buildAdminUrl('/admin/featuretoggles/bulkupdate/hudlorganization'),
            title: 'Bulk Add/Remove Organization Features',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ViewDefaultFeaturePrivileges),
            href: buildAdminUrl('/admin/togglesmanagement/defaultfeatures'),
            title: 'Default Team Feature Toggles',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SET_PRIVILEGES),
            href: buildAdminUrl('/userfeatures'),
            title: 'Bulk Update User Admin Privileges',
          },
        ],
      },
    ];

    const rightPanel = [
      {
        title: 'Miscellaneous',
        links: [
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.RESET_USER_PASSWORD),
            href: buildAdminUrl('/client/ResetPassword.aspx'),
            title: `Reset user's password`,
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.FindMediaAndUploads),
            href: buildAdminUrl('/admin/media/search'),
            title: 'Find Media/Uploads',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.FinalizeLeroyEvent),
            href: buildAdminUrl('/admin/media/video-diagnostics/'),
            title: 'Video Diagnostics',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SEND_TEST_EMAIL),
            href: buildAdminUrl('/system/email.aspx'),
            title: 'Send a Test Email',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.REENCODE_CUTUP),
            href: buildAdminUrl('/system/reencode.aspx'),
            title: 'Re-encode a Cutup',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.REENCODE_CUTUP),
            href: buildAdminUrl('/client/ReorderAngles.aspx'),
            title: 'Re-order Clip Angles',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.MANAGE_CUTUPS),
            title: 'Mark a cutup as published',
            onClick: handleCutupPublish,
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SearchFeedUsers),
            href: buildAdminUrl('/admin/communitysearch/feed-users'),
            title: 'Search feed users',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SearchCommunityContent),
            href: buildAdminUrl('/admin/communitysearch/community-content'),
            title: 'Search community content',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SEARCH_HIGHLIGHTS),
            href: buildAdminUrl('/admin/highlights/highlight-reel'),
            title: 'Highlight Reels',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SearchHighlightsExternal),
            href: buildAdminUrl('/admin/highlights/highlight-clips/search'),
            title: 'Highlight Clips',
          },
          {
            hasAccess:
              hasAdminPrivilege(AdminPrivilege.GameRecapHighlightManagement) ||
              hasAdminPrivilege(AdminPrivilege.HighlightAutoGenManagement),
            href: buildAdminUrl('/admin/highlights/phoenix/beats'),
            title: 'Phoenix Beats',
          },
          {
            hasAccess:
              hasAdminPrivilege(AdminPrivilege.GameRecapHighlightManagement) ||
              hasAdminPrivilege(AdminPrivilege.HighlightAutoGenManagement),
            href: buildAdminUrl('/admin/highlights/autogen'),
            title: 'Auto Gen Highlights',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.MANAGE_CUTUPS),
            href: buildAdminUrl('/client/RestoreData.aspx'),
            title: 'Restore Cutups, Presentations, Reports, Practice Scripts, Installs, Play Tools',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ViewWorld),
            href: buildAdminUrl('/world/Index'),
            title: 'Hudl World',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ManageFreeRecruiters),
            href: buildAdminUrl('/FreeRecruiters'),
            title: 'Manage Free Recruiter Status',
          },
          {
            hasAccess: true,
            href: '/recruit/recruitplus',
            title: 'Recruit Plus',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.RefreshSearch),
            href: buildAdminUrl('/RefreshSearch'),
            title: 'Refresh Search',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.PerformanceCenterManageSettings),
            href: buildAdminUrl('/admin/performancecenter/settings'),
            title: 'Performance Center Manage Settings',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ViewGlobalization),
            href: buildAdminUrl('/admin/globalization/translations'),
            title: 'Globalization',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SET_FEATURE_PRIVILEGES),
            href: buildAdminUrl('/TeamSettings'),
            title: 'Bulk Add/Remove Team Settings',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ViewAssistAugmentation),
            href: buildAdminUrl('/admin/augmentation'),
            title: 'Augmentation',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.ManageFanSearch),
            href: buildAdminUrl('/admin/fansearch/indexing'),
            title: 'Manage Fan Search',
          },
          {
            hasAccess: true,
            href: '/ticketing',
            title: 'Ticketing Tools',
          },
          {
            hasAccess: true,
            href: '/revenue-share/tipalti-status',
            title: 'Check Tipalti Status',
          },
          {
            hasAccess: hasAdminPrivilege(AdminPrivilege.SEARCH_USERS),
            href: '/user/consent-status',
            title: 'User Consent Status',
          },
        ],
      },
    ];

    return [leftPanel, middlePanel, rightPanel];
  }, [handleCutupPublish, hasAdminPrivilege]);

  // Filter out empty sections and links without access
  const parsedPanels = useMemo(() => {
    return panels
      .filter((sections) => sections.length > 0 && sections.some((s) => s?.links?.some((l) => l.hasAccess)))
      .map((sections) => {
        const parsedSections = sections.map(
          (section): Section => ({
            title: section.title,
            links: section.links.filter((link) => link.hasAccess),
          })
        );

        // Make sure each section has at least one link with access
        return parsedSections.filter(
          (section) => section.links.length > 0 && section.links.some((link) => link.hasAccess)
        );
      });
  }, [panels]);

  return { isLoading, panels: parsedPanels };
};
