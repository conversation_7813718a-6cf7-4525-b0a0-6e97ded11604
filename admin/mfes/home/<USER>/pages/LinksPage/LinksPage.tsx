import React from 'react';

import AdminLink from '../../components/AdminLink/AdminLink';
import AdminLinkSection from '../../components/AdminLinkSection/AdminLinkSection';
import SkeletonPanel from '../../components/SkeletonPanel/SkeletonPanel';
import { useLinks } from './useLinks';

import styles from '../App/App.module.css';

function LinksPage(): React.JSX.Element {
  const { isLoading, panels } = useLinks();

  if (isLoading) {
    return (
      <>
        <div className={styles.panel}>
          <SkeletonPanel />
        </div>
        <div className={styles.panel}>
          <SkeletonPanel />
        </div>
        <div className={styles.panel}>
          <SkeletonPanel />
        </div>
      </>
    );
  }

  return (
    <>
      {panels.map((sections) => (
        <div key={`panel-${sections[0].title}`} className={styles.panel}>
          {sections.map((section) => (
            <AdminLinkSection key={section.title} title={section.title} className={styles.section}>
              {section.links.map((link) => (
                <AdminLink key={link.title} href={link.href} title={link.title} onClick={link.onClick} />
              ))}
            </AdminLinkSection>
          ))}
        </div>
      ))}
    </>
  );
}

export default LinksPage;
