export type Renewer = {
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  selectableSeats: string[];
};

export type ValidatedRenewer = Renewer & {
  invalidFirstName: boolean;
  invalidLastName: boolean;
  invalidEmail: boolean;
  invalidSeats: string[];
  duplicateSeats: string[];
  isDuplicateRenewer: boolean;
};

export type RenewalDate = {
  startDate: string | undefined;
  startTime: string | undefined;
  endDate: string | undefined;
  endTime: string | undefined;
  timezone: string | undefined;
};

export enum RenewalCampaignToolState {
  Create = 'Create',
  View = 'View',
}
