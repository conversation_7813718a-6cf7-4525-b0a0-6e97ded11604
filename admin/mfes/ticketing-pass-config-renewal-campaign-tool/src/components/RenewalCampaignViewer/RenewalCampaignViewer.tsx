import { IconUiNavigationBackArrow } from '@hudl/uniform-web';
import { Button } from '@hudl/uniform-web-button-legacy';
import {
  CenteredLoadingSpinner,
  getDateWithoutTimeString,
  getTimeWithoutDateString,
  type PassConfig,
  useEmailValidation,
} from '@local/ticketing-admin-shared';

import { useReservedSeatsForPassConfigQuery } from '../../hooks/useReservedSeatsForPassConfig';
import type { RenewalDate, Renewer, ValidatedRenewer } from '../../types/renewalCampaignToolTypes';
import { validateRenewers } from '../../utilities/renewerValidator';
import { RenewalDateSelector } from '../RenewalDateSelector/RenewalDateSelector';
import { RenewerList } from '../RenewerList/RenewerList';

import styles from './RenewalCampaignViewer.module.scss';

type Props = {
  passConfig: PassConfig;
  navigateBackToPassList: () => void;
};

export function RenewalCampaignViewer(props: Props): React.JSX.Element {
  const { passConfig, navigateBackToPassList } = props;
  const validEmailFunc = useEmailValidation();

  const { isLoading: seatingChartSeatsLoading, data: seatingChartSeats } = useReservedSeatsForPassConfigQuery(
    passConfig.id
  );

  const renewalDate: RenewalDate = {
    startDate: getDateWithoutTimeString(passConfig.renewalCampaign.renewalStartDate),
    endDate: getDateWithoutTimeString(passConfig.renewalCampaign.renewalEndDate),
    startTime: getTimeWithoutDateString(passConfig.renewalCampaign.renewalStartDate),
    endTime: getTimeWithoutDateString(passConfig.renewalCampaign.renewalEndDate),
    timezone: passConfig.renewalCampaign.timeZoneIdentifier,
  };

  const renewers: Renewer[] =
    passConfig.renewalCampaign?.renewers?.map((renewer) => ({
      id: renewer.id,
      email: renewer.email,
      firstName: renewer.firstName,
      lastName: renewer.lastName,
      passConfigId: passConfig.id,
      selectableSeats: renewer.selectableSeatIdentifiers,
    })) ?? [];

  const getValidatedRenewers = (): ValidatedRenewer[] => {
    if (!renewers || !seatingChartSeats) {
      return [];
    }

    return validateRenewers(renewers, seatingChartSeats, validEmailFunc);
  };

  const validatedRenewers = getValidatedRenewers();

  if (seatingChartSeats === undefined || seatingChartSeatsLoading) {
    return <CenteredLoadingSpinner />;
  }

  return (
    <div className={styles.container}>
      <div>
        <Button
          buttonType="secondary"
          size="small"
          onClick={navigateBackToPassList}
          icon={<IconUiNavigationBackArrow />}
        >
          Back to Pass Configs
        </Button>
      </div>
      {renewers.length > 0 && (
        <>
          <div className={styles.renewerListContainer}>
            <RenewerList validatedRenewers={validatedRenewers} />
          </div>
          <RenewalDateSelector renewalDate={renewalDate} setRenewalDate={() => {}} isReadOnly />
        </>
      )}
    </div>
  );
}
