import { useCallback, useMemo } from 'react';

import {
  Button,
  DataTable,
  Headline,
  IconConfirmation,
  IconCritical,
  IconWarning,
  type Rows,
  Text,
  type TextColor,
} from '@hudl/uniform-web';
import { openUrlInNewTab } from '@local/admin-shared';
import { getPassConfigRenewalUrl } from '@local/ticketing-admin-shared';

import { IconDuplicate } from '../../icons/IconDuplicate';
import { IconError } from '../../icons/IconError';
import type { ValidatedRenewer } from '../../types/renewalCampaignToolTypes';
import { renewerHasErrors } from '../../utilities/renewerValidator';

import styles from './RenewerList.module.scss';

type Props = {
  validatedRenewers: ValidatedRenewer[];
};

export function RenewerList(props: Props): React.JSX.Element {
  const { validatedRenewers } = props;

  const copyTextToClipboard = useCallback((text: string): void => {
    navigator.clipboard.writeText(text).catch((err) => {
      console.error('Failed to copy text: ', err);
    });
  }, []);

  const renewersWithErrorsCount = useMemo(() => {
    return validatedRenewers.reduce((acc, renewer) => {
      if (
        renewer.invalidSeats.length > 0 ||
        renewer.duplicateSeats.length > 0 ||
        renewer.invalidEmail ||
        renewer.invalidFirstName ||
        renewer.invalidLastName
      ) {
        return acc + 1;
      }
      return acc;
    }, 0);
  }, [validatedRenewers]);

  const allRenewersHaveIds = useMemo(
    () => validatedRenewers.every((renewer) => renewer.id && renewer.id.trim().length > 0),
    [validatedRenewers]
  );

  const buildRenewerRows = useCallback((): Rows => {
    const getRenewerStatusIcon = (renewer: ValidatedRenewer): React.JSX.Element => {
      if (renewerHasErrors(renewer)) {
        return <IconCritical color="critical" size="large" />;
      }
      if (!renewer.id && renewer.isDuplicateRenewer) {
        return <IconWarning color="warning" size="large" />;
      }
      return <IconConfirmation color="confirmation" size="large" />;
    };

    return validatedRenewers.map((renewer) => {
      const renderRenewerStatus = (): React.JSX.Element => {
        return <div>{getRenewerStatusIcon(renewer)}</div>;
      };

      const renderRenewerSeats = (): React.JSX.Element => {
        if ((renewer.selectableSeats?.length ?? 0) === 0) {
          return (
            <div className={styles.renewerSeat}>
              <div className={styles.renewerSeatErrors} />
              <Text color="critical">{'<no seats selected>'}</Text>
            </div>
          );
        }

        return (
          <div>
            {[...renewer.selectableSeats]
              .sort((a, b) => a.localeCompare(b))
              .map((seat) => {
                const isSeatWithError = renewer.invalidSeats.includes(seat) || renewer.duplicateSeats.includes(seat);
                return (
                  // eslint-disable-next-line react/jsx-key -- no keys
                  <div className={styles.renewerSeat}>
                    <div className={styles.renewerSeatErrors}>
                      {renewer.invalidSeats.includes(seat) && <IconError color="critical" size="small" />}
                      {renewer.duplicateSeats.includes(seat) && <IconDuplicate color="critical" size="small" />}
                    </div>
                    <Text color={isSeatWithError ? 'critical' : 'confirmation'}>{seat}</Text>
                  </div>
                );
              })}
          </div>
        );
      };

      const getRenewerTextColor = (isPropertyInvalid: boolean): TextColor => {
        if (isPropertyInvalid) {
          return 'critical';
        }

        if (!renewer.id && renewer.isDuplicateRenewer) {
          return 'warning';
        }

        return 'default';
      };

      const baseData = [
        { value: renewerHasErrors, element: renderRenewerStatus() },
        {
          value: renewer.email,
          element: (
            <Text color={getRenewerTextColor(renewer.invalidEmail)}>
              {renewer.email.length ? renewer.email : '<empty>'}
            </Text>
          ),
        },
        {
          value: renewer.firstName,
          element: (
            <Text color={getRenewerTextColor(renewer.invalidFirstName)}>
              {renewer.firstName.length ? renewer.firstName : '<empty>'}
            </Text>
          ),
        },
        {
          value: renewer.lastName,
          element: (
            <Text color={getRenewerTextColor(renewer.invalidLastName)}>
              {renewer.lastName.length ? renewer.lastName : '<empty>'}
            </Text>
          ),
        },
        { value: renewer.selectableSeats.join(', '), element: renderRenewerSeats() },
      ];

      if (allRenewersHaveIds && renewer.id) {
        baseData.push({
          value: getPassConfigRenewalUrl(renewer.id),
          element: (
            <div className={styles.renewerLinkButtons}>
              <div>
                <Button
                  buttonStyle="minimal"
                  size="small"
                  onPress={() => openUrlInNewTab(getPassConfigRenewalUrl(renewer.id!))}
                >
                  Open Link
                </Button>
              </div>
              <div>
                <Button
                  buttonStyle="minimal"
                  buttonType="secondary"
                  size="small"
                  onPress={() => copyTextToClipboard(getPassConfigRenewalUrl(renewer.id!))}
                >
                  Copy To Clipboard
                </Button>
              </div>
            </div>
          ),
        });
      }

      return {
        data: baseData,
      };
    }) as Rows;
  }, [validatedRenewers, allRenewersHaveIds, copyTextToClipboard]);

  return (
    <div className={styles.container}>
      <div className={styles.renewerListKey}>
        <Headline level="3">{`Renewers (${validatedRenewers.length})`}</Headline>
        <div className={styles.renewerListItems}>
          <div className={styles.renewerListKeyItem}>
            <IconError color="critical" size="medium" />
            <Text>Invalid Seat: This seat label does not exist on the seating chart.</Text>
          </div>
          <div className={styles.renewerListKeyItem}>
            <IconDuplicate color="critical" size="medium" />
            <Text>Duplicate Seat: This seat label exists on this or another renewer.</Text>
          </div>
          <div className={styles.renewerListKeyItem}>
            <IconWarning color="warning" size="medium" />
            <Text>
              Duplicate Renewer: This renewer has the same name/email as another renewer. Consider consolidating.
            </Text>
          </div>
        </div>
      </div>
      <DataTable
        className={styles.requestTable}
        columnContentTypes={
          allRenewersHaveIds
            ? ['element', 'element', 'element', 'element', 'element', 'text']
            : ['element', 'element', 'element', 'element', 'element']
        }
        columnHeaders={
          allRenewersHaveIds
            ? ['Status', 'Email', 'First Name', 'Last Name', 'Seats', 'Renewal Link']
            : ['Status', 'Email', 'First Name', 'Last Name', 'Seats']
        }
        size="medium"
        style="striped"
        rows={buildRenewerRows()}
      />
      {renewersWithErrorsCount > 0 && (
        <div className={styles.errorCountNote}>
          <IconCritical size="medium" color="critical" />
          <Text color="critical">
            {renewersWithErrorsCount} {renewersWithErrorsCount > 1 ? 'renewers' : 'renewer'} have invalid information.
            Fix these renewers in your CSV, and re-upload to continue.
          </Text>
        </div>
      )}
    </div>
  );
}
