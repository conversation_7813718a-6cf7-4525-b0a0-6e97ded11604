.container {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-three-quarter);
  border-radius: var(--u-space-half);
  background-color: var(--u-color-bg-level2);
  padding: var(--u-space-one-and-half);
}

.requestTable {
  thead {
    background: var(--u-color-bg-level2-accent);
  }

  thead > tr > th:nth-child(5) {
    padding-left: var(--u-space-one);
  }

  tbody > tr {
    width: 100%;
    word-break: break-all;
  }

  tbody > tr > td:nth-child(1) {
    width: 5%;
  }

  tbody > tr > td:nth-child(2) {
    width: 20%;
  }

  tbody > tr > td:nth-child(3) {
    width: 20%;
  }

  tbody > tr > td:nth-child(4) {
    width: 20%;
  }

  tbody > tr > td:nth-child(5) {
    width: 25%;
  }

  tbody > tr > td:nth-child(6) {
    width: 10%;
  }
}

.renewerText {
  display: flex;
  width: 100%;
  height: 100%;
}

.renewerSeat {
  display: flex;
  align-items: center;
  gap: var(--u-space-half);
  width: 100%;
}

.renewerSeatErrors {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  width: 10%;
  gap: var(--u-space-eighth);
}

.renewerListKey {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
}

.renewerListItems {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-half);
}

.renewerListKeyItem {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: var(--u-space-half);
}

.errorCountNote {
  display: flex;
  align-items: center;
  gap: var(--u-space-quarter);
}

.renewerLinkButtons {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-half);
}
