import { useMemo } from 'react';

import { format } from 'date-fns';

import { DataTable, Divider, type Rows } from '@hudl/uniform-web';
import { Button } from '@hudl/uniform-web-button-legacy';
import { encodeOrganizationId } from '@local/admin-shared';
import { CenteredLoadingSpinner, type PassConfig, PassConfigStatus } from '@local/ticketing-admin-shared';

import { useGetAllPassConfigsForOrg } from '../../hooks/useGetAllPassConfigsForOrg';
import { RenewalCampaignToolState } from '../../types/renewalCampaignToolTypes';
import { RenewalCampaignPassConfigInstructions } from '../RenewalCampaignPassConfigInstructions/RenewalCampaignPassConfigInstructions';

import styles from './PassConfigList.module.scss';

type Props = {
  organizationId: string;
  setSelectedPassConfig: (passConfig: PassConfig | undefined, toolState: RenewalCampaignToolState) => void;
};

export function PassConfigList(props: Props): React.JSX.Element {
  const { organizationId, setSelectedPassConfig } = props;
  const encodedOrgId = encodeOrganizationId(organizationId);

  const statusesToQueryFor = useMemo(() => [PassConfigStatus.Draft, PassConfigStatus.Active], []);
  const { data: passConfigsData, isLoading: passConfigsLoading } = useGetAllPassConfigsForOrg(
    encodedOrgId,
    statusesToQueryFor,
    () => {},
    () => {}
  );

  const passConfigs = passConfigsData?.filter((passConfig) => {
    if (passConfig.renewalCampaign) {
      return true;
    }

    const passConfigIsReservedSeating = passConfig.eventFilters?.some(
      (eventFilter) => eventFilter.venueConfigurationId
    );

    if (passConfig.passConfigStatus === PassConfigStatus.Draft.toString() && passConfigIsReservedSeating) {
      return true;
    }

    return false;
  });

  const buildPassConfigRows = (): Rows => {
    if (!passConfigs) {
      return [];
    }

    return passConfigs.map((passConfig) => {
      const hasRenewalCampaign = passConfig.renewalCampaign;
      const renderButton = (): React.JSX.Element => {
        if (hasRenewalCampaign) {
          return (
            <Button
              size="xsmall"
              buttonStyle="minimal"
              buttonType="secondary"
              onClick={() => setSelectedPassConfig(passConfig, RenewalCampaignToolState.View)}
            >
              View Campaign
            </Button>
          );
        }

        return (
          <Button
            size="xsmall"
            buttonStyle="minimal"
            buttonType="confirm"
            onClick={() => setSelectedPassConfig(passConfig, RenewalCampaignToolState.Create)}
          >
            Create Campaign
          </Button>
        );
      };
      const renewalDateFormat = 'MM/dd/yyyy hh:mm a zzz';
      const passDateFormat = 'MM/dd/yyyy';

      const renewalStartDate = passConfig.renewalCampaign?.renewalStartDate
        ? format(new Date(passConfig.renewalCampaign.renewalStartDate), renewalDateFormat)
        : '-';
      const renewalEndDate = passConfig.renewalCampaign?.renewalEndDate
        ? format(new Date(passConfig.renewalCampaign.renewalEndDate), renewalDateFormat)
        : '-';

      return {
        data: [
          passConfig.name,
          passConfig.passConfigStatus,
          passConfig.visibility,
          format(new Date(passConfig.startDate), passDateFormat),
          format(new Date(passConfig.endDate), passDateFormat),
          renewalStartDate,
          renewalEndDate,
          {
            value: passConfig.id,
            element: renderButton(),
          },
        ],
      };
    }) as Rows;
  };

  if (passConfigsLoading || passConfigsData === undefined) {
    return <CenteredLoadingSpinner />;
  }

  return (
    <div className={styles.container}>
      <RenewalCampaignPassConfigInstructions />
      <Divider />
      <DataTable
        className={styles.requestTable}
        columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text', 'text', 'element']}
        columnHeaders={[
          'Pass Config Name',
          'Status',
          'Visibility',
          'Pass Start',
          'Pass End',
          'Renewal Start',
          'Renewal End',
          'Actions',
        ]}
        size="medium"
        style="striped"
        rows={buildPassConfigRows()}
      />
    </div>
  );
}
