import { type ReservedSeat } from '@local/ticketing-admin-shared';

import type { Renewer, ValidatedRenewer } from '../types/renewalCampaignToolTypes';

export const validateRenewers = (
  renewers: Renewer[],
  seatingChartSeats: ReservedSeat[],
  validEmailFunc: (email: string) => boolean
): ValidatedRenewer[] => {
  const validatedRenewers: ValidatedRenewer[] = [];

  const actualSeatMap = new Map<string, boolean>();
  seatingChartSeats.forEach((seat) => {
    actualSeatMap.set(seat.seatIdentifier, true);
  });

  const generalAdmissionSeats = new Set<string>(
    seatingChartSeats.filter((seat) => seat.generalAdmissionArea).map((seat) => seat.seatIdentifier)
  );

  const allRenewerSeats = renewers.flatMap((renewer) => renewer.selectableSeats);
  const allDuplicatedNonGARenewerSeats = allRenewerSeats.filter(
    (seat, index, self) => self.indexOf(seat) !== index && !generalAdmissionSeats.has(seat)
  );

  const renewerKeyMap = new Map<string, number>();
  const duplicateRenewerKeys = new Set<string>();

  renewers.forEach((renewer) => {
    const renewerKey = `${renewer.firstName.trim().toLowerCase()}|${renewer.lastName.trim().toLowerCase()}|${renewer.email.trim().toLowerCase()}`;
    const count = renewerKeyMap.get(renewerKey) || 0;
    renewerKeyMap.set(renewerKey, count + 1);

    if (count > 0) {
      duplicateRenewerKeys.add(renewerKey);
    }
  });

  renewers.forEach((renewer) => {
    const invalidSeats = renewer.selectableSeats.filter((seat) => !actualSeatMap.get(seat));
    const duplicateSeats = renewer.selectableSeats.filter((seat) => allDuplicatedNonGARenewerSeats.includes(seat));

    const renewerKey = `${renewer.firstName.trim().toLowerCase()}|${renewer.lastName.trim().toLowerCase()}|${renewer.email.trim().toLowerCase()}`;
    const isDuplicateRenewer = duplicateRenewerKeys.has(renewerKey);

    validatedRenewers.push({
      ...renewer,
      invalidFirstName: !renewer.firstName.trim().length,
      invalidLastName: !renewer.lastName.trim().length,
      invalidEmail: !validEmailFunc(renewer.email),
      invalidSeats,
      duplicateSeats,
      isDuplicateRenewer,
    });
  });

  return validatedRenewers;
};

export const renewerHasErrors = (renewer: ValidatedRenewer): boolean => {
  return (
    renewer.selectableSeats.length === 0 ||
    renewer.invalidSeats.length > 0 ||
    renewer.duplicateSeats.length > 0 ||
    renewer.invalidEmail ||
    renewer.invalidFirstName ||
    renewer.invalidLastName
  );
};
