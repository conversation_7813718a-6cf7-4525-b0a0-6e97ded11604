import type { Renewer } from '../types/renewalCampaignToolTypes';

const RENEWER_LIST_TEMPLATE = [
  [
    'First Name',
    'Last Name',
    'Email',
    'Seat Identifiers',
    'Note: Do not delete this header row. It will be excluded from the import.',
  ],
];

export const RENEWER_LIST_TEMPLATE_FILENAME = 'renewer_list_template.csv';

export const downloadRenewerListTemplateCsv = (): void => {
  const csvContent = RENEWER_LIST_TEMPLATE.map((row) => row.join(',')).join('\n');
  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = RENEWER_LIST_TEMPLATE_FILENAME;
  a.click();
};

export const parseRenewersFromPopulatedTemplate = (fileData: string[][]): Renewer[] => {
  const dataWithoutHeader = fileData.slice(1);
  const sanitizedFileData = dataWithoutHeader.filter((row) => row[0] || row[1] || row[2] || row[3]);
  return sanitizedFileData.map((row) => {
    const firstName = row[0].trim();
    const lastName = row[1].trim();
    const email = row[2].trim();
    const selectableSeats = row[3]
      .split(',')
      .map((seatId) => seatId.trim().replace(/^"(.*)"$/, '$1'))
      .filter((seatId) => seatId !== '');
    return {
      id: undefined,
      firstName: firstName,
      lastName: lastName,
      email: email,
      selectableSeats: selectableSeats,
    };
  });
};
