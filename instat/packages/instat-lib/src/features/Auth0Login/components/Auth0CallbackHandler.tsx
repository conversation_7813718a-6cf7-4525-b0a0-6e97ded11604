import { useEffect, useRef, useState } from 'react';

import { useAuth0 } from '@auth0/auth0-react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';

import { Loader } from '../../../features/Loader';
import { auth } from '../../../stores';
import type { TUser } from '../../../stores/auth/models';
import { getCurrentAuth0Config } from '../context/auth0config';

const auth0Config = getCurrentAuth0Config();
const hudlRejectUserUri = `https://${auth0Config.hudlDomain}/home`;
// Strip out any query parameters for the redirect URI
// Before: https://localhost.app.thorhudl.com:8086/instat/basketball/hudl-auth-callback?code=1&state=2
// After : https://localhost.app.thorhudl.com:8086/instat/basketball/hudl-auth-callback
const currentHudlAuthCallbackUri = `${window.location.origin}${window.location.pathname}`;
const configureCookieUri = `https://${auth0Config.hudlDomain}/app/users/configure_cookie`;
const instatCookieUri = 'https://new.instatscout.com/app/users/configure_cookie';

const Center = styled.div`
  position: absolute;
  z-index: 12000;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
`;

export function Auth0CallbackHandler() {
  const [idToken, setIdToken] = useState();
  const { user, isAuthenticated, getIdTokenClaims } = useAuth0();
  const navigate = useNavigate();
  const { storeUserInfo, logout } = auth;
  const instatSessionInfo = user?.['https://identity.hudl.com/instat_user'];
  // This is only set when the user logs in with the new approach of attaching IDs to Hudl profiles
  const instatSessionToken = user?.['https://identity.hudl.com/instat_token'];
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    async function getToken() {
      // @ts-expect-error This can be undefined, but don't want to change the underlying code
      const { __raw } = await getIdTokenClaims();
      setIdToken(__raw);
    }
    if (isAuthenticated) {
      getToken();
    }
  }, [isAuthenticated, getIdTokenClaims]);

  useEffect(() => {
    if (!isAuthenticated || !idToken || !formRef?.current) {
      // this component handles the auth0 /authorize callback
      // if the user is not auth0 authenticated, there's nothing to do here
      return;
    }

    const trimmedUser: TUser = {
      token: instatSessionToken,
      first_name: '',
      access_type: 'video', // restrictive at first, gotta give it some value
      email: '',
      last_name: '',
      player_id: null,
      scout_id: 0,
    };

    if (!instatSessionToken) {
      if (instatSessionInfo) {
        // user was authenticated with the old auth flow
        logout();
        return;
      }
      // reject Hudl users signing in on Instat without an Instat session token
      window.location.replace(hudlRejectUserUri);
      return;
    }

    // We set the Instat session data before we proceed with the Auth0 flow
    // This ensures we have the token available for those requests that fire up immediately after the Auth0 flow redirects us back here
    // If we were to place this in the success=true callback, API calls would have gone out without the token and subsequently
    // fail with a 401 which then triggers a logout
    storeUserInfo({
      success: true,
      user: trimmedUser,
      auth0Jwt: idToken,
    });

    if (formRef.current.requestSubmit) {
      formRef.current.requestSubmit();
    } else {
      formRef.current.submit();
    }
  }, [
    instatSessionInfo,
    isAuthenticated,
    idToken,
    formRef,
    logout,
    storeUserInfo,
    navigate,
    user?.given_name,
    user?.family_name,
    instatSessionToken,
  ]);

  if (idToken) {
    const urlParams = new URLSearchParams(window.location.search);
    const fromCookieConfig = urlParams.get('from_instatscout') === 'true';

    const formAction = fromCookieConfig ? instatCookieUri : configureCookieUri;

    const forwardValue = !fromCookieConfig
      ? `${currentHudlAuthCallbackUri}?from_instatscout=true`
      : currentHudlAuthCallbackUri;

    return (
      <Center>
        <Loader diameter={20} />
        <form ref={formRef} method="POST" action={formAction}>
          <input type="hidden" name="token" value={idToken} />
          <input type="hidden" name="forward" value={forwardValue} />
        </form>
      </Center>
    );
  }

  return null;
}
