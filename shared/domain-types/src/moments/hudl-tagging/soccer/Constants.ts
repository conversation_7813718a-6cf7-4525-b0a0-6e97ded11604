import { SoccerPitchZone } from './locationCharts/types';

export const TagConstants = {
  Type: 'type',
  SubType: 'subType',
  Result: 'result',
  InvolvedAthletes: 'involvedAthletes',
  PossessionPercentage: 'possessionPercentage',
  PossessionTimeMs: 'possessionTime',
  AttackingTransitions: 'attackingTransitions',
  Goals: 'goals',
  Shots: 'shots',
  ShotsOnGoal: 'shotsOnGoal',
  ShotsOffGoal: 'shotsOffGoal',
  ShotsBlocked: 'shotsBlocked',
  Corners: 'corners',
  FreeKicks: 'freeKicks',
  Offsides: 'offsides',
  CrossingPlayer: 'crosser',
  Passes: 'passes',
  PassesSuccessful: 'passesSuccessful',
  PassesUnsuccessful: 'passesUnsuccessful',
  PassResult: 'passResult',
  CrossResult: 'crossResult',
  GoalkeepingResult: 'goalkeepingResult',
  SetPieceResult: 'setPieceResult',
  SetPieceViolation: 'setPieceViolation',
  GameSection: 'gameSection',
  ReceivingTeam: 'receivingTeam',
  FouledTeam: 'fouledTeam',
  PreviousMomentType: 'previousMomentType',
  NextMomentType: 'nextMomentType',
  PreviousMomentSubType: 'previousMomentSubType',
  NextMomentSubType: 'nextMomentSubType',
  PreviousMomentTeam: 'previousMomentTeam',
  PreviousMomentLocationX: 'previousMomentLocationX',
  PreviousMomentLocationY: 'previousMomentLocationY',
  NextMomentTeam: 'nextMomentTeam',
  PreviousMomentSequence: 'previousMomentSequence',
  Transition: 'transition',
  Team: 'team',
  SetPieceType: 'setPieceType',
  SetPieceSubType: 'setPieceSubType',
  GoalKeepingType: 'goalkeepingType',
  Shooter: 'shooter',
  CrossReceiver: 'crossReceiver',
  AssistingPlayer: 'assistingPlayer',
  PenaltyKickResult: 'penaltyKickResult',
  IsPenaltyShootout: 'isPenaltyShootout',
  PeriodAttackingDirection: 'periodAttackingDirection',
  CustomType: 'customType',
  IsV3Moment: 'isV3Moment',
  Sequence: 'sequence',
  SequenceStartTime: 'sequenceStartTime',
  SequenceEndTime: 'sequenceEndTime',
  OriginalSequenceStartTime: 'orginalSequenceStartTime',
  OriginalSequenceEndTime: 'originalSequenceEndTime',
  SequenceStartEvent: 'sequenceStartEvent',
  SequenceEndEvent: 'sequenceEndEvent',
  SequenceTeamOnePlayers: 'sequenceTeamOnePlayers',
  SequenceTeamTwoPlayers: 'sequenceTeamTwoPlayers',
  SequencePassCount: 'sequencePassCount',
  SequenceTeamInPossession: 'sequenceTeamInPossession',
  SequenceStartingThird: 'sequenceStartingThird',
  SequenceEndingThird: 'sequenceEndingThird',
  SequenceThirds: 'sequenceThirds',
  FoulCard: 'foulCard',
  PlayerJersey: 'playerJersey',
  PlayerUserId: 'playerUserId',
  ReceivingPlayerJersey: 'receivingPlayerJersey',
  ReceivingPlayerUnknown: 'receivingPlayerUnknown',
  ReceivingPlayerUserId: 'receivingPlayerUserId',
  FouledPlayerJersey: 'fouledPlayerJersey',
  FouledPlayerUnknown: 'fouledPlayerUnknown',
  FouledPlayerUserId: 'fouledPlayerUserId',
  PlayerUnknown: 'playerUnknown',
  TeamOnePlayerZones: 'teamOnePlayerZones',
  TeamTwoPlayerZones: 'teamTwoPlayerZones',
  Points: 'points',
  IsInsideBox: 'isInsideBox',
  FoulType: 'foulType',
};

export const SportsCodeTags = {
  HudlCode: 'HUDL_CODE',
};

export const MomentTypes = {
  Shot: 'shot',
  OwnGoal: 'ownGoal',
  Cross: 'cross',
  SetPiece: 'setPiece',
  Restart: 'restart', // Legacy moment type
  BallOut: 'ballOut',
  Foul: 'foul',
  Pass: 'pass',
  Goalkeeping: 'goalkeeping',
  Goal: 'goal',
  StartPeriod: 'startPeriod',
  EndPeriod: 'endPeriod',
  PassChain: 'passChain',
  PenaltyKick: 'penaltyKick',
  Offside: 'offside',
  Segment: 'segment',
  Custom: 'custom',
};
export const PlayerTags = {
  GK: 'GK',
  UK: 'UK',
};

export const FoulCards = {
  None: 'none',
  Yellow: 'yellow',
  Red: 'red',
};

export const FoulTypes = {
  Foul: 'foul',
  Card: 'card',
};

export const StartPeriodTags = {
  IsPenaltyShootout: 'isPenaltyShootout',
  AttackingDirection: 'attackingDirection',
};

export const AttackingDirections = {
  LeftToRight: 'Left to Right',
  RightToLeft: 'Right to Left',
};

export const SwappedAttackingDirections = {
  [AttackingDirections.LeftToRight]: AttackingDirections.RightToLeft,
  [AttackingDirections.RightToLeft]: AttackingDirections.LeftToRight,
};

export const IsPenaltyShootoutOptions = {
  Yes: 'yes',
  No: 'no',
};

export const PenaltyKickTags = {
  Team: 'team',
  PlayerJersey: 'playerJersey',
  Result: 'penaltyKickResult',
};

export const PassChainTags = {
  PassChainLength: 'passChainLength',
};

export const GoalkeepingTypes = {
  Throw: 'throw',
  Punt: 'punt',
};

export const SetPieceTypes = {
  ThrowIn: 'throwIn',
  Corner: 'corner',
  CornerKick: 'cornerKick',
  GoalKick: 'goalKick',
  FreeKick: 'freeKick',
  PenaltyKick: 'penaltyKick',
  KickOff: 'kickOff',
};

export const SetPieceSubTypes = {
  Pass: 'pass',
  Cross: 'cross',
  Shot: 'shot',
};

export const SetPieceTags = {
  PlayerJersey: 'playerJersey',
  ReceivingPlayer: 'receivingPlayerJersey',
  LocationStartX: 'setPieceLocationStartX',
  LocationStartY: 'setPieceLocationStartY',
  LocationEndX: 'setPieceLocationEndX',
  LocationEndY: 'setPieceLocationEndY',
};

export const PenaltyKickResult = {
  Saved: 'saved',
  OffTarget: 'offTarget',
  Goal: 'goal',
};

export const SuccessStatuses = {
  Successful: 'successful',
  Unsuccessful: 'unsuccessful',
};

export const ShotResult = {
  Blocked: 'blocked',
  Goal: 'goal',
  Saved: 'saved',
  OffTarget: 'offTarget',
  OnGoal: 'onGoal',
  OffGoal: 'offGoal',
};

export const PassResult = {
  Successful: 'successful',
  Intercepted: 'intercepted',
  Blocked: 'blocked',
  OutOfPlay: 'outOfPlay',
  Unsuccessful: 'unsuccessful',
};

export const CrossResult = {
  Successful: 'successful',
  Intercepted: 'intercepted',
  Blocked: 'blocked',
  OutOfPlay: 'outOfPlay',
};

export const GoalkeepingResult = {
  Successful: 'successful',
  Intercepted: 'intercepted',
  Blocked: 'blocked',
  OutOfPlay: 'outOfPlay',
};

export const SetPieceResult = {
  Successful: 'successful',
  Intercepted: 'intercepted',
  Blocked: 'blocked',
  OutOfPlay: 'outOfPlay',
  Goal: 'goal',
  Saved: 'saved',
  OffTarget: 'offTarget',
  Other: 'other',
};

export const ResultSuccessMapping = {
  [ShotResult.Goal]: SuccessStatuses.Successful,
  [ShotResult.Saved]: SuccessStatuses.Unsuccessful,
  [ShotResult.Blocked]: SuccessStatuses.Unsuccessful,
  [ShotResult.OffTarget]: SuccessStatuses.Unsuccessful,
  [PassResult.Successful]: SuccessStatuses.Successful,
  [PassResult.Intercepted]: SuccessStatuses.Unsuccessful,
  [PassResult.Blocked]: SuccessStatuses.Unsuccessful,
  [PassResult.OutOfPlay]: SuccessStatuses.Unsuccessful,
  [PassResult.Unsuccessful]: SuccessStatuses.Unsuccessful,
  [CrossResult.Successful]: SuccessStatuses.Successful,
  [CrossResult.Intercepted]: SuccessStatuses.Unsuccessful,
  [CrossResult.Blocked]: SuccessStatuses.Unsuccessful,
  [CrossResult.OutOfPlay]: SuccessStatuses.Unsuccessful,
  [GoalkeepingResult.Successful]: SuccessStatuses.Successful,
  [GoalkeepingResult.Blocked]: SuccessStatuses.Unsuccessful,
  [GoalkeepingResult.Intercepted]: SuccessStatuses.Unsuccessful,
  [GoalkeepingResult.OutOfPlay]: SuccessStatuses.Unsuccessful,
};

export const PassingMomentTags = {
  PassingPlayerUserId: 'playerUserId',
  ReceivingPlayerUserId: 'receivingPlayerUserId',
  PassingPlayerJersey: 'playerJersey',
  ReceivingPlayerJersey: 'receivingPlayerJersey',
  PassResult: 'passResult',
  StartLocationX: 'passLocationStartX',
  StartLocationY: 'passLocationStartY',
  EndLocationX: 'passLocationEndX',
  EndLocationY: 'passLocationEndY',
};

export const CrossMomentTags = {
  PlayerJersey: 'playerJersey',
  PlayerUserId: 'playerUserId',
  ReceivingPlayerJersey: 'receivingPlayerJersey',
  ReceivingPlayerUserId: 'receivingPlayerUserId',
  CrossResult: 'crossResult',
  StartLocationX: 'crossLocationStartX',
  StartLocationY: 'crossLocationStartY',
  EndLocationX: 'crossLocationEndX',
  EndLocationY: 'crossLocationEndY',
};

export const ShotMomentTags = {
  ShotResult: 'shotResult',
  PlayerJersey: 'playerJersey',
  PlayerUserId: 'playerUserId',
  ShotLocationX: 'shotLocationX',
  ShotLocationY: 'shotLocationY',
  ReceivingPlayerJersey: 'receivingPlayerJersey',
  ReceivingPlayerUserId: 'receivingPlayerUserId',
  ReceivingPlayerUnknown: 'receivingPlayerUnknown',
  OriginalLocationX: 'originalLocationX',
  OriginalLocationY: 'originalLocationY',
};

export const SetPieceMomentTags = {
  SetPieceType: 'setPieceType',
  SetPieceSubType: 'setPieceSubType',
  SetPieceResult: 'setPieceResult', // TODO: REMOVE
  SetPieceViolation: 'setPieceViolation',
  StartLocationX: 'setPieceLocationStartX',
  StartLocationY: 'setPieceLocationStartY',
  EndLocationX: 'setPieceLocationEndX',
  EndLocationY: 'setPieceLocationEndY',
};

export const GoalkeepingMomentTags = {
  GoalkeepingType: 'goalkeepingType',
  GoalkeepingResult: 'goalkeepingResult',
  StartLocationX: 'goalkeepingLocationStartX',
  StartLocationY: 'goalkeepingLocationStartY',
  EndLocationX: 'goalkeepingLocationEndX',
  EndLocationY: 'goalkeepingLocationEndY',
  ReceivingPlayerJersey: 'receivingPlayerJersey',
  ReceivingTeam: 'receivingTeam',
};

export const OffsideMomentTags = {
  PlayerJersey: 'playerJersey',
  LocationX: 'offsideLocationX',
  LocationY: 'offsideLocationY',
};

export const FoulMomentTags = {
  Team: 'team',
  FoulingPlayer: 'playerJersey',
  FouledTeam: 'fouledTeam',
  FouledPlayerJersey: 'fouledPlayerJersey',
  PlayerUnknown: 'playerUnknown',
  FouledPlayerUserId: 'fouledPlayerUserId',
  FouledPlayerUnknown: 'fouledPlayerUnknown',
  FoulCard: 'foulCard',
  IsPenaltyShootout: 'isPenaltyShootout',
  LocationX: 'foulLocationX',
  LocationY: 'foulLocationY',
  IsTeamStat: 'isTeamStat',
  FoulType: 'foulType',
};

export const PitchThirds = {
  Left: 'left',
  Middle: 'middle',
  Right: 'right',
};

export const NamedThirdLocation = { ATK: 'ATK', MID: 'MID', DEF: 'DEF' };

export const MomentDataTypes = {
  Player: 'player',
  Location: 'location',
  Assist: 'assist',
  PassResult: 'passResult',
  Segment: 'segment',
};

export const SoccerGameSections = {
  One: 's1',
  Two: 's2',
  Three: 's3',
  Four: 's4',
  Five: 's5',
  Six: 's6',
  OT1: 'ot1',
  OT2: 'ot2',
};

export const SoccerGoalkeeper = 'Goalkeeper';

export const MIN_LEAD_TIME = 5000;
export const MIN_LAG_TIME = 5000;
export const CLIP_START_PADDING_MS = 5000;
export const CLIP_END_PADDING_MS = 5000;

export const HiddenMomentTypes = [MomentTypes.StartPeriod, MomentTypes.PassChain];

export const AttackingThirdZones = [
  SoccerPitchZone.Zone_0_0,
  SoccerPitchZone.Zone_0_1,
  SoccerPitchZone.Zone_0_2,
  SoccerPitchZone.Zone_0_3,
  SoccerPitchZone.Zone_0_4,
  SoccerPitchZone.Zone_1_0,
  SoccerPitchZone.Zone_1_1,
  SoccerPitchZone.Zone_1_2,
  SoccerPitchZone.Zone_1_3,
  SoccerPitchZone.Zone_1_4,
];
export const MiddleThirdZones = [
  SoccerPitchZone.Zone_2_0,
  SoccerPitchZone.Zone_2_1,
  SoccerPitchZone.Zone_2_2,
  SoccerPitchZone.Zone_2_3,
  SoccerPitchZone.Zone_2_4,
  SoccerPitchZone.Zone_3_0,
  SoccerPitchZone.Zone_3_1,
  SoccerPitchZone.Zone_3_2,
  SoccerPitchZone.Zone_3_3,
  SoccerPitchZone.Zone_3_4,
];
export const DefensiveThirdZones = [
  SoccerPitchZone.Zone_4_0,
  SoccerPitchZone.Zone_4_1,
  SoccerPitchZone.Zone_4_2,
  SoccerPitchZone.Zone_4_3,
  SoccerPitchZone.Zone_4_4,
  SoccerPitchZone.Zone_5_0,
  SoccerPitchZone.Zone_5_1,
  SoccerPitchZone.Zone_5_2,
  SoccerPitchZone.Zone_5_3,
  SoccerPitchZone.Zone_5_4,
];

export default {
  MomentDataTypes,
  MomentTypes,
  PassResult,
  PitchThirds,
  NamedThirdLocation,
  AttackingDirections,
  SwappedAttackingDirections,
  ShotResult,
  SetPieceResult,
  PenaltyKickResult,
  SuccessStatuses,
  TagConstants,
  SoccerGameSections,
  SetPieceTypes,
  SetPieceSubTypes,
  GoalkeepingTypes,
  FoulCards,
  FoulTypes,
  CrossResult,
  GoalkeepingResult,
  PlayerTags,
  HiddenMomentTypes,
  AttackingThirdZones,
  MiddleThirdZones,
  DefensiveThirdZones,
};
