import Sport from '../../../Sport';
import HudlTaggingMomentFactory from '../HudlTaggingMomentFactory';
import MomentHelper from '../services/MomentHelper';
import { getFirstTagValue } from '../services/MomentSorter';
import type { TaggingSettings } from '../taggingSessions';
import { testMoment } from '../testMoments';
import {
  AttackingDirections,
  GoalkeepingTypes,
  HiddenMomentTypes,
  MIN_LAG_TIME,
  MIN_LEAD_TIME,
  MomentTypes,
  NamedThirdLocation,
  ResultSuccessMapping,
  SetPieceMomentTags,
  SetPieceResult,
  ShotResult,
  SoccerGoalkeeper,
  StartPeriodTags,
  SuccessStatuses,
  TagConstants,
} from './Constants';
import { SoccerPitch } from './locationCharts';
import { SoccerPitchZone } from './locationCharts/types';
import { getSequenceEdgeLabel } from './momentHelpers';
import SoccerMomentType from './SoccerMomentType';
import SoccerTaggingMoment from './SoccerTaggingMoment';
import { mapV3Moment, mapV3TaggingSettingsOntoMoments, removeLegacyMoments, V3MomentTypes } from './V3Mapping';

function createMoment(
  params: Parameters<HudlTaggingMomentFactory['createMoment']>[1]
): ReturnType<HudlTaggingMomentFactory['createMoment']> {
  return new HudlTaggingMomentFactory().createMoment(Sport.Soccer, params);
}

function isNumber(number: number): boolean {
  return !(number === null || number === undefined || isNaN(Number(number)));
}

function segmentMoments(moments: SoccerTaggingMoment[]): void {
  const momentsThatEndSegment = [
    { type: MomentTypes.Shot },
    { type: MomentTypes.Segment },
    { type: MomentTypes.OwnGoal },
  ];
  const momentsThatStartSegment = [{ type: MomentTypes.SetPiece }, { type: MomentTypes.Goalkeeping }];

  let sequence = 1;
  let teamInPossession = moments.find((m) => m.team !== null)?.team;

  moments.forEach((moment, idx) => {
    const prevMoment = idx >= 1 ? moments[idx - 1] : null;
    const nextMoment = moments[idx + 1];
    const secondNextMoment = moments[idx + 2];

    if (nextMoment) {
      MomentHelper.addCalculatedTag(moment, TagConstants.NextMomentType, nextMoment.type);
      MomentHelper.addCalculatedTag(moment, TagConstants.NextMomentTeam, nextMoment.team);
      MomentHelper.addCalculatedTag(moment, TagConstants.NextMomentSubType, nextMoment.subType);
    }
    if (prevMoment) {
      MomentHelper.addCalculatedTag(moment, TagConstants.PreviousMomentType, prevMoment.type);
      MomentHelper.addCalculatedTag(moment, TagConstants.PreviousMomentTeam, prevMoment.team);
      MomentHelper.addCalculatedTag(moment, TagConstants.PreviousMomentSubType, prevMoment.subType);
      MomentHelper.addCalculatedTag(moment, TagConstants.PreviousMomentLocationX, `${prevMoment.xLocation || '0'}`);
      MomentHelper.addCalculatedTag(moment, TagConstants.PreviousMomentLocationY, `${prevMoment.yLocation || '0'}`);
      MomentHelper.addCalculatedTag(moment, TagConstants.PreviousMomentSequence, prevMoment.sequence);
    }

    // A transition occurs if the nextMoment is the other team and it was successful.
    // or if both the nextMoment and secondNextMoment or the other team regardless of success.
    const transition =
      nextMoment &&
      teamInPossession &&
      nextMoment.team !== teamInPossession &&
      (nextMoment.isSuccessful ||
        nextMoment.type === MomentTypes.Shot ||
        (secondNextMoment && secondNextMoment.team !== teamInPossession));
    MomentHelper.addCalculatedTag(moment, TagConstants.Sequence, `${sequence}`);
    MomentHelper.addCalculatedTag(moment, TagConstants.SequenceTeamInPossession, `${teamInPossession}`);

    if (testMoment(moment, momentsThatEndSegment)) {
      teamInPossession = nextMoment?.team || teamInPossession;
      sequence++;
      return;
    }
    if (nextMoment && testMoment(nextMoment, momentsThatStartSegment)) {
      teamInPossession = nextMoment?.team || teamInPossession;
      sequence++;
      return;
    }
    if (transition) {
      teamInPossession = teamInPossession === '1' ? '2' : '1';
      sequence++;
      return;
    }
  });
}

function setSequenceStartAndEnd(
  moment: SoccerTaggingMoment,
  _idx: number,
  sequenceMoments: SoccerTaggingMoment[],
  nextSequenceStartTime: number | undefined
): void {
  const firstMoment = sequenceMoments[0];
  const lastMoment = sequenceMoments[sequenceMoments.length - 1];
  const momentsThatSkipBridge = [
    { type: MomentTypes.Shot, result: ShotResult.Goal },
    { type: MomentTypes.EndPeriod },
    { type: MomentTypes.Segment },
  ];

  const startTime = Math.max(firstMoment.startTimeMs - MIN_LEAD_TIME, 0);

  // Unless the last moment in sequence is in skip list, bridge the end time to the start of the next sequence.
  // The clips should have at least MIN_LAG_TIME after the final moment.
  // When not bridging pad the end of the sequence with CLIP_SEQUENCE_END_PADDING_MS.
  const endTime =
    nextSequenceStartTime && !testMoment(lastMoment, momentsThatSkipBridge)
      ? Math.max(nextSequenceStartTime, lastMoment.endTimeMs + MIN_LAG_TIME)
      : lastMoment.endTimeMs + MIN_LAG_TIME;

  const originalStartTime = Math.max(firstMoment.startTimeMs, 0);
  const originalEndTime = nextSequenceStartTime
    ? Math.min(lastMoment.endTimeMs, nextSequenceStartTime)
    : lastMoment.endTimeMs;

  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceStartEvent, getSequenceEdgeLabel(true, sequenceMoments));
  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceEndEvent, getSequenceEdgeLabel(false, sequenceMoments));
  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceStartTime, startTime.toString());
  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceEndTime, endTime.toString());
  MomentHelper.addCalculatedTag(moment, TagConstants.OriginalSequenceStartTime, originalStartTime.toString());
  MomentHelper.addCalculatedTag(moment, TagConstants.OriginalSequenceEndTime, originalEndTime.toString());
}

type MomentSequenceIndex = {
  [key: string]: SoccerTaggingMoment[];
};

function setSequenceInvolvedPlayers(
  moment: SoccerTaggingMoment,
  _idx: number,
  sequenceMoments: SoccerTaggingMoment[]
): void {
  const teamOnePlayers = [
    ...new Set(
      sequenceMoments.flatMap((sm) => sm.involvedAthletes.filter((athlete) => athlete !== '' && athlete.endsWith(`-1`)))
    ),
  ];

  const teamTwoPlayers = [
    ...new Set(
      sequenceMoments.flatMap((sm) => sm.involvedAthletes.filter((athlete) => athlete !== '' && athlete.endsWith(`-2`)))
    ),
  ];
  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceTeamOnePlayers, teamOnePlayers);
  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceTeamTwoPlayers, teamTwoPlayers);
}

function setSequencePassCount(moment: SoccerTaggingMoment, _idx: number, sequenceMoments: SoccerTaggingMoment[]): void {
  const teamInPossession = sequenceMoments.find((m) => m.team !== null)?.team ?? '';
  const passCount = sequenceMoments.filter(
    (sm) =>
      (sm.type === MomentTypes.Pass ||
        sm.type === MomentTypes.Cross ||
        (sm.type === MomentTypes.Goalkeeping &&
          (sm.subType?.toLowerCase() === GoalkeepingTypes.Punt ||
            sm.subType?.toLowerCase() === GoalkeepingTypes.Throw))) &&
      sm.team === teamInPossession
  ).length;
  MomentHelper.addCalculatedTag(moment, TagConstants.SequencePassCount, passCount.toString());
}

function flipThirdLocation(third: string): string {
  if (third === NamedThirdLocation.ATK) {
    return NamedThirdLocation.DEF;
  }
  if (third === NamedThirdLocation.DEF) {
    return NamedThirdLocation.ATK;
  }
  return third;
}

function setSequenceLocationInformation(
  moment: SoccerTaggingMoment,
  _idx: number,
  sequenceMoments: SoccerTaggingMoment[]
): void {
  // startingThird should be the first moment in sequenceMoments with a thirdLocation property.
  const startingThirdMoment = sequenceMoments.find((sm) => sm.thirdLocation && sm.thirdLocation !== '');
  let startingThird = startingThirdMoment?.thirdLocation || '';

  // endingThird should be the last moment in sequenceMoments with a thirdLocation property.
  const endingThirdMoment = [...sequenceMoments].reverse().find((sm) => sm.thirdLocation && sm.thirdLocation !== '');
  let endingThird = endingThirdMoment?.thirdLocation || '';

  // The ending third should be from the perspective of the team in possession
  // If the last moment's team is not the sequence team in possession flip the ending third of the sequence
  if (endingThirdMoment?.team && endingThirdMoment.sequenceTeamInPossession !== endingThirdMoment.team) {
    endingThird = flipThirdLocation(endingThird);
  }
  if (startingThirdMoment?.team && startingThirdMoment.sequenceTeamInPossession !== startingThirdMoment.team) {
    startingThird = flipThirdLocation(startingThird);
  }
  // thirds is an array of all the thirds the sequence goes through.
  const thirds = new Set<string>();
  sequenceMoments.forEach((sm) => {
    if (sm.thirdLocation && sm.thirdLocation !== '') {
      if (sm.team !== sm.sequenceTeamInPossession) {
        thirds.add(flipThirdLocation(sm.thirdLocation));
      } else {
        thirds.add(sm.thirdLocation);
      }
    }
  });
  // Sort the items always as DEF, MID, ATK
  const thirdsArray = [...thirds].sort((a, b) => {
    const sortOrder = ['DEF', 'MID', 'ATK'];
    return sortOrder.indexOf(a) - sortOrder.indexOf(b);
  });
  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceStartingThird, startingThird);
  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceEndingThird, endingThird);
  MomentHelper.addCalculatedTag(moment, TagConstants.SequenceThirds, thirdsArray);
}

function applySequenceMetaData(moments: SoccerTaggingMoment[], fns: any[]): void {
  const momentsBySequence: MomentSequenceIndex = {};
  moments.forEach((moment) => {
    const sequence = moment.getFirstTagValue(TagConstants.Sequence) || '1';
    if (!momentsBySequence[sequence]) {
      momentsBySequence[sequence] = [];
    }
    momentsBySequence[sequence].push(moment);
  });
  Object.keys(momentsBySequence).forEach((sequence) => {
    const nextSequenceStartTime = moments.find(
      (m) => m.sequence === (parseInt(sequence, 10) + 1).toString()
    )?.startTimeMs;
    momentsBySequence[sequence].forEach((moment: SoccerTaggingMoment, idx: number) => {
      fns.forEach((fn) => {
        fn(moment, idx, momentsBySequence[sequence], nextSequenceStartTime);
      });
    });
  });
}

function tagAttackingDirections(moments: SoccerTaggingMoment[]): void {
  let attackingDirection = 'Left to Right';
  moments.forEach((moment) => {
    if (moment.type === MomentTypes.StartPeriod) {
      attackingDirection = getFirstTagValue(moment, StartPeriodTags.AttackingDirection) ?? '';
    }
    MomentHelper.addCalculatedTag(moment, TagConstants.PeriodAttackingDirection, attackingDirection);
  });
}

function tagPeriods(moments: SoccerTaggingMoment[]): void {
  let period = 1;
  let lastEndPeriod = false;
  moments.forEach((moment) => {
    const momentType = createMoment(moment).getMomentType(Sport.Soccer);

    if (momentType === SoccerMomentType.EndGame && lastEndPeriod) {
      // If this is an EndGame immediately after an EndPeriod, don't increment periods.
      MomentHelper.addCalculatedTag(moment, 'period', (period - 1).toString());
      return;
    }
    MomentHelper.addCalculatedTag(moment, 'period', period.toString());
    if (momentType && momentType.isEndPeriodMarker) {
      lastEndPeriod = true;
      period++;
    } else {
      lastEndPeriod = false;
    }
  });
}

function addPassChainTeam(moment: SoccerTaggingMoment, idx: number, moments: SoccerTaggingMoment[]): void {
  if (moment.type === MomentTypes.PassChain) {
    const passStringTeam = moments[idx + 1].team;
    if (passStringTeam) {
      moment.team = passStringTeam;
    }
  }
}

function addBallOutLocation(moment: SoccerTaggingMoment, idx: number, moments: SoccerTaggingMoment[]): void {
  if (moment.type === MomentTypes.BallOut && moments[idx + 1] && moments[idx + 1].type === MomentTypes.SetPiece) {
    const nextMomentLocationX = moments[idx + 1].getFirstTagValue(SetPieceMomentTags.StartLocationX);
    const nextMomentLocationY = moments[idx + 1].getFirstTagValue(SetPieceMomentTags.StartLocationY);
    if (nextMomentLocationX && nextMomentLocationY) {
      MomentHelper.addCalculatedTag(moment, SetPieceMomentTags.StartLocationX, nextMomentLocationX);
    }
  }
}

function addGoalkeeperPlayer(moment: SoccerTaggingMoment): void {
  if (
    moment.type === MomentTypes.Goalkeeping &&
    moment.getFirstTagValue(TagConstants.PlayerUserId) === null &&
    moment.getFirstTagValue(TagConstants.PlayerJersey) === null
  ) {
    moment.setTagValue(TagConstants.PlayerJersey, SoccerGoalkeeper);
  }

  if (
    moment.type === MomentTypes.Shot &&
    moment.result === ShotResult.Saved &&
    moment.getFirstTagValue(TagConstants.ReceivingPlayerUserId) === null &&
    moment.getFirstTagValue(TagConstants.ReceivingPlayerJersey) === null
  ) {
    // intentionally not a calculated tag here because it is editable so its not cleared on recalculation
    moment.setTagValue(TagConstants.ReceivingPlayerJersey, SoccerGoalkeeper);
  }

  if (
    moment.type === MomentTypes.SetPiece &&
    moment.result === SetPieceResult.Saved &&
    moment.getFirstTagValue(TagConstants.ReceivingPlayerUserId) === null &&
    moment.getFirstTagValue(TagConstants.ReceivingPlayerJersey) === null
  ) {
    // intentionally not a calculated tag here because it is editable so its not cleared on recalculation
    moment.setTagValue(TagConstants.ReceivingPlayerJersey, SoccerGoalkeeper);
  }
}

function addSoccerPitchZones(moment: SoccerTaggingMoment, idx: number, moments: SoccerTaggingMoment[]): void {
  if (moment.isV3Moment) {
    return;
  }

  const teamOnePlayerZones: string[] = [];
  const teamTwoPlayerZones: string[] = [];

  const isTeamOneMoment = moment.team === '1';

  const startX = parseFloat(moment.xLocation ?? '');
  const startY = parseFloat(moment.yLocation ?? '');
  if (isNumber(startX) && isNumber(startY) && moment.attackingDirection) {
    const primaryZone = SoccerPitch.getZone([startX, startY], moment.attackingDirection).toString();
    isTeamOneMoment
      ? teamOnePlayerZones.push(moment.primaryPlayer.concat(`_${primaryZone}_p`))
      : teamTwoPlayerZones.push(moment.primaryPlayer.concat(`_${primaryZone}_p`));
  }

  let endX = parseFloat(moment.xEndLocation ?? '');
  let endY = parseFloat(moment.yEndLocation ?? '');
  const endIsStartResults = [ShotResult.Blocked];
  const inferredLocationResults = [ShotResult.Saved];
  const usePreviousMomentLocationResult = [ShotResult.Goal];
  if (moment.result && endIsStartResults.includes(moment.result) && moment.type === MomentTypes.Shot) {
    endX = startX;
    endY = startY;
  }

  if (moment.result && moment.attackingDirection) {
    let secondaryZone = -1;
    if (inferredLocationResults.includes(moment.result)) {
      secondaryZone = SoccerPitchZone.Zone_5_2;
    } else {
      let attkDir = moment.attackingDirection;
      if (ResultSuccessMapping[moment.result] === SuccessStatuses.Unsuccessful) {
        attkDir =
          moment.attackingDirection === AttackingDirections.LeftToRight
            ? AttackingDirections.RightToLeft
            : AttackingDirections.LeftToRight;
      }

      if (
        moment.result &&
        moment.secondaryPlayer &&
        usePreviousMomentLocationResult.includes(moment.result) &&
        idx >= 1
      ) {
        endX = parseFloat(moments[idx - 1].xLocation ?? '');
        endY = parseFloat(moments[idx - 1].yLocation ?? '');
      }

      if (isNumber(endX) && isNumber(endY)) {
        secondaryZone = SoccerPitch.getZone([endX, endY], attkDir);
      }
    }

    if (secondaryZone !== -1 && moment.secondaryPlayer) {
      const secondaryZoneStr = secondaryZone.toString();
      if (ResultSuccessMapping[moment.result] === SuccessStatuses.Successful) {
        isTeamOneMoment
          ? teamOnePlayerZones.push(moment.secondaryPlayer.concat(`_${secondaryZoneStr}_s`))
          : teamTwoPlayerZones.push(moment.secondaryPlayer.concat(`_${secondaryZoneStr}_s`));
      } else if (ResultSuccessMapping[moment.result] === SuccessStatuses.Unsuccessful) {
        isTeamOneMoment
          ? teamTwoPlayerZones.push(moment.secondaryPlayer.concat(`_${secondaryZoneStr}_s`))
          : teamOnePlayerZones.push(moment.secondaryPlayer.concat(`_${secondaryZoneStr}_s`));
      }
    }
  }

  if (teamOnePlayerZones.length) {
    MomentHelper.addCalculatedTag(moment, TagConstants.TeamOnePlayerZones, teamOnePlayerZones);
  }

  if (teamTwoPlayerZones.length) {
    MomentHelper.addCalculatedTag(moment, TagConstants.TeamTwoPlayerZones, teamTwoPlayerZones);
  }
}

function processMomentStream(moments: SoccerTaggingMoment[]): void {
  moments.forEach((moment, idx) => {
    addPassChainTeam(moment, idx, moments);
    addBallOutLocation(moment, idx, moments);
    addGoalkeeperPlayer(moment);
    addSoccerPitchZones(moment, idx, moments);
  });
}

function normalizeV3Time(moment: SoccerTaggingMoment): SoccerTaggingMoment {
  if (!moment.originalStartTimeMs) {
    return moment;
  }
  moment.startTimeMs = moment.originalStartTimeMs;
  moment.endTimeMs = moment.originalStartTimeMs;
  return moment;
}

function calculate(moments: SoccerTaggingMoment[], taggingSettings: TaggingSettings): SoccerTaggingMoment[] {
  if (Array.isArray(moments) && moments[0]?.isSportsCodeMoment) {
    return moments;
  }
  let filteredMoments = moments.sort(
    (m1, m2) => (m1.originalStartTimeMs ?? m1.startTimeMs) - (m2.originalStartTimeMs ?? m2.startTimeMs)
  );
  let v3VideoIds = moments.map((m) => {
    if (m.type === V3MomentTypes.EndGame || m.type === V3MomentTypes.EndHalf) {
      return m.videoId;
    }
    return undefined;
  });
  v3VideoIds = [...new Set(v3VideoIds.filter((vid) => vid !== undefined))];

  if (v3VideoIds.length) {
    moments.forEach((moment) => {
      if (v3VideoIds.includes(moment.videoId)) {
        MomentHelper.addCalculatedTag(moment, TagConstants.IsV3Moment, 'true');
      }
    });
    const taggingSettingsTeamOneMetadata = taggingSettings.teamOne?.metadata || [];
    mapV3TaggingSettingsOntoMoments(moments, taggingSettingsTeamOneMetadata);

    moments.forEach((moment, idx) => {
      if (moment.isV3Moment) {
        normalizeV3Time(moment);
        const mapAction = moment.type ? mapV3Moment[moment.type] : undefined;
        if (mapAction) {
          mapAction(moment, idx, moments);
        }
      }
    });
    filteredMoments = removeLegacyMoments(moments);
  }

  // Thinking about refactoring these so they don't need to loop each time.
  tagPeriods(filteredMoments);
  tagAttackingDirections(filteredMoments);
  processMomentStream(filteredMoments);

  // Remove moments that are no longer needed and don't need to be included for segmenting
  filteredMoments = filteredMoments.filter((m) => m.type !== null && !HiddenMomentTypes.includes(m.type));
  segmentMoments(filteredMoments);
  filteredMoments = filteredMoments.filter(
    (m) => m.type !== MomentTypes.PassChain && m.type !== MomentTypes.StartPeriod
  );
  applySequenceMetaData(filteredMoments, [
    setSequenceStartAndEnd,
    setSequenceInvolvedPlayers,
    setSequencePassCount,
    setSequenceLocationInformation,
  ]);
  return filteredMoments;
}

export default {
  calculate,
};
