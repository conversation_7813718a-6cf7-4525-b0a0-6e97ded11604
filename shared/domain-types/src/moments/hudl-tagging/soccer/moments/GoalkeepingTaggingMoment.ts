import type { LegacyMomentAthlete, MomentAthlete, Tag } from '../../../../types';
import Moment from '../../../Moment';
import { AthleteProperty } from '../../metadata/decorators/AthleteProperty';
import { SelectProperty } from '../../metadata/decorators/SelectProperty';
import { GoalkeepingMomentTags, GoalkeepingResult, TagConstants } from '../Constants';
import SoccerTaggingMoment, { ReceivingPlayer } from '../SoccerTaggingMoment';
import { formatTag } from './decoratorHelpers';

export function getGoalkeepingTaggingMomentClass(tags?: Tag[]): typeof GoalkeepingTaggingMoment {
  const result =
    Moment.getFirstTagValue(tags, GoalkeepingMomentTags.GoalkeepingResult) ||
    Moment.getFirstTagValue(tags, TagConstants.Result);
  switch (result) {
    case GoalkeepingResult.Blocked:
      return BlockedGoalkeepingTaggingMoment;
    case GoalkeepingResult.Intercepted:
      return InterceptedGoalkeepingTaggingMoment;
    case GoalkeepingResult.Successful:
      return SuccessfulGoalkeepingTaggingMoment;
    case GoalkeepingResult.OutOfPlay:
    default:
      return GoalkeepingTaggingMoment;
  }
}

export class GoalkeepingTaggingMoment extends SoccerTaggingMoment {
  @SelectProperty({
    labelI18nKey: 'domainTypes.moment.property.result.label',
    options: [
      GoalkeepingResult.Blocked,
      GoalkeepingResult.Intercepted,
      GoalkeepingResult.OutOfPlay,
      GoalkeepingResult.Successful,
    ],
    format: formatTag(TagConstants.Result),
  })
  get result(): string | null {
    return this.getFirstTagValue(GoalkeepingMomentTags.GoalkeepingResult) || this.getFirstTagValue(TagConstants.Result);
  }

  set result(value: string | null) {
    this.clearSecondaryPlayerTags(); // Reset secondary player tags
    this.setTagValue(TagConstants.Result, value);
    this.setTagValue(GoalkeepingMomentTags.GoalkeepingResult, value);
  }

  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.goalkeeper',
    teamProperty: 'team',
  })
  override get player(): LegacyMomentAthlete | null {
    return super.player;
  }

  override set player(value: MomentAthlete | null) {
    super.player = value;
  }
}

export class InterceptedGoalkeepingTaggingMoment extends GoalkeepingTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.intercepted',
    teamProperty: 'otherTeam',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class BlockedGoalkeepingTaggingMoment extends GoalkeepingTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.blocked',
    teamProperty: 'otherTeam',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class SuccessfulGoalkeepingTaggingMoment extends GoalkeepingTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.received',
    teamProperty: 'team',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}
