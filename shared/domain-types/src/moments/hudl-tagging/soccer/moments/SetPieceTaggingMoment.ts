import type { LegacyMomentAthlete, MomentAthlete, Tag } from '../../../../types';
import Moment from '../../../Moment';
import { AthleteProperty } from '../../metadata/decorators/AthleteProperty';
import { SelectProperty } from '../../metadata/decorators/SelectProperty';
import { SetPieceMomentTags, SetPieceResult, SetPieceSubTypes, SetPieceTypes, TagConstants } from '../Constants';
import SoccerTaggingMoment, { ReceivingPlayer } from '../SoccerTaggingMoment';
import { formatTag } from './decoratorHelpers';

export function getSetPieceTaggingMomentClass(tags?: Tag[]): typeof SetPieceTaggingMoment {
  const result =
    Moment.getFirstTagValue(tags, SetPieceMomentTags.SetPieceResult) ||
    Moment.getFirstTagValue(tags, TagConstants.Result);
  switch (result) {
    case SetPieceResult.Goal:
      const subtype = Moment.getFirstTagValue(tags, TagConstants.SetPieceType);
      if (subtype === SetPieceTypes.PenaltyKick) {
        return SetPieceTaggingMoment; // Penalty kicks are not goals in this context - they only have a single player involved
      }
      return MadeGoalSetPieceTaggingMoment;
    case SetPieceResult.Blocked:
      return BlockedSetPieceTaggingMoment;
    case SetPieceResult.Successful:
      return SuccessfulSetPieceTaggingMoment;
    case SetPieceResult.Intercepted:
      return InterceptedSetPieceTaggingMoment;
    case SetPieceResult.Saved:
      return SavedSetPieceTaggingMoment;
    case SetPieceResult.OffTarget:
    case SetPieceResult.OutOfPlay:
    case SetPieceResult.Other:
    default:
      return SetPieceTaggingMoment;
  }
}

// handle special cases where sub typing is allowed
// additionally these are only for assist + teams - does this need to be handled?
export function getSetPieceSubTypeOptions(moment: SoccerTaggingMoment): string[] {
  const setPieceType = moment.getFirstTagValue(TagConstants.SetPieceType);

  if (
    setPieceType === SetPieceTypes.FreeKick ||
    setPieceType === SetPieceTypes.CornerKick ||
    setPieceType === SetPieceTypes.Corner
  ) {
    return [SetPieceSubTypes.Cross, SetPieceSubTypes.Pass, SetPieceSubTypes.Shot];
  }
  if (setPieceType === SetPieceTypes.KickOff) {
    return [SetPieceSubTypes.Pass, SetPieceSubTypes.Shot];
  }
  // Default: no subtypes
  return [];
}

export function getSetPieceResultsFromSubtype(moment: SoccerTaggingMoment): string[] {
  const setPieceSubType = moment.getFirstTagValue(TagConstants.SetPieceSubType);
  switch (setPieceSubType) {
    case SetPieceSubTypes.Cross:
    case SetPieceSubTypes.Pass:
      return [
        SetPieceResult.Successful,
        SetPieceResult.Blocked,
        SetPieceResult.Intercepted,
        SetPieceResult.OutOfPlay,
        SetPieceResult.Other,
      ];
    case SetPieceSubTypes.Shot:
      return [
        SetPieceResult.Goal,
        SetPieceResult.Saved,
        SetPieceResult.OffTarget,
        SetPieceResult.OutOfPlay,
        SetPieceResult.Other,
      ];
    default:
      const setPieceType = moment.getFirstTagValue(TagConstants.SetPieceType);
      if (setPieceType === SetPieceTypes.PenaltyKick) {
        return [SetPieceResult.Goal, SetPieceResult.Saved, SetPieceResult.OffTarget, SetPieceResult.Other];
      }
      return [
        SetPieceResult.Successful,
        SetPieceResult.Goal,
        SetPieceResult.Saved,
        SetPieceResult.Blocked,
        SetPieceResult.Intercepted,
        SetPieceResult.OffTarget,
        SetPieceResult.OutOfPlay,
        SetPieceResult.Other,
      ];
  }
}

export class SetPieceTaggingMoment extends SoccerTaggingMoment {
  @SelectProperty({
    labelI18nKey: 'domainTypes.moment.property.subType.label',
    options: [], // fallback, empty will result in not being shown
    dynamicOptions: getSetPieceSubTypeOptions,
    format: formatTag(TagConstants.Type),
  })
  get setPieceSubType(): string | null {
    return this.getFirstTagValue(TagConstants.SetPieceSubType);
  }

  set setPieceSubType(value: string | null) {
    const options = getSetPieceResultsFromSubtype(this);
    const currentResult = this.result;
    // only reset result if it is not in the new options
    if (currentResult && !options.includes(currentResult)) {
      this.resetResult();
    }
    this.setTagValue(TagConstants.SetPieceSubType, value);
  }

  @SelectProperty({
    labelI18nKey: 'domainTypes.moment.property.result.label',
    options: [],
    dynamicOptions: getSetPieceResultsFromSubtype,
    format: formatTag(TagConstants.Result),
  })
  get result(): string | null {
    return this.getFirstTagValue(SetPieceMomentTags.SetPieceResult) || this.getFirstTagValue(TagConstants.Result);
  }

  set result(value: string | null) {
    super.clearSecondaryPlayerTags();
    this.setTagValue(TagConstants.Result, value);
    this.setTagValue(SetPieceMomentTags.SetPieceResult, value);
  }

  resetResult(): void {
    this.setTagValue(TagConstants.Result, null);
    this.setTagValue(SetPieceMomentTags.SetPieceResult, null);
  }

  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.property.player.label',
    teamProperty: 'team',
  })
  override get player(): LegacyMomentAthlete | null {
    return super.player;
  }

  override set player(value: MomentAthlete | null) {
    super.player = value;
  }
}

// this player made the goal as the secondary player
export class MadeGoalSetPieceTaggingMoment extends SetPieceTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.tag.result.goal',
    teamProperty: 'team',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class BlockedSetPieceTaggingMoment extends SetPieceTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.blocked',
    teamProperty: 'otherTeam',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class AssistedSetPieceTaggingMoment extends SetPieceTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.property.assist.label',
    teamProperty: 'team',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class SuccessfulSetPieceTaggingMoment extends SetPieceTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.received',
    teamProperty: 'team',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class InterceptedSetPieceTaggingMoment extends SetPieceTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.intercepted',
    teamProperty: 'otherTeam',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class SavedSetPieceTaggingMoment extends SetPieceTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.goalkeeper',
    teamProperty: 'otherTeam',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}
