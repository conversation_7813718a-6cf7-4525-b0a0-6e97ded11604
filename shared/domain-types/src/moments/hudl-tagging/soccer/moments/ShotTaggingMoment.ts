import type { LegacyMomentAthlete, MomentAthlete, Tag } from '../../../../types';
import Moment from '../../../Moment';
import { AthleteProperty } from '../../metadata/decorators/AthleteProperty';
import { SelectProperty } from '../../metadata/decorators/SelectProperty';
import { ShotMomentTags, ShotResult, TagConstants } from '../Constants';
import SoccerTaggingMoment, { ReceivingPlayer } from '../SoccerTaggingMoment';
import { formatTag } from './decoratorHelpers';

export function getShotTaggingMomentClass(tags?: Tag[]): typeof ShotTaggingMoment {
  const result =
    Moment.getFirstTagValue(tags, ShotMomentTags.ShotResult) || Moment.getFirstTagValue(tags, TagConstants.Result);
  switch (result) {
    case ShotResult.Blocked:
      return BlockedShotTaggingMoment;
    case ShotResult.Goal:
      return MadeGoalShotTaggingMoment;
    case ShotResult.Saved:
      return ShotSavedTaggingMoment;
    default:
      return ShotTaggingMoment;
  }
}

export class ShotTaggingMoment extends SoccerTaggingMoment {
  @SelectProperty({
    labelI18nKey: 'domainTypes.moment.property.result.label',
    options: [ShotResult.Saved, ShotResult.OffTarget, ShotResult.Blocked, ShotResult.Goal],
    format: formatTag(TagConstants.Result),
  })
  get result(): string | null {
    return this.getFirstTagValue(ShotMomentTags.ShotResult) || this.getFirstTagValue(TagConstants.Result);
  }

  set result(value: string | null) {
    super.clearSecondaryPlayerTags();
    this.setTagValue(TagConstants.Result, value);
    this.setTagValue(ShotMomentTags.ShotResult, value);
  }

  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.property.player.label',
    teamProperty: 'team',
  })
  override get player(): LegacyMomentAthlete | null {
    return super.player;
  }

  override set player(value: MomentAthlete | null) {
    super.player = value;
  }
}

export class BlockedShotTaggingMoment extends ShotTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.blocked',
    teamProperty: 'otherTeam',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class MadeGoalShotTaggingMoment extends ShotTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.property.assist.label',
    teamProperty: 'team',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}

export class ShotSavedTaggingMoment extends ShotTaggingMoment {
  @AthleteProperty({
    labelI18nKey: 'domainTypes.moment.soccer.player.goalkeeper',
    teamProperty: 'otherTeam',
  })
  get receivingPlayer(): LegacyMomentAthlete | null {
    return this.getPlayerFromTags(ReceivingPlayer);
  }

  set receivingPlayer(value: MomentAthlete | null) {
    this.setPlayerTags(value, ReceivingPlayer);
  }
}
