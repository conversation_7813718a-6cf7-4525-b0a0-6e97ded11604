import { format } from '@hudl/platform-i18n-adapter';

import type { MomentTeamIndex } from '../../../types';
import HudlTaggingMoment from '../HudlTaggingMoment';
import { HudlTaggingMomentPropertyOrder } from '../metadata';
import { TeamProperty } from '../metadata/decorators/TeamProperty';
import { TypeProperty } from '../metadata/decorators/TypeProperty';
import { MomentAthleteTags, type MomentAthleteTagShape } from '../MomentAthleteTags';
import {
  AttackingDirections,
  CrossMomentTags,
  FoulCards,
  FoulMomentTags,
  FoulTypes,
  GoalkeepingMomentTags,
  GoalkeepingTypes,
  NamedThirdLocation,
  OffsideMomentTags,
  PassingMomentTags,
  PassResult,
  PenaltyKickTags,
  PitchThirds,
  ResultSuccessMapping,
  SetPieceMomentTags,
  SetPieceTypes,
  ShotMomentTags,
  ShotResult,
  SoccerGoalkeeper,
  MomentTypes as SoccerMomentTypes,
  SportsCodeTags,
  SuccessStatuses,
  SwappedAttackingDirections,
  TagConstants,
} from './Constants';
import PassesPerSequenceRange from './domain/PassesPerSequenceRange';
import { formatResult } from './momentHelpers';
import { formatTag } from './moments/decoratorHelpers';
import SoccerLocationCalculator from './SoccerLocationCalculator';

export const ReceivingPlayer: MomentAthleteTagShape = {
  userIdTag: TagConstants.ReceivingPlayerUserId,
  jerseyTag: TagConstants.ReceivingPlayerJersey,
  unknownTag: TagConstants.ReceivingPlayerUnknown,
};

const HUDL_DEFINED_MOMENT_TYPES = [
  SoccerMomentTypes.Shot,
  SoccerMomentTypes.Pass,
  SoccerMomentTypes.Cross,
  SoccerMomentTypes.BallOut,
  SetPieceTypes.CornerKick,
  SetPieceTypes.PenaltyKick,
  SetPieceTypes.GoalKick,
  SetPieceTypes.FreeKick,
  SetPieceTypes.ThrowIn,
  SetPieceTypes.KickOff,
  SoccerMomentTypes.Offside,
  SoccerMomentTypes.Foul,
  FoulCards.Yellow,
  FoulCards.Red,
  SoccerMomentTypes.OwnGoal,
  GoalkeepingTypes.Punt, // Kick
  GoalkeepingTypes.Throw, // Throw
  SoccerMomentTypes.BallOut,
  SoccerMomentTypes.EndPeriod,
  SoccerMomentTypes.StartPeriod,
  // Tags below need to be filtered out for selection, the sub types are what the user selects
  SoccerMomentTypes.SetPiece,
  SoccerMomentTypes.Goalkeeping,
];

export default class SoccerTaggingMoment extends HudlTaggingMoment {
  @TypeProperty({
    order: HudlTaggingMomentPropertyOrder.Start,
    labelI18nKey: 'domainTypes.moment.tag.type',
    options: HUDL_DEFINED_MOMENT_TYPES.filter(
      (type) => type !== SoccerMomentTypes.SetPiece && type !== SoccerMomentTypes.Goalkeeping
    ), // the user will never directly select SetPiece
    format: formatTag(TagConstants.Type),
  })
  // We have some special cases to handle here. GoalKeeping type and SetPiece type should be "selected" in the background.
  // A user can select a foul, or a card directly, so we need to handle those cases as well.
  get editableType(): string | null {
    if (this.type === SoccerMomentTypes.SetPiece) {
      const setPieceType = this.getFirstTagValue(SetPieceMomentTags.SetPieceType);
      if (setPieceType === SetPieceTypes.Corner) {
        return SetPieceTypes.CornerKick;
      }
      return setPieceType || null;
    }
    if (this.type === SoccerMomentTypes.Goalkeeping) {
      return this.getFirstTagValue(GoalkeepingMomentTags.GoalkeepingType) || null;
    }
    if (this.type === SoccerMomentTypes.Foul) {
      if (this.getFirstTagValue(FoulMomentTags.FoulType) === FoulTypes.Card) {
        return this.getFirstTagValue(TagConstants.FoulCard) || null;
      }
      return this.getFirstTagValue(FoulMomentTags.FoulType) || null;
    }
    return this.getEditableType();
  }

  set editableType(value: string | null) {
    // handle special cases for SetPiece and Goalkeeping types where the user is actually selecting a sub-type
    const setPieceTypes = Object.values(SetPieceTypes);
    if (value && setPieceTypes.includes(value)) {
      this.setSuperEditableType(SoccerMomentTypes.SetPiece);
      this.setTagValue(SetPieceMomentTags.SetPieceType, value);
      return;
    }
    const goalkeepingTypes = Object.values(GoalkeepingTypes);
    if (value && goalkeepingTypes.includes(value)) {
      this.setSuperEditableType(SoccerMomentTypes.Goalkeeping);
      this.setTagValue(GoalkeepingMomentTags.GoalkeepingType, value);
      return;
    }
    const foulCards = Object.values(FoulCards);
    if (value && foulCards.includes(value)) {
      this.setSuperEditableType(SoccerMomentTypes.Foul);
      this.setTagValue(FoulMomentTags.FoulType, FoulTypes.Card);
      this.setTagValue(TagConstants.FoulCard, value);
      return;
    }
    if (value && value === SoccerMomentTypes.Foul) {
      this.setSuperEditableType(SoccerMomentTypes.Foul);
      this.setTagValue(FoulMomentTags.FoulType, FoulTypes.Foul);
      this.setTagValue(TagConstants.FoulCard, FoulCards.None);
      return;
    }

    this.setSuperEditableType(value);
  }

  setSuperEditableType(value: string | null): void {
    this.setEditableType(value, HUDL_DEFINED_MOMENT_TYPES);
  }

  @TeamProperty()
  get editableTeam(): MomentTeamIndex | null {
    return super.team;
  }

  set editableTeam(team: MomentTeamIndex | null) {
    // Clear all athlete properties, because athletes are coupled to teams
    this.clearAthleteTags();
    super.team = team;
  }

  get otherTeam(): MomentTeamIndex {
    return this.team === '1' ? '2' : '1';
  }

  get gameSection(): string | null {
    return this.getFirstTagValue(TagConstants.GameSection);
  }

  get subType(): string | null {
    const type = this.type;
    switch (type) {
      case SoccerMomentTypes.SetPiece:
        return this.getFirstTagValue(SetPieceMomentTags.SetPieceType);
      case SoccerMomentTypes.Goalkeeping:
        return this.getFirstTagValue(GoalkeepingMomentTags.GoalkeepingType);
      default:
        return null;
    }
  }

  get setPieceSubType(): string | null {
    if (this.subType === SetPieceTypes.PenaltyKick) {
      return SoccerMomentTypes.Shot;
    }
    return this.getFirstTagValue(SetPieceMomentTags.SetPieceSubType);
  }

  get goalKeepingType(): string | null {
    if (this.type !== SoccerMomentTypes.Goalkeeping) {
      return null;
    }
    return this.getFirstTagValue(GoalkeepingMomentTags.GoalkeepingType);
  }

  get setPieceType(): string | null {
    return this.getFirstTagValue(TagConstants.SetPieceType);
  }

  get violation(): boolean {
    return this.getFirstTagValue(SetPieceMomentTags.SetPieceViolation) === 'true';
  }

  get foulCard(): string | null {
    return this.getFirstTagValue(TagConstants.FoulCard);
  }

  get foulType(): string | null {
    return this.getFirstTagValue(FoulMomentTags.FoulType);
  }

  get athletes(): string[] {
    return this.involvedAthletes.map((athlete) => {
      const indexOfDash = athlete.indexOf('-');
      const teamNumber = athlete.substring(indexOfDash + 1);
      return `${athlete.substring(0, indexOfDash)}-${teamNumber === this.team ? this.teamId : this.otherTeamId}`;
    });
  }

  // Aggregates all athletes involved in the moment and returns it as an array.
  get involvedAthletes(): string[] {
    const players = [this.primaryPlayer, this.secondaryPlayer];
    return players.filter((player) => player);
  }

  // The Primary Player is the player who is directly involved in the moment.
  get primaryPlayer(): string {
    let userId;
    let playerJersey;
    const playerTeam = this.team || '1';

    const tagExists =
      this.tagExists(PassingMomentTags.PassingPlayerUserId) ||
      this.tagExists(PassingMomentTags.PassingPlayerJersey) ||
      this.tagExists(ShotMomentTags.PlayerJersey) ||
      this.tagExists(ShotMomentTags.PlayerUserId) ||
      this.tagExists(FoulMomentTags.PlayerUnknown) ||
      this.getFirstTagValueAsBoolean(FoulMomentTags.PlayerUnknown);

    if (tagExists) {
      if (this.type === SoccerMomentTypes.Pass) {
        userId = (this.getFirstTagValue(PassingMomentTags.PassingPlayerUserId) || '').replace(/\s/g, '');
        playerJersey = (this.getFirstTagValue(PassingMomentTags.PassingPlayerJersey) || '').replace(/\s/g, '');
      } else {
        userId = (this.getFirstTagValue(ShotMomentTags.PlayerUserId) || '').replace(/\s/g, '');
        playerJersey = (this.getFirstTagValue(ShotMomentTags.PlayerJersey) || '').replace(/\s/g, '');
      }

      if (!userId && !playerJersey) {
        return `x-${playerTeam}`;
      }
      if (playerJersey === SoccerGoalkeeper) {
        return `g-${playerTeam}`;
      }
      return userId ? `u${userId}-${playerTeam}` : `j${playerJersey}-${playerTeam}`;
    }
    return '';
  }

  // helper to combine secondary player tag values into a single object
  get secondaryPlayerIdAndJersey(): {
    userId: string | null;
    playerJersey: string | null;
    playerUnknown: string | false;
  } {
    const tagExists =
      this.tagExists(ShotMomentTags.ReceivingPlayerUserId) ||
      this.tagExists(ShotMomentTags.ReceivingPlayerJersey) ||
      this.tagExists(ShotMomentTags.ReceivingPlayerUnknown) ||
      this.tagExists(FoulMomentTags.FouledPlayerJersey) ||
      this.tagExists(FoulMomentTags.FouledPlayerUserId) ||
      this.tagExists(FoulMomentTags.FouledPlayerUnknown);

    let userId = '';
    let playerJersey = '';
    let playerUnknown: string | false = false;
    if (tagExists) {
      if (this.type === SoccerMomentTypes.Foul) {
        userId = this.getFirstTagValue(FoulMomentTags.FouledPlayerUserId) || '';
        playerJersey = this.getFirstTagValue(FoulMomentTags.FouledPlayerJersey) || '';
        playerUnknown = this.getFirstTagValue(FoulMomentTags.FouledPlayerUnknown) || false;
        // TODO: handle using Assist player tags for goals instead of ReceivingPlayer tags
      } else {
        userId = this.getFirstTagValue(ShotMomentTags.ReceivingPlayerUserId) || '';
        playerJersey = this.getFirstTagValue(ShotMomentTags.ReceivingPlayerJersey) || '';
        playerUnknown = this.getFirstTagValue(ShotMomentTags.ReceivingPlayerUnknown) || false;
      }
    }
    return {
      userId,
      playerJersey,
      playerUnknown,
    };
  }
  // The secondary player is the player who is indirectly involved in the moment. Receiver/blocker/asisting/fouled player etc.
  get secondaryPlayer(): string {
    // If the secondary player is the recieving or assisting player it should be the same team as the primary player.
    // If it is the intercepting or blocking player or fouled it should be the opposite team.
    const playerTeam =
      this.result && ResultSuccessMapping[this.result] === SuccessStatuses.Successful ? this.team : this.otherTeam;

    const { userId: id, playerJersey: jersey, playerUnknown } = this.secondaryPlayerIdAndJersey;
    let userId = id || '';
    let playerJersey = jersey || '';
    userId = userId.replace(/\s/g, '');
    playerJersey = playerJersey.replace(/\s/g, '');

    if (!userId && !playerJersey && !playerUnknown) {
      return '';
    }

    if (playerUnknown) {
      return `x-${playerTeam}`;
    }

    if (!userId && !playerJersey) {
      return `x-${playerTeam}`;
    }
    if (playerJersey === SoccerGoalkeeper) {
      return `g-${playerTeam}`;
    }
    return userId ? `u${userId}-${playerTeam}` : `j${playerJersey}-${playerTeam}`;
  }
  // get the secondary player id based on the tags that are set for formatting
  get secondaryPlayerId(): string | null {
    const { userId, playerJersey, playerUnknown } = this.secondaryPlayerIdAndJersey;
    if (userId) {
      return userId;
    }
    if (playerJersey) {
      return playerJersey;
    }
    if (playerUnknown) {
      return format('domainTypes.moment.soccer.v1.jersey.unknown');
    }
    return null;
  }

  // helper to reset the secondary player tags
  override clearAthleteTags(): void {
    super.clearAthleteTags();
    this.setPlayerTags(null, ReceivingPlayer);
  }

  clearSecondaryPlayerTags(): void {
    this.setPlayerTags(null, ReceivingPlayer);
    this.setPlayerTags(null, MomentAthleteTags.AssistPlayer);
  }

  // Moment Results by Moment Type
  get result(): string | null {
    const type = this.type;
    let result = null;
    if (type === SoccerMomentTypes.Shot) {
      result = this.getFirstTagValue(ShotMomentTags.ShotResult) || this.getFirstTagValue(TagConstants.Result);
    }
    if (type === SoccerMomentTypes.Pass) {
      result = this.getFirstTagValue(PassingMomentTags.PassResult) || this.getFirstTagValue(TagConstants.Result) || '';
    }
    if (type === SoccerMomentTypes.Cross) {
      result = this.getFirstTagValue(CrossMomentTags.CrossResult) || this.getFirstTagValue(TagConstants.Result);
    }
    if (type === SoccerMomentTypes.SetPiece) {
      result = this.getFirstTagValue(SetPieceMomentTags.SetPieceResult) || this.getFirstTagValue(TagConstants.Result);
    }
    if (type === SoccerMomentTypes.PenaltyKick) {
      result = this.getFirstTagValue(PenaltyKickTags.Result);
    }
    if (type === SoccerMomentTypes.Goalkeeping) {
      result = this.getFirstTagValue(GoalkeepingMomentTags.GoalkeepingResult);
    }
    if (type === SoccerMomentTypes.Foul) {
      result = this.getFirstTagValue(FoulMomentTags.FoulCard);
    }
    return result ? formatResult(result) : null;
  }

  get attackingDirection(): string {
    // Flip the attaacking direction if team 2
    const periodAttackingDirection =
      this.getFirstTagValue(TagConstants.PeriodAttackingDirection) || AttackingDirections.LeftToRight;
    if (this.team === '1') {
      return periodAttackingDirection;
    }
    if (periodAttackingDirection === AttackingDirections.LeftToRight) {
      return AttackingDirections.RightToLeft;
    } else if (periodAttackingDirection === AttackingDirections.RightToLeft) {
      return AttackingDirections.LeftToRight;
    }

    return this.getFirstTagValue(TagConstants.PeriodAttackingDirection) || AttackingDirections.LeftToRight;
  }

  get xLocation(): string | null {
    const type = this.type;
    let xLocation = null;
    if (type === SoccerMomentTypes.Shot) {
      xLocation = this.getFirstTagValue(ShotMomentTags.ShotLocationX);
    } else if (type === SoccerMomentTypes.Pass) {
      xLocation = this.getFirstTagValue(PassingMomentTags.StartLocationX);
    } else if (type === SoccerMomentTypes.Cross) {
      xLocation = this.getFirstTagValue(CrossMomentTags.StartLocationX);
    } else if (type === SoccerMomentTypes.SetPiece) {
      xLocation = this.getFirstTagValue(SetPieceMomentTags.StartLocationX);
    } else if (type === SoccerMomentTypes.BallOut) {
      xLocation = this.getFirstTagValue(SetPieceMomentTags.StartLocationX);
    } else if (type === SoccerMomentTypes.Goalkeeping) {
      xLocation = this.getFirstTagValue(GoalkeepingMomentTags.StartLocationX);
    } else if (type === SoccerMomentTypes.Offside) {
      xLocation = this.getFirstTagValue(OffsideMomentTags.LocationX);
    } else if (type === SoccerMomentTypes.Foul) {
      xLocation = this.getFirstTagValue(FoulMomentTags.LocationX);
    }
    return xLocation ? xLocation : null;
  }

  get yLocation(): string | null {
    const type = this.type;
    let yLocation = null;
    if (type === SoccerMomentTypes.Shot) {
      yLocation = this.getFirstTagValue(ShotMomentTags.ShotLocationY);
    } else if (type === SoccerMomentTypes.Pass) {
      yLocation = this.getFirstTagValue(PassingMomentTags.StartLocationY);
    } else if (type === SoccerMomentTypes.Cross) {
      yLocation = this.getFirstTagValue(CrossMomentTags.StartLocationY);
    } else if (type === SoccerMomentTypes.SetPiece) {
      yLocation = this.getFirstTagValue(SetPieceMomentTags.StartLocationY);
    } else if (type === SoccerMomentTypes.BallOut) {
      yLocation = this.getFirstTagValue(SetPieceMomentTags.StartLocationY);
    } else if (type === SoccerMomentTypes.Goalkeeping) {
      yLocation = this.getFirstTagValue(GoalkeepingMomentTags.StartLocationY);
    } else if (type === SoccerMomentTypes.Offside) {
      yLocation = this.getFirstTagValue(OffsideMomentTags.LocationY);
    } else if (type === SoccerMomentTypes.Foul) {
      yLocation = this.getFirstTagValue(FoulMomentTags.LocationY);
    }
    return yLocation ? yLocation : null;
  }

  get xEndLocation(): string | null {
    const type = this.type;
    let xLocation = null;
    if (type === SoccerMomentTypes.Pass) {
      xLocation = this.getFirstTagValue(PassingMomentTags.EndLocationX);
    } else if (type === SoccerMomentTypes.Cross) {
      xLocation = this.getFirstTagValue(CrossMomentTags.EndLocationX);
    } else if (type === SoccerMomentTypes.SetPiece) {
      xLocation = this.getFirstTagValue(SetPieceMomentTags.EndLocationX);
    } else if (type === SoccerMomentTypes.BallOut) {
      xLocation = this.getFirstTagValue(SetPieceMomentTags.EndLocationX);
    } else if (type === SoccerMomentTypes.Goalkeeping) {
      xLocation = this.getFirstTagValue(GoalkeepingMomentTags.EndLocationX);
    }
    return xLocation ? xLocation : null;
  }

  get yEndLocation(): string | null {
    const type = this.type;
    let xLocation = null;
    if (type === SoccerMomentTypes.Pass) {
      xLocation = this.getFirstTagValue(PassingMomentTags.EndLocationY);
    } else if (type === SoccerMomentTypes.Cross) {
      xLocation = this.getFirstTagValue(CrossMomentTags.EndLocationY);
    } else if (type === SoccerMomentTypes.SetPiece) {
      xLocation = this.getFirstTagValue(SetPieceMomentTags.EndLocationY);
    } else if (type === SoccerMomentTypes.BallOut) {
      xLocation = this.getFirstTagValue(SetPieceMomentTags.EndLocationY);
    } else if (type === SoccerMomentTypes.Goalkeeping) {
      xLocation = this.getFirstTagValue(GoalkeepingMomentTags.EndLocationY);
    }
    return xLocation ? xLocation : null;
  }

  get originalShotLocationX(): string | null {
    return this.getFirstTagValue(ShotMomentTags.OriginalLocationX);
  }

  get originalShotLocationY(): string | null {
    return this.getFirstTagValue(ShotMomentTags.OriginalLocationY);
  }

  // format team:player:locationX:locationY:playerType:successStatus
  get points(): string[] {
    if ((this.isV3Moment && !this.xLocation) || (this.isV3Moment && !this.yLocation)) {
      return [];
    }

    const secondaryPlayerTeam =
      this.result && ResultSuccessMapping[this.result] === SuccessStatuses.Successful ? this.team : this.otherTeam;
    const secondaryActionIsOffensive = this.team === secondaryPlayerTeam;

    const points = [];
    const isSuccessful = this.isSuccessful || this.result === ShotResult.Saved;

    if (this.xLocation && this.yLocation) {
      // We flip the coordinates because the pitch is represented vertically in the points visualization.
      let x = parseFloat(this.yLocation);
      let y = parseFloat(this.xLocation);

      // V3 Penalty Kicks don't need their x & y coordinates to be flipped.
      if (this.isV3Moment && this.setPieceType && this.setPieceType === SetPieceTypes.PenaltyKick) {
        x = parseFloat(this.xLocation);
        y = parseFloat(this.yLocation);
      }

      // If the attacking direction is RightToLeft we need to flip the y coordinate.
      if (this.attackingDirection === AttackingDirections.LeftToRight) {
        y = -y;
        x = -x;
      }
      points.push(
        `${this.team}_${this.primaryPlayer}_${x.toFixed(4)}_${y.toFixed(4)}_p_${isSuccessful ? '1' : '0'}_${this.type}`
      );
    }
    if (
      (this.xEndLocation && this.yEndLocation) ||
      this.hasAssist ||
      this.result === ShotResult.Blocked ||
      this.result === ShotResult.Saved
    ) {
      let xEnd = 0;
      let yEnd = 0;
      if (this.xEndLocation && this.yEndLocation) {
        xEnd = parseFloat(this.yEndLocation);
        yEnd = parseFloat(this.xEndLocation);
      } else if (this.hasAssist) {
        xEnd = parseFloat(this.getFirstTagValue(TagConstants.PreviousMomentLocationY) || '0');
        yEnd = parseFloat(this.getFirstTagValue(TagConstants.PreviousMomentLocationX) || '0');
      } else if (this.result === ShotResult.Blocked) {
        xEnd = parseFloat(this.yLocation || '0');
        yEnd = parseFloat(this.xLocation || '0');
      } else if (this.result === ShotResult.Saved) {
        xEnd = parseFloat('0');
        yEnd = parseFloat('1');
      }
      const secondaryActionAttackingDirection = secondaryActionIsOffensive
        ? this.attackingDirection
        : SwappedAttackingDirections[this.attackingDirection];
      if (secondaryActionAttackingDirection === AttackingDirections.LeftToRight && this.result !== ShotResult.Saved) {
        yEnd = -yEnd;
        xEnd = -xEnd;
      }
      if (this.result === PassResult.OutOfPlay && points[0]) {
        const endPoint = `${this.team}_${this.primaryPlayer}_${xEnd.toFixed(4)}_${yEnd.toFixed(4)}_s_${isSuccessful || !secondaryActionIsOffensive ? '1' : '0'}_${this.type}`;
        points[0] = points[0].concat('__', endPoint);
      } else {
        const endPoint = `${this.team}_${this.secondaryPlayer}_${xEnd.toFixed(4)}_${yEnd.toFixed(4)}_s_${isSuccessful || !secondaryActionIsOffensive ? '1' : '0'}_${this.type}`;
        points.push(endPoint);
        const startPoint = points[0];
        if (points[0]) {
          points[0] = points[0].concat('__', endPoint);
        }
        if (points[1]) {
          points[1] = points[1].concat('__', startPoint);
        }
      }
    }

    return points;
  }

  get thirdLocation(): string {
    // bypass needing tagged location for rule abiding types
    if (this.type === SoccerMomentTypes.StartPeriod) {
      return NamedThirdLocation.MID;
    }
    if (this.subType === SoccerMomentTypes.PenaltyKick) {
      return NamedThirdLocation.ATK;
    }
    if (this.subType === SetPieceTypes.KickOff) {
      return NamedThirdLocation.MID;
    }
    if (this.type === SoccerMomentTypes.Goalkeeping) {
      return NamedThirdLocation.DEF;
    }
    if (this.subType === SetPieceTypes.GoalKick) {
      return NamedThirdLocation.DEF;
    }
    if (this.subType === SetPieceTypes.Corner) {
      return NamedThirdLocation.ATK;
    }
    if (this.type === SoccerMomentTypes.Cross) {
      return NamedThirdLocation.ATK;
    }
    if (this.setPieceSubType === SoccerMomentTypes.Cross) {
      return NamedThirdLocation.ATK;
    }

    const third = SoccerLocationCalculator.getPitchThirdFromX(this.xLocation);

    if (third === PitchThirds.Left && this.attackingDirection === AttackingDirections.LeftToRight) {
      return NamedThirdLocation.DEF;
    } else if (third === PitchThirds.Left && this.attackingDirection === AttackingDirections.RightToLeft) {
      return NamedThirdLocation.ATK;
    } else if (third === PitchThirds.Middle) {
      return NamedThirdLocation.MID;
    } else if (third === PitchThirds.Right && this.attackingDirection === AttackingDirections.RightToLeft) {
      return NamedThirdLocation.DEF;
    } else if (third === PitchThirds.Right && this.attackingDirection === AttackingDirections.LeftToRight) {
      return NamedThirdLocation.ATK;
    }
    return '';
  }

  get teamOnePlayerZones(): string[] | null {
    return this.getTagValues(TagConstants.TeamOnePlayerZones);
  }

  get teamTwoPlayerZones(): string[] | null {
    return this.getTagValues(TagConstants.TeamTwoPlayerZones);
  }

  get isV3Moment(): string | null {
    return this.getFirstTagValue(TagConstants.IsV3Moment);
  }

  get isSportsCodeMoment(): boolean {
    return Boolean(this.getFirstTagValue(SportsCodeTags.HudlCode));
  }

  get isSuccessful(): boolean {
    return ResultSuccessMapping[this.result || ''] === SuccessStatuses.Successful;
  }

  get successStatus(): string {
    return ResultSuccessMapping[this.result || ''];
  }

  get hasAssist(): boolean {
    return this.type === SoccerMomentTypes.Shot && this.isSuccessful && Boolean(this.secondaryPlayer);
  }

  get isPenaltyShootout(): string | null {
    const type = this.type;
    if (type !== SoccerMomentTypes.Foul) {
      return null;
    }
    const isPenaltyShootout = this.getFirstTagValue(TagConstants.IsPenaltyShootout);
    return isPenaltyShootout;
  }

  get previousMoment(): { type: string | null; team: string | null; subType: string | null; sequence: string | null } {
    const type = this.getFirstTagValue(TagConstants.PreviousMomentType);
    const team = this.getFirstTagValue(TagConstants.PreviousMomentTeam);
    const subType = this.getFirstTagValue(TagConstants.PreviousMomentSubType);
    const sequence = this.getFirstTagValue(TagConstants.PreviousMomentSequence);
    return { type, team, subType, sequence };
  }

  get nextMoment(): { type: string | null; team: string | null; subType: string | null } {
    const type = this.getFirstTagValue(TagConstants.NextMomentType);
    const team = this.getFirstTagValue(TagConstants.NextMomentTeam);
    const subType = this.getFirstTagValue(TagConstants.NextMomentSubType);
    return { type, team, subType };
  }

  // Sequence Information
  get sequence(): string | null {
    return this.getFirstTagValue(TagConstants.Sequence);
  }

  get sequenceStartTime(): number {
    // convert tag to number
    const sequenceStartTime = this.getFirstTagValue(TagConstants.SequenceStartTime);
    return sequenceStartTime ? Number(sequenceStartTime) : 0;
  }

  get sequenceEndTime(): number {
    // convert tag to number
    const sequenceEndTime = this.getFirstTagValue(TagConstants.SequenceEndTime);
    return sequenceEndTime ? Number(sequenceEndTime) : 0;
  }

  get originalSequenceStartTime(): number {
    // convert tag to number
    const originalSequenceStartTime = this.getFirstTagValue(TagConstants.OriginalSequenceStartTime);
    return originalSequenceStartTime ? Number(originalSequenceStartTime) : 0;
  }

  get originalSequenceEndTime(): number {
    // convert tag to number
    const originalSequenceEndTime = this.getFirstTagValue(TagConstants.OriginalSequenceEndTime);
    return originalSequenceEndTime ? Number(originalSequenceEndTime) : 0;
  }

  get sequenceStartEvent(): string | null {
    return this.getFirstTagValue(TagConstants.SequenceStartEvent);
  }

  get sequenceEndEvent(): string | null {
    return this.getFirstTagValue(TagConstants.SequenceEndEvent);
  }

  get sequencePassCount(): string | null {
    return this.getFirstTagValue(TagConstants.SequencePassCount);
  }

  get passesPerSequenceRange(): string | null {
    const range = this.getFirstTagValue(TagConstants.SequencePassCount);
    if (range !== null) {
      if (parseInt(range, 10) > 9) {
        return PassesPerSequenceRange.TenPlus;
      }
    }
    return range;
  }

  get sequenceTeamOnePlayers(): string[] | null {
    return this.getTagValues(TagConstants.SequenceTeamOnePlayers);
  }

  get sequenceTeamTwoPlayers(): string[] | null {
    return this.getTagValues(TagConstants.SequenceTeamTwoPlayers);
  }

  get sequenceTeamInPossession(): string | null {
    return this.getFirstTagValue(TagConstants.SequenceTeamInPossession);
  }

  get sequenceThirds(): string[] | null {
    return this.getTagValues(TagConstants.SequenceThirds);
  }

  get sequenceStartingThird(): string | null {
    return this.getFirstTagValue(TagConstants.SequenceStartingThird);
  }

  get sequenceEndingThird(): string | null {
    return this.getFirstTagValue(TagConstants.SequenceEndingThird);
  }

  get isTeamStat(): boolean {
    return this.getFirstTagValueAsBoolean(FoulMomentTags.IsTeamStat);
  }

  get isInsideBox(): boolean {
    const isInsideBoxValue = this.getFirstTagValue(TagConstants.IsInsideBox);
    return isInsideBoxValue === 'true';
  }
}
