import { DirectionsOfPlay, TeamMetadataTypes } from '../SportConstants';
import type { TaggingSettingsPlayerSettingsMetadata } from '../taggingSessions';
import {
  AttackingDirections,
  GoalkeepingTypes,
  IsPenaltyShootoutOptions,
  MomentTypes,
  PassChainTags,
  PassingMomentTags,
  PenaltyKickResult,
  PenaltyKickTags,
  SetPieceTags,
  SetPieceTypes,
  ShotMomentTags,
  ShotResult,
  StartPeriodTags,
  TagConstants,
} from './Constants';
import SoccerTaggingMoment from './SoccerTaggingMoment';

export const V3MomentTypes = {
  StartPeriod: 'startPeriod',
  Possession: 'possession',
  Pass: 'pass',
  ProblemReport: 'problemReport',
  OutOfBounds: 'outOfBounds',
  Restart: 'restart',
  ThrowIn: 'throwIn',
  Transition: 'transition',
  Foul: 'foul',
  FreeKick: 'freeKick',
  Cross: 'cross',
  GoalkeeperPunt: 'goalkeeperPunt',
  PassString: 'passString',
  Shot: 'shot',
  GoalKick: 'goalKick',
  Goal: 'goal',
  PenaltyKick: 'penaltyKick',
  Corner: 'corner',
  EndHalf: 'endHalf',
  GoalkeeperThrow: 'goalkeeperThrow',
  EndGame: 'endGame',
  StartShootout: 'startShootout',
  ShootoutShot: 'shootoutShot',
  Offsides: 'offsides',
  Custom: 'custom',
};

const MARKED_FOR_REMOVAL_TYPE = 'markedForRemoval';

export const V3Tags = {
  StartLocationX: 'startLocationX',
  StartLocationY: 'startLocationY',
  PassStringLength: 'passStringLength',
  PassResult: 'result',
  Result: 'result',
  Goal: 'goal',
  OffGoal: 'offGoal',
  OnGoal: 'onGoal',
  Blocked: 'blocked',
  PlayerJersey: 'playerJersey',
  AssistUserId: 'assistUserId',
  AssistJersey: 'assistJersey',
  AssistPlayerUnknown: 'assistPlayerUnknown',
};

export const mapV3TaggingSettingsOntoMoments = (
  moments: SoccerTaggingMoment[],
  taggingSettingsMetadata: TaggingSettingsPlayerSettingsMetadata[]
) => {
  const startingDirectionsOfPlay = taggingSettingsMetadata?.find(
    (item) => item.key === TeamMetadataTypes.StartingDirectionOfPlay
  )?.values;

  // Translate V3 tags to augmentated tags
  if (startingDirectionsOfPlay && startingDirectionsOfPlay?.length > 0) {
    const attackingDirections = startingDirectionsOfPlay.map((direction) => {
      switch (direction) {
        case DirectionsOfPlay.LeftToRight:
          return AttackingDirections.LeftToRight;
        case DirectionsOfPlay.RightToLeft:
          return AttackingDirections.RightToLeft;
        default:
          return AttackingDirections.LeftToRight;
      }
    });

    moments
      .filter((moment) => moment.type === MomentTypes.StartPeriod)
      .forEach((startPeriod, idx) => {
        startPeriod.setTagValue(StartPeriodTags.AttackingDirection, attackingDirections[idx]);
      });
  }
};

export const mapV3Moment: Record<
  string,
  (moment: SoccerTaggingMoment, idx: number, moments: SoccerTaggingMoment[]) => void
> = {
  [V3MomentTypes.Corner]: (moment: SoccerTaggingMoment) => setSetPieceTags(moment, SetPieceTypes.Corner),
  [V3MomentTypes.Cross]: (moment: SoccerTaggingMoment) => moment.setTagValue(TagConstants.Type, MomentTypes.Cross),
  [V3MomentTypes.EndGame]: (moment: SoccerTaggingMoment) =>
    moment.setTagValue(TagConstants.Type, MomentTypes.EndPeriod),
  [V3MomentTypes.EndHalf]: (moment: SoccerTaggingMoment) =>
    moment.setTagValue(TagConstants.Type, MomentTypes.EndPeriod),
  [V3MomentTypes.Foul]: (moment: SoccerTaggingMoment) => moment.setTagValue(TagConstants.Type, MomentTypes.Foul),
  [V3MomentTypes.FreeKick]: (moment: SoccerTaggingMoment) => setSetPieceTags(moment, SetPieceTypes.FreeKick),
  [V3MomentTypes.GoalKick]: (moment: SoccerTaggingMoment) => setSetPieceTags(moment, SetPieceTypes.GoalKick),
  [V3MomentTypes.GoalkeeperPunt]: (moment: SoccerTaggingMoment) => setGoalkeepingTags(moment, GoalkeepingTypes.Punt),
  [V3MomentTypes.GoalkeeperThrow]: (moment: SoccerTaggingMoment) => setGoalkeepingTags(moment, GoalkeepingTypes.Throw),
  [V3MomentTypes.Offsides]: (moment: SoccerTaggingMoment) => moment.setTagValue(TagConstants.Type, MomentTypes.Offside),
  [V3MomentTypes.ThrowIn]: (moment: SoccerTaggingMoment) => setSetPieceTags(moment, SetPieceTypes.ThrowIn),
  [V3MomentTypes.Pass]: (moment: SoccerTaggingMoment) => setPassTags(moment),
  [V3MomentTypes.StartPeriod]: (moment: SoccerTaggingMoment) =>
    moment.setTagValue(TagConstants.Type, MomentTypes.StartPeriod),
  [V3MomentTypes.OutOfBounds]: (moment: SoccerTaggingMoment, idx: number, moments: SoccerTaggingMoment[]) => {
    if (moments[idx - 1]?.type === V3MomentTypes.Goal) {
      setSetPieceTags(moment, SetPieceTypes.KickOff);
      moment.setTagValue(TagConstants.Team, moments[idx - 1]?.team === '1' ? '2' : '1');
    } else if (moments[idx + 1]?.type === V3MomentTypes.Foul || moments[idx - 1]?.type === V3MomentTypes.Foul) {
      moments[idx]?.setTagValue(TagConstants.Type, MARKED_FOR_REMOVAL_TYPE);
    } else {
      moment.setTagValue(TagConstants.Type, MomentTypes.BallOut);
      moment.setTagValue(TagConstants.Team, moments[idx + 1]?.team === '1' ? '2' : '1');
    }
  },
  [V3MomentTypes.PenaltyKick]: (moment: SoccerTaggingMoment, idx: number, moments: SoccerTaggingMoment[]) => {
    setSetPieceTags(moment, SetPieceTypes.PenaltyKick);
    setPenaltyKickSetPieceResultTags(moment, idx, moments);
  },
  [V3MomentTypes.Shot]: (moment: SoccerTaggingMoment, idx: number, moments: SoccerTaggingMoment[]) => {
    const nextMoment = moments[idx + 1];
    const attackingDirection = moments
      .slice(0, idx)
      .reverse()
      .find((m) => m.type === MomentTypes.StartPeriod)
      ?.getFirstTagValue(StartPeriodTags.AttackingDirection);
    const assistingUserId =
      nextMoment?.type === V3MomentTypes.Goal ? nextMoment.getFirstTagValue(V3Tags.AssistUserId) : null;
    const assistingJersey =
      nextMoment?.type === V3MomentTypes.Goal ? nextMoment.getFirstTagValue(V3Tags.AssistJersey) : null;
    const assistUnknown =
      nextMoment?.type === V3MomentTypes.Goal ? nextMoment.getFirstTagValue(V3Tags.AssistPlayerUnknown) : null;
    const assistTags = [
      {
        tag: ShotMomentTags.ReceivingPlayerUserId,
        value: assistingUserId,
      },
      {
        tag: ShotMomentTags.ReceivingPlayerJersey,
        value: assistingJersey,
      },
      {
        tag: ShotMomentTags.ReceivingPlayerUnknown,
        value: assistUnknown,
      },
    ];
    // assist tag is the first tag in array with a non-null value
    const assistTag = assistTags.find((tag) => tag.value) || null;
    setShotTags(moment, attackingDirection, assistTag);
  },
  [V3MomentTypes.StartShootout]: (moment: SoccerTaggingMoment) => {
    moment.setTagValue(TagConstants.Type, MomentTypes.StartPeriod);
    moment.setTagValue(StartPeriodTags.IsPenaltyShootout, IsPenaltyShootoutOptions.Yes);
  },
  [V3MomentTypes.ShootoutShot]: (moment: SoccerTaggingMoment) => {
    moment.setTagValue(TagConstants.Type, MomentTypes.PenaltyKick);
    setPenaltyKickShootoutResultTags(moment);
  },
  [V3MomentTypes.PassString]: (moment: SoccerTaggingMoment) => {
    moment.setTagValue(TagConstants.Type, MomentTypes.PassChain);
    moment.setTagValue(PassChainTags.PassChainLength, moment.getFirstTagValue(V3Tags.PassStringLength));
    moment.deleteTag(V3Tags.PassStringLength);
  },
};

function setSetPieceTags(moment: SoccerTaggingMoment, setPieceType: string) {
  moment.setTagValue(TagConstants.Type, MomentTypes.SetPiece);
  moment.setTagValue(TagConstants.SetPieceType, setPieceType);
}

function setGoalkeepingTags(moment: SoccerTaggingMoment, goalkeepingType: string) {
  moment.setTagValue(TagConstants.Type, MomentTypes.Goalkeeping);
  moment.setTagValue(TagConstants.GoalKeepingType, goalkeepingType);
}

function setPassTags(moment: SoccerTaggingMoment) {
  moment.setTagValue(TagConstants.Type, MomentTypes.Pass);
  moment.setTagValue(TagConstants.PassResult, moment.getFirstTagValue(V3Tags.PassResult));
  moment.setTagValue(PassingMomentTags.StartLocationX, moment.getFirstTagValue(V3Tags.StartLocationX));
  moment.setTagValue(PassingMomentTags.StartLocationY, moment.getFirstTagValue(V3Tags.StartLocationY));
  moment.deleteTag(V3Tags.StartLocationX);
  moment.deleteTag(V3Tags.StartLocationY);
  moment.deleteTag(V3Tags.PassResult);
}

function setPenaltyKickSetPieceResultTags(moment: SoccerTaggingMoment, idx: number, moments: SoccerTaggingMoment[]) {
  // V3 Tags Penalty Kicks: Penalty Kick -> Restart / Possession  -> Shot -> Goal/Out of bounds/Other
  let shotMomentIndex = -1;
  for (let i = idx + 2; i > idx; i--) {
    if (moments[i]?.type === V3MomentTypes.Shot) {
      shotMomentIndex = i;
      break;
    }
  }

  if (shotMomentIndex > 0) {
    const shotMoment = moments[shotMomentIndex];
    moment.setTagValue(SetPieceTags.PlayerJersey, shotMoment.getFirstTagValue(V3Tags.PlayerJersey));
    moment.setTagValue(SetPieceTags.LocationStartX, shotMoment.getFirstTagValue(V3Tags.StartLocationX));
    moment.setTagValue(SetPieceTags.LocationStartY, shotMoment.getFirstTagValue(V3Tags.StartLocationY));

    // Add isInsideBox property for penalty kicks (always inside box)
    moment.setTagValue(TagConstants.IsInsideBox, 'true');

    switch (shotMoment.getFirstTagValue(V3Tags.Result)) {
      case V3Tags.Goal:
        moment.setTagValue(TagConstants.SetPieceResult, PenaltyKickResult.Goal);
        break;
      case V3Tags.OffGoal:
        moment.setTagValue(TagConstants.SetPieceResult, PenaltyKickResult.OffTarget);
        break;
      default:
        moment.setTagValue(TagConstants.SetPieceResult, PenaltyKickResult.Saved);
        break;
    }
    shotMoment?.setTagValue(TagConstants.Type, MARKED_FOR_REMOVAL_TYPE);
  }
}

function setPenaltyKickShootoutResultTags(moment: SoccerTaggingMoment) {
  switch (moment.getFirstTagValue(V3Tags.Result)) {
    case V3Tags.Goal:
      moment.setTagValue(PenaltyKickTags.Result, PenaltyKickResult.Goal);
      break;
    case V3Tags.OffGoal:
      moment.setTagValue(PenaltyKickTags.Result, PenaltyKickResult.OffTarget);
      break;
    default:
      moment.setTagValue(PenaltyKickTags.Result, PenaltyKickResult.Saved);
      break;
  }
}

function mapShotPosition(v3x: number, v3y: number, reverse: boolean) {
  const x = reverse ? -1 * v3y : v3y;
  const y = -1 * v3x;

  return { x, y };
}

function isLocationInsideBox(x: number, y: number): boolean {
  const penaltyBoxLeft = -0.66;
  const penaltyBoxRight = 0.66;
  const penaltyBoxTop = 1;
  const penaltyBoxBottom = 0.66;

  return x >= penaltyBoxLeft && x <= penaltyBoxRight && y >= penaltyBoxBottom && y <= penaltyBoxTop;
}

function setShotTags(
  moment: SoccerTaggingMoment,
  attackingDirection: string | null | undefined,
  assistTag: { tag: string; value: string | null } | null
) {
  moment.setTagValue(TagConstants.Type, MomentTypes.Shot);
  if (assistTag && !moment.getFirstTagValue(assistTag.tag)) {
    moment.setTagValue(assistTag.tag, assistTag.value);
  }

  const hasXLocation = moment.tags.find((t) => t.key === V3Tags.StartLocationX);
  const hasYLocation = moment.tags.find((t) => t.key === V3Tags.StartLocationY);

  if (hasXLocation && hasYLocation) {
    const originalX = moment.getFirstTagValue(V3Tags.StartLocationX);
    const originalY = moment.getFirstTagValue(V3Tags.StartLocationY);

    const isInsideBox = isLocationInsideBox(parseFloat(originalX || '0'), parseFloat(originalY || '0'));
    moment.setTagValue(TagConstants.IsInsideBox, isInsideBox.toString());
    const shotPosition = mapShotPosition(
      parseFloat(originalX || '0'),
      parseFloat(originalY || '0'),
      (attackingDirection === AttackingDirections.RightToLeft && moment.team === '1') ||
        (moment.team === '2' && attackingDirection === AttackingDirections.LeftToRight)
    );

    moment.setTagValue(ShotMomentTags.ShotLocationX, shotPosition.x.toString());
    moment.setTagValue(ShotMomentTags.ShotLocationY, shotPosition.y.toString());

    // Store original coordinates in new tags before deleting the V3 tags
    moment.setTagValue(ShotMomentTags.OriginalLocationX, originalX);
    moment.setTagValue(ShotMomentTags.OriginalLocationY, originalY);

    moment.deleteTag(V3Tags.StartLocationX);
    moment.deleteTag(V3Tags.StartLocationY);
  }

  switch (moment.getFirstTagValue(V3Tags.Result)) {
    case V3Tags.Goal:
      moment.setTagValue(ShotMomentTags.ShotResult, ShotResult.Goal);
      break;
    case V3Tags.OffGoal:
      moment.setTagValue(ShotMomentTags.ShotResult, ShotResult.OffTarget);
      break;
    case V3Tags.Blocked:
      moment.setTagValue(ShotMomentTags.ShotResult, ShotResult.Blocked);
      break;
    default:
      moment.setTagValue(ShotMomentTags.ShotResult, ShotResult.Saved);
  }
  moment.deleteTag(V3Tags.Result);
}

export const removeLegacyMoments = (moments: SoccerTaggingMoment[]) => {
  const legacyMomentTypes = new Set([
    V3MomentTypes.Possession,
    V3MomentTypes.Transition,
    V3MomentTypes.Goal,
    V3MomentTypes.Restart,
    V3MomentTypes.ProblemReport,
    MARKED_FOR_REMOVAL_TYPE,
  ]);
  const filteredMoments = moments.filter((m) => !legacyMomentTypes.has(m.type ?? ''));
  return filteredMoments;
};

export default { mapV3Moment };
