export interface Tag {
  key: string;
  values: string[];
}

export interface MomentJson {
  momentId: string;
  startTimeMs: number;
  endTimeMs: number;
  tags: Tag[];
}

export interface UserIdMomentAthlete {
  userId: string;
}

export interface JerseyMomentAthlete {
  jersey: string;
}

export interface UnknownMomentAthlete {
  unknown: true | string;
}

export interface NoneMomentAthlete {
  none: true | string;
}

/**
 * Only used in wrestling at the moment
 */
export interface NameMomentAthlete {
  name: string;
}

type Nullable<T> = { [K in keyof T]: T[K] | null };

/**
 * Legacy moment athlete shape. Each property is nullable but we only
 * expect one property to be non-null at a time
 */
export type LegacyMomentAthlete = Nullable<UserIdMomentAthlete> &
  Nullable<JerseyMomentAthlete> &
  Nullable<UnknownMomentAthlete> &
  Nullable<NoneMomentAthlete>;

/**
 * Name is not an original property for moment athletes. It was added for wrestling. This type should only
 * be used for helpers
 */
export type LegacyMomentAthleteWithName = LegacyMomentAthlete & Nullable<NameMomentAthlete>;

/**
 * New moment athlete shape. Forces only one property (`userId`, `jersey`, `unknown` or `none`)
 * to be defined at a time
 */
export type PartialMomentAthlete =
  | UserIdMomentAthlete
  | JerseyMomentAthlete
  | UnknownMomentAthlete
  | NoneMomentAthlete
  | NameMomentAthlete;

export type MomentAthlete = PartialMomentAthlete | LegacyMomentAthlete;

export type MomentAthleteWithTeam<T extends MomentAthlete> = T & { team: MomentTeamIndex | null };

export type MomentTeamIndex = '1' | '2';

export type TaggingLocationPlayingSurface = 'ice-hockey-rink'; // add more as needed

export type TaggingLocation = {
  x: string | null;
  y: string | null;
};
