import type {
  JerseyMome<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MomentTeamIndex,
  NameM<PERSON><PERSON><PERSON><PERSON>let<PERSON>,
  NoneMoment<PERSON>thlet<PERSON>,
  UnknownM<PERSON><PERSON><PERSON><PERSON>let<PERSON>,
  UserIdMomentAthlete,
} from './types';

export function isUserIdMomentAthlete(athlete: <PERSON><PERSON><PERSON>let<PERSON>): athlete is UserIdMomentAthlete {
  return 'userId' in athlete && !!athlete.userId;
}

export function isJerseyMomentAthlete(athlete: <PERSON><PERSON>th<PERSON><PERSON>): athlete is JerseyMomentAthlete {
  return 'jersey' in athlete && !!athlete.jersey;
}

export function isUnknownMomentAthlete(athlete: <PERSON>Athlete): athlete is UnknownMomentAthlete {
  return 'unknown' in athlete && !!athlete.unknown;
}

export function isNoneMomentAthlete(athlete: <PERSON><PERSON>thlete): athlete is NoneMomentAthlete {
  return 'none' in athlete && !!athlete.none;
}

export function isNameMomentAthlete(athlete: <PERSON>Athlete): athlete is NameMomentAthlete {
  return 'name' in athlete && !!athlete.name;
}

export function getMoment<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(athlete: <PERSON><PERSON><PERSON><PERSON><PERSON>): string {
  if (isUserIdMoment<PERSON>th<PERSON><PERSON>(athlete)) {
    return `user-${athlete.userId}`;
  }

  if (isJerseyMomentAthlete(athlete)) {
    return `jersey-${athlete.jersey}`;
  }

  if (isNoneMomentAthlete(athlete)) {
    return 'none';
  }

  return 'unknown';
}

export function toOtherTeamIndex(teamIndex: MomentTeamIndex | null): MomentTeamIndex | null {
  switch (teamIndex) {
    case '1':
      return '2';
    case '2':
      return '1';
    default:
      return teamIndex;
  }
}
