import { format } from '@hudl/platform-i18n-adapter';

import Constants from '../../moments/hudl-tagging/soccer/Constants';
import SoccerTaggingMoment from '../../moments/hudl-tagging/soccer/SoccerTaggingMoment';
import { formatCustomMomentType } from '../formatCustomMoment';

const {
  PassResult,
  CrossResult,
  ShotResult,
  FoulCards,
  FoulTypes,
  GoalkeepingResult,
  SetPieceResult,
  SetPieceTypes,
  PenaltyKickResult,
  MomentTypes,
} = Constants;

const I18N_MOMENT_PREFIX = 'domainTypes.moment.soccer';
const I18N_MOMENT_TAG_RESULT_PREFIX = `${I18N_MOMENT_PREFIX}.tag.result`;

// additional labels for soccer moments, displayed on data chips
export function formatSoccerAdditionalLabels(moment: SoccerTaggingMoment): string[] | null {
  if (!moment || !moment.type) {
    return null;
  }
  const resultLabel = `${I18N_MOMENT_TAG_RESULT_PREFIX}.${moment.result}`;
  const labels: string[] = [];
  if (moment.type === MomentTypes.Shot && moment.result === ShotResult.Goal) {
    return labels; // no additional labels for goals
  }
  if (moment.result && moment.type !== MomentTypes.Foul) {
    labels.push(format(resultLabel));
  }
  return labels;
}

export function getSecondaryPlayer(moment: SoccerTaggingMoment): string | null {
  if (!moment || !moment.secondaryPlayerId) {
    return null;
  }
  return moment.secondaryPlayerId;
}

export function formatSoccerMomentLabel(moment: SoccerTaggingMoment): string | null {
  return moment.type; // TODO: i18n this and maybe use subtype or result for some moments
}

export type SoccerMomentLabels = [] | [string] | [string, string];

// TODO: somehow combine the shared functionality
export const createSoccerDataChipLabels = (moment: SoccerTaggingMoment): string[] => {
  let momentLabels: string[] = [];

  if (moment.type === MomentTypes.Custom) {
    momentLabels = [formatCustomMomentType(moment)];
  } else if (moment.type === MomentTypes.Pass || moment.type === MomentTypes.Cross) {
    momentLabels = createPassCrossTypeSoccerDataChipLabels(moment);
  } else if (moment.type === MomentTypes.Shot) {
    momentLabels = createShotTypeSoccerDataChipLabels(moment);
  } else if (moment.type === MomentTypes.BallOut || moment.type === MomentTypes.Offside) {
    momentLabels = createBallOutOffsideTypeSoccerLabels(moment);
  } else if (moment.type === MomentTypes.Foul) {
    momentLabels = createFoulTypeSoccerLabels(moment);
  } else if (moment.type === MomentTypes.Goalkeeping) {
    momentLabels = createGoalkeepingTypeSoccerLabels(moment);
  } else if (moment.type === MomentTypes.SetPiece) {
    momentLabels = createSetPieceTypeSoccerDataChipLabels(moment);
  } else {
    momentLabels = createBaseTypeSoccerLabels(moment);
  }

  return momentLabels;
};

export const createPassCrossTypeSoccerDataChipLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  const firstChipStrings = [];
  let secondChipString = undefined;

  const labelI8nTypeKey = `domainTypes.moment.soccer.v1.${moment.type}`;
  const typeFormattedResult = format(labelI8nTypeKey);
  if (typeFormattedResult === labelI8nTypeKey) {
    firstChipStrings.push(moment.type);
  } else {
    firstChipStrings.push(typeFormattedResult);
  }

  if (
    moment.result &&
    (moment.result !== PassResult.OutOfPlay || moment.result !== CrossResult.OutOfPlay) &&
    moment.secondaryPlayerId
  ) {
    // Secondary Result Chip: Adds formatted string for second chip
    const labelI8nSecondaryKey = `domainTypes.moment.soccer.v1.result.${moment.result}`;
    const secondaryFormattedResult = format(labelI8nSecondaryKey);
    if (secondaryFormattedResult !== labelI8nSecondaryKey) {
      secondChipString = secondaryFormattedResult;
    }
  }
  return secondChipString ? [firstChipStrings.join(' '), secondChipString] : [firstChipStrings.join(' ')];
};

export const createShotTypeSoccerDataChipLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  const firstChipStrings = [];
  let secondChipString = undefined;

  let labelI8nTypeKey = 'domainTypes.moment.soccer.v1';
  if (moment.result === ShotResult.Goal) {
    labelI8nTypeKey += `.${moment.result}`;
  } else {
    labelI8nTypeKey += `.${moment.type}`;
  }

  const typeFormattedResult = format(labelI8nTypeKey);
  if (typeFormattedResult === labelI8nTypeKey) {
    firstChipStrings.push(moment.type);
  } else {
    firstChipStrings.push(typeFormattedResult);
  }

  if (moment.result && moment.secondaryPlayer && moment.result !== ShotResult.OffTarget) {
    // Secondary Result Chip: Adds formatted string for second chip
    const labelI8nSecondaryKey = `domainTypes.moment.soccer.v1.result.${moment.result}`;
    const secondaryFormattedResult = format(labelI8nSecondaryKey);
    if (secondaryFormattedResult !== labelI8nSecondaryKey) {
      secondChipString = secondaryFormattedResult;
    }
  }
  return secondChipString ? [firstChipStrings.join(' '), secondChipString] : [firstChipStrings.join(' ')];
};

export const createSetPieceTypeSoccerDataChipLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  const firstChipStrings = [];
  let secondChipString = undefined;

  // Type: SetPiece
  // SubType: ThrowIn, Corner, Free Kick, Penalty Kick, Kickoff
  let labelI8nTypeKey = `domainTypes.moment.soccer.v1.${moment.type}.${moment.subType}`;
  if (moment.subType === SetPieceTypes.ThrowIn && (moment.violation || moment.result === SetPieceResult.OutOfPlay)) {
    labelI8nTypeKey = `domainTypes.moment.soccer.v1.${moment.type}.${moment.subType}.lowercase`;
  } else if (moment.subType === SetPieceTypes.PenaltyKick && moment.result === PenaltyKickResult.Goal) {
    labelI8nTypeKey += `.${moment.result}`;
  }
  const typeFormattedResult = format(labelI8nTypeKey);
  if (typeFormattedResult === labelI8nTypeKey) {
    firstChipStrings.push(moment.type);
  } else {
    firstChipStrings.push(typeFormattedResult);
  }

  // No Shot, Cross, Pass, etc. for these types
  const noSubTypeSetPieceTypes = [SetPieceTypes.GoalKick, SetPieceTypes.ThrowIn, SetPieceTypes.PenaltyKick];

  // Primary: Add subtype, assuming exists and violation is present to override
  let labelI8nSupplementaryKey = null;
  if (moment.violation) {
    labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.violation`;
  } else if (moment.subType && !noSubTypeSetPieceTypes.includes(moment.subType) && moment.setPieceSubType) {
    labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.${moment.setPieceSubType}`;
  }
  if (labelI8nSupplementaryKey) {
    const subTypeResult = format(labelI8nSupplementaryKey);
    if (subTypeResult !== labelI8nSupplementaryKey) {
      firstChipStrings.push(`${subTypeResult}`);
    }
  }

  if (moment.subType === SetPieceTypes.PenaltyKick && moment.result === SetPieceResult.Goal) {
    return [firstChipStrings.join(' ')];
  }

  let labelI8nResultKey = `domainTypes.moment.soccer.v1`;
  if (moment.result === SetPieceResult.Goal) {
    // Secondary Result Chip: Adds Goal as secondary result chip instead of assist
    labelI8nResultKey += `.${moment.result}`;
    const resultFormatted = format(labelI8nResultKey);
    if (labelI8nResultKey && resultFormatted !== labelI8nResultKey) {
      secondChipString = resultFormatted;
    }
  } else if (moment.result) {
    // Secondary Result Chip: Adds formatted result string for second chip
    labelI8nResultKey += `.result.${moment.result}`;
    const resultFormatted = format(labelI8nResultKey);
    if (labelI8nResultKey && resultFormatted !== labelI8nResultKey) {
      secondChipString = resultFormatted;
    }
  }

  return secondChipString ? [firstChipStrings.join(' '), secondChipString] : [firstChipStrings.join(' ')];
};

export const createSoccerMomentLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  let momentLabels = [];

  if (moment.type === MomentTypes.Pass || moment.type === MomentTypes.Cross) {
    momentLabels = createBaseTypeSoccerLabels(moment);
  } else if (moment.type === MomentTypes.Shot) {
    momentLabels = createShotTypeSoccerLabels(moment);
  } else if (moment.type === MomentTypes.BallOut || moment.type === MomentTypes.Offside) {
    momentLabels = createBallOutOffsideTypeSoccerLabels(moment);
  } else if (moment.type === MomentTypes.Foul) {
    momentLabels = createFoulTypeSoccerLabels(moment);
  } else if (moment.type === MomentTypes.Goalkeeping) {
    momentLabels = createGoalkeepingTypeSoccerLabels(moment);
  } else if (moment.type === MomentTypes.SetPiece) {
    momentLabels = createSetPieceTypeSoccerLabels(moment);
  } else {
    momentLabels = createBaseTypeSoccerLabels(moment);
  }

  return momentLabels;
};

export const createBaseTypeSoccerLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  const firstChipStrings = [];
  let secondChipString = undefined;

  // Type: Pass, Cross
  const labelI8nTypeKey = `domainTypes.moment.soccer.v1.${moment.type}`;
  const typeFormattedResult = format(labelI8nTypeKey);
  if (typeFormattedResult === labelI8nTypeKey) {
    firstChipStrings.push(moment.type);
  } else {
    firstChipStrings.push(typeFormattedResult);
  }

  let labelI8nSupplementaryKey = null;
  if (
    moment.result &&
    (moment.result !== PassResult.OutOfPlay || moment.result !== CrossResult.OutOfPlay) &&
    !moment.isV3Moment
  ) {
    // Secondary Result Chip: Adds formatted string for second chip
    const labelI8nSecondaryKey = `domainTypes.moment.soccer.v1.result.${moment.result}`;
    const secondaryFormattedResult = format(labelI8nSecondaryKey);
    if (secondaryFormattedResult !== labelI8nSecondaryKey) {
      secondChipString = secondaryFormattedResult;
    }
  } else if (moment.result === PassResult.OutOfPlay || moment.result === CrossResult.OutOfPlay) {
    // Primary Result: Adds out of Play to first tag
    labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.${moment.result}`;
    const formattedResult = format(labelI8nSupplementaryKey);
    if (formattedResult !== labelI8nSupplementaryKey) {
      firstChipStrings.push(formattedResult);
    }
  } else if (moment.successStatus) {
    // V3 / Missing Data: Adds Successful, Unsuccessful to primary chip if no result data for second chip is available
    labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.${moment.successStatus}`;
    const successFormattedResult = format(labelI8nSupplementaryKey);
    if (successFormattedResult !== labelI8nSupplementaryKey) {
      firstChipStrings.push(successFormattedResult);
    }
  }
  return secondChipString ? [firstChipStrings.join(' '), secondChipString] : [firstChipStrings.join(' ')];
};

export const createShotTypeSoccerLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  const firstChipStrings = [];
  let secondChipString = undefined;

  // Type: Shot
  let labelI8nTypeKey = 'domainTypes.moment.soccer.v1';
  if (moment.result === ShotResult.Goal) {
    labelI8nTypeKey += `.${moment.result}`;
  } else {
    labelI8nTypeKey += `.${moment.type}`;
  }

  const typeFormattedResult = format(labelI8nTypeKey);
  if (typeFormattedResult === labelI8nTypeKey) {
    firstChipStrings.push(moment.type);
  } else {
    firstChipStrings.push(typeFormattedResult);
  }

  let labelI8nSupplementaryKey = null;
  if (moment.result && moment.secondaryPlayer && moment.result !== ShotResult.OffTarget) {
    // Secondary Result Chip: Adds formatted string for second chip
    const labelI8nSecondaryKey = `domainTypes.moment.soccer.v1.result.${moment.result}`;
    const secondaryFormattedResult = format(labelI8nSecondaryKey);
    if (secondaryFormattedResult !== labelI8nSecondaryKey) {
      secondChipString = secondaryFormattedResult;
    }
  } else if (moment.result === ShotResult.OffTarget) {
    // Primary Result: Adds off Target to first tag
    labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.${moment.result}`;
    const formattedResult = format(labelI8nSupplementaryKey);
    if (formattedResult !== labelI8nSupplementaryKey) {
      firstChipStrings.push(formattedResult);
    }
  } else if (moment.result && !moment.secondaryPlayer && moment.result !== ShotResult.Goal) {
    // V3 / Missing Data: Adds result to first chip if missing secondary player information
    labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.${moment.result}`;
    const formattedResult = format(labelI8nSupplementaryKey);
    if (formattedResult !== labelI8nSupplementaryKey) {
      firstChipStrings.push(formattedResult);
    }
  }
  return secondChipString ? [firstChipStrings.join(' '), secondChipString] : [firstChipStrings.join(' ')];
};

export const createFoulTypeSoccerLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  const firstChipStrings = [];
  let secondChipString = undefined;

  // Type: Foul
  if (moment.foulType !== FoulTypes.Card) {
    const labelI8nTypeKey = 'domainTypes.moment.soccer.v1.foul';
    const typeFormattedResult = format(labelI8nTypeKey);
    if (typeFormattedResult === labelI8nTypeKey) {
      firstChipStrings.push(moment.type);
    } else {
      firstChipStrings.push(typeFormattedResult);
    }
  }

  if (moment.primaryPlayer && moment.foulType !== FoulTypes.Card) {
    const labelI8nFoulAgainstKey = 'domainTypes.moment.soccer.v1.foul.fouled';
    const againstFormattedResult = format(labelI8nFoulAgainstKey);
    if (againstFormattedResult !== labelI8nFoulAgainstKey) {
      firstChipStrings.push(againstFormattedResult);
    }
  }

  if (moment.result === FoulCards.Red || moment.result === FoulCards.Yellow) {
    // Primary Result: Adds card to first tag
    const labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.foul.card.${moment.result}`;
    const formattedResult = format(labelI8nSupplementaryKey);
    if (formattedResult !== labelI8nSupplementaryKey) {
      if (moment.foulType !== FoulTypes.Card) {
        firstChipStrings.push(`(${formattedResult})`);
      } else if (moment.foulType === FoulTypes.Card) {
        firstChipStrings.push(`${formattedResult}`);
      }
    }
  }

  if (moment.secondaryPlayer) {
    // Secondary Result Chip: Adds fouled player for second chip
    const labelI8nSecondaryKey = 'domainTypes.moment.soccer.v1.foul.committed';
    const secondaryFormattedResult = format(labelI8nSecondaryKey);
    if (secondaryFormattedResult !== labelI8nSecondaryKey) {
      secondChipString = secondaryFormattedResult;
    }
  }

  return secondChipString ? [firstChipStrings.join(' '), secondChipString] : [firstChipStrings.join(' ')];
};

export const createGoalkeepingTypeSoccerLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  const firstChipStrings = [];
  let secondChipString = undefined;

  // Type: Goalkeeping
  let labelI8nTypeKey = 'domainTypes.moment.soccer.v1.goalkeeping';
  if (moment.subType) {
    labelI8nTypeKey += `.${moment.subType}`;
  }
  const typeFormattedResult = format(labelI8nTypeKey);
  if (typeFormattedResult === labelI8nTypeKey) {
    firstChipStrings.push(moment.type);
  } else {
    firstChipStrings.push(typeFormattedResult);
  }

  if (moment.result === GoalkeepingResult.OutOfPlay) {
    // Primary Result: Adds out of Play to first tag
    const labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.${moment.result}`;
    const formattedResult = format(labelI8nSupplementaryKey);
    if (formattedResult !== labelI8nSupplementaryKey) {
      firstChipStrings.push(formattedResult);
    }
  } else if (moment.result) {
    // Secondary Result Chip: Adds formatted string for second chip
    const labelI8nSecondaryKey = `domainTypes.moment.soccer.v1.result.${moment.result}`;
    const secondaryFormattedResult = format(labelI8nSecondaryKey);
    if (secondaryFormattedResult !== labelI8nSecondaryKey) {
      secondChipString = secondaryFormattedResult;
    }
  }

  return secondChipString ? [firstChipStrings.join(' '), secondChipString] : [firstChipStrings.join(' ')];
};

export const createSetPieceTypeSoccerLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  const firstChipStrings = [];
  let secondChipString = undefined;

  // Type: SetPiece
  // SubType: ThrowIn, Corner, Free Kick, Penalty Kick, Kickoff
  let labelI8nTypeKey = `domainTypes.moment.soccer.v1.${moment.type}.${moment.subType}`;
  if (moment.subType === SetPieceTypes.ThrowIn && (moment.violation || moment.result === SetPieceResult.OutOfPlay)) {
    labelI8nTypeKey = `domainTypes.moment.soccer.v1.${moment.type}.${moment.subType}.lowercase`;
  } else if (moment.subType === SetPieceTypes.PenaltyKick && moment.result === PenaltyKickResult.Goal) {
    labelI8nTypeKey += `.${moment.result}`;
  }
  const typeFormattedResult = format(labelI8nTypeKey);
  if (typeFormattedResult === labelI8nTypeKey) {
    firstChipStrings.push(moment.type);
  } else {
    firstChipStrings.push(typeFormattedResult);
  }

  // No Shot, Cross, Pass, etc. for these types
  const noSubTypeSetPieceTypes = [SetPieceTypes.GoalKick, SetPieceTypes.ThrowIn, SetPieceTypes.PenaltyKick];

  // Primary: Add subtype, assuming exists and violation is present to override
  let labelI8nSupplementaryKey = null;
  if (moment.violation) {
    labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.violation`;
  } else if (moment.subType && !noSubTypeSetPieceTypes.includes(moment.subType) && moment.setPieceSubType) {
    labelI8nSupplementaryKey = `domainTypes.moment.soccer.v1.${moment.setPieceSubType}`;
  }
  if (labelI8nSupplementaryKey) {
    const subTypeResult = format(labelI8nSupplementaryKey);
    if (subTypeResult !== labelI8nSupplementaryKey) {
      firstChipStrings.push(`${subTypeResult}`);
    }
  }

  if (moment.subType === SetPieceTypes.PenaltyKick && moment.result === SetPieceResult.Goal) {
    return [firstChipStrings.join(' ')];
  }

  let labelI8nResultKey = `domainTypes.moment.soccer.v1`;
  if (moment.result === SetPieceResult.OutOfPlay || moment.result === SetPieceResult.OffTarget) {
    // Primary Chip: Adds formatted result string for first chip
    labelI8nResultKey += `.${moment.result}`;
    const supplementaryPrimaryFormattedDetail = format(labelI8nResultKey);
    if (supplementaryPrimaryFormattedDetail !== labelI8nResultKey) {
      firstChipStrings.push(supplementaryPrimaryFormattedDetail);
    }
  } else if (moment.result === SetPieceResult.Goal) {
    // Secondary Result Chip: Adds Goal as secondary result chip instead of assist
    labelI8nResultKey += `.${moment.result}`;
    const resultFormatted = format(labelI8nResultKey);
    if (labelI8nResultKey && resultFormatted !== labelI8nResultKey) {
      secondChipString = resultFormatted;
    }
  } else if (moment.result) {
    // Secondary Result Chip: Adds formatted result string for second chip
    labelI8nResultKey += `.result.${moment.result}`;
    const resultFormatted = format(labelI8nResultKey);
    if (labelI8nResultKey && resultFormatted !== labelI8nResultKey) {
      secondChipString = resultFormatted;
    }
  }

  return secondChipString ? [firstChipStrings.join(' '), secondChipString] : [firstChipStrings.join(' ')];
};

export const createBallOutOffsideTypeSoccerLabels = (moment: SoccerTaggingMoment): SoccerMomentLabels => {
  // Type: Ballout, Offside
  const labelI8nTypeKey = `domainTypes.moment.soccer.v1.${moment.type}`;
  const typeFormattedResult = format(labelI8nTypeKey);

  if (typeFormattedResult === labelI8nTypeKey) {
    return [moment.type as string];
  }
  return [typeFormattedResult];
};
