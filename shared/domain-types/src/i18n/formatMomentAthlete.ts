import { format } from '@hudl/platform-i18n-adapter';

import { SoccerGoalkeeper } from '../moments/hudl-tagging/soccer/Constants';
import { isJerseyMomentAthlete, isNoneMomentAthlete, isUserIdMomentAthlete } from '../typeHelpers';
import type { MomentAthlete } from '../types';

export interface RosterAthleteShape {
  userId?: string;
  jersey?: string;
  firstName?: string;
  lastName?: string;
}

export interface FormatMomentAthleteContext {
  roster: Map<string, RosterAthleteShape>;
  noneLabelI18nKey?: string;
}

export type FormatMomentAthleteFn = (
  athlete: MomentAthlete | null,
  context: FormatMomentAthleteContext
) => string | undefined;

export const defaultFormatMomentAthlete: FormatMomentAthleteFn = (athlete, { roster, noneLabelI18nKey }) => {
  if (!athlete) {
    return undefined;
  }

  if (isUserIdMomentAthlete(athlete)) {
    return formatRosterAthlete(athlete.userId, roster);
  }

  if (isJersey<PERSON>oment<PERSON>th<PERSON><PERSON>(athlete)) {
    return formatJersey(athlete.jersey);
  }

  if (isNoneMomentAthlete(athlete)) {
    return format(noneLabelI18nKey ? noneLabelI18nKey : 'domainTypes.moment.property.player.noneLabel');
  }

  return format('domainTypes.moment.property.player.unknownAthlete');
};

function formatRosterAthlete(userId: string, roster: Map<string, RosterAthleteShape>): string {
  const rosterAthlete = roster.get(userId);
  if (!rosterAthlete) {
    return '';
  }

  const parts = [
    rosterAthlete.jersey ? formatJersey(rosterAthlete.jersey) : undefined,
    rosterAthlete.firstName,
    rosterAthlete.lastName,
  ];

  return parts.filter((p) => !!p).join(' ');
}

function formatJersey(jersey: string): string {
  if (jersey === SoccerGoalkeeper) {
    return format('domainTypes.moment.soccer.v1.jersey.goalkeeper');
  }
  return `#${jersey}`;
}
