import { <PERSON>Goalkeeper } from '../moments/hudl-tagging/soccer/Constants';
import { isJerseyMomentAthlete, isNameMomentAthlete, isNoneMomentAthlete, isUserIdMomentAthlete } from '../typeHelpers';
import type { MomentAthlete } from '../types';
import type { FormatMomentAthleteContext } from './formatMomentAthlete';

/**
 * Represents the order that special athlete types should be sorted in
 */
enum SpecialAthleteTypeSortOrder {
  Goalkeeper = 1,
  None = 2,
  Unknown = 3,
}

interface AthleteSortData {
  jersey?: number;
  fullName?: string;
  specialType?: SpecialAthleteTypeSortOrder;
}

/**
 * Comparison function for `MomentAthletes`
 */
export function defaultSortMomentAthletes(
  a: MomentAthlete,
  b: MomentAthlete,
  context: FormatMomentAthleteContext
): number {
  return sortAthleteData(getAthleteSortData(a, context), getAthleteSortData(b, context));
}

/**
 * Sorts an array of `MomentAthlete`s
 */
export function defaultSortMomentAthletesArray(
  athletes: MomentAthlete[],
  context: FormatMomentAthleteContext
): MomentAthlete[] {
  const withSortableData = athletes.map((athlete) => ({ athlete, sortData: getAthleteSortData(athlete, context) }));
  const sorted = withSortableData.sort((a, b) => sortAthleteData(a.sortData, b.sortData));

  return sorted.map(({ athlete }) => athlete);
}

/**
 * Represents the order moment athletes are sorted in
 */
const ATHLETE_SORT_ORDER: ((a: AthleteSortData, b: AthleteSortData) => number)[] = [
  // first: by jersey
  (a, b) => sortByNumber(a.jersey, b.jersey),
  // second: by name
  (a, b) => sortByString(a.fullName, b.fullName),
  // third: special types
  (a, b) => sortByNumber(a.specialType, b.specialType),
];

/**
 * Comparison function for `SortableAthleteData`
 * uses `SORT_ORDER` configured above
 */
function sortAthleteData(a: AthleteSortData, b: AthleteSortData): number {
  for (const sort of ATHLETE_SORT_ORDER) {
    const result = sort(a, b);
    if (result !== 0) {
      return result;
    }
  }

  return 0;
}

/**
 * Converts moment athletes in to data that can be sorted easily
 */
function getAthleteSortData(athlete: MomentAthlete, { roster }: FormatMomentAthleteContext): AthleteSortData {
  if (isUserIdMomentAthlete(athlete)) {
    const rosterAthlete = roster.get(athlete.userId);
    return {
      jersey: getSortableJersey(rosterAthlete?.jersey),
      fullName: [rosterAthlete?.firstName, rosterAthlete?.lastName].filter((p) => !!p).join(' '),
    };
  }

  if (isJerseyMomentAthlete(athlete)) {
    if (athlete.jersey === SoccerGoalkeeper) {
      return { specialType: SpecialAthleteTypeSortOrder.Goalkeeper };
    }

    return { jersey: getSortableJersey(athlete.jersey) };
  }

  if (isNameMomentAthlete(athlete)) {
    return { fullName: athlete.name };
  }

  if (isNoneMomentAthlete(athlete)) {
    return { specialType: SpecialAthleteTypeSortOrder.None };
  }

  return { specialType: SpecialAthleteTypeSortOrder.Unknown };
}

/**
 * Converts a jersey string to a sortable number
 *
 * - invalid jersey numbers get sorted to the end
 * - 00 gets sorted to the start
 */
function getSortableJersey(jersey: string | undefined | null): number {
  if (jersey === undefined || jersey === null) {
    return Number.MAX_SAFE_INTEGER;
  }

  if (jersey === '00') {
    return Number.MIN_SAFE_INTEGER;
  }

  const parsedJersey = parseInt(jersey, 10);
  return isNaN(parsedJersey) ? Number.MAX_SAFE_INTEGER : parsedJersey;
}

function sortByNumber(a: number | undefined, b: number | undefined): number {
  return (a ?? Number.MAX_SAFE_INTEGER) - (b ?? Number.MAX_SAFE_INTEGER);
}

function sortByString(a: string | undefined, b: string | undefined): number {
  if (!a) {
    return b ? 1 : 0;
  }

  if (!b) {
    return a ? -1 : 0;
  }

  return a.localeCompare(b);
}
