# Changelog

## 18.39.0

- Updates Soccer Moment editing to allow changing the Goalkeeper
- Adds formatting for Soccer Goalkeeper
- Types Soccer TagCalculator
- Adds NameMomentAthlete for wrestling
- Adds athlete sorting for soccer

## 18.38.2

- Stores original shot location data on SoccerTaggingMoment

## 18.38.1

- Added `isInsideBox` property to shots in soccer

## 18.38.0

- Added new `i18n` values for Ice Hockey filters

## 18.37.0

- Added Soccer filters using new stat definitions

## 18.36.0

- Added support for editing soccer moments.
- Ports soccer tagging moments to TypeScript
- Creates sub-classes for the various soccer moment types

## 18.35.0

- Allows for editing custom moment types

## 18.34.0

- Create `points` value for V3 Soccer moments

## 18.33.0

- Allows for editing a moment's start and end times
- Allows for modifying moment `source`
- Tweaks volleyball virtual clip calculation (behavior is the same)

## 18.32.0

- Added 'rally', 'scoreTeamOne', and 'scoreTeamTwo' tags to volleyball moments in 'TagCalculator'

## 18.31.0

- Added support for editing volleball moments.
- Ports volleyball tagging moments to TypeScript
- Creates sub-classes for the various volleyball moment types

## 18.30.0

- Fix volleyball set assist calculation

## 18.29.0

- Added support for editing moment types.

## 18.28.0

- Added `Green` as a new `PenaltySubType` for Lacrosse

## 18.27.0

- Tweaked `getUpdatedTags` to ignore new tags without values

## 18.26.0

- Added location property decorators and metadata

## 18.25.0

- Added `canQuickEdit` and `order` options to moment properties

## 18.24.0

- Added moment property decorators and metadata
- Partially migrated `HudlTaggingMoment` to typescript
- Added default `formatMomentAthlete` function
- Migrated `IceHockeyTaggingMoment` into more specific classes

## 18.23.0

- Adding `formatIceHockeyAdditionalLabels` to Ice Hockey format functions

## 18.22.1

- Logging changes in `formatIceHockeyMomentLabel`

## 18.22.0

- Added `formatIceHockeyMomentTypeLabel`, `formatIceHockeyMomentLabel`, and `formatIceHockeyShotAdditionalLabel` to Ice Hockey format functions

## 18.21.1

- Added and exported location zones consts for soccer

## 18.21.0

- Updated `calculateVirtualClips` to fallback to media segmentation if sport-specific segmentation fails

## 18.20.0

- Updating `VirtualClip` to have a `source` property
- Added `ClassicVirtualClip`, `V3VideoVirtualClip`, and `V3PlaylistVirtualClip`
  - Added corresponding type checking helpers, like `isClassicClipVirtualClip`

## 18.19.0

- Added `hasPenalty`, `returnYardsRange` as derived moment properties
- Added `penaltyYards`, `kicker`, and `returner` as moment properties
- Added `ReturnYardsRange` to translate stats for the derived moment

## 18.18.0

- Exporting `IceHockeyTaggingMoment`
- Updating `DerivedMomentsCalculator` to return moment classes instead of JSON
- Added `powerPlayResult`, `powerPlayTeam`, and `penaltyKillTeam` to `IceHockeyTaggingMoment`

## 18.17.0

- Added soccer constants `DefensiveThirdZones`, `MiddleThirdZones` and `AttackingThirdZones` that define which zones fall into which third of the field
- Add default virtual clipping for tagged videos with no "Segment" moments or MediaSegments

## 18.16.0

- Added unknown rotation logic for volleyball moments

## 18.15.0

- Added i18n string for deadball moment types

## 18.14.0

- Updated Vite config.

## 18.13.0

- Added moment label formatting utilities for soccer
- Fixed issues with splicing moment array while looping in soccer tag calculator

## 18.12.0

- Update build tooling to use `@hudl/vite-config`

## 18.11.1

- Fixed null reference exception in Basketball CalculateVirtualClips code

## 18.11.0

- Added 3 point Takedown in wrestling
- Added 5 point Near Fall in wrestling

## 18.10.0

- Updated `moduleResolution` to `bundler`.

## 18.9.0

- Enable `isolatedModules` in tsconfig and export types using `type` keyword

## 18.8.0

- Fixes a bug where the TagCalculator would be improperly called on SportsCode TaggingSessions.

## 18.7.0

- Adding a `PassesPerSequenceRange` moment property for soccer

## 18.6.0

- Added abstract `BaseballSoftballTaggingMoment` and shared baseball and softball `Constants` to merge shared code for diamond sports
- Updated baseball and softball derived / calculated tag logic

## 18.5.0

- Added `SequenceStatingThird`, `SequenceEndingThird`, and `SequenceThirds` to soccer tagging moment
- Assume third locations for moments that must occur in a third by soccer rules

## 18.4.0

- Removing `startTimeMs` from Video's `segmentTimings`, because it was unused.

## 18.3.0

- Added `FoulCard` to soccer constants
- Mapped blocked shot moments for V3 soccer
- Exported key stats objects and helper functions

## 18.2.1

- Removed noisy console.warn statement causing unhelpful test suite noise

## 18.2.0

- Fix out tagging for baseball / softball to include strikeouts and caught balls

## 18.1.0

- Update lead and lag time for soccer sequences.

## 18.0.0

- Major version upgrade to immer

## 17.41.0

- Add support for V3 Soccer games for attacking directions and soccer moment grid

## 17.40.0

- Create sequences for each segment moment type for soccer.
- Fixes a bug where team in posession calculation would crash for soccer.

## 17.39.0

- Updated WrestlingTaggingMoment to return `redScore` and `greenScore` as separate values

## 17.38.0

- Refactored Soccer Moment data to support new card types.
- Changed soccer `calculateVirtualClip` to segment on a single moment by default.
- Applied sequence level tags to each moment in the Soccer `TagCalculator`

## 17.37.0

- Added i18n for soccer attacking and defensive transitions and set pieces won

## 17.36.0

- Added support for V3 Soccer games by mapping the V3 moments to the new Soccer tagging workflow moment types

## 17.35.0

- Added Sinker to baseball PitchType and RunnerLeftEarly to softball RunnerOnFirst, RunnerOnSecond, and RunnerOnThird

## 17.34.0

- Added SetPiece moment type tags and segmenting rules to Soccer.

## 17.33.0

- Updated Soccer SoccerTaggingMoment to point to new shot moment assisting player tag `receivingPlayer`

## 17.32.0

- Reworked baseball / softball's `OccupiedBases` to be a string representation instead of an array of integers

## 17.31.0

- Updated Wrestling calculateVirtualClips to add pre-roll and post-roll at beginning and end of match

## 17.30.0

- Removed references to soccer duels.

## 17.29.0

- Updated dependency from performance-core-i18n to platform-i18n-adapter due to package name change

## 17.28.0

- Migrate wrestling moment format functions from `watch` app to `domain-types`
- Migrate basketball moment format functions from `watch` app to `domain-types`

## 17.27.0

- Added `crossReceiver` property to `SoccerTaggingMoment`

## 17.26.0

- Added `cross` property to soccer Constants

## 17.25.1

- Uses shorter `x` prefix for unknown soccer player strings.
- Strips whitespace characters from soccer player tags.

## 17.25.0

- Configures new rules for splitting virtual clips for soccer.
- Adds derived subTypes to soccer moments.
- Detects posession transitions and annotates moments with `transition` property.

## 17.24.0

- Renamed `reachOnError` to `reachedOnError` for baseball / softball

## 17.23.0

- Added `gameSection` properties for soccer

## 17.22.0

- Added `passer` and `passReceiver` properties for soccer
- Changed virtual clip generation for soccer to create a clip for each moment.
- Renamed `Stats` to `TagConstants` in the Soccer Constants

## 17.21.1

- Fix unsupported `replaceAll` function call in `Sport.getSport`

## 17.21.0

- Change `medicalForFeit` to `injuryDefault`

## 17.20.0

- Reset Wrestling score column to 0-0 between matches

## 17.19.0

- Added `greenUserId`, `greenName`, `redUserId`, and `redName` properties for wrestling

## 17.18.0

- Added `winColor` and `winType` properties for wrestling

## 17.17.0

- Fixed non-unique id for basketball assist moments
- Fixed bug when passing a null value to `setTagValue` in `HudlTaggingMoment`

## 17.16.0

- Adds type to wrestling `TagConstants`

## 17.15.0

- Added `hitLocationX` and `hitLocationY` properties for baseball / softball
- Added a derived `hitLocationZone` property for baseball / softball

## 17.14.0

- Add configuration and logic for baseball / softball spray chart

## 17.13.0

- Refactor code around wrestling to follow the new best practices on imports and exports.

## 17.12.0

⚠️ IMPORTANT NOTE ⚠️: By mistake, the version was not updated on <https://github.com/hudl/hudl-frontends/pull/1007/files>. This means that the version 17.12.0 was override by the version 17.13.0. In other words, version 17.12.0 and 17.13.0 contains exactly the same code.

- Add calculated tags `OutsAt` and `PlayType` to baseball/softball for filtering purposes

## 17.11.0

- Added a generic 'Segment' moment type to be used for clipping untagged video

## 17.10.0

- Separated `Sacrifice` into `SacrificeFly` and `SacrificeBunt` for baseball / softball

## 17.9.0

- Add `CreatedDate` to Cutup

## 17.8.0

- Tweaking `OccupiedBases` to be integers instead of strings

## 17.7.0

- Remove `OutCount` tag calculation for Baseball/Softball

## 17.6.0

- Videos with multiple media segments but no tagging session will be split into a clip per segment by default.

## 17.5.0

- Add `SwingType.Slap` to Baseball

## 17.4.0

- Update Baseball/Softball `HitType` with new values

## 17.3.0

- Add `OutCount` constants
- Add `DropThirdStrike` option to `PitchResult`
- Updated `OccupiedBases` constant values
- Updated some `PitchType` values to remove spaces and dashes

## 17.2.0

- Update Baseball/Softball virtual clip calculation to ignore non-"play" moment types

## 17.1.0

- Renamed `RunnerAt` to `RunnerOn` for all Runner properties
- Renamed `OffensiveTeam` to `BattingPlayerTeam`
- Updated player properties
  - `PitchingPlayer` is now `PitchingPlayerUserId` and `PitchingPlayerJersey`
  - `BattingPlayer` is now `BattingPlayerUserId` and `BattingPlayerJersey`
- Added `PitchResult.DeadBall` and `PitchResult.IllegalPitch`
- Moved `InningHalf` and `InningCount` to no longer be derived
- Moved `HitResult`'s `OutAtFirst`, `OutAtSecond`, `OutAtThird`, and `OutAtHome` to be a `RunningResult` instead
- Removed `SwingType.CheckedSwing`
- Removed `Steal` and `Pickoff` moment types

## 17.0.0

- Reworking diamond sports data to be more runner centric

## 16.4.0

- Add `isImpossibleSequence` check to `moments/hudl-tagging/basketball/calculateVirtualClips.ts`
  - Circuit breaker that splits a virtual clip into two separate clips
  - Intent is for us to add to that function whenever additional play sequences are discovered that are
    impossible in basketball, for instance a different team taking a shot without a change of possession.

## 16.3.0

- Add Balls and Strikes constants

## 16.2.0

- Separate DiamondSportsTaggingMoment into BaseballTaggingMoment and SoftballTaggingMoment
- Add `Pickoff` as a BaseballMomentType
- Add PitchType to both Baseball and Softball Constants
- Remove teamInContextPhase moment property
- Rename `PitchResult.Hit` to `PitchResult.BallInPlay`
- Rename `StrikeType.SwingAndMiss` to `StrikeType.StrikeSwinging`

## 16.1.0

- Add teamInContextPhase moment property to DiamondSportsTaggingMoment

## 16.0.1

- Fix BasketballMomentType warnings due to not matching parent getI18nLabel interface

## 16.0.0

- Repository is relocated into the hudl-frontends monorepo

## 15.5.0

- Add first iteration of Diamond Sports Tagging Data

## 15.4.0

- Add TeamLevel

## 15.3.0

- Update calculateVirtualClips() for Diamond Sports (Baseball/Softball):
  - Assume every moment maps directly to a clip
  - No longer using the default calculator

## 15.2.0

- Add basic implementation of calculateVirtualClips() for Diamond Sports (Baseball/Softball).

## 15.1.0

- Update VirtualClip to include clip title

## 15.0.0

### Changed

- Moved CI/CD off of TeamCity and onto GitHub Actions.
- Package now dual published to Nexus and GitHub Packages.
- Migrated project to Yarn2.

### Breaking

- Package renamed to `@hudl/hudl-domain-types`.
- Renamed default branch to `main`.

## 14.11.0

- Add whoScoredForTeamOne to be able to identify scorer for team one

## 14.10.0

- Update calculateVirtualClips() for Basketball:

  - Include `Steal` at end and start of plays
  - Update ordering of Team Rebounds being tagged 0.5s earlier and Fouls
  - Combine Turnover/Steal into Steal
  - Combine Off. Foul (charge)/Turnover/ChargeTaken into Charge

## 14.9.0

- Update Assist moment

## 14.8.0

- Add SnapConfidence

## 14.7.0

- Update calculateVirtualClips() for Basketball to include `Assist` as a separate moment before a Shot

## 14.6.0

- Update calculateVirtualClips() for Basketball to include off/def rebounds in 2 plays

## 14.5.0

- Added KickYardsRange to be able to bucket kickyards in groups of 20
-

## 14.4.1

- Support Node 12.x, 14.x, and 16.x

## 14.4.0

- Improve calculateVirtualClips() for Basketball to calculate plays instead of basic time increments

## 14.3.0

- Add pass zone property to moment

## 14.2.0

- Change possession logic to not end on a shot made

## 14.1.0

- Add basic implementation of calculateVirtualClips() for Basketball

## 14.0.0

- Remove contextualCutupId from ClassicClip

## 13.5.0

- Add permissions to Cutup

## 13.4.0

- Add getter/setter for KICK YARDS in FootballTaggingMoment
- Add getter/setter for RET YARDS in FootballTaggingMoment
- Add getter for PENALTY in FootballTaggingMoment
- Add getter for OPP TEAM in FootballTaggingMoment

## 13.3.0

- Add creatorId to Cutup

## 13.2.0

- Fix football caching to support accessing the same key by different data types
- Add seasonId to ClassicClip
- Add additional tag constants for football

## 13.1.0

- Fix Lacrosse possession logic for an edge case

## 13.0.0

- Clean up moment tag accessors
- FootballTaggingMoment to TypeScript
- More and cleaner football tag caching
- More FootballTaggingMoment properties to support <https://github.com/hudl/hudl-videospa/pull/2320>

## 12.8.0

- Add thumbnailUrl to Cutup

## 12.7.0

- Add Football score differential tag calculator

## 12.6.1

- Fix volleyball score calculation including custom tags

## 12.6.0

- Add calculateVirtualClips()
- Add period to VolleyballTaggingMoment

## 12.5.0

- Add VirtualClip

## 12.4.1

- Adds Node 12 to the engines range

## 12.4.0

- Add getter/setter for KEY PLAYER in FootballTaggingMoment
- Add getter/setter for PASSER and OPP PASSER in FootballTaggingMoment
- Add getter/setter for RUSHER and OPP RUSHER in FootballTaggingMoment
- Add getter/setter for RECEIVER and OPP RECEIVER in FootballTaggingMoment
- Add getter/setter for TACKLER1, TACKLER2, OPP TACKLER1, and OPP TACKLER2 in FootballTaggingMoment
- Add getter/setter for INTERCEPTED BY and OPP INTERCEPTED BY in FootballTaggingMoment
- Add getter/setter for RECOVERED BY and OPP RECOVERED BY in FootballTaggingMoment

# 12.3.0

- Bind ClipAngleId to AngleId
- Add Id and ClipAngleTrackMap properties to cutup enhancer

## 12.2.0

- Add getter/setter for 2 MIN in FootballTaggingMoment
- Add getter/setter for DEF STR in FootballTaggingMoment
- Add getter/setter for OPP TEAM in FootballTaggingMoment
- Add getter/setter for DEF STR in FootballTaggingMoment
- Add getter/setter for PERSONNEL in FootballTaggingMoment
- Add getter/setter for DEF FRONT in FootballTaggingMoment
- Add getter/setter for COVERAGE in FootballTaggingMoment
- Add getter/setter for BLITZ in FootballTaggingMoment
- Add getter/setter for GAP in FootballTaggingMoment
- Add getter/setter for SERIES in FootballTaggingMoment

## 12.1.0

- Export SportClass. Long term we need to think through how to best handle these enum-like classes

## 12.0.0

- Use TypeScript for build
- Add Prettier
- Upgrade to match VSPA's eslint config
- Support tree shaking in Webpack

## 11.13.0

- Add immerable to Cutup, ClassicClip
- Set classic clip id as a string
- Add cutupIds to ClassicClip

## 11.12.0

- Add cutupEnhancer to Cutup

## 11.11.0

- Add linkId to TreeCategory

## 11.10.0

- Set clipIds to null when parsing a Cutup

## 11.9.0

- Add ClassicColumn

## 11.8.0

- Add TypeScript strict mode
- Add classic domain

## 11.7.0

- Update tsconfig to match Hudl's standard, including strict mode
- Add more football types

## 11.6.0

- Add getter/setter for MOTION in FootballTaggingMoment
- Add getter/setter for MOTION DIR in FootballTaggingMoment
- Add getter/setter for BACKFIELD in FootballTaggingMoment
- Add getter for hasMotion in FootballTaggingMoment

## 11.5.0

- Expose the Sport class publicly

## 11.4.0

- Add getter/setter for OFF STR in FootballTaggingMoment
- Add getter/setter for relativeDirection in FootballTaggingMoment

## 11.3.0

- Add getter/setter for Hash in FootballTaggingMoment

## 11.2.1

- Changed Basketball DerivedMomentsCalculator behavior for Turnovers to change possession on unforced turnovers.

## 11.2.0

- Add getter/setter for OFF PLAY in FootballTaggingMoment

## 11.1.0

- Ignore case for football tags
- Add accessor for yard line
- Add accessor for offensiveFormation
- Add accessor for efficiency

## 11.0.0

- Store a derivedTags tag on moments with calculated tags

## 10.4.1

- Add unit tests around BasketballTaggingMoment getI18nLabel

## 10.4.0

- Add FaceOffViolations for lacrosse stat calculation

## 10.3.0

- Broaden safeguard for null/undefined values on playerIn and playerOut tag values

## 10.2.0

- Add IgnoreForStats for ice hockey defensive shot

## 10.1.0

- Change the SoccerLocationCalculator method getPitchThirdFromX() to not throw an error when a null is received and return a null instead

## 10.0.0

- Removed FiftyFifty and Tackle from Soccer moment types

## 9.1.0

- Change moment sorting logic for moment decoration

## 9.0.0

- Remove turnovers from ice hockey

## 8.4.0

- Decorate power play result and empty net moments for ice hockey

## 8.3.0

- Add EmptyNet and GoaliePulled moments for ice hockey

## 8.2.0

- Add PlayerNone player tags to represent empty net scenario in ice hockey

## 8.1.0

- Add setBothTeams() and isBothTeams() in HudlTaggingMoment
- Update evenStrength ice hockey moments to be both teams

## 8.0.0

- Change ice hockey strength tags to be team agnostic
- Add logic for decorating ice hockey moments with strength

## 7.1.0

- Derive flipped blocked and saved shots in ice hockey

## 7.0.0

- Update ice hockey to better calculate even strength moments
- _Breaking Change:_ Removed IceHockey FullStrength moment

## 6.8.0

- Add zone calculated tag for shots and flip zone for flipped faceoffs in ice hockey

## 6.7.0

- Added Soccer GoalkeeperPunt and GoalkeeperThrow moment types

## 6.6.0

- Add Alley Oop, Buzzer Beater, Floater, Hook Shot, Jump Shot, Off Balance, and Post Move back to possible basketball shot types. Turns out competitive may not use these, but internationally customers do.

## 6.5.0

- Ignore isCoincidental penalty served and strength never goes below 3 for ice hockey power play calculation

## 6.4.0

- Add Zone player stats to ice hockey

## 6.3.0

- Prevent ice hockey blocked shot tag label from using blocking team's jersey number

## 6.2.0

- Added PassString moment type to Soccer

## 6.1.0

- Remove Alley Oop, Buzzer Beater, Floater, Hook Shot, Jump Shot, Off Balance, and Post Move from possible basketball shot types. These extra options caused confusion and extra work for our assist analysts, without providing extra value for our users.

## 6.0.0

- Add ice hockey isCoincidental tag.
- Add ice hockey linkedData tag
- _Breaking Change:_ Removed IceHockey coincidental subtypes

## 5.11.0

- Added AttackLocations constant for Volleyball player stats

## 5.10.0

- Add Zone moment data to ice hockey

## 5.9.0

- Add ice hockey full strength calculation

## 5.8.0

- Update ice hockey power plays for overlapping

## 5.7.0

- Added Coincidental subtype to ice hockey penalties

## 5.6.0

- Add WhoIsOnTheIce to icehockey PlayerStatTypes

## 5.5.0

- Update ice hockey power play calculation to create a stats moment, use ManUp/ManDown for subtype, provide a temporarily incorrect strength, and a result and short-handed result, all of which to be used for filtering and reporting

## 5.4.0

- Add StartingDirectionOfPlay to TeamMetadataTypes. Also adds DirectionsOfPlay constants for StartingDirectionsOfPlay values

## 5.3.0

- Add Turnovers player stats to ice hockey

## 5.2.0

- Added ShotResult.Goal moments to Soccer 'Shot On Target' stat calculator

## 5.1.0

- Remove ice hockey possession calculator

## 5.0.1

- Fix TagCalculator exports

## 5.0.0

- Upgrade Babel, Jest, and eslint, enable TypeScript, and Dockerize build

## 4.16.0

- Removed caching from HudlTaggingMoment properties

## 4.15.0

- Add SoccerLocationCalculator and its getPitchThirdFromX(). Add PitchThirds constants for soccer

## 4.14.0

- Add Soccer Passing constants

## 4.13.0

- Add home/away indicator to FootballTaggingMoment context string

## 4.12.0

- Add Soccer 50/50 constants
- Add Soccer Tackle constants

## 4.11.0

- Add Cross tags for Soccer

## 4.10.0

- Add AttackLocationStart and AttackLocationEnd tags

## 4.9.0

- Add OrganizationType

## 4.8.1

- Move immutable to a dependencies section

## 4.8.0

- Add ODK to football

## 4.7.0

- Use play number tag for football description

## 4.6.0

- Match football tags to classic breakdown tags

## 4.5.0

- Added EliteSoccer foul details
- Added EliteSoccer Goal Type constants

## 4.4.0

- Added new duel options for hudl-playertracking

## 4.3.0

- Added methods to generate context and description strings based on football clip data

## 4.2.0

- Added EliteSoccer Substitution constants for hudl-playertracking

## 4.1.0

- Add Football

## 4.0.0

- Changed EliteSoccer behavior constants for hudl-playertracking

## 3.12.0

- Added more EliteSoccer constants for hudl-playertracking

## 3.11.0

- Added EliteSoccer constants for use in hudl-playertracking

## 3.10.0

- Added possessionchange moment for possession header

## 3.9.0

- Start lacrosse possessions on faceoff wins and not violations

## 3.8.0

- Move Secondary Assist to IceHockeyTaggingMoment from HudlTaggingMoment

## 3.7.0

- Ice Hockey Face-off, Possession, and Power Play moments and calculations

## 3.6.1

- Don't start a possessoin on a shot saved for the other team when the shooting team recovers the ground ball

## 3.6.0

- Split lacrosse power play moments when there is a goal in the middle

## 3.5.0

- Lacrosse attacking possessions

## 3.4.0

- Sort moments in tag calculator

## 3.3.0

- Calculate box score for lacrosse

## 3.2.0

- Re-introduce Ice Hockey

## 3.1.0

- Fixed gender indexes

## 3.0.0

- Removed unused label() code and added moment internationalization for lacrosse

## 2.7.0

- Add violations to lacrosse

## 2.6.0

- Added CommonFoul subtype to lacrosse

## 2.5.0

- Added unknown players for substitutions

## 2.4.0

- update how list of players to use is determined for Hudl-Tagging moments in i18n

## 2.3.0

- Added ShotClockViolation for Lacrosse

## 2.2.0

- Calculate power plays for lacrosse

### 2.1.2

- Update references to <http://npm.thorhudl.com> to reference new Nexus server.

## 2.1.1

- Reverts 2.1.0

## 2.1.0

- Added Ice Hockey moment types, constants and stats

## 2.0.0

- renamed possession calculator to derived moment calculator
- added faceoff calculator and rides calculator

## 1.59.0

- Lacrosse possession calculation

## 1.58.0

- LAX Faceoff Violation

## 1.57.0

- LAX Updates
- Updated Lacrosse moments and logic
- Added PenaltyServed moment to lacrosse and linkId to HudlTaggingMoment
- Added ability to tag athletes from different teams on the same moment
- Added Gender, and constants for female penalties in lacrosse

## 1.56.1

- Fix possession calculator to return no possession moments for other sports

## 1.56.0

- Added PossessionCalculator
- Implemented PossessionCalculator for basketball

## 1.55.0

- Added calculation of assistedBlock for volleyball

## 1.54.0

- Adding BLOB and SLOB for basketball

## 1.53.4

- Make sure rotation moments are included in VolleyballMomentType.

## 1.53.3

- Fixed case where the tag label shows junk in soccer when the shot type is skipped

## 1.53.2

- Fixed issue where editing a tag and changing it to a custom tag returns incorrect i18nKey

## 1.53.1

- Fixed title-case use on custom tag labels in HudlTaggingMoment

## 1.53.0

- Added TagDescriptorTypeConstants

## 1.52.0

- Added custom descriptor logic to HudlTaggingMoment

## 1.51.0

- Created separate i18n key for soccer custom tags

## 1.50.0

- Added DefaultMomentType and modified ScoreCalculator to return null when unknown sport

## 1.49.0

- Updates for i18n of Volleyball

## 1.48.0

- Lacrosse changes for women's lacrosse support

## 1.47.1

- Fix for result i18n key generation when dealing with arrays

## 1.47.0

- Lacrosse faceoff results and new faceoff calculations

## 1.46.0

- Added startShootout constant

## 1.45.0

- Added turnover subtypes and moment data types for lacrosse

## 1.44.0

- Added moments and stats for lacrosse penalties

## 1.43.0

- Added soccer shootout moments

## 1.42.0

- Volleyball Set Tracking and Auto Assist

## 1.41.0

- Add ability to track multiple players on 'players' field

## 1.40.0

- Add support for custom types

## 1.39.0

- Adds rotation calculation for Volleyball

## 1.37.0

- Lacrosse shot location fix

## 1.36.0

- Added turnovers to lacrosse stat calculation

## 1.35.0

- Update for Volleyball Web Tagging

## 1.34.0

- Added Lacrosse Blocked ShotResult

## 1.33.0

- Added Lacrosse PlayerStatTypes and MomentDataTypes

## 1.30.0

- Added Lacrosse moment types, constants and stats

## 1.27.0

- Updated VolleyballTaggingMoment to populate player0 - player3.

## 1.26.0

- Added Quality TagConstant and field on VolleyballTaggingMoment.

## 1.25.0

- Update Sport object

## 1.23.1

- Patch to add substitution basketball moment, additional logic checks, and define a string constant.

## 1.23.0

- Add substituion and playerIn/playerOut to HudlTaggingMoment.
- Add sport-agnostic constants.

## 1.22.0

- Add MatchedStartTimeMs and MatchedEndTimeMs to HudlTaggingMoment

## 1.21.0

- Changing VolleyballTaggingMoment to use "amount" instead of "value".

## 1.20.0

- Set soccer's out of bounds label to "Out of Play"

## 1.19.0

- Add ProblemReport moment type for all sports for future use by Hudl Assist

## 1.18.1

- Patch to allow players to be cleared on volleyball tagging moments

## 1.18.0

- Brings in support for adding calculated tags to moments. Will be used initially for calculating Periods and Basketball Advanced Stats for the V3 Filtering page.

## 1.17.0

- Add support for player labels

## 1.16.0

- Support multiple shot types tagged on a basketball moment
- Add Hook Shot shot type, change Jumper to Jump Shot

## 1.15.0

- Added soccer MomentDataTypes and PlayerStatTypes constants for web tagging player stats. Also added shotresult = Goal

## 1.14.0

- Add rotation-related tags for volleyball
- Add value to ScoreAdjustment for volleyball

## 1.13.0

- Don't count FT Violations as FT Attempts in basketball stat calculation

## 1.12.0

- Add SoccerTaggingMoment for shot labels

## 1.11.0

- Add ShotType to BasketballTaggingMoment

## 1.9.0

- Update label to account for technical fouls for basketball

## 1.8.0

- Add Volleyball score calculator

## 1.7.0

- Add Basketball SubType for 'technical'

## 1.6.0

- Add VolleyballTaggingMoment to support multiple player select

## 1.5.0

- Add Possession MomentType for basketball

## 1.3.0

- Add source and last modified fields to HudlTaggingMoment

## 1.2.1

- Update toJSON method to force strings in the values arrays
- Update originalStartTimeMs and originalEndTimeMs to have consistent setters

## 1.1.1

- Update toJSON method to return momentId to match MomentDisplay format

## 1.0.1

- Bug for Assist data in BasketballTaggingMoment

## 1.0.0

- Major updates around structure and methods for HudlTaggingMoments

## 0.4.0

- Added `getSport` to `Sports`

## 0.3.0

- Correcting the name from `hudl-domaintypes` to `hudl-domain-types`

## 0.2.0

- Added Sport, Moment, HudlTaggingMoment, TagConstants, MomentType, SoccerMomentType, VolleyballMomentType, HudlTaggingPeriodCalculator

## 0.1.0

- Initial release
