{"name": "@hudl/hudl-domain-types", "version": "18.39.0", "description": "Domain-specific types and services to be used across clusters", "contributors": [{"name": "Web Gems, Your Brand Here, <PERSON><PERSON>ner, BBQ", "url": "https://hudl.slack.com/archives/C0479456U9H", "channel": "#performance-core-eng"}], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"build": "vite build", "clean": "rimraf dist node_modules/.cache", "dev": "vite build --watch", "lint": "eslint ./src --ext ts,js", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "nuke": "pnpm run clean && rimraf node_modules", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "release": "release-package", "test": "vitest", "test:ci": "vitest --coverage --silent", "test:nowatch": "vitest --watch=false", "types": "tsc --project tsconfig-declarations.json", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src"}, "dependencies": {"immer": "^9.0.6", "immutable": "^3.8.1", "invariant": "^2.2.1", "lodash.assigninwith": "^4.2.0", "lodash.clonedeep": "^4.1.1", "lodash.isnull": "^3.0.0"}, "devDependencies": {"@hudl/eslint-config": "workspace:*", "@hudl/platform-i18n-adapter": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "@types/lodash.assigninwith": "4.2.7", "@types/lodash.clonedeep": "4.5.7", "@types/lodash.isnull": "3.0.7", "@vitejs/plugin-react-swc": "3.7.0", "@vitest/coverage-v8": "3.2.4", "config": "workspace:*", "eslint": "8.45.0", "vite": "5.3.1", "vite-tsconfig-paths": "5.0.1"}, "peerDependencies": {"@hudl/platform-i18n-adapter": ">= 2"}}