import { faker } from '@faker-js/faker';

/**
 * Creates random First & Last Name for a user
 * @returns string
 */
export function createUserFirstName(): string {
  return faker.person.firstName();
}
/**
 * Creates random First & Last Name for a user
 * @returns string
 */
export function createUserLastName(): string {
  return faker.person.lastName();
}
/**
 * Creates random First & Last Name for a user
 * @returns string
 */
export function createUserFullName(): string {
  return faker.person.fullName();
}

/**
 * Creates numeric ID. Used for generic team or user IDs
 * @returns string
 */
export function createRandomNumericId(digits: number): string {
  return faker.string.numeric(digits);
}

/**
 * Create a random 24-character hex string
 */
export function createRandomHexString(numCharacters: number = 24): string {
  return faker.string.uuid().replace(/-/g, '').slice(0, numCharacters);
}

/**
 * Generates a random identifier based on a UUID with an optional pretext.
 * If `encoded` is true, the pretext and hex string (from the UUID) are base64 encoded.
 * If `encoded` is false, only the raw 24-character hex string (from the UUID) is returned.
 *
 * @param {string} pretext - The optional prefix to append to the hex string (default is 'Random').
 * @param {boolean} encoded - Whether to base64 encode the result (default is true).
 * @returns {string} - The generated identifier, either raw or base64 encoded.
 */
export function createRandomUUID(pretext: string = 'Random', encoded: boolean = true): string {
  // Generate a random UUID of 24-character hex string
  const uuid = createRandomHexString();

  return encoded ? Buffer.from(`${pretext}${uuid}`).toString('base64') : uuid;
}

/**
 * Creates a random base64 encoded schoolId
 * @returns schoolId
 */
export function createRandomSchoolId({ encoded = true }): string {
  if (encoded) {
    return Buffer.from(`School${faker.string.numeric(6)}`).toString('base64');
  }
  return `${faker.string.numeric(6)}`;
}

/**
 * Creates a random videoId
 * @param encoded - Whether to return the videoId as a base64 encoded string or a raw hex string.
 * @returns videoId
 */
export function createRandomVideoId(encoded: boolean = true): string {
  if (encoded) {
    return Buffer.from(`Video${createRandomHexString()}`).toString('base64');
  }
  return `${createRandomHexString()}`;
}

/**
 * Creates a random encoded momentId
 * @returns momentId
 */
export function createRandomMomentId(encoded: boolean = true): string {
  if (encoded) {
    return Buffer.from(`Moment${createRandomHexString()}`).toString('base64');
  }
  return `${createRandomHexString()}`;
}

/**
 * Generates a random set of words based on the specified word count.
 * Defined as a single number or tuple of [min, max].
 * @param {number | [number, number]} wordCount - Number of words to generate
 *    - Single number generates that exact number of words.
 *    - Tuple of [min, max], generates a random number of words within the range. Defaults to [1, 10]
 * @returns A string containing the generated random words.
 */
export function createRandomWords(wordCount: number | [number, number] = [1, 10]): string {
  let count: number;

  if (Array.isArray(wordCount)) {
    const [min, max] = wordCount;
    count = faker.number.int({ min, max });
  } else {
    count = wordCount;
  }

  return faker.lorem.words(count);
}

/**
 * Generates a random city name
 * @returns A string containing the generated random city name.
 */
export function createRandomCity(): string {
  return faker.location.city();
}

/**
 * Generates a random state
 * @abbreviation set to true, uses the state abbreviation vs full name
 * @returns A string containing the generated random state.
 */
export function createRandomState({ abbreviation = false }): string {
  return faker.location.state({ abbreviated: abbreviation });
}

/**
 * Generates a random date in the distant future.
 * @param {number} years - Number of years in the future to generate the date.
 * @returns A string containing the generated date in ISO format.
 */
export function createDistantFutureDate({ years = 50 }): string {
  return faker.date.future({ years: years }).toISOString();
}

/**
 * Generates a random date in the future
 * @param {number} days - Number of days in the future to generate the date.
 * @returns A string containing the generated date in ISO format.
 */
export function createUpcomingDate({ days = 3 }): string {
  return faker.date.soon({ days: days }).toISOString();
}
