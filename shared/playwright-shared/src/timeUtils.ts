import { formatInTimeZone, zonedTimeToUtc } from 'date-fns-tz';

export enum TimeAdjustment {
  None,
  StartOfDay,
  EndOfDay,
}

export enum TimeZone {
  Central = 'America/Chicago',
  Eastern = 'America/New_York',
  Mountain = 'America/Phoenix',
  Pacific = 'America/Los_Angeles',
  UTC = 'UTC',
}

/**
 * Helper function to format a time into a desired format.
 * @param {Date | string} date - The date to format.
 * @param {string} [format='HH:mm'] - The desired time format (default is 'HH:mm').
 * @param {boolean} [useUTC=false] - Whether to convert the date into locale or UTC time
 * @param {boolean} [use12HourFormat=false] - Whether to return a string in 12 or 24-hour format
 * @param {boolean} [appendMeridian=false] - Whether to append 'AM/PM' to end of string
 * @returns {string} The formatted time string.
 */
export const getFormattedTime = (
  date: Date | string,
  format: string = 'HH:mm',
  useUTC: boolean = false,
  use12HourFormat: boolean = false,
  appendMeridian: boolean = false
): string => {
  if (typeof date === 'string') {
    date = new Date(date);
  }

  if (!(date instanceof Date) || isNaN(date.getTime())) {
    throw new Error('Invalid date provided');
  }

  let hours = String(useUTC ? date.getUTCHours() : date.getHours()).padStart(2, '0');
  const minutes = String(useUTC ? date.getUTCMinutes() : date.getMinutes()).padStart(2, '0');
  const seconds = String(useUTC ? date.getUTCSeconds() : date.getSeconds()).padStart(2, '0');
  const milliseconds = String(useUTC ? date.getUTCMilliseconds() : date.getMilliseconds()).padStart(3, '0');

  let meridian = 'AM';

  // Convert hour to 12-hour format
  if (use12HourFormat) {
    if (Number(hours) > 12) {
      meridian = 'PM';
    }
    hours = String(Number(hours) % 12 === 0 ? 12 : Number(hours) % 12);
  }

  let formattedTime: string;

  // Format the time based on the requested format
  switch (format) {
    case 'HH:mm':
      formattedTime = `${hours}:${minutes}`;
      break;
    case 'HH:mm:ss':
      formattedTime = `${hours}:${minutes}:${seconds}`;
      break;
    case 'HH:mm:ss.SSS':
      formattedTime = `${hours}:${minutes}:${seconds}.${milliseconds}`;
      break;
    default:
      // If the format doesn't match any of the predefined options, default to 'HH:mm'
      formattedTime = `${hours}:${minutes}`;
  }

  // Append meridian AM/PM if needed
  if (use12HourFormat && appendMeridian) {
    formattedTime += `${meridian}`;
  }
  return formattedTime;
};

/**
 * Function to format a date into YYYY-MM-DD or custom format.
 * @param {Date | string} date - The date to format.
 * @param {string} [format='YYYY-MM-DD'] - The desired date format. Supports 'YYYY-MM-DD' and 'YYYY-MM-DD HH:mm:ss'.
 * @param {boolean} [useUTC=false] - Whether to convert the date into locale or UTC time
 * @returns {string} The formatted date string.
 */
export const getFormattedDate = (
  date: Date | string,
  format: string = 'YYYY-MM-DD',
  useUTC: boolean = false
): string => {
  if (typeof date === 'string') {
    date = new Date(date);
  }

  if (!(date instanceof Date)) {
    throw new Error('Invalid date provided');
  }

  const year = useUTC ? date.getUTCFullYear() : date.getFullYear();
  const month = String((useUTC ? date.getUTCMonth() : date.getMonth()) + 1).padStart(2, '0'); // Months are 0-indexed
  const day = String(useUTC ? date.getUTCDate() : date.getDate()).padStart(2, '0');

  if (format === 'YYYY-MM-DD HH:mm:ss') {
    const hours = String(useUTC ? date.getUTCHours() : date.getHours()).padStart(2, '0');
    const minutes = String(useUTC ? date.getUTCMinutes() : date.getMinutes()).padStart(2, '0');
    const seconds = String(useUTC ? date.getUTCSeconds() : date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  if (format === 'YYYY-MM-DDTHH:mm:ss.sssZ') {
    const hours = String(useUTC ? date.getUTCHours() : date.getHours()).padStart(2, '0');
    const minutes = String(useUTC ? date.getUTCMinutes() : date.getMinutes()).padStart(2, '0');
    const seconds = String(useUTC ? date.getUTCSeconds() : date.getSeconds()).padStart(2, '0');
    const milliseconds = String(useUTC ? date.getUTCMilliseconds() : date.getMilliseconds()).padStart(3, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}Z`;
  }

  return `${year}-${month}-${day}`;
};

/**
 * Get a future date formatted as YYYY-MM-DD.
 * @param {number} days - The number of days to add to the current date.
 * @param {string} [format='YYYY-MM-DD'] - The desired date format.
 * @param {boolean} [useUTC=false] - Whether to convert the date into locale or UTC time
 * @returns {string} The formatted future date string.
 */
export const getFutureDate = (days: number, format: string = 'YYYY-MM-DD', useUTC: boolean = false): string => {
  const today = new Date();
  const futureDate = new Date(today);
  useUTC ? futureDate.setUTCDate(today.getUTCDate() + days) : futureDate.setDate(today.getDate() + days);
  return getFormattedDate(futureDate, format, useUTC);
};

/**
 * Get a past date formatted as YYYY-MM-DD.
 * @param {number} days - The number of days to subtract from the current date.
 * * @param {string} [format='YYYY-MM-DD'] - The desired date format.
 * @returns {string} The formatted past date string.
 */
export const getPastDate = (days: number, format: string = 'YYYY-MM-DD'): string => {
  const today = new Date();
  const pastDate = new Date(today);
  pastDate.setDate(today.getDate() - days);
  return getFormattedDate(pastDate, format);
};
/**
 * Adjust a given time by adding or subtracting hours and/or minutes, and return the formatted result.
 * @param {string} timeUtc - The UTC time string in ISO 8601 format (e.g., '2024-11-20T17:19:56.688Z').
 * @param {number} [hoursToAdd=0] - The number of hours to add (can be negative to subtract).
 * @param {number} [minutesToAdd=0] - The number of minutes to add (can be negative to subtract).
 * @param {string} [formatString='HH:mm'] - The desired time format (default is 'HH:mm').
 * @param {boolean} getTimeFormat - If the adjusted time should be a date or time format.
 * @param {TimeAdjustment} adjustment - If the adjusted time should be the start or end of the day.
 * @returns {string} The adjusted time formatted as a string.
 */
export const adjustTime = (
  timeUtc: string,
  hoursToAdd: number = 0,
  minutesToAdd: number = 0,
  formatString: string = 'HH:mm',
  getTimeFormat: boolean = true,
  adjustment: TimeAdjustment = TimeAdjustment.None
): string => {
  // Parse the UTC time string into a JavaScript Date object (this creates a new Date Object in local time)
  const date = new Date(timeUtc);

  // Adjust the time based on the parameters
  switch (adjustment) {
    case TimeAdjustment.StartOfDay:
      date.setUTCHours(0, 0, 0, 0);
      return getFormattedDate(date, formatString, true);
    case TimeAdjustment.EndOfDay:
      date.setUTCHours(23, 59, 59, 999);
      return getFormattedDate(date, formatString, true);
    default:
      date.setUTCHours(date.getUTCHours() + hoursToAdd);
      date.setUTCMinutes(date.getUTCMinutes() + minutesToAdd);
  }

  // Return the formatted adjusted time
  return getTimeFormat ? getFormattedTime(date, formatString) : getFormattedDate(date, formatString, true);
};

export const getPreviousMonth = (date: Date): Date => {
  //Takes a date and returns the first day of the previous month as a date
  const month = date.getMonth();
  const year = date.getFullYear();
  if (month === 0) {
    return new Date(year - 1, 11, 1);
  } else {
    return new Date(year, month - 1, 1);
  }
};

export const getNextMonth = (date: Date): Date => {
  //Takes a date and returns the first day of the next month as a date
  const month = date.getMonth();
  const year = date.getFullYear();
  if (month === 11) {
    return new Date(year + 1, 0, 1);
  } else {
    return new Date(year, month + 1, 1);
  }
};

/**
 * Gets today's date as a string via the getFutureDate or getPastDate function and returns the month today's date is in
 * @param {string} [format='YYYY-MM-DD'] - The desired date format. Must use the other helper function to give a string date
 */
export const getThisMonth = (currentDate: string): string => {
  //assumes current date will be a string
  const date = new Date(currentDate);
  //Takes the current date returns the first day of the current month as a date
  const month = date.getMonth();
  const year = date.getFullYear();
  const firstDayofCurrentMonth = new Date(year, month, 1);
  return firstDayofCurrentMonth.toISOString();
};

/**
 * Creates a string that represents utc time for a date in relation to today. To be used for setting the start or end date for an event
 * Optional parameter of days from today if you want to add a number of days from today
 * @param {number} hourOfDay - The desired hour of day in utc
 * @param {number} minuteOfHour- The desired minute of day in utc
 * @param {number} daysFromToday - The optional desired number of days from today
 */
export const createUtcTimestamp = (hourOfDay: number, minuteOfHour: number, daysFromToday: number = 0): string => {
  const today = new Date();
  if (daysFromToday !== 0) {
    today.setUTCDate(today.getUTCDate() + daysFromToday);
  }
  const year = today.getUTCFullYear();
  const month = String(today.getUTCMonth() + 1).padStart(2, '0');
  const day = String(today.getUTCDate()).padStart(2, '0');
  const hours = String(hourOfDay).padStart(2, '0');
  const minute = String(minuteOfHour).padStart(2, '0');
  const second = String(today.getUTCSeconds()).padStart(2, '0');
  const eventForDay = `${year}-${month}-${day}T${hours}:${minute}:${second}Z`;
  return eventForDay;
};
/**
 * Returns the difference in hours between UTC time and the specified time zones local time
 * @param {string} timeZone - The time zone the offset is being calculated ('America/Chicago')
 * @param {boolean} [toUtc=true] - A flag to determine the direction of the conversion:
 *   - true: Returns the offset to convert UTC to local time
 *   - false: Returns the offset to convert local time to UTC
 * @returns {number} The time zone offset in hours
 */
export const getTimeZoneOffset = (timeZone: string, toUtc: boolean = true): number => {
  const now = new Date();

  /// Get current time in UTC
  const nowUtc = new Date(now.toISOString());

  // Get time zone specific date
  const options: Intl.DateTimeFormatOptions = {
    timeZone,
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    hour12: false,
  };

  // Split into separate parts in the given time zone
  const parts = new Intl.DateTimeFormat('en-US', options).formatToParts(now);

  const year = parts.find((p) => p.type === 'year')?.value;
  const month = parts.find((p) => p.type === 'month')?.value?.padStart(2, '0');
  const day = parts.find((p) => p.type === 'day')?.value?.padStart(2, '0');
  const hour = parts.find((p) => p.type === 'hour')?.value?.padStart(2, '0');
  const minute = parts.find((p) => p.type === 'minute')?.value?.padStart(2, '0');
  const second = parts.find((p) => p.type === 'second')?.value?.padStart(2, '0');

  // Create ISO string
  const localDateString = `${year}-${month}-${day}T${hour}:${minute}:${second}`;
  const localDate = new Date(localDateString + 'Z');

  // Calculate the hour difference from milliseconds
  const timeDifference = nowUtc.getTime() - localDate.getTime();
  const offset = Math.round(timeDifference / (1000 * 60 * 60));

  return toUtc ? -offset : offset;
};

/**
 * Converts a given date, time, and timezone into a UTC string using date-fns-tz.
 * @param dateString - The date string to format (e.g., 'YYYY-MM-DD').
 * @param timeString - The time string to format (e.g., 'HH:mm').
 * @param timeZone - The IANA timezone string to use for the input (e.g., 'America/Chicago', 'Asia/Tokyo').
 * @returns - The UTC version of the given date, time, and timezone as a string (YYYY-MM-DD HH:mm:ss).
 */
export const convertToUtc = (dateString: string, timeString: string, timeZone: string): string => {
  // Combine date and time strings to create a full local date-time string
  const combinedDateTimeLocalString = `${dateString} ${timeString}`; // e.g., '2026-12-20 15:00'
  const utcDateObject = zonedTimeToUtc(combinedDateTimeLocalString, timeZone);
  // Error handling: zonedTimeToUtc can return an "Invalid Date" if inputs are bad
  if (isNaN(utcDateObject.getTime())) {
    throw new Error(
      `Failed to convert to UTC. Invalid date/time/timezone: ${combinedDateTimeLocalString} in ${timeZone}`
    );
  }
  const formattedUtcString = formatInTimeZone(utcDateObject, 'UTC', 'yyyy-MM-dd HH:mm:ss');

  return formattedUtcString;
};

/**
 * Validates start time and date on the user's end against a given base time and date.
 * The base time and date can be any pre-verified time and date for the event in any timezone.
 * Validates by converting both time/date objects to UTC and comparing them.
 * @param baseDate - The pre-verified date string to use for comparison.
 * @param baseTime - The pre-verified time string to use for comparison.
 * @param baseTimeZone - The pre-verified timezone string to use for comparison.
 * @param dateToCheck - The date string to check against the base date.
 * @param timeToCheck - The time string to check against the base date.
 */
export const validateLocalToUtc = (
  baseDate: string,
  baseTime: string,
  baseTimeZone: string,
  dateToCheck: string,
  timeToCheck: string
): boolean => {
  // Get user's local time zone
  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  // Convert the input on user's end to UTC
  const utcDateString = convertToUtc(dateToCheck, timeToCheck, userTimeZone);
  // Convert the pre-verified base value to UTC
  const utcBaseValue = convertToUtc(baseDate, baseTime, baseTimeZone);
  // Compare the user's UTC value to the base UTC value
  if (utcDateString === utcBaseValue) {
    return true;
  } else {
    return false;
  }
};
