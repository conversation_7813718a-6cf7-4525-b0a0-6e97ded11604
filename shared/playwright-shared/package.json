{"name": "playwright-shared", "version": "0.0.0", "private": true, "contributors": [{"name": "Mandalorians", "url": "https://hudl.slack.com/archives/C050MC1USQ5", "channel": "#ask-front-end-platform", "qaChannel": "#squad-mandalorians"}], "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"build": "vite build", "clean": "rimraf dist node_modules/.cache", "dev": "vite build --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "nuke": "pnpm run clean && rimraf node_modules", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../prettier.config.mjs --ignore-path ../../.prettierignore", "test": "vitest", "test:nowatch": "vitest --watch=false", "types": "tsc --project tsconfig-declarations.json --sourceRoot $PWD/src", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.78.0", "@hudl/aws-toolkit": "workspace:*", "date-fns": "2.29.3", "date-fns-tz": "2.0.0"}, "devDependencies": {"@faker-js/faker": "^9.2.0", "@hudl/eslint-config": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "@types/axe-core": "^3.0.6", "@types/lodash": "^4.17.13", "@types/lodash.merge": "^4.6.9", "axe-core": "4.10.2", "config": "workspace:*", "eslint": "8.45.0", "lodash": "^4.17.21", "vite": "5.3.1"}, "peerDependencies": {"@axe-core/playwright": "^4.0.0", "@playwright/test": "^1.49.0"}}