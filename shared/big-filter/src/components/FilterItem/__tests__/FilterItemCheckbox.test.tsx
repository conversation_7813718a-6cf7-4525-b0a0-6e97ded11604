import { render, screen, waitFor } from '@testing-library/react';

import { FilterItemCheckbox } from '../FilterItemCheckbox';

describe('FilterItemCheckbox Component', () => {
  it('renders the component properly', () => {
    render(
      <FilterItemCheckbox
        config={{
          key: '',
          labelKey: 'big-filter.filter-config.game-span-label-text',
          type: 'checkbox',
          options: [
            { labelKey: 'big-filter.filter-config.game-span-option-3-games-text', value: '1' },
            { labelKey: 'big-filter.filter-config.game-span-option-5-games-text', value: '2' },
            { labelKey: 'big-filter.filter-config.game-span-option-10-games-text', value: '3' },
          ],
          qaId: 'checkbox-component',
        }}
        handleChange={() => {}}
        setAsyncState={() => {}}
        value={['1', '2']}
      />
    );

    expect(screen.getByText('Last 3 games')).toBeInTheDocument();
    expect(screen.getByText('Last 5 games')).toBeInTheDocument();
    expect(screen.getByText('Last 10 games')).toBeInTheDocument();
    expect(screen.getByText('Game range')).toBeInTheDocument();
    expect(screen.getByTestId('checkbox-component')).toBeInTheDocument();

    expect(screen.getByTestId('checkbox-component-option-0')).toBeChecked();
    expect(screen.getByTestId('checkbox-component-option-1')).toBeChecked();
    expect(screen.getByTestId('checkbox-component-option-2')).not.toBeChecked();
  });

  it('calls handleChange when selection is changed', async () => {
    const mockFn = vi.fn();

    render(
      <FilterItemCheckbox
        config={{
          key: '',
          labelKey: 'big-filter.filter-config.game-span-label-text',
          type: 'checkbox',
          options: [
            { labelKey: 'big-filter.filter-config.game-span-option-3-games-text', value: '1' },
            { labelKey: 'big-filter.filter-config.game-span-option-5-games-text', value: '2' },
            { labelKey: 'big-filter.filter-config.game-span-option-10-games-text', value: '3' },
          ],
          qaId: 'checkbox-component',
        }}
        handleChange={mockFn}
        setAsyncState={() => {}}
        value={['1']}
      />
    );

    expect(mockFn).toBeCalledTimes(0);

    await waitFor(() => {
      screen.getByTestId('checkbox-component-option-1').click();
    });

    expect(mockFn).toBeCalledTimes(1);
    expect(mockFn).toBeCalledWith(['1', '2']);

    // Try to toggle the other way
    await waitFor(() => {
      screen.getByTestId('checkbox-component-option-0').click();
    });

    expect(mockFn).toBeCalledTimes(2);
    // Note: The click above wouldn't have been saved, so the value is ["1"] before the click
    expect(mockFn).toBeCalledWith([]);
  });

  it('should be ordered by the original options when changed', async () => {
    const mockFn = vi.fn();

    render(
      <FilterItemCheckbox
        config={{
          key: '',
          labelKey: 'big-filter.filter-config.game-span-label-text',
          type: 'checkbox',
          options: [
            { labelKey: 'big-filter.filter-config.game-span-option-3-games-text', value: '1' },
            { labelKey: 'big-filter.filter-config.game-span-option-5-games-text', value: '2' },
            { labelKey: 'big-filter.filter-config.game-span-option-10-games-text', value: '3' },
          ],
          qaId: 'select-component',
        }}
        handleChange={mockFn}
        setAsyncState={() => {}}
        value={['2']}
      />
    );

    expect(mockFn).toBeCalledTimes(0);

    await waitFor(() => {
      screen.getByTestId('select-component-option-0').click();
    });

    expect(mockFn).toBeCalledWith(['1', '2']);
  });

  it('Toggle users option if makeSelectionUnique is set to true', async () => {
    const mockFn = vi.fn();

    render(
      <FilterItemCheckbox
        config={{
          key: '',
          labelKey: 'big-filter.filter-config.basketball.defense-coverage-label-text',
          type: 'checkbox',
          options: [
            { labelKey: 'big-filter.filter-config.basketball.defense-coverage-man-text', value: 'Man' },
            { labelKey: 'big-filter.filter-config.basketball.defense-coverage-zone-text', value: 'Zone' },
          ],
          makeSelectionUnique: true,
          qaId: 'defense-coverage',
        }}
        handleChange={mockFn}
        setAsyncState={() => {}}
        value={['Man']}
      />
    );

    expect(mockFn).toBeCalledTimes(0);

    await waitFor(() => {
      screen.getByTestId('defense-coverage-option-1').click();
    });

    expect(mockFn).toBeCalledTimes(1);

    expect(mockFn).toBeCalledWith(['Zone']);
  });
});
