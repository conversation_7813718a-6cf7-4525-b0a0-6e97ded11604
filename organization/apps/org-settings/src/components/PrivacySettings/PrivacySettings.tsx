import { ReactNode, useCallback, useState } from 'react';

import { Divider, Headline, Radio, RadioGroup, Text } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import ConfirmationModal from './ConfirmationModal/ConfirmationModal';

import styles from './styles.module.scss';

function PrivacySettings(): ReactNode {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [orgLevelDataPrivacy, setOrgLevelDataPrivacy] = useState<boolean>(true);

  const toggleModalVisibility = useCallback(() => {
    setIsModalOpen((prev) => !prev);
  }, []);

  const removeOrgLevelDataPrivacy = useCallback(() => {
    toggleModalVisibility();
    setOrgLevelDataPrivacy(false);

    // To-DO: The actual backend call to remove org level consent
  }, [toggleModalVisibility]);

  const handleOrgLevelDataPrivacyChange = useCallback(() => {
    if (orgLevelDataPrivacy) {
      toggleModalVisibility();
      return;
    }

    setOrgLevelDataPrivacy(true);
  }, [orgLevelDataPrivacy, toggleModalVisibility]);

  return (
    <div className={styles.privacySettingsContainer}>
      <div className={styles.privacySettingsHeaderSection}>
        <Headline level="3">{format('privacySettings.header')}</Headline>
        <Text level="small" color="subtle">
          {format('privacySettings.subheader')}
        </Text>
      </div>

      <div className={styles.dataPrivacySectionContainer}>
        <Text className={styles.dataPrivacyText}>{format('privacySettings.dataPrivacy')}</Text>
        <div className={styles.dataPrivacySection}>
          <div className={styles.requireConsentContainer}>
            <Text level="small">{format('privacy.requireConsent.message')}</Text>
            <Text level="micro">{format('privacy.requireConsent.message.subtext-v2')}</Text>
          </div>
          <div>
            <RadioGroup
              valueChecked={orgLevelDataPrivacy ? 1 : 0}
              onChange={handleOrgLevelDataPrivacyChange}
              orientation="horizontal"
              formSize="small"
              className={styles.dataPrivacyRadioGroup}
            >
              <Radio label={format('privacySettings.yes')} value={1} />
              <Radio label={format('privacySettings.no')} value={0} />
            </RadioGroup>
          </div>
        </div>
        <Divider />
      </div>

      <ConfirmationModal
        isModalOpen={isModalOpen}
        toggleModalVisibility={toggleModalVisibility}
        removeOrgLevelDataPrivacy={removeOrgLevelDataPrivacy}
      />
    </div>
  );
}

export default PrivacySettings;
