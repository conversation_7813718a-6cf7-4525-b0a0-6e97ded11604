import { StyleSheet } from 'react-native';

import BigPauseIcon from './BigPauseIcon';
import BigPlayIcon from './BigPlayIcon';
import BigReplayIcon from './BigReplayIcon';
import { PlayButton, type PlayButtonProps } from './PlayButton';

const styles = StyleSheet.create({
  icon: {
    width: 80,
    height: 80,
  },
  container: {
    width: '100%',
    height: '100%',
    padding: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default function BigPlayButton(props: PlayButtonProps): React.JSX.Element {
  return (
    <PlayButton
      icon={{
        play: <BigPlayIcon style={styles.icon} />,
        pause: <BigPauseIcon style={styles.icon} />,
        replay: <BigReplayIcon style={styles.icon} />,
      }}
      style={styles.container}
      {...props}
    />
  );
}
