import { useCallback, useContext, useRef, useState } from 'react';
import { Animated, type StyleProp, type ViewStyle } from 'react-native';

import { PlayerContext } from '@theoplayer/react-native-ui';

import { IconAudio, IconAudioOff } from '@hudl/rn-uniform';

import { ActionButton, type ActionButtonProps } from '../ActionButton';
import { VolumeSlider } from './VolumeSlider';

import cssStyles from './style.module.css';

export interface VolumeButtonProps extends ActionButtonProps {
  iconVolumeStyle?: StyleProp<ViewStyle>;
}

export function VolumeButton({
  style,
  testID,
  hoverStyle,
  activeStyle,
  iconVolumeStyle,
}: VolumeButtonProps): React.JSX.Element {
  const { player } = useContext(PlayerContext);
  const [volume, setVolumeState] = useState<number>(player.volume);
  const [muted, setMutedState] = useState<boolean>(player.muted);
  const sliderOpacity = useRef(new Animated.Value(0)).current;

  const onPress = useCallback(() => {
    setMutedState((value) => !value);
    player.muted = !muted;
  }, [muted, player]);

  /**
   * Globally used to handle adjustments to volume
   * @param {Number} volumeLevel
   */
  const setVolume = useCallback(
    (volumeLevel: number) => {
      if (muted && volumeLevel !== 0) {
        setMutedState(false);
        player.muted = false;
      } else if (!muted && volumeLevel === 0) {
        setMutedState(true);
        player.muted = true;
      }
      setVolumeState(volumeLevel);
      player.volume = volumeLevel;
    },
    [muted, player]
  );

  const mouseOver = useCallback(() => {
    Animated.timing(sliderOpacity, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [sliderOpacity]);

  const mouseOut = useCallback(() => {
    Animated.timing(sliderOpacity, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, [sliderOpacity]);

  return (
    <div
      className={cssStyles.soundContainer}
      data-qa-id={testID}
      onMouseOver={mouseOver}
      onFocus={mouseOver}
      onMouseOut={mouseOut}
      onBlur={mouseOut}
    >
      <ActionButton
        svg={muted ? <IconAudioOff style={iconVolumeStyle} /> : <IconAudio style={iconVolumeStyle} />}
        onPress={onPress}
        style={style}
        hoverStyle={hoverStyle}
        activeStyle={activeStyle}
      />
      <VolumeSlider opacity={sliderOpacity} muted={muted} volume={volume} setVolume={setVolume} />
    </div>
  );
}
