import { Deviated_SeekBar } from '../../__patches__/Deviated_SeekBar';
import { ActionButton, type ActionButtonProps } from './ActionButton';
import BigPauseIcon from './BigPauseIcon';
import BigPlayButton from './BigPlayButton';
import BigPlayIcon from './BigPlayIcon';
import BigReplayIcon from './BigReplayIcon';
import CastButton, { type CastButtonProps } from './CastButton';
import { FullscreenButton } from './FullscreenButton';
import { LiveButton, type LiveButtonProps } from './LiveButton';
import Panel, { type PanelProps } from './Panel';
import PlaybackSpeedIcon, { type PlaybackSpeedIconProps } from './PlaybackSpeedIcon';
import { PlaybackSpeedSelector, type PlaybackSpeedSelectorProps } from './PlaybackSpeedSelector';
import { PlayButton } from './PlayButton';
import PlayerText, { type PlayerTextProps } from './PlayerText';
import { Popover, type PopoverProps } from './Popover';
import { SettingsMenuButton, type SettingsMenuButtonProps } from './SettingsMenuButton';
import { SkipButton, type SkipButtonProps } from './SkipButton';
import { TimeLabel, type TimeLabelProps } from './TimeLabel';
import { VideoQualitySelector } from './VideoQualitySelector';
import { VolumeButton, type VolumeButtonProps } from './volume-button';

export {
  ActionButton,
  type ActionButtonProps,
  BigPauseIcon,
  BigPlayButton,
  BigPlayIcon,
  BigReplayIcon,
  CastButton,
  type CastButtonProps,
  Deviated_SeekBar,
  FullscreenButton,
  LiveButton,
  type LiveButtonProps,
  Panel,
  type PanelProps,
  PlaybackSpeedIcon,
  type PlaybackSpeedIconProps,
  PlaybackSpeedSelector,
  type PlaybackSpeedSelectorProps,
  PlayButton,
  PlayerText,
  type PlayerTextProps,
  Popover,
  type PopoverProps,
  SettingsMenuButton,
  type SettingsMenuButtonProps,
  SkipButton,
  type SkipButtonProps,
  TimeLabel,
  type TimeLabelProps,
  VideoQualitySelector,
  VolumeButton,
  type VolumeButtonProps,
};

export {
  AirplayButton,
  type AirplayButtonProps,
  CastMessage,
  MuteButton,
  type MuteButtonProps,
  PipButton,
  type PipButtonProps,
  CenteredDelayedActivityIndicator,
  type DelayedActivityIndicatorProps as CenteredDelayedActivityIndicatorProps,
  SeekBar,
  type SeekBarProps,
  SettingsSvg as SettingsIcon,
  Spacer,
  ControlBar,
  type ControlBarProps,
  CenteredControlBar,
  type CenteredControlBarProps,
} from '@theoplayer/react-native-ui';
