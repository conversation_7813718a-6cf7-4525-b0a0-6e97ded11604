import React, { useCallback, useContext } from 'react';
import { StyleSheet } from 'react-native';

import { PlayerContext, useCurrentTime, useDuration, useSeekable } from '@theoplayer/react-native-ui';

import { NEAR_LIVE_THRESHOLD_MS } from '../../constants';
import { ActionButton, type ActionButtonProps } from './ActionButton';
import DotIcon from './DotIcon';

const styles = StyleSheet.create({
  icon: {
    width: 8,
    height: 8,
  },
});

export interface LiveButtonProps extends ActionButtonProps {}

export function LiveButton({ style, testID, hoverStyle, activeStyle }: LiveButtonProps): React.JSX.Element {
  const { player, locale } = useContext(PlayerContext);
  const currentTime = useCurrentTime();
  const duration = useDuration();
  const seekable = useSeekable();
  const normalizedDuration = isNaN(duration) || !isFinite(duration) ? 0 : Math.max(0, duration);
  const seekableEnd = seekable.length > 0 ? seekable[0].end : normalizedDuration;
  const isNearLive = Math.abs(currentTime - seekableEnd) <= NEAR_LIVE_THRESHOLD_MS;

  const dotSvg = <DotIcon color={isNearLive ? 'critical' : 'nonessential'} style={styles.icon} />;

  const onPress = useCallback(() => {
    player.currentTime = seekableEnd;
  }, [player, seekableEnd]);

  return (
    <ActionButton
      style={style}
      testID={testID}
      touchable={true}
      svg={dotSvg}
      onPress={onPress}
      afterText={locale.liveLabel}
      hoverStyle={hoverStyle}
      activeStyle={activeStyle}
    />
  );
}
