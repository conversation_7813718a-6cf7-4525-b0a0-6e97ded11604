import { type ReactNode, useCallback, useContext, useEffect, useState } from 'react';
import { type StyleProp, type ViewStyle } from 'react-native';

import { PlayerContext, type SkipButtonProps as THEOSkipButtonProps } from '@theoplayer/react-native-ui';
import { PlayerEventType } from 'react-native-theoplayer';

import { IconBackward5Sec, IconForward5Sec } from '@hudl/rn-uniform';

import { SKIP_DIRECTION_BACKWARD, SKIP_DIRECTION_FORWARD, type SkipDirection } from '../../constants';
import { ActionButton, type ActionButtonProps } from './ActionButton';

export interface SkipButtonProps extends THEOSkipButtonProps, ActionButtonProps {
  iconSkipStyle?: StyleProp<ViewStyle>;
  initiallyEnabled?: boolean;
  triggerSkipOverlay?: (direction: SkipDirection, seconds: number, isEdgeSkip?: boolean) => void;
}

/**
 * The default skip button for the `react-native-theoplayer` UI. This is largely a copy of THEO's implementation,
 * but it has more styling props exposed so that we have more control over the styling.
 */
export function SkipButton({
  style,
  iconSkipStyle,
  initiallyEnabled = true,
  skip,
  icon,
  triggerSkipOverlay,
  hoverStyle,
  activeStyle,
  testID,
}: SkipButtonProps): React.JSX.Element {
  const { player } = useContext(PlayerContext);

  const [enabled, setEnabled] = useState<boolean>(initiallyEnabled);
  useEffect(() => {
    const onUpdateEnabled = (): void => {
      setEnabled(player.seekable.length > 0 || player.buffered.length > 0);
    };
    const onPlaying = (): void => {
      const isCasting = player.cast.chromecast?.casting ?? false;
      setEnabled(player.seekable.length > 0 || player.buffered.length > 0 || isCasting);
    };
    player.addEventListener([PlayerEventType.PROGRESS, PlayerEventType.SOURCE_CHANGE], onUpdateEnabled);
    player.addEventListener(PlayerEventType.PLAYING, onPlaying);
    return () => {
      player.removeEventListener([PlayerEventType.PROGRESS, PlayerEventType.SOURCE_CHANGE], onUpdateEnabled);
      player.removeEventListener(PlayerEventType.PLAYING, onPlaying);
    };
  }, [player]);

  const onPress = useCallback(() => {
    const direction = skip >= 0 ? SKIP_DIRECTION_FORWARD : SKIP_DIRECTION_BACKWARD;
    const seconds = Math.abs(skip);

    const targetTime = player.currentTime + skip * 1e3;
    player.currentTime = targetTime;

    const isEdgeSkip = targetTime < 0 || targetTime > player.duration;
    triggerSkipOverlay?.(direction, seconds, isEdgeSkip);
  }, [skip, player, triggerSkipOverlay]);

  const forwardSvg: ReactNode = icon?.forward ?? <IconForward5Sec style={iconSkipStyle} />;
  const backwardSvg: ReactNode = icon?.backward ?? <IconBackward5Sec style={iconSkipStyle} />;

  if (!enabled) {
    return <></>;
  }

  return (
    <ActionButton
      svg={skip < 0 ? backwardSvg : forwardSvg}
      style={style}
      onPress={onPress}
      hoverStyle={hoverStyle}
      activeStyle={activeStyle}
      testID={testID}
    />
  );
}
