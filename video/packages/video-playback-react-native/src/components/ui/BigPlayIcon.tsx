import { Path, Rect } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';

import type { UniformIconProps } from '../../types/UniformIconProps';

export default function BigPlayIcon({ style, ...props }: UniformIconProps): React.JSX.Element {
  return (
    <Icon width="81" height="80" viewBox="0 0 81 80" fill="none" style={style} {...props}>
      <Rect x="0.779785" width="80" height="80" rx="40" fill="white" fillOpacity="0.2" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M29.9898 24.359C29.8523 24.5475 29.7798 24.767 29.7798 24.9921V55.007C29.7798 55.639 30.3398 56.151 31.0298 56.151C31.276 56.151 31.5173 56.0835 31.7235 55.959L56.3448 40.951C56.9185 40.6012 57.0735 39.8915 56.691 39.367C56.5998 39.2412 56.4823 39.1327 56.3448 39.0492L31.7235 24.0424C31.5098 23.9121 31.2685 23.8492 31.0298 23.8492C30.626 23.8492 30.231 24.0287 29.9898 24.359Z"
        fill="white"
      />
    </Icon>
  );
}
