import { useCallback, useContext, useState } from 'react';
import { Platform, Pressable, type StyleProp, StyleSheet, View, type ViewStyle } from 'react-native';

import {
  PlayerContext,
  SvgContext,
  type ActionButtonProps as THEOActionButtonProps,
} from '@theoplayer/react-native-ui';

import PlayerText from './PlayerText';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  afterText: {
    marginLeft: 4,
  },
});

export interface ActionButtonProps extends Omit<THEOActionButtonProps, 'icon'> {
  afterText?: string;
  hoverStyle?: StyleProp<ViewStyle>;
  activeStyle?: StyleProp<ViewStyle>;
}

/**
 * The default button component that renders an image/svg source for the `react-native-theoplayer` UI.
 * Now includes hover and active states for web.
 */
export function ActionButton(props: ActionButtonProps): React.JSX.Element {
  const { style, svg, onPress, highlighted, testID, afterText, hoverStyle, activeStyle } = props;
  const [focused, setFocused] = useState<boolean>(false);
  const [hovered, setHovered] = useState(false);
  const [pressed, setPressed] = useState(false);
  const context = useContext(PlayerContext);
  const shouldChangeTintColor = highlighted || (focused && Platform.isTV);
  const touchable = props.touchable != false;
  const { style: contextStyle } = useContext(PlayerContext);

  const onTouch = useCallback(() => {
    if (context.ui.buttonsEnabled_) {
      onPress?.();
    }
    context.ui.onUserAction_();
  }, [context.ui, onPress]);

  const containerStyle = [
    styles.container,
    hovered && hoverStyle ? hoverStyle : null,
    pressed && activeStyle ? activeStyle : null,
    style,
  ];

  const handleFocus = useCallback(() => {
    context.ui.onUserAction_();
    setFocused(true);
  }, [context.ui]);

  const handleBlur = useCallback(() => {
    setFocused(false);
  }, []);

  const handleHoverIn = useCallback(() => {
    setHovered(true);
  }, []);

  const handleHoverOut = useCallback(() => {
    setHovered(false);
  }, []);

  const handlePressIn = useCallback(() => {
    setPressed(true);
  }, []);

  const handlePressOut = useCallback(() => {
    setPressed(false);
  }, []);

  if (!touchable) {
    return (
      <View style={containerStyle} testID={testID}>
        {svg && <View>{svg}</View>}
        {afterText && <PlayerText style={styles.afterText}>{afterText}</PlayerText>}
      </View>
    );
  }

  return (
    <Pressable
      style={containerStyle}
      testID={testID}
      onPress={onTouch}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onHoverIn={handleHoverIn}
      onHoverOut={handleHoverOut}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
    >
      {svg && (
        <View>
          <SvgContext.Provider
            value={{
              fill: shouldChangeTintColor ? contextStyle.colors.iconSelected : contextStyle.colors.icon,
              height: '100%',
              width: '100%',
            }}
          >
            {svg}
          </SvgContext.Provider>
        </View>
      )}
      {afterText && <PlayerText style={styles.afterText}>{afterText}</PlayerText>}
    </Pressable>
  );
}
