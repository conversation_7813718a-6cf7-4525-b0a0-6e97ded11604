import React, { useCallback } from 'react';

import { getMarvelOrigin } from '@hudl/frontends-environment';
import { Button, Text } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import styles from './styles.module.scss';

export function ErrorState(): React.JSX.Element {
  const onBackToLogIn = useCallback(() => {
    window.location.href = `${getMarvelOrigin()}/logout`;
  }, []);
  return (
    <div className={styles.container}>
      <Text color="subtle">{format('consent.request-consent.error-page.content')}</Text>
      <Button buttonStyle="minimal" onPress={onBackToLogIn}>
        {format('consent.shared.back-to-log-in')}
      </Button>
    </div>
  );
}
