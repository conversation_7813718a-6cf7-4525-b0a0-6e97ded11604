import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { useSearchParams } from 'react-router-dom';

import { Button, Input, Spinner, Text, ToastMessenger } from '@hudl/uniform-web';
import { format, formatMessage } from 'frontends-i18n';

import { ConsentState, ConsentStatusType } from '../../enums/enums';
import { ConsentInformation } from '../../types/ConsentInformation';
import { getUserConsentStatuses, requestConsent, TooManyRequestConsentCallsError } from '../apiHelpers';
import { ConsentPending } from '../ConsentPending/ConsentPending';
import { ErrorState } from '../ErrorState/ErrorState';
import { ADULT_CONSENT_AGE, getAge } from '../utils/ageUtils';
import { isValidProviderEmail } from '../utils/emailUtils';

import styles from './styles.module.scss';

const validateIsValidProviderEmail = (providerEmail: string): string | null => {
  if (!isValidProviderEmail(providerEmail)) {
    return format('consent.request-consent.email-input.invalid-email-text');
  }

  return null;
};

const validateIsYouthUsingOwnEmailForProvider = (
  age: number | null,
  providerEmail: string,
  subjectEmail: string | null | undefined
): string | null => {
  if (age && age < ADULT_CONSENT_AGE && subjectEmail && providerEmail.trim() === subjectEmail.trim()) {
    return format('consent.request-consent.email-input.invalid-matching-email-text');
  }

  return null;
};

export function RequestConsent(): React.JSX.Element {
  const [searchParams] = useSearchParams();

  const [consentStatus, setConsentStatus] = useState<ConsentInformation | null>(null);

  const [dateOfBirth, setDateOfBirth] = useState<string>('');
  const [providerEmail, setProviderEmail] = useState<string>('');

  const [dobHasError, setDOBHasError] = useState<boolean>(false);
  const [hasBlurredEmailField, setHasBlurredEmailField] = useState<boolean>(false);
  const [showEmailGathering, setShowEmailGathering] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isErrorLoadingData, setIsErrorLoadingData] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const redirectToken = searchParams.get('redirectToken');
  const auth0State = searchParams.get('state');

  // Debounce the date of birth to prevent instant form appearance
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowEmailGathering(true);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [dateOfBirth]);

  useEffect(() => {
    if (!redirectToken) {
      console.error('No redirect token found in URL parameters.');
      setIsErrorLoadingData(true);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    getUserConsentStatuses(redirectToken)
      .then(setConsentStatus)
      .catch((error) => {
        console.error('Failed to fetch user data:', error);
        setIsErrorLoadingData(true);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [redirectToken]);

  useEffect(() => {
    if (consentStatus && consentStatus.dateOfBirth) {
      setDateOfBirth(consentStatus.dateOfBirth);
    }
  }, [consentStatus]);

  const age = useMemo(() => getAge(dateOfBirth), [dateOfBirth]);

  // Helper function to get the primary consent (first one or most relevant)
  const primaryConsent = useMemo(() => {
    if (!consentStatus?.consents || consentStatus.consents.length === 0) {
      return null;
    }
    // Return the first consent or you could add logic to find the most relevant one
    return consentStatus.consents[0];
  }, [consentStatus]);

  const needsSdpConsent = primaryConsent?.type === ConsentStatusType.SDP;
  const isValidDateOfBirth = dateOfBirth && dateOfBirth !== '' && new Date(dateOfBirth) <= new Date();

  const enterEmailPrompt = useMemo(() => {
    if (!isValidDateOfBirth || age === null) {
      return '';
    }

    if (age < 13) {
      return format('consent.request-consent.enter-email-prompt.under-13');
    }

    if (age < ADULT_CONSENT_AGE && needsSdpConsent) {
      return format('consent.request-consent.enter-email-prompt.under-18');
    }

    if (needsSdpConsent) {
      return format('consent.request-consent.enter-email-prompt.over-18');
    }

    return '';
  }, [age, isValidDateOfBirth, needsSdpConsent]);

  const isValidProviderEmailErrorMessage = useMemo(() => {
    return validateIsValidProviderEmail(providerEmail);
  }, [providerEmail]);
  const isYouthUsingOwnEmailForProviderErrorMessage = useMemo(() => {
    return validateIsYouthUsingOwnEmailForProvider(age, providerEmail, String(primaryConsent?.subjectEmail));
  }, [age, providerEmail, primaryConsent]);
  const emailErrorMessage = isValidProviderEmailErrorMessage || isYouthUsingOwnEmailForProviderErrorMessage;
  const emailInputHasError =
    (hasBlurredEmailField && !!isValidProviderEmailErrorMessage) || !!isYouthUsingOwnEmailForProviderErrorMessage;

  let emailHelpText = '';
  if (emailErrorMessage) {
    emailHelpText = emailErrorMessage;
  } else if (age !== null && age < ADULT_CONSENT_AGE) {
    emailHelpText = format('consent.request-consent.email-input.help-text');
  }

  useEffect(() => {
    // If the user changes their DoB to indicate they are an adult athlete,
    // this pre-populates the ProviderEmail with the adult athlete's email.
    if (age && age > ADULT_CONSENT_AGE && primaryConsent?.subjectEmail) {
      setProviderEmail(String(primaryConsent.subjectEmail));
    } else {
      setProviderEmail(primaryConsent?.providerEmail || '');
    }
  }, [age, primaryConsent]);

  const handleEmailBlur = useCallback(() => {
    setHasBlurredEmailField(true);
  }, [setHasBlurredEmailField]);

  const handleOnChangeDOB = useCallback((date: string) => {
    setShowEmailGathering(false);
    setDateOfBirth(date);
  }, []);

  const handleDOBBlur = useCallback(() => {
    setDOBHasError(!isValidDateOfBirth);
  }, [isValidDateOfBirth]);

  const handleSendReminder = useCallback(() => {
    (async () => {
      const primaryConsentProviderEmail = primaryConsent?.providerEmail || '';
      const primaryConsentId = primaryConsent?.id ?? '';
      try {
        await requestConsent(
          redirectToken ?? '',
          auth0State ?? '',
          dateOfBirth,
          primaryConsentId,
          primaryConsentProviderEmail,
          true
        );

        const successText = formatMessage(
          { id: 'consent.request-consent.reminder-email.success' },
          {
            emailAddress: <b>{primaryConsentProviderEmail}</b>,
          }
        );
        ToastMessenger.show({
          text: successText,
          duration: 'short',
          type: 'confirmation',
        });
      } catch (error) {
        console.error('Failed to send reminder:', error);

        if (error instanceof TooManyRequestConsentCallsError) {
          ToastMessenger.show({
            text: format('consent.request-consent.reminder-email.too-many-requests-v2'),
            duration: 'short',
            type: 'information',
          });
        } else {
          ToastMessenger.show({
            text: format('consent.request-consent.reminder-email.failure'),
            duration: 'short',
            type: 'critical',
            action: {
              text: format('consent.request-consent.reminder-email.failure.retry'),
              onPress: handleSendReminder,
            },
          });
        }
      }
    })().catch(console.error);
  }, [redirectToken, auth0State, dateOfBirth, primaryConsent]);

  const handleReEnterEmail = useCallback(() => {
    if (consentStatus && primaryConsent) {
      const updatedConsents = consentStatus.consents.map((consent) =>
        consent.id === primaryConsent.id ? { ...consent, state: ConsentState.NotStarted } : consent
      );
      setConsentStatus({
        ...consentStatus,
        consents: updatedConsents,
      });
    }
  }, [consentStatus, primaryConsent]);

  const handleRequestConsent = useCallback(() => {
    if (!emailErrorMessage && providerEmail && redirectToken && primaryConsent) {
      setIsSubmitting(true);
      requestConsent(redirectToken, auth0State ?? '', dateOfBirth, primaryConsent.id, providerEmail)
        .then(() => {
          // Update the consent status to pending
          if (consentStatus) {
            const updatedConsents = consentStatus.consents.map((consent) =>
              consent.id === primaryConsent.id ? { ...consent, state: ConsentState.Pending, providerEmail } : consent
            );
            setConsentStatus({
              ...consentStatus,
              consents: updatedConsents,
            });
          }
        })
        .catch((error) => {
          console.error('Failed to request consent:', error);
        })
        .finally(() => {
          setIsSubmitting(false);
        });
    }
  }, [auth0State, consentStatus, dateOfBirth, emailErrorMessage, providerEmail, redirectToken, primaryConsent]);

  if (isLoading) {
    return (
      <div className={styles.container}>
        <Spinner />
      </div>
    );
  }

  if (isErrorLoadingData) {
    return <ErrorState />;
  }

  if (primaryConsent?.state === ConsentState.NotStarted) {
    const emailLabel =
      !age || age < ADULT_CONSENT_AGE
        ? format('consent.request-consent.email-input.label')
        : format('consent.request-consent.email-input.adult-label');
    const emailPlaceholder =
      !age || age < ADULT_CONSENT_AGE
        ? format('consent.request-consent.email-input.placeholder')
        : format('consent.request-consent.email-input.adult-placeholder');
    return (
      <div className={styles.container}>
        <ToastMessenger />
        <Text>{format('consent.request-consent.birthday-text')}</Text>
        <Input
          className={styles.input}
          isRequired
          type="date"
          label={format('consent.request-consent.date-of-birth.label')}
          value={dateOfBirth}
          onChange={handleOnChangeDOB}
          onBlur={handleDOBBlur}
          hasError={dobHasError}
          autoFocus
        />
        {isValidDateOfBirth && showEmailGathering && (
          <>
            <Text>{enterEmailPrompt}</Text>
            <Input
              className={styles.input}
              isRequired
              type="email"
              label={emailLabel}
              placeholder={emailPlaceholder}
              helpText={emailHelpText}
              value={providerEmail}
              onChange={setProviderEmail}
              onBlur={handleEmailBlur}
              hasError={emailInputHasError}
            />
            <Button
              className={styles.input}
              onPress={handleRequestConsent}
              isDisabled={!isValidDateOfBirth || !!emailErrorMessage || !providerEmail || isSubmitting}
              status={isSubmitting ? 'spinning' : undefined}
            >
              {format('consent.request-consent.continue-button')}
            </Button>
          </>
        )}
      </div>
    );
  }

  const canSendReminder = !!(primaryConsent?.providerEmail && primaryConsent?.id);
  const canReEnterEmail = !!primaryConsent?.id;
  return (
    <ConsentPending
      age={age}
      providerEmail={providerEmail}
      canSendReminder={canSendReminder}
      canReEnterEmail={canReEnterEmail}
      handleSendReminder={handleSendReminder}
      handleReEnterEmail={handleReEnterEmail}
    />
  );
}
