import React, { type ReactNode } from 'react';

import { IntlProvider } from 'react-intl';
import { Outlet } from 'react-router-dom';

import { Environment, TooltipProvider } from '@hudl/uniform-web';
import { getIntl } from 'frontends-i18n';

import { AppHeader } from './AppHeader/AppHeader';

import styles from './styles.module.scss';

export function App(): ReactNode {
  const i18n = getIntl();

  return (
    <Environment environment="dark">
      <IntlProvider messages={i18n.messages} locale={i18n.locale} textComponent="span">
        <AppHeader />
        <TooltipProvider>
          <main className={styles.pageContainer}>
            <Outlet />
          </main>
        </TooltipProvider>
      </IntlProvider>
    </Environment>
  );
}
