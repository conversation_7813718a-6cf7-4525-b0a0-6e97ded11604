import React, { type ReactNode, UIEvent, useCallback, useEffect, useState } from 'react';

import { useSearchParams } from 'react-router-dom';

import { Button, Checkbox, CheckboxGroup, Spinner, Text, Title, Tooltip } from '@hudl/uniform-web';
import { format, formatMessage } from 'frontends-i18n';

import { ConsentState, DisplayedConsentPage } from '../../enums/enums';
import { ConsentAthleteDetails } from '../../types/ConsentAthleteDetails';
import { getAthleteDetails, updateConsentStatusState, verifyRedirectSignature } from '../apiHelpers';
import { ErrorState } from '../ErrorState/ErrorState';
import { PostConsent } from '../PostConsent/PostConsent';
import { AthleteDetails } from '../shared/AthleteDetails/AthleteDetails';
import { HudlDescription } from '../shared/HudlDescription/HudlDescription';
import { legalText } from './LegalText';

import styles from './ConsentForm.module.scss';

type UrlParamsType = {
  status: string;
  externalPayload: string;
  signature: string;
};

export function ConsentForm(): ReactNode {
  const [searchParams] = useSearchParams();
  const [athleteDetails, setAthleteDetails] = useState<ConsentAthleteDetails | null>(null);
  const [isLoadingAthleteDetails, setIsLoadingAthleteDetails] = useState(true);
  const [isErrorLoadingAthleteDetails, setIsErrorLoadingAthleteDetails] = useState(false);
  const [isLoadingSignatureVerification, setIsLoadingSignatureVerification] = useState<boolean>(true);
  const [isErrorLoadingSignatureVerification, setIsErrorLoadingSignatureVerification] = useState<boolean>(false);
  const [isVerified, setIsVerified] = useState<boolean>(false);
  const [isUpdatingState, setIsUpdatingState] = useState<boolean>(false);
  const [hasReadTerms, setHasReadTerms] = useState<boolean>(false);
  const [isCheckboxTooltipVisible, setIsCheckboxTooltipVisible] = useState<boolean>(false);
  const [consentDecision, setConsentDecision] = useState<string | null>(null);
  const [displayedPage, setDisplayedPage] = useState<DisplayedConsentPage>(DisplayedConsentPage.ProvideConsent);
  const isUnder18 = true; //TODO: Set this with age logic

  const getUrlParams = useCallback((): UrlParamsType => {
    const status = searchParams.get('status') || '';
    const externalPayload = searchParams.get('externalPayload') || '';
    const signature = searchParams.get('signature') || '';

    return {
      status,
      externalPayload,
      signature,
    };
  }, [searchParams]);

  useEffect(() => {
    (async () => {
      const urlParams = getUrlParams();

      if (urlParams.status && urlParams.externalPayload && urlParams.signature) {
        await verifyRedirectSignature({
          status: urlParams.status,
          externalPayload: urlParams.externalPayload,
          signature: urlParams.signature,
        })
          .then((pageIsVerified) => {
            setIsVerified(pageIsVerified ?? false);
          })
          .catch(() => {
            setIsErrorLoadingSignatureVerification(true);
          })
          .finally(() => {
            setIsLoadingSignatureVerification(false);
          });
      } else {
        setIsErrorLoadingSignatureVerification(true);
        setIsLoadingSignatureVerification(false);
      }
    })().catch(() => setIsErrorLoadingSignatureVerification(true));
  }, [searchParams, getUrlParams]);

  useEffect(() => {
    getAthleteDetails(getUrlParams())
      .then(setAthleteDetails)
      .catch(() => {
        setIsErrorLoadingAthleteDetails(true);
      })
      .finally(() => {
        setIsLoadingAthleteDetails(false);
      });
  }, [getUrlParams]);

  const handleLegalTermsScroll = useCallback(
    (event: UIEvent<HTMLDivElement>) => {
      if (hasReadTerms) return;

      const target = event.target as HTMLDivElement;
      const { scrollTop, scrollHeight, clientHeight } = target;
      const buffer = 10; // Buffer to account for any rounding issues
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - buffer;

      if (isAtBottom) {
        setHasReadTerms(true);
      }
    },
    [hasReadTerms]
  );

  const setConsentStatusState = useCallback(
    (newState: ConsentState): void => {
      (async () => {
        const urlParams = getUrlParams();
        setIsUpdatingState(true);
        await updateConsentStatusState({
          status: urlParams.status,
          externalPayload: urlParams.externalPayload,
          signature: urlParams.signature,
          consentState: newState,
        })
          .then(() => {
            setDisplayedPage(
              newState === ConsentState.Approved
                ? DisplayedConsentPage.ConsentApproved
                : DisplayedConsentPage.ConsentDenied
            );
          })
          .catch(() => {
            setIsErrorLoadingSignatureVerification(true);
          })
          .finally(() => {
            setIsUpdatingState(false);
          });
      })().catch(() => setIsErrorLoadingSignatureVerification(true));
    },
    [getUrlParams]
  );

  const onMouseEnterCheckbox = useCallback(() => {
    setIsCheckboxTooltipVisible(true);
  }, [setIsCheckboxTooltipVisible]);

  const onMouseLeaveCheckbox = useCallback(() => {
    setIsCheckboxTooltipVisible(false);
  }, [setIsCheckboxTooltipVisible]);

  const onCheckboxChange = useCallback(
    (value: 'grant' | 'deny'): undefined => {
      setConsentDecision(value);
    },
    [setConsentDecision]
  );

  const onSubmit = useCallback((): undefined => {
    if (consentDecision === 'deny') {
      setConsentStatusState(ConsentState.Denied);
    } else {
      setConsentStatusState(ConsentState.Approved);
    }
  }, [consentDecision, setConsentStatusState]);

  const handleOnGoBack = useCallback((): void => {
    setDisplayedPage(DisplayedConsentPage.ProvideConsent);
  }, [setDisplayedPage]);

  if (isLoadingSignatureVerification || isLoadingAthleteDetails) {
    return <Spinner />;
  }

  if (!isVerified || isErrorLoadingSignatureVerification) {
    return <ErrorState />;
  }

  if (displayedPage === DisplayedConsentPage.ConsentApproved || displayedPage === DisplayedConsentPage.ConsentDenied) {
    const isConsentGranted = displayedPage === DisplayedConsentPage.ConsentApproved;
    return (
      <PostConsent
        isConsentGranted={isConsentGranted}
        isUnder18={isUnder18}
        athleteDetails={athleteDetails}
        onGoBack={handleOnGoBack}
      />
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.headerContainer}>
        <Title as={'h1'} size={'xxlarge'} className={styles.headerText}>
          {isUnder18
            ? format('consent.consent-form.youth.header', {
                name: athleteDetails?.fullName ?? format('consent.shared.default-name'),
              })
            : format('consent.consent-form.adult.header')}
        </Title>
        <Text color={'contrast'}>
          {isUnder18
            ? formatMessage(
                {
                  id: 'consent.consent-form.youth.sub-header',
                },
                { bold: (chunks: (string | React.ReactNode)[]) => <strong>{chunks}</strong> }
              )
            : format('consent.consent-form.adult.sub-header')}
        </Text>
      </div>
      {!isErrorLoadingAthleteDetails && athleteDetails ? <AthleteDetails athleteDetails={athleteDetails} /> : null}
      <div className={styles.termsContainer}>
        <Text size={'large'} className={styles.labelText}>
          {isUnder18 ? format('consent.consent-form.youth.privacy') : format('consent.consent-form.adult.privacy')}
        </Text>
        <div>
          <div className={styles.reviewTermsTextContainer}>
            <Text size={'large'}>{format('consent.consent-form.review-terms')}</Text>
            <Text color={'critical'}> *</Text>
          </div>
          <div className={styles.legalContainer} onScroll={handleLegalTermsScroll}>
            <Text size={'large'} className={styles.legalText}>
              {legalText}
            </Text>
          </div>
        </div>
      </div>
      <Tooltip
        content={format('consent.consent-form.checkbox.hover-text')}
        tooltipClassName={hasReadTerms ? styles.hideTooltip : ''}
        isOpen={!hasReadTerms && isCheckboxTooltipVisible} // Needed for mobile
      >
        <div onMouseEnter={onMouseEnterCheckbox} onMouseLeave={onMouseLeaveCheckbox}>
          <CheckboxGroup
            onChange={onCheckboxChange}
            isDisabled={!hasReadTerms}
            valuesChecked={consentDecision ? [consentDecision] : []}
            className={styles.checkboxGroup}
            formSize={'large'}
          >
            <Checkbox
              value="grant"
              label={
                isUnder18
                  ? format('consent.consent-form.youth.checkbox.grant')
                  : format('consent.consent-form.adult.checkbox.grant')
              }
            />
            <Checkbox
              value="deny"
              label={
                isUnder18
                  ? format('consent.consent-form.youth.checkbox.deny')
                  : format('consent.consent-form.adult.checkbox.deny')
              }
            />
          </CheckboxGroup>
        </div>
      </Tooltip>
      <div className={styles.buttonContainer}>
        <Tooltip
          content={format('consent.consent-form.submit-button.hover-text')}
          tooltipClassName={consentDecision ? styles.hideTooltip : ''}
        >
          <Button className={styles.submitButton} onPress={onSubmit} isDisabled={isUpdatingState || !consentDecision}>
            {format('consent.consent-form.submit-button')}
          </Button>
        </Tooltip>
      </div>
      <HudlDescription />
    </div>
  );
}
