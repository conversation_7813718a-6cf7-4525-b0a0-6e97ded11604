.container {
  display: flex;
  flex-direction: column;
  max-width: 885px;
  width: 100%;
  align-items: stretch;
  gap: var(--u-space-two);
}

.headerContainer {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one);
}

.headerText {
  color: var(--u-color-content-default);
  font-size: calc(var(--u-space-two) + var(--u-space-half));
  font-weight: 400;
  line-height: 120%; /* 48px */
  letter-spacing: -1px;
}

.termsContainer {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one-and-half);
}

.labelText {
  font-weight: 700;
  line-height: 150%;
}

.reviewTermsTextContainer {
  display: flex;
  flex-direction: row;
}

.legalContainer {
  width: 100%;
  height: 14.25rem;
  min-height: 7.5rem;
  background-color: var(--u-color-background-callout);
  border-radius: var(--small, 2px);
  border: 1px solid var(--u-color-line-subtle);
  opacity: 0.8;
  padding: var(--u-space-three-quarter) var(--u-space-one);
  overflow-y: auto;
  overflow-x: hidden;
}

.legalText {
  white-space: pre-wrap;
}

.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-half);
}

.buttonContainer {
  display: flex;
  margin-left: auto;
}

.submitButton {
  width: fit-content;
}

.hideTooltip {
  display: none;
}

.hudlLogo {
  width: 150px;
  margin: var(--u-space-two) auto;
}
