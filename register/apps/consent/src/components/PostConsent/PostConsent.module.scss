.postConsentContentContainer {
  display: flex;
  flex-direction: column;
  max-width: 900px;
  align-items: stretch;
  gap: var(--u-space-two);
}

.headerContainer {
  display: flex;
  flex-direction: column;
  gap: var(--u-space-one);
}

.headerText {
  color: var(--u-color-content-default);
  font-size: calc(var(--u-space-two) + var(--u-space-half));
  font-weight: 400;
  line-height: 120%; /* 48px */
  letter-spacing: -1px;
}

.learnMoreText {
  font-weight: 700;
  line-height: 150%;
}

.buttonContainer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: var(--u-space-one);
}

.consentIcon {
  margin: 0 auto;
}

.consentIconDenied {
  path {
    fill: var(--u-color-utility-information);
  }
  circle {
    stroke: var(--u-color-utility-information);
  }
}

.consentIconGranted {
  path {
    fill: var(--u-color-utility-confirmation);
  }
  circle {
    stroke: var(--u-color-utility-confirmation);
  }
}
