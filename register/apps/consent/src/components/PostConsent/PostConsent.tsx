import React, { type ReactNode, useCallback } from 'react';

import { <PERSON><PERSON>, <PERSON>vide<PERSON>, <PERSON>, Text, Title } from '@hudl/uniform-web';
import { format, formatMessage } from 'frontends-i18n';

import { ConsentAthleteDetails } from '../../types/ConsentAthleteDetails';
import { CheckMark } from '../icons/CheckMark';
import { AthleteDetails } from '../shared/AthleteDetails/AthleteDetails';
import { HudlDescription } from '../shared/HudlDescription/HudlDescription';

import styles from './PostConsent.module.scss';

export type PostConsentProps = {
  isConsentGranted: boolean;
  isUnder18: boolean;
  athleteDetails: ConsentAthleteDetails | null;
  onGoBack: () => void;
};

export function PostConsent({ isConsentGranted, isUnder18, athleteDetails, onGoBack }: PostConsentProps): ReactNode {
  const handleGoBack = useCallback((): void => {
    onGoBack();
  }, [onGoBack]);

  const onLearnMore = useCallback((): void => {
    location.href = 'https://www.hudl.com/support/athletes-guide-to-hudl/guides';
  }, []);

  const onLogIn = useCallback((): void => {
    location.href = 'https://www.hudl.com/login';
  }, []);

  const onCreateAccount = useCallback((): void => {
    location.href = 'https://www.hudl.com/login?screen_hint=signup';
  }, []);

  if (!isConsentGranted) {
    return (
      <div className={styles.postConsentContentContainer}>
        <CheckMark isConsentGranted={false} />
        <div className={styles.headerContainer}>
          <Title as={'h1'} size={'xxlarge'} className={styles.headerText}>
            {isUnder18
              ? format('consent.post-consent.denied.youth.header', {
                  name: athleteDetails?.fullName ?? format('consent.shared.default-name'),
                })
              : format('consent.post-consent.denied.adult.header')}
          </Title>
          <Text>
            {isUnder18
              ? formatMessage(
                  {
                    id: 'consent.post-consent.denied.youth.sub-header',
                  },
                  {
                    bold: (chunks: (string | React.ReactNode)[]) => <strong>{chunks}</strong>,
                    grantConsentLink: (
                      <Link type="article" onClick={handleGoBack}>
                        {format('consent.post-consent.denied.grant-consent-link')}
                      </Link>
                    ),
                  }
                )
              : formatMessage(
                  {
                    id: 'consent.post-consent.denied.adult.sub-header',
                  },
                  {
                    bold: (chunks: (string | React.ReactNode)[]) => <strong>{chunks}</strong>,
                    grantConsentLink: (
                      <Link type="article" onClick={handleGoBack}>
                        {format('consent.post-consent.denied.grant-consent-link')}
                      </Link>
                    ),
                  }
                )}
          </Text>
        </div>
        {athleteDetails && <AthleteDetails athleteDetails={athleteDetails} />}
        <Divider orientation="horizontal" />
        <Text size={'large'} className={styles.learnMoreText}>
          {isUnder18
            ? format('consent.post-consent.denied.youth.learn-more')
            : format('consent.post-consent.denied.adult.learn-more')}
        </Text>
        <div className={styles.buttonContainer}>
          <Button onPress={onLearnMore}>{format('consent.post-consent.denied.learn-more-button')}</Button>
        </div>
        <HudlDescription />
      </div>
    );
  }

  return (
    <div className={styles.postConsentContentContainer}>
      <CheckMark isConsentGranted={true} />
      <div className={styles.headerContainer}>
        <Title as={'h1'} size={'xxlarge'} className={styles.headerText}>
          {isUnder18
            ? format('consent.post-consent.granted.youth.header', {
                name: athleteDetails?.fullName ?? format('consent.shared.default-name'),
              })
            : format('consent.post-consent.granted.adult.header')}
        </Title>
        <Text>
          {isUnder18
            ? formatMessage(
                { id: 'consent.post-consent.granted.youth.sub-header' },
                {
                  bold: (chunks: (string | React.ReactNode)[]) => <strong>{chunks}</strong>,
                  updateConsentLink: (
                    <Link type="article" onClick={handleGoBack}>
                      {format('consent.post-consent.granted.youth.update-consent')}
                    </Link>
                  ),
                }
              )
            : formatMessage(
                { id: 'consent.post-consent.granted.adult.sub-header' },
                {
                  bold: (chunks: (string | React.ReactNode)[]) => <strong>{chunks}</strong>,
                  updateConsentLink: (
                    <Link type="article" onClick={handleGoBack}>
                      {format('consent.post-consent.granted.adult.update-consent')}
                    </Link>
                  ),
                }
              )}
        </Text>
      </div>
      {athleteDetails && <AthleteDetails athleteDetails={athleteDetails} />}
      <Divider orientation="horizontal" />
      {isUnder18 && (
        <div>
          <Text size={'large'} className={styles.learnMoreText}>
            {format('consent.post-consent.granted.youth.create-account')}
          </Text>
          <Text>{format('consent.post-consent.granted.youth.create-account.description')}</Text>
        </div>
      )}
      <div className={styles.buttonContainer}>
        <Button onPress={onLogIn} buttonType={'secondary'}>
          {format('consent.shared.log-in')}
        </Button>
        {isUnder18 && <Button onPress={onCreateAccount}>{format('consent.shared.create-my-own-account')}</Button>}
      </div>
      <HudlDescription />
    </div>
  );
}
