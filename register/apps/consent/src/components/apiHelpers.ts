import { getMarvelOrigin } from '@hudl/frontends-environment';

import { ConsentState } from '../enums/enums';
import { Auth0ContinueResponse } from '../types/Auth0ContinueResponse';
import { ConsentAthleteDetails } from '../types/ConsentAthleteDetails';
import { ConsentInformation } from '../types/ConsentInformation';
import { getUserLocale } from './utils/localeUtils';

export type VerifySignatureParams = {
  status: string;
  externalPayload: string;
  signature: string;
};

export type UpdateConsentStatusStateParams = VerifySignatureParams & {
  consentState: ConsentState;
};

export class TooManyRequestConsentCallsError extends Error {
  public constructor() {
    super('Too many requests for consent');
  }
}

export const verifyRedirectSignature = async (verifySignatureParams: VerifySignatureParams): Promise<boolean> => {
  const url = `${getMarvelOrigin()}/api/v2/user-consent/verify-redirect-signature`;
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        status: verifySignatureParams.status,
        externalPayload: verifySignatureParams.externalPayload,
        signature: verifySignatureParams.signature,
      }),
    });

    if (!response) {
      console.error('Failed to verify signature; null response:', response);
      return false;
    }
    const responseText = await response?.text();
    if (!response?.ok) {
      console.error('Failed to verify signature:', response, responseText);
      return false;
    }
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
};

export const updateConsentStatusState = async (updateStateParams: UpdateConsentStatusStateParams): Promise<boolean> => {
  const url = `${getMarvelOrigin()}/api/v2/user-consent/update-consent-status-state`;
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        status: updateStateParams.status,
        externalPayload: updateStateParams.externalPayload,
        signature: updateStateParams.signature,
        consentState: updateStateParams.consentState,
      }),
    });

    if (!response) {
      console.error('Failed to update consent status state; null response:', response);
      return false;
    }
    const responseText = await response?.text();
    if (!response?.ok) {
      console.error('Failed to update consent status state:', response, responseText);
      return false;
    }
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
};

export const getUserConsentStatuses = async (token: string): Promise<ConsentInformation> => {
  const url = `${getMarvelOrigin()}/api/v2/user-consent/get-consent-statuses-by-token?jwt=${encodeURIComponent(token)}`;
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const responseText = await response.text();
      console.error('Failed to get user consent statuses: ', response, responseText);
      // TODO - should these error states throw an error or return an error state?
      return {} as ConsentInformation;
    }
    try {
      const data = (await response.json()) as ConsentInformation;
      return data;
    } catch (jsonError) {
      console.error('Failed to parse user consent statuses JSON:', jsonError);
      return {} as ConsentInformation;
    }
  } catch (error) {
    console.error(error);
    return {} as ConsentInformation;
  }
};

export const getAthleteDetails = async (signatureParams: VerifySignatureParams): Promise<ConsentAthleteDetails> => {
  const queryParams = new URLSearchParams({
    status: signatureParams.status,
    externalPayload: signatureParams.externalPayload,
    signature: signatureParams.signature,
  });
  const url = `${getMarvelOrigin()}/api/v2/user-consent/get-athlete-details?${queryParams.toString()}`;
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const responseText = await response.text();
    throw new Error(`Failed to get athlete details: ${response.status} ${responseText}`);
  }
  try {
    const data = (await response.json()) as ConsentAthleteDetails;
    return data;
  } catch (jsonError) {
    throw new Error(`Failed to parse athlete details JSON: ${jsonError}`);
  }
};

export const requestConsent = async (
  auth0RedirectToken: string,
  auth0State: string,
  dateOfBirth: string,
  consentStatusId: string,
  providerEmail: string,
  isResendReminder?: boolean
): Promise<string | undefined> => {
  const url = `${getMarvelOrigin()}/api/v2/user-consent/request-consent-by-token`;
  try {
    const response = await fetch(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        auth0RedirectToken,
        auth0State,
        consentStatusId,
        providerEmail,
        language: getUserLocale(),
        dateOfBirth,
        isReSendReminder: isResendReminder || false,
      }),
    });

    if (!response.ok) {
      if (response.status === 429) {
        throw new TooManyRequestConsentCallsError();
      }
      const responseText = await response.text();
      console.error('Failed to request consent: ', response, responseText);
      throw new Error('Failed to request consent: ' + responseText);
    }
    try {
      const data = (await response.json()) as Auth0ContinueResponse;
      console.log('Request consent response:', data);
      return data.redirectUri;
    } catch (jsonError) {
      console.error('Failed to parse user data JSON: ', jsonError);
      return;
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
};
