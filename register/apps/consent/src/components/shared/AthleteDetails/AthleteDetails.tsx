import React from 'react';

import { Text } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import { ConsentAthleteDetails } from '../../../types/ConsentAthleteDetails';
import { AthleteDetailsChip, AthleteDetailsChipType } from '../AthleteDetailsChip/AthleteDetailsChip';

import styles from './AthleteDetails.module.scss';

type AthleteDetailsProps = {
  athleteDetails: ConsentAthleteDetails;
};

export function AthleteDetails({ athleteDetails }: AthleteDetailsProps): React.JSX.Element {
  return (
    <div className={styles.container}>
      <Text size={'large'} className={styles.labelText}>
        {format('consent.shared.athlete-details')}
      </Text>
      {(athleteDetails.fullName || athleteDetails.age) && (
        <div className={styles.row}>
          {athleteDetails.fullName && (
            <AthleteDetailsChip
              text={`${athleteDetails.fullName}`}
              imageUrl={athleteDetails.image}
              type={AthleteDetailsChipType.Athlete}
            />
          )}
          {athleteDetails.age && (
            <AthleteDetailsChip
              text={format('consent.shared.athlete-details.age', { age: athleteDetails.age })}
              type={AthleteDetailsChipType.Age}
            />
          )}
        </div>
      )}
      {athleteDetails.teams && (
        <div className={styles.row}>
          {athleteDetails.teams?.map((team) => (
            <AthleteDetailsChip
              text={`${athleteDetails.organizationName} • ${team.name}`}
              imageUrl={team.image}
              type={AthleteDetailsChipType.Team}
              key={team.name}
            />
          ))}
        </div>
      )}
    </div>
  );
}
