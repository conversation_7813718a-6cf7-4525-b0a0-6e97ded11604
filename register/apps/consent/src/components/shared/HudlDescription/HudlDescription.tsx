import React from 'react';

import { Divider, <PERSON>, Text } from '@hudl/uniform-web';
import { format, formatMessage } from 'frontends-i18n';

import styles from './HudlDescription.module.scss';

export function HudlDescription(): React.JSX.Element {
  return (
    <div className={styles.container}>
      <Divider />
      <Text size={'small'} color={'subtle'}>
        {formatMessage(
          { id: 'consent.shared.hudl-description' },
          {
            privacyPolicyLink: (
              <Link className={styles.sendReminderText} type="article" href={'https://www.hudl.com/privacy'}>
                {format('consent.shared.hudl-description.privacy-policy')}
              </Link>
            ),
            termsOfServiceLink: (
              <Link className={styles.sendReminderText} type="article" href={'https://www.hudl.com/terms'}>
                {format('consent.shared.hudl-description.terms-of-service')}
              </Link>
            ),
          }
        )}
      </Text>
    </div>
  );
}
