import React from 'react';

import { AvatarTeam, AvatarUser, Text } from '@hudl/uniform-web';

import styles from './AthleteDetailsChip.module.scss';

export enum AthleteDetailsChipType {
  Athlete = 'athlete',
  Team = 'team',
  Age = 'age',
}

type AthleteDetailsChipProps = {
  text?: string;
  imageUrl?: string;
  type: AthleteDetailsChipType;
};

export function AthleteDetailsChip({ text, imageUrl, type }: AthleteDetailsChipProps): React.JSX.Element {
  const shouldShowAvatar = type === AthleteDetailsChipType.Athlete || type === AthleteDetailsChipType.Team;

  return (
    <div className={styles.container}>
      {shouldShowAvatar &&
        (type === AthleteDetailsChipType.Team ? (
          <AvatarTeam imageUrl={imageUrl} size={'xsmall'} />
        ) : (
          <AvatarUser imageUrl={imageUrl} size={'xsmall'} />
        ))}
      <Text color={'subtle'} size={'small'} className={styles.text}>
        {text}
      </Text>
    </div>
  );
}
