import React, { type ReactNode, useCallback, useState } from 'react';

import { getMarvelOrigin } from '@hudl/frontends-environment';
import { IconUiNavigationForwardArrow, Link, Text, Title, ToastMessenger } from '@hudl/uniform-web';
import { format, formatMessage } from 'frontends-i18n';

import { CheckMark } from '../icons/CheckMark';
import { ADULT_CONSENT_AGE } from '../utils/ageUtils';

import styles from './styles.module.scss';

type ConsentPendingProps = {
  age: number | null;
  providerEmail: string;
  canSendReminder: boolean;
  canReEnterEmail: boolean;
  handleSendReminder: () => void;
  handleReEnterEmail: () => void;
};

export function ConsentPending(props: ConsentPendingProps): React.JSX.Element {
  const [hasSentEmailReminder, setHasSentEmailReminder] = useState(false);

  const handleSendReminder = useCallback(() => {
    props.handleSendReminder();
    setHasSentEmailReminder(true);
  }, [props.handleSendReminder]);

  const renderRow = (text: string): ReactNode => {
    return (
      <div className={styles.arrowRow}>
        <IconUiNavigationForwardArrow />
        <Text>{text}</Text>
      </div>
    );
  };

  const renderYouthContent = (): ReactNode => {
    const sendReminderText = format('consent.consent-pending.youth.send-reminder.link-text');
    const youthSendEmailReminderLink = hasSentEmailReminder ? (
      <Text className={styles.sendReminderText}>{sendReminderText}</Text>
    ) : (
      <Link className={styles.sendReminderText} type="article" onClick={handleSendReminder}>
        {sendReminderText}
      </Link>
    );
    const guardianEmailText = props.providerEmail
      ? format('consent.consent-pending.youth.we-emailed-your-guardian.with-email', {
          providerEmail: props.providerEmail,
        })
      : format('consent.consent-pending.youth.we-emailed-your-guardian');
    return (
      <>
        {renderRow(guardianEmailText)}
        {renderRow(format('consent.consent-pending.youth.you-will-be-able'))}
        {props.canSendReminder && (
          <Text>
            {formatMessage(
              { id: 'consent.consent-pending.youth.send-reminder' },
              { sendReminderLink: youthSendEmailReminderLink }
            )}
          </Text>
        )}
        {props.canReEnterEmail && (
          <>
            {renderRow(format('consent.consent-pending.youth.change-email'))}
            <Text>
              {formatMessage(
                { id: 'consent.consent-pending.youth.re-enter-email' },
                {
                  reEnterEmailLink: (
                    <Link type="article" onClick={props.handleReEnterEmail}>
                      {format('consent.consent-pending.youth.re-enter-email.link-text')}
                    </Link>
                  ),
                }
              )}
            </Text>
          </>
        )}
      </>
    );
  };

  const renderAdultContent = (): ReactNode => {
    const adultSendEmailReminderComponent = hasSentEmailReminder ? (
      <Text className={styles.sendReminderText}>{format('consent.consent-pending.adult.reminder-email-text')}</Text>
    ) : (
      <Text>
        {formatMessage(
          { id: 'consent.consent-pending.adult.reminder-email-link' },
          {
            reSendEmailLink: (
              <Link className={styles.sendReminderText} type="article" onClick={handleSendReminder}>
                {format('consent.consent-pending.adult.reminder-email-link.link-text')}
              </Link>
            ),
          }
        )}
      </Text>
    );
    return (
      <>
        {props.providerEmail &&
          renderRow(
            format('consent.consent-pending.adult.open-email', {
              providerEmail: props.providerEmail,
            })
          )}
        {renderRow(format('consent.consent-pending.adult.you-will-be-able'))}
        {props.canSendReminder && adultSendEmailReminderComponent}
        {props.canReEnterEmail && (
          <>
            {renderRow(format('consent.consent-pending.adult.need-to-change-email'))}
            <Text>
              {formatMessage(
                { id: 'consent.consent-pending.adult.re-enter-email' },
                {
                  reEnterEmailLink: (
                    <Link type="article" onClick={props.handleReEnterEmail}>
                      {format('consent.consent-pending.adult.re-enter-email.link-text')}
                    </Link>
                  ),
                }
              )}
            </Text>
          </>
        )}
      </>
    );
  };

  const isUnderAdultAge = !props.age || props.age < ADULT_CONSENT_AGE;
  const titleText = isUnderAdultAge
    ? format('consent.consent-pending.title-text.youth')
    : format('consent.consent-pending.title-text.adult');
  return (
    <div className={styles.container}>
      <ToastMessenger />
      <CheckMark isConsentGranted={true} />
      <Title as="h3">{titleText}</Title>
      <div className={styles.arrowListContainer}>
        <Text size="xsmall" className={styles.almostThereText}>
          {format('consent.consent-pending.almost-there')}
        </Text>
        {isUnderAdultAge ? renderYouthContent() : renderAdultContent()}
      </div>
      <Link className={styles.backLink} type="implied" href={`${getMarvelOrigin()}/logout`}>
        {format('consent.shared.back-to-log-in')}
      </Link>
    </div>
  );
}
