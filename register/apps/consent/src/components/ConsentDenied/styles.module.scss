.container {
  display: flex;
  flex-direction: column;
  max-width: 900px;
  justify-content: center;
  align-items: center;
  gap: var(--u-space-three-quarter);
}

.arrowListContainer {
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(237, 240, 242, 1); // light var(--u-color-bg-level0-accent)
  border-radius: var(--u-space-quarter);
  padding: var(--u-space-one-and-half) var(--u-space-one-and-quarter);
  gap: var(--u-space-three-quarter);
}

.arrowRow {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--u-space-half);

  p {
    font-weight: bold;
  }

  .arrowIcon {
    width: 20px;
    height: 20px;
  }
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--u-space-half);

  .title {
    font-weight: var(--u-font-weight-default);
    font-size: 22px;
    text-align: center;
  }
}

.backLink {
  margin-top: var(--u-space-one);
  &.backLink:hover {
    background: none;
  }
  .backLinkText {
    font-weight: 700;
  }
}
