import React, { type ReactNode, useCallback, useEffect } from 'react';

import { useLoaderData, useNavigate } from 'react-router-dom';

import { Button, IconUiNavigationForwardArrow, Text } from '@hudl/uniform-web';
import { format } from 'frontends-i18n';

import { ConsentState } from '../../enums/enums';
import { ConsentLoaderData } from '../../utils/ConsentLoader';
import { Exclamation } from '../icons/Exclamation';

import styles from './styles.module.scss';

export function ConsentDenied(): React.JSX.Element {
  const { consentData, token } = useLoaderData() as ConsentLoaderData;
  const navigate = useNavigate();

  const deniedConsentStatuses = consentData.consents?.filter((consent) => consent.state === ConsentState.Denied) ?? [];
  const isUnderage =
    deniedConsentStatuses.length > 0 &&
    deniedConsentStatuses[0].subjectEmail !== deniedConsentStatuses[0].providerEmail;
  const email = deniedConsentStatuses[0].providerEmail || '';

  useEffect(() => {
    if (!token) {
      navigate('/consent/error');
    }
  }, [token, navigate]);

  const renderRow = (text: string): ReactNode => {
    return (
      <div className={styles.arrowRow}>
        <div>
          <IconUiNavigationForwardArrow className={styles.arrowIcon} />
        </div>
        <Text color="subtle">{text}</Text>
      </div>
    );
  };

  const renderUnderageDeniedContent = (): ReactNode => {
    return (
      <div className={styles.arrowListContainer}>
        {renderRow(format('consent.denied-consent.parent-guardian-consent.email-on-file', { email }))}
        {renderRow(format('consent.denied-consent.parent-guardian-consent.no-account-or-team-access'))}
        {renderRow(format('consent.denied-consent.parent-guardian-consent.no-account-access'))}
      </div>
    );
  };

  const renderAdultDeniedContent = (): ReactNode => {
    return (
      <div className={styles.arrowListContainer}>
        {renderRow(format('consent.denied-consent.adult-consent.email-on-file', { email }))}
        {renderRow(format('consent.denied-consent.adult-consent.unable-to-access-account'))}
      </div>
    );
  };

  const title = isUnderage
    ? format('consent.denied-consent.parent-guardian-consent.header')
    : format('consent.denied-consent.adult-consent.header');

  const handleBackToLogin = useCallback(() => {
    navigate('/login');
  }, [navigate]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Exclamation />
        <Text className={styles.title}>{title}</Text>
      </div>
      {isUnderage ? renderUnderageDeniedContent() : renderAdultDeniedContent()}
      <Button className={styles.backLink} buttonStyle="minimal" onPress={handleBackToLogin}>
        <Text color="subtle" className={styles.backLinkText}>
          {format('consent.back-to-log-in')}
        </Text>
      </Button>
    </div>
  );
}
