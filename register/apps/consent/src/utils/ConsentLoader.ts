import { redirect } from 'react-router-dom';

import { getUserConsentStatuses } from '../components/apiHelpers';
import { ConsentState } from '../enums/enums';
import { ConsentInformation } from '../types/ConsentInformation';

export type ConsentLoaderData = {
  consentData: ConsentInformation;
  token: string;
};

const ConsentRoutes = {
  Error: '/consent/error',
  Denied: '/consent/denied',
  RequestConsent: '/consent/request-consent',
};

function getRouteForConsentState(consentData: ConsentInformation): string {
  if (!consentData.consents || consentData.consents.length === 0) {
    return ConsentRoutes.Error;
  }

  const consentStates = consentData.consents.map((consent) => consent.state);

  if (consentStates.includes(ConsentState.Denied)) {
    return ConsentRoutes.Denied;
  }

  if (consentStates.some((state) => state === ConsentState.NotStarted || state === ConsentState.Pending)) {
    return ConsentRoutes.RequestConsent;
  }

  return ConsentRoutes.Error;
}

export async function consentLoader({ request }: { request: Request }): Promise<Response | ConsentLoaderData> {
  try {
    const url = new URL(request.url);
    const token = url.searchParams.get('redirectToken');

    if (!token) {
      console.error('No authentication token found in URL parameters');
      throw redirect(ConsentRoutes.Error);
    }

    const consentData = await getUserConsentStatuses(token);
    if (!consentData || !consentData.consents || !Array.isArray(consentData.consents)) {
      console.error('Invalid consent data received from API');
      throw redirect(ConsentRoutes.Error);
    }

    const targetRoute = getRouteForConsentState(consentData);
    if (url.pathname !== targetRoute) {
      const targetUrl = new URL(targetRoute, url.origin);
      url.searchParams.forEach((value, key) => {
        targetUrl.searchParams.set(key, value);
      });
      throw redirect(targetUrl.toString());
    }

    return {
      consentData,
      token,
    };
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }

    console.error('Error in consent loader:', error);
    throw redirect(ConsentRoutes.Error);
  }
}
