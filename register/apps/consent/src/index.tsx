import React from 'react';

import { createRoot } from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';

import { awaitPreloaders } from 'frontends-shared';

import { App } from './components/App/App';
import { ConsentDenied } from './components/ConsentDenied/ConsentDenied';
import { ConsentForm } from './components/ConsentForm/ConsentForm';
import { ErrorState } from './components/ErrorState/ErrorState';
import { RequestConsent } from './components/RequestConsent/RequestConsent';
import { RouterError } from './components/RouterError';
import { consentLoader } from './utils/ConsentLoader';

import './index.css';
import '@hudl/uniform-web/style.css';

const container = document.getElementById('root');
if (!container) {
  throw new Error('No root element found');
}
const root = createRoot(container);

const router = createBrowserRouter([
  {
    path: '/consent',
    errorElement: <RouterError />,
    element: <App />,
    loader: awaitPreloaders,
    children: [
      {
        path: '',
        element: <ConsentForm />,
      },
      {
        path: 'request-consent',
        element: <RequestConsent />,
        loader: consentLoader,
      },
      {
        path: 'denied',
        element: <ConsentDenied />,
        loader: consentLoader,
      },
      {
        path: '*',
        element: <ErrorState />,
      },
    ],
  },
]);

root.render(<RouterProvider router={router} />);
