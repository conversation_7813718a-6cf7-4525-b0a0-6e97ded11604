{"version": 1.1, "pre-load-external-sets": [], "sets": {"consent": {"keys": ["consent.consent-form.checkbox.hover-text", "consent.consent-form.review-terms", "consent.consent-form.submit-button", "consent.consent-form.submit-button.hover-text", "consent.consent-form.adult.checkbox.deny", "consent.consent-form.adult.checkbox.grant", "consent.consent-form.adult.header", "consent.consent-form.adult.privacy", "consent.consent-form.adult.sub-header", "consent.consent-form.youth.checkbox.deny", "consent.consent-form.youth.checkbox.grant", "consent.consent-form.youth.header", "consent.consent-form.youth.privacy", "consent.consent-form.youth.sub-header", "consent.consent-pending.title-text.youth", "consent.consent-pending.title-text.adult", "consent.consent-pending.almost-there", "consent.consent-pending.youth.we-emailed-your-guardian", "consent.consent-pending.youth.we-emailed-your-guardian.with-email", "consent.consent-pending.youth.you-will-be-able", "consent.consent-pending.youth.send-reminder", "consent.consent-pending.youth.send-reminder.link-text", "consent.consent-pending.youth.change-email", "consent.consent-pending.youth.re-enter-email", "consent.consent-pending.youth.re-enter-email.link-text", "consent.consent-pending.adult.open-email", "consent.consent-pending.adult.you-will-be-able", "consent.consent-pending.adult.reminder-email-text", "consent.consent-pending.adult.reminder-email-link", "consent.consent-pending.adult.reminder-email-link.link-text", "consent.consent-pending.adult.need-to-change-email", "consent.consent-pending.adult.re-enter-email", "consent.consent-pending.adult.re-enter-email.link-text", "consent.post-consent.denied.grant-consent-link", "consent.post-consent.denied.learn-more-button", "consent.post-consent.denied.adult.header", "consent.post-consent.denied.adult.sub-header", "consent.post-consent.denied.adult.learn-more", "consent.post-consent.denied.youth.header", "consent.post-consent.denied.youth.sub-header", "consent.post-consent.denied.youth.learn-more", "consent.post-consent.granted.adult.header", "consent.post-consent.granted.adult.sub-header", "consent.post-consent.granted.adult.update-consent", "consent.post-consent.granted.youth.create-account", "consent.post-consent.granted.youth.create-account.description", "consent.post-consent.granted.youth.header", "consent.post-consent.granted.youth.sub-header", "consent.post-consent.granted.youth.update-consent", "consent.request-consent.birthday-text", "consent.request-consent.continue-button", "consent.request-consent.date-of-birth.label", "consent.request-consent.enter-email-prompt.under-13", "consent.request-consent.enter-email-prompt.under-18", "consent.request-consent.enter-email-prompt.over-18", "consent.request-consent.email-input.label", "consent.request-consent.email-input.adult-label", "consent.request-consent.email-input.placeholder", "consent.request-consent.email-input.adult-placeholder", "consent.request-consent.email-input.help-text", "consent.request-consent.email-input.invalid-email-text", "consent.request-consent.email-input.invalid-matching-email-text", "consent.request-consent.error-page.content", "consent.request-consent.reminder-email.success", "consent.request-consent.reminder-email.failure", "consent.request-consent.reminder-email.failure.retry", "consent.request-consent.reminder-email.too-many-requests", "consent.request-consent.reminder-email.too-many-requests-v2", "consent.denied-consent.parent-guardian-consent.header", "consent.denied-consent.adult-consent.header", "consent.denied-consent.adult-consent.unable-to-access-account", "consent.denied-consent.adult-consent.email-on-file", "consent.denied-consent.parent-guardian-consent.no-account-access", "consent.denied-consent.parent-guardian-consent.email-on-file", "consent.denied-consent.parent-guardian-consent.no-account-or-team-access", "consent.shared.athlete-details", "consent.shared.athlete-details.age", "consent.shared.back-to-log-in", "consent.shared.default-name", "consent.shared.hudl-description", "consent.shared.hudl-description.privacy-policy", "consent.shared.hudl-description.terms-of-service", "consent.shared.log-in", "consent.shared.create-my-own-account"]}}, "base-language": {"consent.consent-form.checkbox.hover-text": "Review the information above to make your consent decision.", "consent.consent-form.review-terms": "Review the information below to make your consent decision and enable the submit button.", "consent.consent-form.submit-button": "Submit Consent Decision", "consent.consent-form.submit-button.hover-text": "Review the information above to make your consent decision and enable the submit button.", "consent.consent-form.adult.checkbox.deny": "I deny consent to be able to create my own account and access personalized athletic content.", "consent.consent-form.adult.checkbox.grant": "I grant consent to be able to create my own account and access personalized athletic content.", "consent.consent-form.adult.header": "Your Consent Is Needed to Use Hudl", "consent.consent-form.adult.privacy": "Your Privacy Is Important to Us.", "consent.consent-form.adult.sub-header": "Your school requires your consent before you can access your Hudl account.", "consent.consent-form.youth.checkbox.deny": "I deny consent for my child to create their own account and access personalized athletic content.", "consent.consent-form.youth.checkbox.grant": "I grant consent for my child to create their own account and access personalized athletic content.", "consent.consent-form.youth.header": "Provide Consent for {name} to Use Hudl", "consent.consent-form.youth.privacy": "Your Athlete’s Privacy Is Important to Us.", "consent.consent-form.youth.sub-header": "Since they’re under <bold>18</bold>, their school requires your consent before they can access their account.", "consent.consent-pending.title-text.youth": "Guardian Consent Pending", "consent.consent-pending.title-text.adult": "Provide Your Consent", "consent.consent-pending.almost-there": "Almost There!", "consent.consent-pending.youth.we-emailed-your-guardian": "We've emailed your parent or guardian.", "consent.consent-pending.youth.we-emailed-your-guardian.with-email": "We've emailed your parent or guardian: {providerEmail}.", "consent.consent-pending.youth.you-will-be-able": "You'll be able to use Hudl as soon as they give permission.", "consent.consent-pending.youth.send-reminder": "Ask them to check their email to finish this step, or {sendReminderLink} if needed.", "consent.consent-pending.youth.send-reminder.link-text": "send a reminder", "consent.consent-pending.youth.change-email": "Need to change their email?", "consent.consent-pending.youth.re-enter-email": "{reEnterEmailLink} for your parent or guardian.", "consent.consent-pending.youth.re-enter-email.link-text": "Re-enter the email", "consent.consent-pending.adult.open-email": "Open the email we sent to {providerEmail}.", "consent.consent-pending.adult.you-will-be-able": "You'll be able to use Hudl once you verify your age and give consent.", "consent.consent-pending.adult.reminder-email-text": "Re-send email.", "consent.consent-pending.adult.reminder-email-link": "{reSendEmailLink}.", "consent.consent-pending.adult.reminder-email-link.link-text": "Re-send email", "consent.consent-pending.adult.need-to-change-email": "Need to change your email?", "consent.consent-pending.adult.re-enter-email": "{reEnterEmailLink}.", "consent.consent-pending.adult.re-enter-email.link-text": "Re-enter it here", "consent.post-consent.denied.grant-consent-link": "grant consent", "consent.post-consent.denied.learn-more-button": "Learn More About Hudl", "consent.post-consent.denied.adult.header": "You’ve Denied <PERSON><PERSON> to Use Hudl", "consent.post-consent.denied.adult.sub-header": "<bold>Your school requires your consent before you can access your Hudl account.</bold> By denying consent, you will not be able to have your own account or access team content. You can update your decision at any time and {grantConsentLink}.", "consent.post-consent.denied.adult.learn-more": "Would you like to learn more about <PERSON><PERSON> and how we support your athletic development?", "consent.post-consent.denied.youth.header": "We Respect Your Decision to <PERSON><PERSON> <PERSON> for {name} to Use Hudl", "consent.post-consent.denied.youth.sub-header": "<bold>Since they’re under 18, their school requires your consent before they can access their account.</bold> By denying consent, your child will not be able to have their own account or access team content. You can update your decision at any time and {grantConsentLink}.", "consent.post-consent.denied.youth.learn-more": "Would you like to learn more about <PERSON><PERSON> and how we support athlete development?", "consent.post-consent.granted.adult.header": "Thanks for Granting <PERSON>sent—You're All Set to Use Hudl", "consent.post-consent.granted.adult.sub-header": "<bold>You can now have your own account and access team content.</bold> You can return at any time to {updateConsentLink}.", "consent.post-consent.granted.adult.update-consent": "update your decision", "consent.post-consent.granted.youth.create-account": "Create your own account to follow their athletic journey.", "consent.post-consent.granted.youth.create-account.description": "Find scores, schedules, highlights, livestreams, tickets and more—all in one place.", "consent.post-consent.granted.youth.header": "Thank You for Granting Consent for {name} to <PERSON> Hudl", "consent.post-consent.granted.youth.sub-header": "<bold>Your child now has access to their own account and team content.</bold> You can {updateConsentLink} at any time.", "consent.post-consent.granted.youth.update-consent": "update your consent", "consent.request-consent.birthday-text": "When's your birthday? We need it for tryouts, recruiting and to help keep Hudl safe for everyone.", "consent.request-consent.continue-button": "Continue", "consent.request-consent.date-of-birth.label": "Date of Birth", "consent.request-consent.enter-email-prompt.under-13": "You're under 13, so we need your parent or guardian's permission for you to use Hudl.", "consent.request-consent.enter-email-prompt.under-18": "You're under 18, so your school requires a parent or guardian's permission for you to use Hudl.", "consent.request-consent.enter-email-prompt.over-18": "Your school requires consent to use Hudl. We'll email you to verify and complete approval.", "consent.request-consent.email-input.label": "Guardian Email", "consent.request-consent.email-input.adult-label": "Your Email", "consent.request-consent.email-input.placeholder": "Enter guardian email", "consent.request-consent.email-input.adult-placeholder": "Enter your email", "consent.request-consent.email-input.help-text": "If your parent or guardian uses Hudl, enter their account email.", "consent.request-consent.email-input.invalid-email-text": "Invalid Email Address.", "consent.request-consent.email-input.invalid-matching-email-text": "This email matches yours. Please provide a guardian's email.", "consent.request-consent.error-page.content": "It looks like something went wrong. Try logging in again.", "consent.request-consent.reminder-email.success": "You sent a reminder to {emailAddress}.", "consent.request-consent.reminder-email.failure": "Unable to send a reminder. Please try again.", "consent.request-consent.reminder-email.failure.retry": "Retry", "consent.request-consent.reminder-email.too-many-requests": "You can only send one reminder per day. Please try again tomorrow.", "consent.request-consent.reminder-email.too-many-requests-v2": "You can only send one reminder per day. Please try again later.", "consent.denied-consent.parent-guardian-consent.header": "Your Parent or Guardian has Denied <PERSON><PERSON> for You to Use Hudl", "consent.denied-consent.adult-consent.header": "You’ve Denied <PERSON><PERSON> to Use Hudl", "consent.denied-consent.adult-consent.unable-to-access-account": "You will not be able to have your own account or access team content.", "consent.denied-consent.adult-consent.email-on-file": "Your email on file: {email}.", "consent.denied-consent.parent-guardian-consent.no-account-access": "You will not have access to your account until they give you permission.", "consent.denied-consent.parent-guardian-consent.email-on-file": "Your parent or guardian on file: {email}.", "consent.denied-consent.parent-guardian-consent.no-account-or-team-access": "You will not be able to have your own account or access team content.", "consent.shared.athlete-details": "Athlete Details", "consent.shared.athlete-details.age": "{age} years", "consent.shared.back-to-log-in": "Back to Log In", "consent.shared.default-name": "Your Athlete", "consent.shared.hudl-description": "Hudl is a safe, secure platform where athletes can review game footage, build skills, track their progress, and connect with coaches and teammates. View our {privacyPolicyLink} and {termsOfServiceLink}.", "consent.shared.hudl-description.privacy-policy": "Privacy Policy", "consent.shared.hudl-description.terms-of-service": "Terms of Service", "consent.shared.log-in": "Log In", "consent.shared.create-my-own-account": "Create My Own Account"}}